"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBlogDto = void 0;
const class_validator_1 = require("class-validator");
class CreateBlogDto {
    title;
    slug;
    content;
    summary;
    tags;
    coverImage;
    destination;
    places;
}
exports.CreateBlogDto = CreateBlogDto;
__decorate([
    (0, class_validator_1.IsString)({ message: 'Title must be a string.' }),
    (0, class_validator_1.Length)(5, 100, {
        message: 'Title must be between 5 and 100 characters.',
    }),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Slug must be a string.' }),
    (0, class_validator_1.Length)(3, 100, {
        message: 'Slug must be between 3 and 100 characters.',
    }),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "slug", void 0);
__decorate([
    (0, class_validator_1.IsString)({ message: 'Content must be a string.' }),
    (0, class_validator_1.Length)(20, 5000, {
        message: 'Content must be between 20 and 5000 characters.',
    }),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Summary must be a string.' }),
    (0, class_validator_1.MaxLength)(300, {
        message: 'Summary must not exceed 300 characters.',
    }),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "summary", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Tags must be an array of strings.' }),
    (0, class_validator_1.ArrayMaxSize)(10, {
        message: 'A maximum of 10 tags is allowed.',
    }),
    (0, class_validator_1.IsString)({ each: true, message: 'Each tag must be a string.' }),
    __metadata("design:type", Array)
], CreateBlogDto.prototype, "tags", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Cover image must be a string URL.' }),
    (0, class_validator_1.MaxLength)(500, {
        message: 'Cover image URL must not exceed 500 characters.',
    }),
    __metadata("design:type", String)
], CreateBlogDto.prototype, "coverImage", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Destination must be a string.' }),
    __metadata("design:type", Object)
], CreateBlogDto.prototype, "destination", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Places must be an array.' }),
    (0, class_validator_1.ArrayMaxSize)(5, {
        message: 'A maximum of 5 places is allowed.',
    }),
    __metadata("design:type", Array)
], CreateBlogDto.prototype, "places", void 0);
//# sourceMappingURL=create-blog.dto.js.map