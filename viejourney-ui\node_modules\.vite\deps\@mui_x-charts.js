import {
  reactMajor_default,
  useComponentRenderer
} from "./chunk-2TGZILUC.js";
import {
  Bar<PERSON>hart,
  BarElement,
  BarLabel,
  BarPlot,
  ChartsAxis,
  ChartsAxisHighlight,
  ChartsAxisHighlightPath,
  ChartsClipPath,
  ChartsGrid,
  ChartsText,
  ChartsXAxis,
  ChartsYAxis,
  axisClasses,
  barClasses,
  barElementClasses,
  barLabelClasses,
  chartsAxisHighlightClasses,
  chartsGridClasses,
  getAxisHighlightUtilityClass,
  getAxisUtilityClass,
  getBarElementUtilityClass,
  getBarLabelUtilityClass,
  getBarUtilityClass,
  getChartsGridUtilityClass,
  getDefaultBaseline,
  getDefaultTextAnchor,
  useInternalIsZoomInteracting,
  useUtilityClasses
} from "./chunk-C6M2VKWF.js";
import {
  PieArc,
  PieArcLabel,
  PieArcLabelPlot,
  PieArcPlot,
  PieChart,
  PiePlot,
  getPercentageValue,
  getPieArcLabelUtilityClass,
  getPieArcUtilityClass,
  getPieCoordinates,
  pieArcClasses,
  pieArcLabelClasses,
  pieClasses
} from "./chunk-RI6DBWEH.js";
import {
  ANIMATION_DURATION_MS,
  ANIMATION_TIMING_FUNCTION,
  AXIS_LABEL_DEFAULT_HEIGHT,
  ChartDataProvider,
  ChartProvider,
  ChartsAxisTooltipContent,
  ChartsItemTooltipContent,
  ChartsLabel,
  ChartsLabelMark,
  ChartsLegend,
  ChartsLocalizationProvider,
  ChartsOverlay,
  ChartsSurface,
  ChartsTooltip,
  ChartsTooltipCell,
  ChartsTooltipContainer,
  ChartsTooltipPaper,
  ChartsTooltipRow,
  ChartsTooltipTable,
  ChartsWrapper,
  ContinuousColorLegend,
  DEFAULT_AXIS_SIZE_HEIGHT,
  DEFAULT_AXIS_SIZE_WIDTH,
  DEFAULT_MARGINS,
  DEFAULT_RADIUS_AXIS_KEY,
  DEFAULT_ROTATION_AXIS_KEY,
  DEFAULT_X_AXIS_KEY,
  DEFAULT_Y_AXIS_KEY,
  PiecewiseColorLegend,
  Symbol,
  arc_default,
  area_default,
  bluePalette,
  bluePaletteDark,
  bluePaletteLight,
  blueberryTwilightPalette,
  blueberryTwilightPaletteDark,
  blueberryTwilightPaletteLight,
  bumpX,
  bumpY,
  catmullRom_default,
  chartsTooltipClasses,
  cheerfulFiestaPalette,
  cheerfulFiestaPaletteDark,
  cheerfulFiestaPaletteLight,
  composeClasses,
  continuousColorLegendClasses,
  cyanPalette,
  cyanPaletteDark,
  cyanPaletteLight,
  defaultizeMargin,
  defaultizeValueFormatter,
  deg2rad,
  generateSvg2rotation,
  generateUtilityClass,
  generateUtilityClasses,
  getAxisIndex,
  getChartsTooltipUtilityClass,
  getColor_default2 as getColor_default,
  getInteractionItemProps,
  getLabel,
  getSVGPoint,
  getValueToPositionMapper,
  greenPalette,
  greenPaletteDark,
  greenPaletteLight,
  isBandScale,
  labelClasses,
  labelGradientClasses,
  labelMarkClasses,
  legendClasses,
  line_default,
  linear_default,
  mangoFusionPalette,
  mangoFusionPaletteDark,
  mangoFusionPaletteLight,
  monotoneX,
  monotoneY,
  natural_default,
  number_default,
  orangePalette,
  orangePaletteDark,
  orangePaletteLight,
  piecewiseColorDefaultLabelFormatter,
  piecewiseColorLegendClasses,
  pinkPalette,
  pinkPaletteDark,
  pinkPaletteLight,
  purplePalette,
  purplePaletteDark,
  purplePaletteLight,
  rad2deg,
  rainbowSurgePalette,
  rainbowSurgePaletteDark,
  rainbowSurgePaletteLight,
  redPalette,
  redPaletteDark,
  redPaletteLight,
  selectorChartPolarCenter,
  selectorChartsHighlightXAxisIndex,
  selectorChartsInteractionRotationAxisIndex,
  selectorChartsInteractionRotationAxisValue,
  selectorChartsVoronoiIsVoronoiEnabled,
  seriesConfig,
  stepAfter,
  stepBefore,
  step_default,
  strawberrySkyPalette,
  strawberrySkyPaletteDark,
  strawberrySkyPaletteLight,
  symbolsFill,
  useAnimate,
  useAnimateArea,
  useAnimateBar,
  useAnimateBarLabel,
  useAnimateLine,
  useAnimatePieArc,
  useAnimatePieArcLabel,
  useAxesTooltip,
  useAxisTooltip,
  useBarSeries,
  useBarSeriesContext,
  useChartCartesianAxis,
  useChartContainerProps,
  useChartContext,
  useChartGradientId,
  useChartGradientIdBuilder,
  useChartGradientIdObjectBound,
  useChartHighlight,
  useChartId,
  useChartInteraction,
  useChartPolarAxis,
  useChartRootRef,
  useChartVoronoi,
  useChartZAxis,
  useChartsLocalization,
  useChartsSlots,
  useDrawingArea,
  useForkRef,
  useId,
  useInteractionAllItemProps,
  useInteractionItemProps,
  useItemHighlighted,
  useItemHighlightedGetter,
  useItemTooltip,
  useLegend,
  useLineSeries,
  useLineSeriesContext,
  useMouseTracker,
  usePieSeries,
  usePieSeriesContext,
  useRadarItemTooltip,
  useRadarSeries,
  useRadarSeriesContext,
  useRadiusAxes,
  useRadiusAxis,
  useRadiusScale,
  useRotationAxes,
  useRotationAxis,
  useRotationScale,
  useScatterSeries,
  useScatterSeriesContext,
  useSelector,
  useSeries,
  useSkipAnimation,
  useSlotProps_default,
  useStore,
  useSvgRef,
  useXAxes,
  useXAxis,
  useXColorScale,
  useXScale,
  useYAxes,
  useYAxis,
  useYColorScale,
  useYScale,
  useZAxes,
  useZAxis,
  useZColorScale,
  yellowPalette,
  yellowPaletteDark,
  yellowPaletteLight
} from "./chunk-ZSDK7NHJ.js";
import "./chunk-WQN2LJTS.js";
import "./chunk-T7G2LBTZ.js";
import {
  warnOnce
} from "./chunk-TTGKUZR6.js";
import "./chunk-EMO2UJSG.js";
import "./chunk-DOHLSIW5.js";
import "./chunk-OUABY33R.js";
import "./chunk-PCFUJVZ7.js";
import {
  useThemeProps
} from "./chunk-ZZOFVYJW.js";
import "./chunk-LD57ITAG.js";
import "./chunk-ZEQGI43S.js";
import "./chunk-6F3VBANY.js";
import "./chunk-QGU3KYMG.js";
import "./chunk-AACZXOME.js";
import "./chunk-AE7QQJLU.js";
import "./chunk-IC44O6WL.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-FD6QZ34J.js";
import "./chunk-76ZCIU6B.js";
import "./chunk-4YKN7KBD.js";
import "./chunk-NJNAB6DJ.js";
import "./chunk-4ZW4DUFH.js";
import "./chunk-MFXI75J6.js";
import "./chunk-32BHBKTK.js";
import "./chunk-L7VF6BRO.js";
import "./chunk-HT2LQK3A.js";
import "./chunk-QB4SDFOZ.js";
import {
  styled_default,
  useTheme
} from "./chunk-2364KQYK.js";
import "./chunk-KRX2OWMK.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  clsx,
  clsx_default,
  require_prop_types
} from "./chunk-CCWQ57J5.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-charts/esm/context/useChartApiContext.js
var React = __toESM(require_react(), 1);
function useChartApiContext() {
  const {
    publicAPI
  } = useChartContext();
  const apiRef = React.useRef(publicAPI);
  React.useEffect(() => {
    apiRef.current = publicAPI;
  }, [publicAPI]);
  return apiRef;
}

// node_modules/@mui/x-charts/esm/models/seriesType/index.js
function isDefaultizedBarSeries(series) {
  return series.type === "bar";
}
function isBarSeries(series) {
  return series.type === "bar";
}

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/ChartsReferenceLine.js
var React4 = __toESM(require_react(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/ChartsXReferenceLine.js
var React2 = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/chartsReferenceLineClasses.js
function getReferenceLineUtilityClass(slot) {
  return generateUtilityClass("MuiChartsReferenceLine", slot);
}
var referenceLineClasses = generateUtilityClasses("MuiChartsReferenceLine", ["root", "vertical", "horizontal", "line", "label"]);

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/common.js
var ReferenceLineRoot = styled_default("g")(({
  theme
}) => ({
  [`& .${referenceLineClasses.line}`]: {
    fill: "none",
    stroke: (theme.vars || theme).palette.text.primary,
    shapeRendering: "crispEdges",
    strokeWidth: 1,
    pointerEvents: "none"
  },
  [`& .${referenceLineClasses.label}`]: _extends({
    fill: (theme.vars || theme).palette.text.primary,
    stroke: "none",
    pointerEvents: "none",
    fontSize: 12
  }, theme.typography.body1)
}));

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/ChartsXReferenceLine.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var getTextParams = ({
  top,
  height,
  spacingY,
  labelAlign = "middle"
}) => {
  switch (labelAlign) {
    case "start":
      return {
        y: top + spacingY,
        style: {
          dominantBaseline: "hanging",
          textAnchor: "start"
        }
      };
    case "end":
      return {
        y: top + height - spacingY,
        style: {
          dominantBaseline: "auto",
          textAnchor: "start"
        }
      };
    default:
      return {
        y: top + height / 2,
        style: {
          dominantBaseline: "central",
          textAnchor: "start"
        }
      };
  }
};
function getXReferenceLineClasses(classes) {
  return composeClasses({
    root: ["root", "vertical"],
    line: ["line"],
    label: ["label"]
  }, getReferenceLineUtilityClass, classes);
}
function ChartsXReferenceLine(props) {
  const {
    x,
    label = "",
    spacing = 5,
    classes: inClasses,
    labelAlign,
    lineStyle,
    labelStyle,
    axisId
  } = props;
  const {
    top,
    height
  } = useDrawingArea();
  const xAxisScale = useXScale(axisId);
  const xPosition = xAxisScale(x);
  if (xPosition === void 0) {
    if (true) {
      warnOnce(`MUI X Charts: the value ${x} does not exist in the data of x axis with id ${axisId}.`, "error");
    }
    return null;
  }
  const d = `M ${xPosition} ${top} l 0 ${height}`;
  const classes = getXReferenceLineClasses(inClasses);
  const spacingX = typeof spacing === "object" ? spacing.x ?? 0 : spacing;
  const spacingY = typeof spacing === "object" ? spacing.y ?? 0 : spacing;
  const textParams = _extends({
    x: xPosition + spacingX,
    text: label,
    fontSize: 12
  }, getTextParams({
    top,
    height,
    spacingY,
    labelAlign
  }), {
    className: classes.label
  });
  return (0, import_jsx_runtime.jsxs)(ReferenceLineRoot, {
    className: classes.root,
    children: [(0, import_jsx_runtime.jsx)("path", {
      d,
      className: classes.line,
      style: lineStyle
    }), (0, import_jsx_runtime.jsx)(ChartsText, _extends({}, textParams, {
      style: _extends({}, textParams.style, labelStyle)
    }))]
  });
}
true ? ChartsXReferenceLine.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The id of the axis used for the reference value.
   * @default The `id` of the first defined axis.
   */
  axisId: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * The label to display along the reference line.
   */
  label: import_prop_types.default.string,
  /**
   * The alignment if the label is in the chart drawing area.
   * @default 'middle'
   */
  labelAlign: import_prop_types.default.oneOf(["end", "middle", "start"]),
  /**
   * The style applied to the label.
   */
  labelStyle: import_prop_types.default.object,
  /**
   * The style applied to the line.
   */
  lineStyle: import_prop_types.default.object,
  /**
   * Additional space around the label in px.
   * Can be a number or an object `{ x, y }` to distinguish space with the reference line and space with axes.
   * @default 5
   */
  spacing: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.shape({
    x: import_prop_types.default.number,
    y: import_prop_types.default.number
  })]),
  /**
   * The x value associated with the reference line.
   * If defined the reference line will be vertical.
   */
  x: import_prop_types.default.oneOfType([import_prop_types.default.instanceOf(Date), import_prop_types.default.number, import_prop_types.default.string]).isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/ChartsYReferenceLine.js
var React3 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var getTextParams2 = ({
  left,
  width,
  spacingX,
  labelAlign = "middle"
}) => {
  switch (labelAlign) {
    case "start":
      return {
        x: left + spacingX,
        style: {
          dominantBaseline: "auto",
          textAnchor: "start"
        }
      };
    case "end":
      return {
        x: left + width - spacingX,
        style: {
          dominantBaseline: "auto",
          textAnchor: "end"
        }
      };
    default:
      return {
        x: left + width / 2,
        style: {
          dominantBaseline: "auto",
          textAnchor: "middle"
        }
      };
  }
};
function getYReferenceLineClasses(classes) {
  return composeClasses({
    root: ["root", "horizontal"],
    line: ["line"],
    label: ["label"]
  }, getReferenceLineUtilityClass, classes);
}
function ChartsYReferenceLine(props) {
  const {
    y,
    label = "",
    spacing = 5,
    classes: inClasses,
    labelAlign,
    lineStyle,
    labelStyle,
    axisId
  } = props;
  const {
    left,
    width
  } = useDrawingArea();
  const yAxisScale = useYScale(axisId);
  const yPosition = yAxisScale(y);
  if (yPosition === void 0) {
    if (true) {
      warnOnce(`MUI X Charts: the value ${y} does not exist in the data of y axis with id ${axisId}.`, "error");
    }
    return null;
  }
  const d = `M ${left} ${yPosition} l ${width} 0`;
  const classes = getYReferenceLineClasses(inClasses);
  const spacingX = typeof spacing === "object" ? spacing.x ?? 0 : spacing;
  const spacingY = typeof spacing === "object" ? spacing.y ?? 0 : spacing;
  const textParams = _extends({
    y: yPosition - spacingY,
    text: label,
    fontSize: 12
  }, getTextParams2({
    left,
    width,
    spacingX,
    labelAlign
  }), {
    className: classes.label
  });
  return (0, import_jsx_runtime2.jsxs)(ReferenceLineRoot, {
    className: classes.root,
    children: [(0, import_jsx_runtime2.jsx)("path", {
      d,
      className: classes.line,
      style: lineStyle
    }), (0, import_jsx_runtime2.jsx)(ChartsText, _extends({}, textParams, {
      style: _extends({}, textParams.style, labelStyle)
    }))]
  });
}
true ? ChartsYReferenceLine.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The id of the axis used for the reference value.
   * @default The `id` of the first defined axis.
   */
  axisId: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types2.default.object,
  /**
   * The label to display along the reference line.
   */
  label: import_prop_types2.default.string,
  /**
   * The alignment if the label is in the chart drawing area.
   * @default 'middle'
   */
  labelAlign: import_prop_types2.default.oneOf(["end", "middle", "start"]),
  /**
   * The style applied to the label.
   */
  labelStyle: import_prop_types2.default.object,
  /**
   * The style applied to the line.
   */
  lineStyle: import_prop_types2.default.object,
  /**
   * Additional space around the label in px.
   * Can be a number or an object `{ x, y }` to distinguish space with the reference line and space with axes.
   * @default 5
   */
  spacing: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.shape({
    x: import_prop_types2.default.number,
    y: import_prop_types2.default.number
  })]),
  /**
   * The y value associated with the reference line.
   * If defined the reference line will be horizontal.
   */
  y: import_prop_types2.default.oneOfType([import_prop_types2.default.instanceOf(Date), import_prop_types2.default.number, import_prop_types2.default.string]).isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsReferenceLine/ChartsReferenceLine.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
function ChartsReferenceLine(props) {
  const {
    x,
    y
  } = props;
  if (x !== void 0 && y !== void 0) {
    throw new Error("MUI X Charts: The ChartsReferenceLine cannot have both `x` and `y` props set.");
  }
  if (x === void 0 && y === void 0) {
    throw new Error("MUI X Charts: The ChartsReferenceLine should have a value in `x` or `y` prop.");
  }
  if (x !== void 0) {
    return (0, import_jsx_runtime3.jsx)(ChartsXReferenceLine, _extends({}, props));
  }
  return (0, import_jsx_runtime3.jsx)(ChartsYReferenceLine, _extends({}, props));
}
true ? ChartsReferenceLine.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The id of the axis used for the reference value.
   * @default The `id` of the first defined axis.
   */
  axisId: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types3.default.object,
  /**
   * The label to display along the reference line.
   */
  label: import_prop_types3.default.string,
  /**
   * The alignment if the label is in the chart drawing area.
   * @default 'middle'
   */
  labelAlign: import_prop_types3.default.oneOf(["end", "middle", "start"]),
  /**
   * The style applied to the label.
   */
  labelStyle: import_prop_types3.default.object,
  /**
   * The style applied to the line.
   */
  lineStyle: import_prop_types3.default.object,
  /**
   * Additional space around the label in px.
   * Can be a number or an object `{ x, y }` to distinguish space with the reference line and space with axes.
   * @default 5
   */
  spacing: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.shape({
    x: import_prop_types3.default.number,
    y: import_prop_types3.default.number
  })]),
  /**
   * The x value associated with the reference line.
   * If defined the reference line will be vertical.
   */
  x: import_prop_types3.default.oneOfType([import_prop_types3.default.instanceOf(Date), import_prop_types3.default.number, import_prop_types3.default.string]),
  /**
   * The y value associated with the reference line.
   * If defined the reference line will be horizontal.
   */
  y: import_prop_types3.default.oneOfType([import_prop_types3.default.instanceOf(Date), import_prop_types3.default.number, import_prop_types3.default.string])
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineChart.js
var React20 = __toESM(require_react(), 1);
var import_prop_types15 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/AreaPlot.js
var React9 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/AreaElement.js
var React7 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/AnimatedArea.js
var React6 = __toESM(require_react(), 1);
var import_prop_types4 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/AppearingMask.js
var React5 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/internals/cleanId.js
function cleanId(id) {
  return id.replace(" ", "_");
}

// node_modules/@mui/x-charts/esm/LineChart/AppearingMask.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var appearingMaskClasses = generateUtilityClasses("MuiAppearingMask", ["animate"]);
var AnimatedRect = styled_default("rect")({
  animationName: "animate-width",
  animationTimingFunction: ANIMATION_TIMING_FUNCTION,
  animationDuration: "0s",
  [`&.${appearingMaskClasses.animate}`]: {
    animationDuration: `${ANIMATION_DURATION_MS}ms`
  },
  "@keyframes animate-width": {
    from: {
      width: 0
    }
  }
});
function AppearingMask(props) {
  const drawingArea = useDrawingArea();
  const chartId = useChartId();
  const clipId = cleanId(`${chartId}-${props.id}`);
  return (0, import_jsx_runtime4.jsxs)(React5.Fragment, {
    children: [(0, import_jsx_runtime4.jsx)("clipPath", {
      id: clipId,
      children: (0, import_jsx_runtime4.jsx)(AnimatedRect, {
        className: props.skipAnimation ? "" : appearingMaskClasses.animate,
        x: 0,
        y: 0,
        width: drawingArea.left + drawingArea.width + drawingArea.right,
        height: drawingArea.top + drawingArea.height + drawingArea.bottom
      })
    }), (0, import_jsx_runtime4.jsx)("g", {
      clipPath: `url(#${clipId})`,
      children: props.children
    })]
  });
}

// node_modules/@mui/x-charts/esm/LineChart/AnimatedArea.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var _excluded = ["skipAnimation", "ownerState"];
function AnimatedArea(props) {
  const {
    skipAnimation,
    ownerState
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const animatedProps = useAnimateArea(props);
  return (0, import_jsx_runtime5.jsx)(AppearingMask, {
    skipAnimation,
    id: `${ownerState.id}-area-clip`,
    children: (0, import_jsx_runtime5.jsx)("path", _extends({
      fill: ownerState.gradientId ? `url(#${ownerState.gradientId})` : ownerState.color,
      filter: (
        // eslint-disable-next-line no-nested-ternary
        ownerState.isHighlighted ? "brightness(140%)" : ownerState.gradientId ? void 0 : "brightness(120%)"
      ),
      opacity: ownerState.isFaded ? 0.3 : 1,
      stroke: "none",
      "data-series": ownerState.id,
      "data-highlighted": ownerState.isHighlighted || void 0,
      "data-faded": ownerState.isFaded || void 0
    }, other, animatedProps))
  });
}
true ? AnimatedArea.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  d: import_prop_types4.default.string.isRequired,
  ownerState: import_prop_types4.default.shape({
    classes: import_prop_types4.default.object,
    color: import_prop_types4.default.string.isRequired,
    gradientId: import_prop_types4.default.string,
    id: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.string]).isRequired,
    isFaded: import_prop_types4.default.bool.isRequired,
    isHighlighted: import_prop_types4.default.bool.isRequired
  }).isRequired,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types4.default.bool
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/AreaElement.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var _excluded2 = ["id", "classes", "color", "gradientId", "slots", "slotProps", "onClick"];
function getAreaElementUtilityClass(slot) {
  return generateUtilityClass("MuiAreaElement", slot);
}
var areaElementClasses = generateUtilityClasses("MuiAreaElement", ["root", "highlighted", "faded", "series"]);
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getAreaElementUtilityClass, classes);
};
function AreaElement(props) {
  const {
    id,
    classes: innerClasses,
    color,
    gradientId,
    slots,
    slotProps,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const interactionProps = useInteractionItemProps({
    type: "line",
    seriesId: id
  });
  const {
    isFaded,
    isHighlighted
  } = useItemHighlighted({
    seriesId: id
  });
  const ownerState = {
    id,
    classes: innerClasses,
    color,
    gradientId,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses2(ownerState);
  const Area = (slots == null ? void 0 : slots.area) ?? AnimatedArea;
  const areaProps = useSlotProps_default({
    elementType: Area,
    externalSlotProps: slotProps == null ? void 0 : slotProps.area,
    additionalProps: _extends({}, interactionProps, {
      onClick,
      cursor: onClick ? "pointer" : "unset"
    }),
    className: classes.root,
    ownerState
  });
  return (0, import_jsx_runtime6.jsx)(Area, _extends({}, other, areaProps));
}
true ? AreaElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types5.default.object,
  color: import_prop_types5.default.string.isRequired,
  d: import_prop_types5.default.string.isRequired,
  gradientId: import_prop_types5.default.string,
  id: import_prop_types5.default.oneOfType([import_prop_types5.default.number, import_prop_types5.default.string]).isRequired,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types5.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types5.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types5.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/useAreaPlotData.js
var React8 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/internals/getCurve.js
function getCurveFactory(curveType) {
  switch (curveType) {
    case "catmullRom":
      return catmullRom_default.alpha(0.5);
    case "linear":
      return linear_default;
    case "monotoneX":
      return monotoneX;
    case "monotoneY":
      return monotoneY;
    case "natural":
      return natural_default;
    case "step":
      return step_default;
    case "stepBefore":
      return stepBefore;
    case "stepAfter":
      return stepAfter;
    case "bumpY":
      return bumpY;
    case "bumpX":
      return bumpX;
    default:
      return monotoneX;
  }
}

// node_modules/@mui/x-charts/esm/LineChart/useAreaPlotData.js
function useAreaPlotData(xAxes, yAxes) {
  const seriesData = useLineSeriesContext();
  const defaultXAxisId = useXAxes().xAxisIds[0];
  const defaultYAxisId = useYAxes().yAxisIds[0];
  const getGradientId = useChartGradientIdBuilder();
  const allData = React8.useMemo(() => {
    if (seriesData === void 0) {
      return [];
    }
    const {
      series,
      stackingGroups
    } = seriesData;
    const areaPlotData = [];
    for (const stackingGroup of stackingGroups) {
      const groupIds = stackingGroup.ids;
      for (let i = groupIds.length - 1; i >= 0; i -= 1) {
        const seriesId = groupIds[i];
        const {
          xAxisId = defaultXAxisId,
          yAxisId = defaultYAxisId,
          stackedData,
          data,
          connectNulls,
          baseline,
          curve,
          strictStepCurve,
          area
        } = series[seriesId];
        if (!area || !(xAxisId in xAxes) || !(yAxisId in yAxes)) {
          continue;
        }
        const xScale = xAxes[xAxisId].scale;
        const xPosition = getValueToPositionMapper(xScale);
        const yScale = yAxes[yAxisId].scale;
        const xData = xAxes[xAxisId].data;
        const gradientId = yAxes[yAxisId].colorScale && getGradientId(yAxisId) || xAxes[xAxisId].colorScale && getGradientId(xAxisId) || void 0;
        if (true) {
          if (xData === void 0) {
            throw new Error(`MUI X Charts: ${xAxisId === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisId}"`} should have data property to be able to display a line plot.`);
          }
          if (xData.length < stackedData.length) {
            throw new Error(`MUI X Charts: The data length of the x axis (${xData.length} items) is lower than the length of series (${stackedData.length} items).`);
          }
        }
        const shouldExpand = (curve == null ? void 0 : curve.includes("step")) && !strictStepCurve && isBandScale(xScale);
        const formattedData = (xData == null ? void 0 : xData.flatMap((x, index) => {
          const nullData = data[index] == null;
          if (shouldExpand) {
            const rep = [{
              x,
              y: stackedData[index],
              nullData,
              isExtension: false
            }];
            if (!nullData && (index === 0 || data[index - 1] == null)) {
              rep.unshift({
                x: (xScale(x) ?? 0) - (xScale.step() - xScale.bandwidth()) / 2,
                y: stackedData[index],
                nullData,
                isExtension: true
              });
            }
            if (!nullData && (index === data.length - 1 || data[index + 1] == null)) {
              rep.push({
                x: (xScale(x) ?? 0) + (xScale.step() + xScale.bandwidth()) / 2,
                y: stackedData[index],
                nullData,
                isExtension: true
              });
            }
            return rep;
          }
          return {
            x,
            y: stackedData[index],
            nullData
          };
        })) ?? [];
        const d3Data = connectNulls ? formattedData.filter((d2) => !d2.nullData) : formattedData;
        const areaPath = area_default().x((d2) => d2.isExtension ? d2.x : xPosition(d2.x)).defined((d2) => connectNulls || !d2.nullData || !!d2.isExtension).y0((d2) => {
          if (typeof baseline === "number") {
            return yScale(baseline);
          }
          if (baseline === "max") {
            return yScale.range()[1];
          }
          if (baseline === "min") {
            return yScale.range()[0];
          }
          const value = d2.y && yScale(d2.y[0]);
          if (Number.isNaN(value)) {
            return yScale.range()[0];
          }
          return value;
        }).y1((d2) => d2.y && yScale(d2.y[1]));
        const d = areaPath.curve(getCurveFactory(curve))(d3Data) || "";
        areaPlotData.push({
          area: series[seriesId].area,
          color: series[seriesId].color,
          gradientId,
          d,
          seriesId
        });
      }
    }
    return areaPlotData;
  }, [seriesData, defaultXAxisId, defaultYAxisId, xAxes, yAxes, getGradientId]);
  return allData;
}

// node_modules/@mui/x-charts/esm/LineChart/AreaPlot.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var _excluded3 = ["slots", "slotProps", "onItemClick", "skipAnimation"];
var AreaPlotRoot = styled_default("g", {
  name: "MuiAreaPlot",
  slot: "Root"
})({
  [`& .${areaElementClasses.root}`]: {
    transition: "opacity 0.2s ease-in, fill 0.2s ease-in"
  }
});
var useAggregatedData = () => {
  const {
    xAxis: xAxes
  } = useXAxes();
  const {
    yAxis: yAxes
  } = useYAxes();
  return useAreaPlotData(xAxes, yAxes);
};
function AreaPlot(props) {
  const {
    slots,
    slotProps,
    onItemClick,
    skipAnimation: inSkipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const isZoomInteracting = useInternalIsZoomInteracting();
  const skipAnimation = useSkipAnimation(isZoomInteracting || inSkipAnimation);
  const completedData = useAggregatedData();
  return (0, import_jsx_runtime7.jsx)(AreaPlotRoot, _extends({}, other, {
    children: completedData.map(({
      d,
      seriesId,
      color,
      area,
      gradientId
    }) => !!area && (0, import_jsx_runtime7.jsx)(AreaElement, {
      id: seriesId,
      d,
      color,
      gradientId,
      slots,
      slotProps,
      onClick: onItemClick && ((event) => onItemClick(event, {
        type: "line",
        seriesId
      })),
      skipAnimation
    }, seriesId))
  }));
}
true ? AreaPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when a line area item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {LineItemIdentifier} lineItemIdentifier The line item identifier.
   */
  onItemClick: import_prop_types6.default.func,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types6.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types6.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LinePlot.js
var React13 = __toESM(require_react(), 1);
var import_prop_types9 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/LineElement.js
var React11 = __toESM(require_react(), 1);
var import_prop_types8 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/AnimatedLine.js
var React10 = __toESM(require_react(), 1);
var import_prop_types7 = __toESM(require_prop_types(), 1);
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var _excluded4 = ["skipAnimation", "ownerState"];
var AnimatedLine = React10.forwardRef(function AnimatedLine2(props, ref) {
  const {
    skipAnimation,
    ownerState
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const animateProps = useAnimateLine(_extends({}, props, {
    ref
  }));
  return (0, import_jsx_runtime8.jsx)(AppearingMask, {
    skipAnimation,
    id: `${ownerState.id}-line-clip`,
    children: (0, import_jsx_runtime8.jsx)("path", _extends({
      stroke: ownerState.gradientId ? `url(#${ownerState.gradientId})` : ownerState.color,
      strokeWidth: 2,
      strokeLinejoin: "round",
      fill: "none",
      filter: ownerState.isHighlighted ? "brightness(120%)" : void 0,
      opacity: ownerState.isFaded ? 0.3 : 1,
      "data-series": ownerState.id,
      "data-highlighted": ownerState.isHighlighted || void 0,
      "data-faded": ownerState.isFaded || void 0
    }, other, animateProps))
  });
});
if (true) AnimatedLine.displayName = "AnimatedLine";
true ? AnimatedLine.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  d: import_prop_types7.default.string.isRequired,
  ownerState: import_prop_types7.default.shape({
    classes: import_prop_types7.default.object,
    color: import_prop_types7.default.string.isRequired,
    gradientId: import_prop_types7.default.string,
    id: import_prop_types7.default.oneOfType([import_prop_types7.default.number, import_prop_types7.default.string]).isRequired,
    isFaded: import_prop_types7.default.bool.isRequired,
    isHighlighted: import_prop_types7.default.bool.isRequired
  }).isRequired,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types7.default.bool
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineElement.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var _excluded5 = ["id", "classes", "color", "gradientId", "slots", "slotProps", "onClick"];
function getLineElementUtilityClass(slot) {
  return generateUtilityClass("MuiLineElement", slot);
}
var lineElementClasses = generateUtilityClasses("MuiLineElement", ["root", "highlighted", "faded", "series"]);
var useUtilityClasses3 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getLineElementUtilityClass, classes);
};
function LineElement(props) {
  const {
    id,
    classes: innerClasses,
    color,
    gradientId,
    slots,
    slotProps,
    onClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const interactionProps = useInteractionItemProps({
    type: "line",
    seriesId: id
  });
  const {
    isFaded,
    isHighlighted
  } = useItemHighlighted({
    seriesId: id
  });
  const ownerState = {
    id,
    classes: innerClasses,
    color,
    gradientId,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses3(ownerState);
  const Line = (slots == null ? void 0 : slots.line) ?? AnimatedLine;
  const lineProps = useSlotProps_default({
    elementType: Line,
    externalSlotProps: slotProps == null ? void 0 : slotProps.line,
    additionalProps: _extends({}, interactionProps, {
      onClick,
      cursor: onClick ? "pointer" : "unset"
    }),
    className: classes.root,
    ownerState
  });
  return (0, import_jsx_runtime9.jsx)(Line, _extends({}, other, lineProps));
}
true ? LineElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types8.default.object,
  color: import_prop_types8.default.string.isRequired,
  d: import_prop_types8.default.string.isRequired,
  gradientId: import_prop_types8.default.string,
  id: import_prop_types8.default.oneOfType([import_prop_types8.default.number, import_prop_types8.default.string]).isRequired,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types8.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types8.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types8.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/useLinePlotData.js
var React12 = __toESM(require_react(), 1);
function useLinePlotData(xAxes, yAxes) {
  const seriesData = useLineSeriesContext();
  const defaultXAxisId = useXAxes().xAxisIds[0];
  const defaultYAxisId = useYAxes().yAxisIds[0];
  const getGradientId = useChartGradientIdBuilder();
  const allData = React12.useMemo(() => {
    if (seriesData === void 0) {
      return [];
    }
    const {
      series,
      stackingGroups
    } = seriesData;
    const linePlotData = [];
    for (const stackingGroup of stackingGroups) {
      const groupIds = stackingGroup.ids;
      for (const seriesId of groupIds) {
        const {
          xAxisId = defaultXAxisId,
          yAxisId = defaultYAxisId,
          stackedData,
          data,
          connectNulls,
          curve,
          strictStepCurve
        } = series[seriesId];
        if (!(xAxisId in xAxes) || !(yAxisId in yAxes)) {
          continue;
        }
        const xScale = xAxes[xAxisId].scale;
        const xPosition = getValueToPositionMapper(xScale);
        const yScale = yAxes[yAxisId].scale;
        const xData = xAxes[xAxisId].data;
        const gradientId = yAxes[yAxisId].colorScale && getGradientId(yAxisId) || xAxes[xAxisId].colorScale && getGradientId(xAxisId) || void 0;
        if (true) {
          if (xData === void 0) {
            throw new Error(`MUI X Charts: ${xAxisId === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisId}"`} should have data property to be able to display a line plot.`);
          }
          if (xData.length < stackedData.length) {
            warnOnce(`MUI X Charts: The data length of the x axis (${xData.length} items) is lower than the length of series (${stackedData.length} items).`, "error");
          }
        }
        const shouldExpand = (curve == null ? void 0 : curve.includes("step")) && !strictStepCurve && isBandScale(xScale);
        const formattedData = (xData == null ? void 0 : xData.flatMap((x, index) => {
          const nullData = data[index] == null;
          if (shouldExpand) {
            const rep = [{
              x,
              y: stackedData[index],
              nullData,
              isExtension: false
            }];
            if (!nullData && (index === 0 || data[index - 1] == null)) {
              rep.unshift({
                x: (xScale(x) ?? 0) - (xScale.step() - xScale.bandwidth()) / 2,
                y: stackedData[index],
                nullData,
                isExtension: true
              });
            }
            if (!nullData && (index === data.length - 1 || data[index + 1] == null)) {
              rep.push({
                x: (xScale(x) ?? 0) + (xScale.step() + xScale.bandwidth()) / 2,
                y: stackedData[index],
                nullData,
                isExtension: true
              });
            }
            return rep;
          }
          return {
            x,
            y: stackedData[index],
            nullData
          };
        })) ?? [];
        const d3Data = connectNulls ? formattedData.filter((d2) => !d2.nullData) : formattedData;
        const linePath = line_default().x((d2) => d2.isExtension ? d2.x : xPosition(d2.x)).defined((d2) => connectNulls || !d2.nullData || !!d2.isExtension).y((d2) => yScale(d2.y[1]));
        const d = linePath.curve(getCurveFactory(curve))(d3Data) || "";
        linePlotData.push({
          color: series[seriesId].color,
          gradientId,
          d,
          seriesId
        });
      }
    }
    return linePlotData;
  }, [seriesData, defaultXAxisId, defaultYAxisId, xAxes, yAxes, getGradientId]);
  return allData;
}

// node_modules/@mui/x-charts/esm/LineChart/LinePlot.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var _excluded6 = ["slots", "slotProps", "skipAnimation", "onItemClick"];
var LinePlotRoot = styled_default("g", {
  name: "MuiAreaPlot",
  slot: "Root"
})({
  [`& .${lineElementClasses.root}`]: {
    transition: "opacity 0.2s ease-in, fill 0.2s ease-in"
  }
});
var useAggregatedData2 = () => {
  const {
    xAxis: xAxes
  } = useXAxes();
  const {
    yAxis: yAxes
  } = useYAxes();
  return useLinePlotData(xAxes, yAxes);
};
function LinePlot(props) {
  const {
    slots,
    slotProps,
    skipAnimation: inSkipAnimation,
    onItemClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded6);
  const isZoomInteracting = useInternalIsZoomInteracting();
  const skipAnimation = useSkipAnimation(isZoomInteracting || inSkipAnimation);
  const completedData = useAggregatedData2();
  return (0, import_jsx_runtime10.jsx)(LinePlotRoot, _extends({}, other, {
    children: completedData.map(({
      d,
      seriesId,
      color,
      gradientId
    }) => {
      return (0, import_jsx_runtime10.jsx)(LineElement, {
        id: seriesId,
        d,
        color,
        gradientId,
        skipAnimation,
        slots,
        slotProps,
        onClick: onItemClick && ((event) => onItemClick(event, {
          type: "line",
          seriesId
        }))
      }, seriesId);
    })
  }));
}
true ? LinePlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when a line item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {LineItemIdentifier} lineItemIdentifier The line item identifier.
   */
  onItemClick: import_prop_types9.default.func,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types9.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types9.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types9.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/MarkPlot.js
var import_prop_types12 = __toESM(require_prop_types(), 1);
var React16 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/LineChart/CircleMarkElement.js
var React14 = __toESM(require_react(), 1);
var import_prop_types10 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/markElementClasses.js
function getMarkElementUtilityClass(slot) {
  return generateUtilityClass("MuiMarkElement", slot);
}
var markElementClasses = generateUtilityClasses("MuiMarkElement", ["root", "highlighted", "faded", "animate", "series"]);
var useUtilityClasses4 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted,
    skipAnimation
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded", skipAnimation ? void 0 : "animate"]
  };
  return composeClasses(slots, getMarkElementUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/LineChart/CircleMarkElement.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
var _excluded7 = ["x", "y", "id", "classes", "color", "dataIndex", "onClick", "skipAnimation", "isFaded", "isHighlighted"];
var Circle = styled_default("circle")({
  [`&.${markElementClasses.animate}`]: {
    transitionDuration: `${ANIMATION_DURATION_MS}ms`,
    transitionProperty: "cx, cy",
    transitionTimingFunction: ANIMATION_TIMING_FUNCTION
  }
});
function CircleMarkElement(props) {
  const {
    x,
    y,
    id,
    classes: innerClasses,
    color,
    dataIndex,
    onClick,
    skipAnimation,
    isFaded = false,
    isHighlighted = false
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const theme = useTheme();
  const interactionProps = useInteractionItemProps({
    type: "line",
    seriesId: id,
    dataIndex
  });
  const ownerState = {
    id,
    classes: innerClasses,
    isHighlighted,
    isFaded,
    color,
    skipAnimation
  };
  const classes = useUtilityClasses4(ownerState);
  return (0, import_jsx_runtime11.jsx)(Circle, _extends({}, other, {
    cx: x,
    cy: y,
    r: 5,
    fill: (theme.vars || theme).palette.background.paper,
    stroke: color,
    strokeWidth: 2,
    className: classes.root,
    onClick,
    cursor: onClick ? "pointer" : "unset"
  }, interactionProps, {
    "data-highlighted": isHighlighted || void 0,
    "data-faded": isFaded || void 0
  }));
}
true ? CircleMarkElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types10.default.object,
  /**
   * The index to the element in the series' data array.
   */
  dataIndex: import_prop_types10.default.number.isRequired,
  id: import_prop_types10.default.oneOfType([import_prop_types10.default.number, import_prop_types10.default.string]).isRequired,
  /**
   * The shape of the marker.
   */
  shape: import_prop_types10.default.oneOf(["circle", "cross", "diamond", "square", "star", "triangle", "wye"]).isRequired,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types10.default.bool
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/MarkElement.js
var React15 = __toESM(require_react(), 1);
var import_prop_types11 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/internals/getSymbol.js
function getSymbol(shape) {
  switch (shape) {
    case "circle":
      return 0;
    case "cross":
      return 1;
    case "diamond":
      return 2;
    case "square":
      return 3;
    case "star":
      return 4;
    case "triangle":
      return 5;
    case "wye":
      return 6;
    default:
      return 0;
  }
}

// node_modules/@mui/x-charts/esm/LineChart/MarkElement.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
var _excluded8 = ["x", "y", "id", "classes", "color", "shape", "dataIndex", "onClick", "skipAnimation", "isFaded", "isHighlighted"];
var MarkElementPath = styled_default("path", {
  name: "MuiMarkElement",
  slot: "Root"
})(({
  ownerState,
  theme
}) => ({
  fill: (theme.vars || theme).palette.background.paper,
  stroke: ownerState.color,
  strokeWidth: 2,
  [`&.${markElementClasses.animate}`]: {
    transitionDuration: `${ANIMATION_DURATION_MS}ms`,
    transitionProperty: "transform, transform-origin",
    transitionTimingFunction: ANIMATION_TIMING_FUNCTION
  }
}));
function MarkElement(props) {
  const {
    x,
    y,
    id,
    classes: innerClasses,
    color,
    shape,
    dataIndex,
    onClick,
    skipAnimation,
    isFaded = false,
    isHighlighted = false
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded8);
  const interactionProps = useInteractionItemProps({
    type: "line",
    seriesId: id,
    dataIndex
  });
  const ownerState = {
    id,
    classes: innerClasses,
    isHighlighted,
    isFaded,
    color,
    skipAnimation
  };
  const classes = useUtilityClasses4(ownerState);
  return (0, import_jsx_runtime12.jsx)(MarkElementPath, _extends({}, other, {
    style: {
      transform: `translate(${x}px, ${y}px)`,
      transformOrigin: `${x}px ${y}px`
    },
    ownerState,
    className: classes.root,
    d: Symbol(symbolsFill[getSymbol(shape)])(),
    onClick,
    cursor: onClick ? "pointer" : "unset"
  }, interactionProps, {
    "data-highlighted": isHighlighted || void 0,
    "data-faded": isFaded || void 0
  }));
}
true ? MarkElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types11.default.object,
  /**
   * The index to the element in the series' data array.
   */
  dataIndex: import_prop_types11.default.number.isRequired,
  id: import_prop_types11.default.oneOfType([import_prop_types11.default.number, import_prop_types11.default.string]).isRequired,
  /**
   * If `true`, the marker is faded.
   * @default false
   */
  isFaded: import_prop_types11.default.bool,
  /**
   * If `true`, the marker is highlighted.
   * @default false
   */
  isHighlighted: import_prop_types11.default.bool,
  /**
   * The shape of the marker.
   */
  shape: import_prop_types11.default.oneOf(["circle", "cross", "diamond", "square", "star", "triangle", "wye"]).isRequired,
  /**
   * If `true`, animations are skipped.
   */
  skipAnimation: import_prop_types11.default.bool
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/MarkPlot.js
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
var _excluded9 = ["slots", "slotProps", "skipAnimation", "onItemClick"];
function MarkPlot(props) {
  const {
    slots,
    slotProps,
    skipAnimation: inSkipAnimation,
    onItemClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded9);
  const isZoomInteracting = useInternalIsZoomInteracting();
  const skipAnimation = useSkipAnimation(isZoomInteracting || inSkipAnimation);
  const seriesData = useLineSeriesContext();
  const {
    xAxis,
    xAxisIds
  } = useXAxes();
  const {
    yAxis,
    yAxisIds
  } = useYAxes();
  const chartId = useChartId();
  const {
    instance,
    store
  } = useChartContext();
  const {
    isFaded,
    isHighlighted
  } = useItemHighlightedGetter();
  const xAxisHighlightIndexes = useSelector(store, selectorChartsHighlightXAxisIndex);
  const highlightedItems = React16.useMemo(() => {
    const rep = {};
    for (const {
      dataIndex,
      axisId
    } of xAxisHighlightIndexes) {
      if (rep[axisId] === void 0) {
        rep[axisId] = /* @__PURE__ */ new Set([dataIndex]);
      } else {
        rep[axisId].add(dataIndex);
      }
    }
    return rep;
  }, [xAxisHighlightIndexes]);
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  return (0, import_jsx_runtime13.jsx)("g", _extends({}, other, {
    children: stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.map((seriesId) => {
        const {
          xAxisId = defaultXAxisId,
          yAxisId = defaultYAxisId,
          stackedData,
          data,
          showMark = true,
          shape = "circle"
        } = series[seriesId];
        if (showMark === false) {
          return null;
        }
        const xScale = getValueToPositionMapper(xAxis[xAxisId].scale);
        const yScale = yAxis[yAxisId].scale;
        const xData = xAxis[xAxisId].data;
        if (xData === void 0) {
          throw new Error(`MUI X Charts: ${xAxisId === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisId}"`} should have data property to be able to display a line plot.`);
        }
        const clipId = cleanId(`${chartId}-${seriesId}-line-clip`);
        const colorGetter = getColor_default(series[seriesId], xAxis[xAxisId], yAxis[yAxisId]);
        const Mark = (slots == null ? void 0 : slots.mark) ?? (shape === "circle" ? CircleMarkElement : MarkElement);
        const isSeriesHighlighted = isHighlighted({
          seriesId
        });
        const isSeriesFaded = !isSeriesHighlighted && isFaded({
          seriesId
        });
        return (0, import_jsx_runtime13.jsx)("g", {
          clipPath: `url(#${clipId})`,
          "data-series": seriesId,
          children: xData == null ? void 0 : xData.map((x, index) => {
            const value = data[index] == null ? null : stackedData[index][1];
            return {
              x: xScale(x),
              y: value === null ? null : yScale(value),
              position: x,
              value,
              index
            };
          }).filter(({
            x,
            y,
            index,
            position,
            value
          }) => {
            if (value === null || y === null) {
              return false;
            }
            if (!instance.isPointInside(x, y)) {
              return false;
            }
            if (showMark === true) {
              return true;
            }
            return showMark({
              x,
              y,
              index,
              position,
              value
            });
          }).map(({
            x,
            y,
            index
          }) => {
            var _a;
            return (0, import_jsx_runtime13.jsx)(Mark, _extends({
              id: seriesId,
              dataIndex: index,
              shape,
              color: colorGetter(index),
              x,
              y,
              skipAnimation,
              onClick: onItemClick && ((event) => onItemClick(event, {
                type: "line",
                seriesId,
                dataIndex: index
              })),
              isHighlighted: ((_a = highlightedItems[xAxisId]) == null ? void 0 : _a.has(index)) || isSeriesHighlighted,
              isFaded: isSeriesFaded
            }, slotProps == null ? void 0 : slotProps.mark), `${seriesId}-${index}`);
          })
        }, seriesId);
      });
    })
  }));
}
true ? MarkPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when a line mark item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {LineItemIdentifier} lineItemIdentifier The line mark item identifier.
   */
  onItemClick: import_prop_types12.default.func,
  /**
   * If `true`, animations are skipped.
   */
  skipAnimation: import_prop_types12.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types12.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types12.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightPlot.js
var React18 = __toESM(require_react(), 1);
var import_prop_types14 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightElement.js
var React17 = __toESM(require_react(), 1);
var import_prop_types13 = __toESM(require_prop_types(), 1);
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var _excluded10 = ["x", "y", "id", "classes", "color", "shape"];
function getHighlightElementUtilityClass(slot) {
  return generateUtilityClass("MuiHighlightElement", slot);
}
var lineHighlightElementClasses = generateUtilityClasses("MuiHighlightElement", ["root"]);
var useUtilityClasses5 = (ownerState) => {
  const {
    classes,
    id
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`]
  };
  return composeClasses(slots, getHighlightElementUtilityClass, classes);
};
function LineHighlightElement(props) {
  const {
    x,
    y,
    color,
    shape
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded10);
  const classes = useUtilityClasses5(props);
  const Element = shape === "circle" ? "circle" : "path";
  const additionalProps = shape === "circle" ? {
    cx: 0,
    cy: 0,
    r: other.r === void 0 ? 5 : other.r
  } : {
    d: Symbol(symbolsFill[getSymbol(shape)])()
  };
  const transformOrigin = reactMajor_default > 18 ? {
    transformOrigin: `${x} ${y}`
  } : {
    "transform-origin": `${x} ${y}`
  };
  return (0, import_jsx_runtime14.jsx)(Element, _extends({
    pointerEvents: "none",
    className: classes.root,
    transform: `translate(${x} ${y})`,
    fill: color
  }, transformOrigin, additionalProps, other));
}
true ? LineHighlightElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types13.default.object,
  id: import_prop_types13.default.oneOfType([import_prop_types13.default.number, import_prop_types13.default.string]).isRequired,
  shape: import_prop_types13.default.oneOf(["circle", "cross", "diamond", "square", "star", "triangle", "wye"]).isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/LineHighlightPlot.js
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
var _excluded11 = ["slots", "slotProps"];
function LineHighlightPlot(props) {
  const {
    slots,
    slotProps
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded11);
  const seriesData = useLineSeriesContext();
  const {
    xAxis,
    xAxisIds
  } = useXAxes();
  const {
    yAxis,
    yAxisIds
  } = useYAxes();
  const {
    instance
  } = useChartContext();
  const store = useStore();
  const highlightedIndexes = useSelector(store, selectorChartsHighlightXAxisIndex);
  if (highlightedIndexes.length === 0) {
    return null;
  }
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    stackingGroups
  } = seriesData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  const Element = (slots == null ? void 0 : slots.lineHighlight) ?? LineHighlightElement;
  return (0, import_jsx_runtime15.jsx)("g", _extends({}, other, {
    children: highlightedIndexes.flatMap(({
      dataIndex: highlightedIndex,
      axisId: highlightedAxisId
    }) => stackingGroups.flatMap(({
      ids: groupIds
    }) => {
      return groupIds.flatMap((seriesId) => {
        const {
          xAxisId = defaultXAxisId,
          yAxisId = defaultYAxisId,
          stackedData,
          data,
          disableHighlight,
          shape = "circle"
        } = series[seriesId];
        if (disableHighlight || data[highlightedIndex] == null) {
          return null;
        }
        if (highlightedAxisId !== xAxisId) {
          return null;
        }
        const xScale = getValueToPositionMapper(xAxis[xAxisId].scale);
        const yScale = yAxis[yAxisId].scale;
        const xData = xAxis[xAxisId].data;
        if (xData === void 0) {
          throw new Error(`MUI X Charts: ${xAxisId === DEFAULT_X_AXIS_KEY ? "The first `xAxis`" : `The x-axis with id "${xAxisId}"`} should have data property to be able to display a line plot.`);
        }
        const x = xScale(xData[highlightedIndex]);
        const y = yScale(stackedData[highlightedIndex][1]);
        if (!instance.isPointInside(x, y)) {
          return null;
        }
        const colorGetter = getColor_default(series[seriesId], xAxis[xAxisId], yAxis[yAxisId]);
        return (0, import_jsx_runtime15.jsx)(Element, _extends({
          id: seriesId,
          color: colorGetter(highlightedIndex),
          x,
          y,
          shape
        }, slotProps == null ? void 0 : slotProps.lineHighlight), `${seriesId}`);
      });
    }))
  }));
}
true ? LineHighlightPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types14.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types14.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/LineChart/useLineChartProps.js
var React19 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/LineChart/LineChart.plugins.js
var LINE_CHART_PLUGINS = [useChartZAxis, useChartCartesianAxis, useChartInteraction, useChartHighlight];

// node_modules/@mui/x-charts/esm/LineChart/useLineChartProps.js
var _excluded12 = ["xAxis", "yAxis", "series", "width", "height", "margin", "colors", "dataset", "sx", "onAreaClick", "onLineClick", "onMarkClick", "axisHighlight", "disableLineItemHighlight", "hideLegend", "grid", "children", "slots", "slotProps", "skipAnimation", "loading", "highlightedItem", "onHighlightChange", "className", "showToolbar"];
var useLineChartProps = (props) => {
  var _a, _b, _c, _d, _e;
  const {
    xAxis,
    yAxis,
    series,
    width,
    height,
    margin,
    colors,
    dataset,
    sx,
    onAreaClick,
    onLineClick,
    onMarkClick,
    axisHighlight,
    disableLineItemHighlight,
    grid,
    children,
    slots,
    slotProps,
    skipAnimation,
    loading,
    highlightedItem,
    onHighlightChange,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded12);
  const id = useId();
  const clipPathId = `${id}-clip-path`;
  const seriesWithDefault = React19.useMemo(() => series.map((s) => _extends({
    disableHighlight: !!disableLineItemHighlight,
    type: "line"
  }, s)), [disableLineItemHighlight, series]);
  const chartContainerProps = _extends({}, other, {
    series: seriesWithDefault,
    width,
    height,
    margin,
    colors,
    dataset,
    xAxis: xAxis ?? [{
      id: DEFAULT_X_AXIS_KEY,
      scaleType: "point",
      data: Array.from({
        length: Math.max(...series.map((s) => (s.data ?? dataset ?? []).length))
      }, (_, index) => index)
    }],
    yAxis,
    highlightedItem,
    onHighlightChange,
    disableAxisListener: ((_a = slotProps == null ? void 0 : slotProps.tooltip) == null ? void 0 : _a.trigger) !== "axis" && (axisHighlight == null ? void 0 : axisHighlight.x) === "none" && (axisHighlight == null ? void 0 : axisHighlight.y) === "none",
    className,
    skipAnimation,
    plugins: LINE_CHART_PLUGINS
  });
  const gridProps = {
    vertical: grid == null ? void 0 : grid.vertical,
    horizontal: grid == null ? void 0 : grid.horizontal
  };
  const clipPathGroupProps = {
    clipPath: `url(#${clipPathId})`
  };
  const clipPathProps = {
    id: clipPathId
  };
  const areaPlotProps = {
    slots,
    slotProps,
    onItemClick: onAreaClick
  };
  const linePlotProps = {
    slots,
    slotProps,
    onItemClick: onLineClick
  };
  const markPlotProps = {
    slots,
    slotProps,
    onItemClick: onMarkClick,
    skipAnimation
  };
  const overlayProps = {
    slots,
    slotProps,
    loading
  };
  const chartsAxisProps = {
    slots,
    slotProps
  };
  const axisHighlightProps = _extends({
    x: "line"
  }, axisHighlight);
  const lineHighlightPlotProps = {
    slots,
    slotProps
  };
  const legendProps = {
    slots,
    slotProps
  };
  const chartsWrapperProps = {
    sx,
    legendPosition: (_c = (_b = props.slotProps) == null ? void 0 : _b.legend) == null ? void 0 : _c.position,
    legendDirection: (_e = (_d = props.slotProps) == null ? void 0 : _d.legend) == null ? void 0 : _e.direction
  };
  return {
    chartsWrapperProps,
    chartContainerProps,
    gridProps,
    clipPathProps,
    clipPathGroupProps,
    areaPlotProps,
    linePlotProps,
    markPlotProps,
    overlayProps,
    chartsAxisProps,
    axisHighlightProps,
    lineHighlightPlotProps,
    legendProps,
    children
  };
};

// node_modules/@mui/x-charts/esm/LineChart/LineChart.js
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
var LineChart = React20.forwardRef(function LineChart2(inProps, ref) {
  var _a, _b, _c, _d;
  const props = useThemeProps({
    props: inProps,
    name: "MuiLineChart"
  });
  const {
    chartsWrapperProps,
    chartContainerProps,
    gridProps,
    clipPathProps,
    clipPathGroupProps,
    areaPlotProps,
    linePlotProps,
    markPlotProps,
    overlayProps,
    chartsAxisProps,
    axisHighlightProps,
    lineHighlightPlotProps,
    legendProps,
    children
  } = useLineChartProps(props);
  const {
    chartDataProviderProps,
    chartsSurfaceProps
  } = useChartContainerProps(chartContainerProps, ref);
  const Tooltip = ((_a = props.slots) == null ? void 0 : _a.tooltip) ?? ChartsTooltip;
  const Toolbar3 = (_b = props.slots) == null ? void 0 : _b.toolbar;
  return (0, import_jsx_runtime16.jsx)(ChartDataProvider, _extends({}, chartDataProviderProps, {
    children: (0, import_jsx_runtime16.jsxs)(ChartsWrapper, _extends({}, chartsWrapperProps, {
      children: [props.showToolbar && Toolbar3 ? (0, import_jsx_runtime16.jsx)(Toolbar3, _extends({}, (_c = props.slotProps) == null ? void 0 : _c.toolbar)) : null, !props.hideLegend && (0, import_jsx_runtime16.jsx)(ChartsLegend, _extends({}, legendProps)), (0, import_jsx_runtime16.jsxs)(ChartsSurface, _extends({}, chartsSurfaceProps, {
        children: [(0, import_jsx_runtime16.jsx)(ChartsGrid, _extends({}, gridProps)), (0, import_jsx_runtime16.jsxs)("g", _extends({}, clipPathGroupProps, {
          children: [(0, import_jsx_runtime16.jsx)(AreaPlot, _extends({}, areaPlotProps)), (0, import_jsx_runtime16.jsx)(LinePlot, _extends({}, linePlotProps)), (0, import_jsx_runtime16.jsx)(ChartsOverlay, _extends({}, overlayProps)), (0, import_jsx_runtime16.jsx)(ChartsAxisHighlight, _extends({}, axisHighlightProps))]
        })), (0, import_jsx_runtime16.jsx)(ChartsAxis, _extends({}, chartsAxisProps)), (0, import_jsx_runtime16.jsx)("g", {
          "data-drawing-container": true,
          children: (0, import_jsx_runtime16.jsx)(MarkPlot, _extends({}, markPlotProps))
        }), (0, import_jsx_runtime16.jsx)(LineHighlightPlot, _extends({}, lineHighlightPlotProps)), (0, import_jsx_runtime16.jsx)(ChartsClipPath, _extends({}, clipPathProps)), children]
      })), !props.loading && (0, import_jsx_runtime16.jsx)(Tooltip, _extends({}, (_d = props.slotProps) == null ? void 0 : _d.tooltip))]
    }))
  }));
});
if (true) LineChart.displayName = "LineChart";
true ? LineChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types15.default.shape({
    current: import_prop_types15.default.object
  }),
  /**
   * The configuration of axes highlight.
   * @see See {@link https://mui.com/x/react-charts/highlighting/ highlighting docs} for more details.
   * @default { x: 'line' }
   */
  axisHighlight: import_prop_types15.default.shape({
    x: import_prop_types15.default.oneOf(["band", "line", "none"]),
    y: import_prop_types15.default.oneOf(["band", "line", "none"])
  }),
  children: import_prop_types15.default.node,
  className: import_prop_types15.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string), import_prop_types15.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types15.default.arrayOf(import_prop_types15.default.object),
  desc: import_prop_types15.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types15.default.bool,
  /**
   * If `true`, render the line highlight item.
   */
  disableLineItemHighlight: import_prop_types15.default.bool,
  /**
   * Options to enable features planned for the next major.
   */
  experimentalFeatures: import_prop_types15.default.shape({
    preferStrictDomainInLineCharts: import_prop_types15.default.bool
  }),
  /**
   * Option to display a cartesian grid in the background.
   */
  grid: import_prop_types15.default.shape({
    horizontal: import_prop_types15.default.bool,
    vertical: import_prop_types15.default.bool
  }),
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types15.default.number,
  /**
   * If `true`, the legend is not rendered.
   */
  hideLegend: import_prop_types15.default.bool,
  /**
   * The controlled axis highlight.
   * Identified by the axis id, and data index.
   */
  highlightedAxis: import_prop_types15.default.arrayOf(import_prop_types15.default.shape({
    axisId: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]).isRequired,
    dataIndex: import_prop_types15.default.number.isRequired
  })),
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types15.default.shape({
    dataIndex: import_prop_types15.default.number,
    seriesId: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types15.default.string,
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types15.default.bool,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types15.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.shape({
    bottom: import_prop_types15.default.number,
    left: import_prop_types15.default.number,
    right: import_prop_types15.default.number,
    top: import_prop_types15.default.number
  })]),
  /**
   * Callback fired when an area element is clicked.
   */
  onAreaClick: import_prop_types15.default.func,
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types15.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types15.default.func,
  /**
   * The function called when the pointer position corresponds to a new axis data item.
   * This update can either be caused by a pointer movement, or an axis update.
   * In case of multiple axes, the function is called if at least one axis is updated.
   * The argument contains the identifier for all axes with a `data` property.
   * @param {AxisItemIdentifier[]} axisItems The array of axes item identifiers.
   */
  onHighlightedAxisChange: import_prop_types15.default.func,
  /**
   * Callback fired when a line element is clicked.
   */
  onLineClick: import_prop_types15.default.func,
  /**
   * Callback fired when a mark element is clicked.
   */
  onMarkClick: import_prop_types15.default.func,
  /**
   * The series to display in the line chart.
   * An array of [[LineSeries]] objects.
   */
  series: import_prop_types15.default.arrayOf(import_prop_types15.default.object).isRequired,
  /**
   * If true, shows the default chart toolbar.
   * @default false
   */
  showToolbar: import_prop_types15.default.bool,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types15.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types15.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types15.default.object,
  sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
  theme: import_prop_types15.default.oneOf(["dark", "light"]),
  title: import_prop_types15.default.string,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types15.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  xAxis: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    barGapRatio: import_prop_types15.default.number,
    categoryGapRatio: import_prop_types15.default.number,
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      type: import_prop_types15.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types15.default.string,
      values: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number, import_prop_types15.default.string]).isRequired)
    }), import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["band"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      type: import_prop_types15.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types15.default.string,
      values: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number, import_prop_types15.default.string]).isRequired)
    }), import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["point"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["log"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["pow"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["sqrt"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["time"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["utc"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["x"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    height: import_prop_types15.default.number,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["linear"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelMinGap: import_prop_types15.default.number,
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func
  })]).isRequired),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  yAxis: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    barGapRatio: import_prop_types15.default.number,
    categoryGapRatio: import_prop_types15.default.number,
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      type: import_prop_types15.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types15.default.string,
      values: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number, import_prop_types15.default.string]).isRequired)
    }), import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["band"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      type: import_prop_types15.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types15.default.string,
      values: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number, import_prop_types15.default.string]).isRequired)
    }), import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["point"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["log"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["pow"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["sqrt"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["time"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["utc"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  }), import_prop_types15.default.shape({
    axis: import_prop_types15.default.oneOf(["y"]),
    classes: import_prop_types15.default.object,
    colorMap: import_prop_types15.default.oneOfType([import_prop_types15.default.shape({
      color: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.string.isRequired), import_prop_types15.default.func]).isRequired,
      max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
      type: import_prop_types15.default.oneOf(["continuous"]).isRequired
    }), import_prop_types15.default.shape({
      colors: import_prop_types15.default.arrayOf(import_prop_types15.default.string).isRequired,
      thresholds: import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]).isRequired).isRequired,
      type: import_prop_types15.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types15.default.array,
    dataKey: import_prop_types15.default.string,
    disableLine: import_prop_types15.default.bool,
    disableTicks: import_prop_types15.default.bool,
    domainLimit: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["nice", "strict"]), import_prop_types15.default.func]),
    fill: import_prop_types15.default.string,
    hideTooltip: import_prop_types15.default.bool,
    id: import_prop_types15.default.oneOfType([import_prop_types15.default.number, import_prop_types15.default.string]),
    ignoreTooltip: import_prop_types15.default.bool,
    label: import_prop_types15.default.string,
    labelStyle: import_prop_types15.default.object,
    max: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    min: import_prop_types15.default.oneOfType([import_prop_types15.default.instanceOf(Date), import_prop_types15.default.number]),
    offset: import_prop_types15.default.number,
    position: import_prop_types15.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types15.default.bool,
    scaleType: import_prop_types15.default.oneOf(["linear"]),
    slotProps: import_prop_types15.default.object,
    slots: import_prop_types15.default.object,
    stroke: import_prop_types15.default.string,
    sx: import_prop_types15.default.oneOfType([import_prop_types15.default.arrayOf(import_prop_types15.default.oneOfType([import_prop_types15.default.func, import_prop_types15.default.object, import_prop_types15.default.bool])), import_prop_types15.default.func, import_prop_types15.default.object]),
    tickInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.array, import_prop_types15.default.func]),
    tickLabelInterval: import_prop_types15.default.oneOfType([import_prop_types15.default.oneOf(["auto"]), import_prop_types15.default.func]),
    tickLabelPlacement: import_prop_types15.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types15.default.object,
    tickMaxStep: import_prop_types15.default.number,
    tickMinStep: import_prop_types15.default.number,
    tickNumber: import_prop_types15.default.number,
    tickPlacement: import_prop_types15.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types15.default.number,
    valueFormatter: import_prop_types15.default.func,
    width: import_prop_types15.default.number
  })]).isRequired)
} : void 0;

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterChart.js
var React26 = __toESM(require_react(), 1);
var import_prop_types19 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterPlot.js
var React24 = __toESM(require_react(), 1);
var import_prop_types18 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ScatterChart/Scatter.js
var React23 = __toESM(require_react(), 1);
var import_prop_types17 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterMarker.js
var React21 = __toESM(require_react(), 1);
var import_prop_types16 = __toESM(require_prop_types(), 1);
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
var _excluded13 = ["seriesId", "isFaded", "isHighlighted", "x", "y", "color", "size", "dataIndex"];
function ScatterMarker(props) {
  const {
    isFaded,
    isHighlighted,
    x,
    y,
    color,
    size
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded13);
  return (0, import_jsx_runtime17.jsx)("circle", _extends({
    cx: 0,
    cy: 0,
    r: (isHighlighted ? 1.2 : 1) * size,
    transform: `translate(${x}, ${y})`,
    fill: color,
    opacity: isFaded ? 0.3 : 1,
    cursor: other.onClick ? "pointer" : "unset"
  }, other));
}
true ? ScatterMarker.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The fill color of the marker.
   */
  color: import_prop_types16.default.string.isRequired,
  /**
   * The index of the data point.
   */
  dataIndex: import_prop_types16.default.number.isRequired,
  /**
   * If `true`, the marker is faded.
   */
  isFaded: import_prop_types16.default.bool.isRequired,
  /**
   * If `true`, the marker is highlighted.
   */
  isHighlighted: import_prop_types16.default.bool.isRequired,
  /**
   * Callback fired when clicking on a scatter item.
   * @param {MouseEvent} event Mouse event recorded on the `<svg/>` element.
   */
  onClick: import_prop_types16.default.func,
  /**
   * The series ID.
   */
  seriesId: import_prop_types16.default.oneOfType([import_prop_types16.default.number, import_prop_types16.default.string]).isRequired,
  /**
   * The size of the marker.
   */
  size: import_prop_types16.default.number.isRequired,
  /**
   * The x coordinate of the data point.
   */
  x: import_prop_types16.default.number.isRequired,
  /**
   * The y coordinate of the data point.
   */
  y: import_prop_types16.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/ScatterChart/scatterClasses.js
function getScatterUtilityClass(slot) {
  return generateUtilityClass("MuiScatter", slot);
}
var scatterClasses = generateUtilityClasses("MuiScatter", ["root"]);
var useUtilityClasses6 = (classes) => {
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getScatterUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/ScatterChart/useScatterPlotData.js
var React22 = __toESM(require_react(), 1);
function useScatterPlotData(series, xScale, yScale, isPointInside) {
  return React22.useMemo(() => {
    const getXPosition = getValueToPositionMapper(xScale);
    const getYPosition = getValueToPositionMapper(yScale);
    const temp = [];
    for (let i = 0; i < series.data.length; i += 1) {
      const scatterPoint = series.data[i];
      const x = getXPosition(scatterPoint.x);
      const y = getYPosition(scatterPoint.y);
      const isInRange = isPointInside(x, y);
      if (isInRange) {
        temp.push({
          x,
          y,
          id: scatterPoint.id,
          seriesId: series.id,
          type: "scatter",
          dataIndex: i
        });
      }
    }
    return temp;
  }, [xScale, yScale, series.data, series.id, isPointInside]);
}

// node_modules/@mui/x-charts/esm/ScatterChart/Scatter.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var _excluded14 = ["ownerState"];
function Scatter(props) {
  const {
    series,
    xScale,
    yScale,
    color,
    colorGetter,
    onItemClick,
    classes: inClasses,
    slots,
    slotProps
  } = props;
  const {
    instance
  } = useChartContext();
  const store = useStore();
  const isVoronoiEnabled = useSelector(store, selectorChartsVoronoiIsVoronoiEnabled);
  const skipInteractionHandlers = isVoronoiEnabled || series.disableHover;
  const {
    isFaded,
    isHighlighted
  } = useItemHighlightedGetter();
  const scatterPlotData = useScatterPlotData(series, xScale, yScale, instance.isPointInside);
  const Marker = (slots == null ? void 0 : slots.marker) ?? ScatterMarker;
  const _useSlotProps = useSlotProps_default({
    elementType: Marker,
    externalSlotProps: slotProps == null ? void 0 : slotProps.marker,
    additionalProps: {
      seriesId: series.id,
      size: series.markerSize
    },
    ownerState: {}
  }), markerProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded14);
  const classes = useUtilityClasses6(inClasses);
  return (0, import_jsx_runtime18.jsx)("g", {
    "data-series": series.id,
    className: classes.root,
    children: scatterPlotData.map((dataPoint, i) => {
      const isItemHighlighted = isHighlighted(dataPoint);
      const isItemFaded = !isItemHighlighted && isFaded(dataPoint);
      return (0, import_jsx_runtime18.jsx)(Marker, _extends({
        dataIndex: dataPoint.dataIndex,
        color: colorGetter ? colorGetter(i) : color,
        isHighlighted: isItemHighlighted,
        isFaded: isItemFaded,
        x: dataPoint.x,
        y: dataPoint.y,
        onClick: onItemClick && ((event) => onItemClick(event, {
          type: "scatter",
          seriesId: series.id,
          dataIndex: dataPoint.dataIndex
        })),
        "data-highlighted": isItemHighlighted || void 0,
        "data-faded": isItemFaded || void 0
      }, skipInteractionHandlers ? void 0 : getInteractionItemProps(instance, dataPoint), markerProps), dataPoint.id ?? dataPoint.dataIndex);
    })
  });
}
true ? Scatter.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types17.default.object,
  color: import_prop_types17.default.string.isRequired,
  colorGetter: import_prop_types17.default.func,
  /**
   * Callback fired when clicking on a scatter item.
   * @param {MouseEvent} event Mouse event recorded on the `<svg/>` element.
   * @param {ScatterItemIdentifier} scatterItemIdentifier The scatter item identifier.
   */
  onItemClick: import_prop_types17.default.func,
  series: import_prop_types17.default.object.isRequired,
  slotProps: import_prop_types17.default.object,
  slots: import_prop_types17.default.object,
  xScale: import_prop_types17.default.func.isRequired,
  yScale: import_prop_types17.default.func.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterPlot.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
function ScatterPlot(props) {
  const {
    slots,
    slotProps,
    onItemClick
  } = props;
  const seriesData = useScatterSeriesContext();
  const {
    xAxis,
    xAxisIds
  } = useXAxes();
  const {
    yAxis,
    yAxisIds
  } = useYAxes();
  const {
    zAxis,
    zAxisIds
  } = useZAxes();
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    seriesOrder
  } = seriesData;
  const defaultXAxisId = xAxisIds[0];
  const defaultYAxisId = yAxisIds[0];
  const defaultZAxisId = zAxisIds[0];
  const ScatterItems = (slots == null ? void 0 : slots.scatter) ?? Scatter;
  return (0, import_jsx_runtime19.jsx)(React24.Fragment, {
    children: seriesOrder.map((seriesId) => {
      const {
        id,
        xAxisId,
        yAxisId,
        zAxisId,
        color
      } = series[seriesId];
      const colorGetter = seriesConfig.colorProcessor(series[seriesId], xAxis[xAxisId ?? defaultXAxisId], yAxis[yAxisId ?? defaultYAxisId], zAxis[zAxisId ?? defaultZAxisId]);
      const xScale = xAxis[xAxisId ?? defaultXAxisId].scale;
      const yScale = yAxis[yAxisId ?? defaultYAxisId].scale;
      return (0, import_jsx_runtime19.jsx)(ScatterItems, _extends({
        xScale,
        yScale,
        color,
        colorGetter,
        series: series[seriesId],
        onItemClick,
        slots,
        slotProps
      }, slotProps == null ? void 0 : slotProps.scatter), id);
    })
  });
}
true ? ScatterPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when clicking on a scatter item.
   * @param {MouseEvent} event Mouse event recorded on the `<svg/>` element.
   * @param {ScatterItemIdentifier} scatterItemIdentifier The scatter item identifier.
   */
  onItemClick: import_prop_types18.default.func,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types18.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types18.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/ScatterChart/useScatterChartProps.js
var React25 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterChart.plugins.js
var SCATTER_CHART_PLUGINS = [useChartZAxis, useChartCartesianAxis, useChartInteraction, useChartHighlight, useChartVoronoi];

// node_modules/@mui/x-charts/esm/ScatterChart/useScatterChartProps.js
var _excluded15 = ["xAxis", "yAxis", "zAxis", "series", "axisHighlight", "voronoiMaxRadius", "disableVoronoi", "hideLegend", "width", "height", "margin", "colors", "sx", "grid", "onItemClick", "children", "slots", "slotProps", "loading", "highlightedItem", "onHighlightChange", "className", "showToolbar"];
var useScatterChartProps = (props) => {
  var _a, _b, _c, _d;
  const {
    xAxis,
    yAxis,
    zAxis,
    series,
    axisHighlight,
    voronoiMaxRadius,
    disableVoronoi,
    width,
    height,
    margin,
    colors,
    sx,
    grid,
    onItemClick,
    children,
    slots,
    slotProps,
    loading,
    highlightedItem,
    onHighlightChange,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded15);
  const seriesWithDefault = React25.useMemo(() => series.map((s) => _extends({
    type: "scatter"
  }, s)), [series]);
  const chartContainerProps = _extends({}, other, {
    series: seriesWithDefault,
    width,
    height,
    margin,
    colors,
    xAxis,
    yAxis,
    zAxis,
    highlightedItem,
    onHighlightChange,
    disableVoronoi,
    voronoiMaxRadius,
    onItemClick: disableVoronoi ? void 0 : onItemClick,
    className,
    plugins: SCATTER_CHART_PLUGINS,
    slots,
    slotProps
  });
  const chartsAxisProps = {
    slots,
    slotProps
  };
  const gridProps = {
    vertical: grid == null ? void 0 : grid.vertical,
    horizontal: grid == null ? void 0 : grid.horizontal
  };
  const scatterPlotProps = {
    onItemClick: disableVoronoi ? onItemClick : void 0,
    slots,
    slotProps
  };
  const overlayProps = {
    loading,
    slots,
    slotProps
  };
  const legendProps = {
    slots,
    slotProps
  };
  const axisHighlightProps = _extends({
    y: "none",
    x: "none"
  }, axisHighlight);
  const chartsWrapperProps = {
    sx,
    legendPosition: (_b = (_a = props.slotProps) == null ? void 0 : _a.legend) == null ? void 0 : _b.position,
    legendDirection: (_d = (_c = props.slotProps) == null ? void 0 : _c.legend) == null ? void 0 : _d.direction
  };
  return {
    chartsWrapperProps,
    chartContainerProps,
    chartsAxisProps,
    gridProps,
    scatterPlotProps,
    overlayProps,
    legendProps,
    axisHighlightProps,
    children
  };
};

// node_modules/@mui/x-charts/esm/ScatterChart/ScatterChart.js
var import_jsx_runtime20 = __toESM(require_jsx_runtime(), 1);
var ScatterChart = React26.forwardRef(function ScatterChart2(inProps, ref) {
  var _a, _b, _c, _d;
  const props = useThemeProps({
    props: inProps,
    name: "MuiScatterChart"
  });
  const {
    chartsWrapperProps,
    chartContainerProps,
    chartsAxisProps,
    gridProps,
    scatterPlotProps,
    overlayProps,
    legendProps,
    axisHighlightProps,
    children
  } = useScatterChartProps(props);
  const {
    chartDataProviderProps,
    chartsSurfaceProps
  } = useChartContainerProps(chartContainerProps, ref);
  const Tooltip = ((_a = props.slots) == null ? void 0 : _a.tooltip) ?? ChartsTooltip;
  const Toolbar3 = (_b = props.slots) == null ? void 0 : _b.toolbar;
  return (0, import_jsx_runtime20.jsx)(ChartDataProvider, _extends({}, chartDataProviderProps, {
    children: (0, import_jsx_runtime20.jsxs)(ChartsWrapper, _extends({}, chartsWrapperProps, {
      children: [props.showToolbar && Toolbar3 ? (0, import_jsx_runtime20.jsx)(Toolbar3, _extends({}, (_c = props.slotProps) == null ? void 0 : _c.toolbar)) : null, !props.hideLegend && (0, import_jsx_runtime20.jsx)(ChartsLegend, _extends({}, legendProps)), (0, import_jsx_runtime20.jsxs)(ChartsSurface, _extends({}, chartsSurfaceProps, {
        children: [(0, import_jsx_runtime20.jsx)(ChartsAxis, _extends({}, chartsAxisProps)), (0, import_jsx_runtime20.jsx)(ChartsGrid, _extends({}, gridProps)), (0, import_jsx_runtime20.jsx)("g", {
          "data-drawing-container": true,
          children: (0, import_jsx_runtime20.jsx)(ScatterPlot, _extends({}, scatterPlotProps))
        }), (0, import_jsx_runtime20.jsx)(ChartsOverlay, _extends({}, overlayProps)), (0, import_jsx_runtime20.jsx)(ChartsAxisHighlight, _extends({}, axisHighlightProps)), children]
      })), !props.loading && (0, import_jsx_runtime20.jsx)(Tooltip, _extends({
        trigger: "item"
      }, (_d = props.slotProps) == null ? void 0 : _d.tooltip))]
    }))
  }));
});
if (true) ScatterChart.displayName = "ScatterChart";
true ? ScatterChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types19.default.shape({
    current: import_prop_types19.default.object
  }),
  /**
   * The configuration of axes highlight.
   * @see See {@link https://mui.com/x/react-charts/highlighting/ highlighting docs} for more details.
   * @default { x: 'none', y: 'none' }
   */
  axisHighlight: import_prop_types19.default.shape({
    x: import_prop_types19.default.oneOf(["band", "line", "none"]),
    y: import_prop_types19.default.oneOf(["band", "line", "none"])
  }),
  children: import_prop_types19.default.node,
  className: import_prop_types19.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string), import_prop_types19.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types19.default.arrayOf(import_prop_types19.default.object),
  desc: import_prop_types19.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types19.default.bool,
  /**
   * If true, the interaction will not use the Voronoi cell and fall back to hover events.
   * @default false
   */
  disableVoronoi: import_prop_types19.default.bool,
  /**
   * Option to display a cartesian grid in the background.
   */
  grid: import_prop_types19.default.shape({
    horizontal: import_prop_types19.default.bool,
    vertical: import_prop_types19.default.bool
  }),
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types19.default.number,
  /**
   * If `true`, the legend is not rendered.
   */
  hideLegend: import_prop_types19.default.bool,
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types19.default.shape({
    dataIndex: import_prop_types19.default.number,
    seriesId: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types19.default.string,
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types19.default.bool,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types19.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.shape({
    bottom: import_prop_types19.default.number,
    left: import_prop_types19.default.number,
    right: import_prop_types19.default.number,
    top: import_prop_types19.default.number
  })]),
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types19.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types19.default.func,
  /**
   * Callback fired when clicking on a scatter item.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element if using Voronoi cells. Or the Mouse event from the scatter element, when `disableVoronoi=true`.
   * @param {ScatterItemIdentifier} scatterItemIdentifier The scatter item identifier.
   */
  onItemClick: import_prop_types19.default.func,
  /**
   * The series to display in the scatter chart.
   * An array of [[ScatterSeries]] objects.
   */
  series: import_prop_types19.default.arrayOf(import_prop_types19.default.object).isRequired,
  /**
   * If true, shows the default chart toolbar.
   * @default false
   */
  showToolbar: import_prop_types19.default.bool,
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types19.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types19.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types19.default.object,
  sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
  theme: import_prop_types19.default.oneOf(["dark", "light"]),
  title: import_prop_types19.default.string,
  /**
   * Defines the maximal distance between a scatter point and the pointer that triggers the interaction.
   * If `undefined`, the radius is assumed to be infinite.
   */
  voronoiMaxRadius: import_prop_types19.default.number,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types19.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  xAxis: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    barGapRatio: import_prop_types19.default.number,
    categoryGapRatio: import_prop_types19.default.number,
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      type: import_prop_types19.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types19.default.string,
      values: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number, import_prop_types19.default.string]).isRequired)
    }), import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["band"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      type: import_prop_types19.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types19.default.string,
      values: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number, import_prop_types19.default.string]).isRequired)
    }), import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["point"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["log"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["pow"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["sqrt"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["time"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["utc"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["x"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    height: import_prop_types19.default.number,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["linear"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelMinGap: import_prop_types19.default.number,
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func
  })]).isRequired),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  yAxis: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    barGapRatio: import_prop_types19.default.number,
    categoryGapRatio: import_prop_types19.default.number,
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      type: import_prop_types19.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types19.default.string,
      values: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number, import_prop_types19.default.string]).isRequired)
    }), import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["band"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      type: import_prop_types19.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types19.default.string,
      values: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number, import_prop_types19.default.string]).isRequired)
    }), import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["point"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["log"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["pow"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["sqrt"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["time"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["utc"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  }), import_prop_types19.default.shape({
    axis: import_prop_types19.default.oneOf(["y"]),
    classes: import_prop_types19.default.object,
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    disableLine: import_prop_types19.default.bool,
    disableTicks: import_prop_types19.default.bool,
    domainLimit: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["nice", "strict"]), import_prop_types19.default.func]),
    fill: import_prop_types19.default.string,
    hideTooltip: import_prop_types19.default.bool,
    id: import_prop_types19.default.oneOfType([import_prop_types19.default.number, import_prop_types19.default.string]),
    ignoreTooltip: import_prop_types19.default.bool,
    label: import_prop_types19.default.string,
    labelStyle: import_prop_types19.default.object,
    max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
    offset: import_prop_types19.default.number,
    position: import_prop_types19.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types19.default.bool,
    scaleType: import_prop_types19.default.oneOf(["linear"]),
    slotProps: import_prop_types19.default.object,
    slots: import_prop_types19.default.object,
    stroke: import_prop_types19.default.string,
    sx: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.func, import_prop_types19.default.object, import_prop_types19.default.bool])), import_prop_types19.default.func, import_prop_types19.default.object]),
    tickInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.array, import_prop_types19.default.func]),
    tickLabelInterval: import_prop_types19.default.oneOfType([import_prop_types19.default.oneOf(["auto"]), import_prop_types19.default.func]),
    tickLabelPlacement: import_prop_types19.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types19.default.object,
    tickMaxStep: import_prop_types19.default.number,
    tickMinStep: import_prop_types19.default.number,
    tickNumber: import_prop_types19.default.number,
    tickPlacement: import_prop_types19.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types19.default.number,
    valueFormatter: import_prop_types19.default.func,
    width: import_prop_types19.default.number
  })]).isRequired),
  /**
   * The configuration of the z-axes.
   */
  zAxis: import_prop_types19.default.arrayOf(import_prop_types19.default.shape({
    colorMap: import_prop_types19.default.oneOfType([import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      type: import_prop_types19.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types19.default.string,
      values: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number, import_prop_types19.default.string]).isRequired)
    }), import_prop_types19.default.shape({
      color: import_prop_types19.default.oneOfType([import_prop_types19.default.arrayOf(import_prop_types19.default.string.isRequired), import_prop_types19.default.func]).isRequired,
      max: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      min: import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]),
      type: import_prop_types19.default.oneOf(["continuous"]).isRequired
    }), import_prop_types19.default.shape({
      colors: import_prop_types19.default.arrayOf(import_prop_types19.default.string).isRequired,
      thresholds: import_prop_types19.default.arrayOf(import_prop_types19.default.oneOfType([import_prop_types19.default.instanceOf(Date), import_prop_types19.default.number]).isRequired).isRequired,
      type: import_prop_types19.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types19.default.array,
    dataKey: import_prop_types19.default.string,
    id: import_prop_types19.default.string,
    max: import_prop_types19.default.number,
    min: import_prop_types19.default.number
  }))
} : void 0;

// node_modules/@mui/x-charts/esm/SparkLineChart/SparkLineChart.js
var React27 = __toESM(require_react(), 1);
var import_prop_types20 = __toESM(require_prop_types(), 1);
var import_jsx_runtime21 = __toESM(require_jsx_runtime(), 1);
var _excluded16 = ["xAxis", "yAxis", "width", "height", "margin", "color", "sx", "showTooltip", "showHighlight", "axisHighlight", "children", "slots", "slotProps", "data", "plotType", "valueFormatter", "area", "curve", "className", "disableClipping", "clipAreaOffset"];
var SPARK_LINE_DEFAULT_MARGIN = 5;
var SparkLineChart = React27.forwardRef(function SparkLineChart2(props, ref) {
  var _a, _b, _c;
  const {
    xAxis,
    yAxis,
    width,
    height,
    margin = SPARK_LINE_DEFAULT_MARGIN,
    color,
    sx,
    showTooltip,
    showHighlight,
    axisHighlight: inAxisHighlight,
    children,
    slots,
    slotProps,
    data,
    plotType = "line",
    valueFormatter = (value) => value === null ? "" : value.toString(),
    area,
    curve = "linear",
    className,
    disableClipping,
    clipAreaOffset
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded16);
  const id = useId();
  const clipPathId = `${id}-clip-path`;
  const clipPathOffset = {
    top: (clipAreaOffset == null ? void 0 : clipAreaOffset.top) ?? 1,
    right: (clipAreaOffset == null ? void 0 : clipAreaOffset.right) ?? 1,
    bottom: (clipAreaOffset == null ? void 0 : clipAreaOffset.bottom) ?? 1,
    left: (clipAreaOffset == null ? void 0 : clipAreaOffset.left) ?? 1
  };
  const defaultXHighlight = showHighlight && plotType === "bar" ? {
    x: "band"
  } : {
    x: "none"
  };
  const axisHighlight = _extends({}, defaultXHighlight, inAxisHighlight);
  const Tooltip = ((_a = props.slots) == null ? void 0 : _a.tooltip) ?? ChartsTooltip;
  const colors = React27.useMemo(() => {
    if (color == null) {
      return void 0;
    }
    return typeof color === "function" ? (mode) => [color(mode)] : [color];
  }, [color]);
  return (0, import_jsx_runtime21.jsxs)(ChartDataProvider, {
    series: [_extends({
      type: plotType,
      data,
      valueFormatter
    }, plotType === "bar" ? {} : {
      area,
      curve,
      disableHighlight: !showHighlight
    })],
    width,
    height,
    margin,
    xAxis: [_extends({
      id: DEFAULT_X_AXIS_KEY,
      scaleType: plotType === "bar" ? "band" : "point",
      data: Array.from({
        length: data.length
      }, (_, index) => index),
      hideTooltip: xAxis === void 0
    }, xAxis, {
      position: "none"
    })],
    yAxis: [_extends({
      id: DEFAULT_Y_AXIS_KEY
    }, yAxis, {
      position: "none"
    })],
    colors,
    disableAxisListener: (!showTooltip || ((_b = slotProps == null ? void 0 : slotProps.tooltip) == null ? void 0 : _b.trigger) !== "axis") && (axisHighlight == null ? void 0 : axisHighlight.x) === "none" && (axisHighlight == null ? void 0 : axisHighlight.y) === "none",
    children: [(0, import_jsx_runtime21.jsxs)(ChartsSurface, _extends({
      className,
      ref,
      sx
    }, other, {
      children: [(0, import_jsx_runtime21.jsxs)("g", {
        clipPath: `url(#${clipPathId})`,
        children: [plotType === "bar" && (0, import_jsx_runtime21.jsx)(BarPlot, {
          skipAnimation: true,
          slots,
          slotProps
        }), plotType === "line" && (0, import_jsx_runtime21.jsxs)(React27.Fragment, {
          children: [(0, import_jsx_runtime21.jsx)(AreaPlot, {
            skipAnimation: true,
            slots,
            slotProps
          }), (0, import_jsx_runtime21.jsx)(LinePlot, {
            skipAnimation: true,
            slots,
            slotProps
          })]
        })]
      }), plotType === "line" && (0, import_jsx_runtime21.jsx)(LineHighlightPlot, {
        slots,
        slotProps
      }), disableClipping ? null : (0, import_jsx_runtime21.jsx)(ChartsClipPath, {
        id: clipPathId,
        offset: clipPathOffset
      }), (0, import_jsx_runtime21.jsx)(ChartsAxisHighlight, _extends({}, axisHighlight)), children]
    })), showTooltip && (0, import_jsx_runtime21.jsx)(Tooltip, _extends({}, (_c = props.slotProps) == null ? void 0 : _c.tooltip))]
  });
});
if (true) SparkLineChart.displayName = "SparkLineChart";
true ? SparkLineChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types20.default.shape({
    current: import_prop_types20.default.object
  }),
  /**
   * Set to `true` to fill spark line area.
   * Has no effect if plotType='bar'.
   * @default false
   */
  area: import_prop_types20.default.bool,
  axisHighlight: import_prop_types20.default.shape({
    x: import_prop_types20.default.oneOf(["band", "line", "none"]),
    y: import_prop_types20.default.oneOf(["band", "line", "none"])
  }),
  children: import_prop_types20.default.node,
  className: import_prop_types20.default.string,
  /**
   * The clipped area offset in pixels.
   *
   * This prevents partial clipping of lines when they are drawn on the edge of the drawing area.
   *
   * @default { top: 1, right: 1, bottom: 1, left: 1 }
   */
  clipAreaOffset: import_prop_types20.default.shape({
    bottom: import_prop_types20.default.number,
    left: import_prop_types20.default.number,
    right: import_prop_types20.default.number,
    top: import_prop_types20.default.number
  }),
  /**
   * Color used to colorize the sparkline.
   * @default rainbowSurgePalette[0]
   */
  color: import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.string]),
  /**
   * @default 'linear'
   */
  curve: import_prop_types20.default.oneOf(["bumpX", "bumpY", "catmullRom", "linear", "monotoneX", "monotoneY", "natural", "step", "stepAfter", "stepBefore"]),
  /**
   * Data to plot.
   */
  data: import_prop_types20.default.arrayOf(import_prop_types20.default.number).isRequired,
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types20.default.arrayOf(import_prop_types20.default.object),
  desc: import_prop_types20.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types20.default.bool,
  /**
   * When `true`, the chart's drawing area will not be clipped and elements within can visually overflow the chart.
   *
   * @default false
   */
  disableClipping: import_prop_types20.default.bool,
  /**
   * If true, the voronoi interaction are ignored.
   */
  disableVoronoi: import_prop_types20.default.bool,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types20.default.number,
  /**
   * The controlled axis highlight.
   * Identified by the axis id, and data index.
   */
  highlightedAxis: import_prop_types20.default.arrayOf(import_prop_types20.default.shape({
    axisId: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]).isRequired,
    dataIndex: import_prop_types20.default.number.isRequired
  })),
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types20.default.shape({
    dataIndex: import_prop_types20.default.number,
    seriesId: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types20.default.string,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types20.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   * @default 5
   */
  margin: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.shape({
    bottom: import_prop_types20.default.number,
    left: import_prop_types20.default.number,
    right: import_prop_types20.default.number,
    top: import_prop_types20.default.number
  })]),
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types20.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types20.default.func,
  /**
   * The function called when the pointer position corresponds to a new axis data item.
   * This update can either be caused by a pointer movement, or an axis update.
   * In case of multiple axes, the function is called if at least one axis is updated.
   * The argument contains the identifier for all axes with a `data` property.
   * @param {AxisItemIdentifier[]} axisItems The array of axes item identifiers.
   */
  onHighlightedAxisChange: import_prop_types20.default.func,
  /**
   * Callback fired when clicking close to an item.
   * This is only available for scatter plot for now.
   * @param {MouseEvent} event Mouse event caught at the svg level
   * @param {ScatterItemIdentifier} scatterItemIdentifier Identify which item got clicked
   */
  onItemClick: import_prop_types20.default.func,
  /**
   * Type of plot used.
   * @default 'line'
   */
  plotType: import_prop_types20.default.oneOf(["bar", "line"]),
  /**
   * Set to `true` to highlight the value.
   * With line, it shows a point.
   * With bar, it shows a highlight band.
   * @default false
   */
  showHighlight: import_prop_types20.default.bool,
  /**
   * Set to `true` to enable the tooltip in the sparkline.
   * @default false
   */
  showTooltip: import_prop_types20.default.bool,
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types20.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types20.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types20.default.object,
  sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
  theme: import_prop_types20.default.oneOf(["dark", "light"]),
  title: import_prop_types20.default.string,
  /**
   * Formatter used by the tooltip.
   * @param {number} value The value to format.
   * @returns {string} the formatted value.
   * @default (value: number | null) => (value === null ? '' : value.toString())
   */
  valueFormatter: import_prop_types20.default.func,
  /**
   * Defines the maximal distance between a scatter point and the pointer that triggers the interaction.
   * If `undefined`, the radius is assumed to be infinite.
   */
  voronoiMaxRadius: import_prop_types20.default.number,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types20.default.number,
  /**
   * The xAxis configuration.
   * Notice it is a single [[AxisConfig]] object, not an array of configuration.
   */
  xAxis: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    barGapRatio: import_prop_types20.default.number,
    categoryGapRatio: import_prop_types20.default.number,
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      type: import_prop_types20.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types20.default.string,
      values: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number, import_prop_types20.default.string]).isRequired)
    }), import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["band"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      type: import_prop_types20.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types20.default.string,
      values: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number, import_prop_types20.default.string]).isRequired)
    }), import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["point"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["log"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["pow"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["sqrt"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["time"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["utc"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["x"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    height: import_prop_types20.default.number,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["linear"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelMinGap: import_prop_types20.default.number,
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func
  })]),
  /**
   * The yAxis configuration.
   * Notice it is a single [[AxisConfig]] object, not an array of configuration.
   */
  yAxis: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    barGapRatio: import_prop_types20.default.number,
    categoryGapRatio: import_prop_types20.default.number,
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      type: import_prop_types20.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types20.default.string,
      values: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number, import_prop_types20.default.string]).isRequired)
    }), import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["band"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      type: import_prop_types20.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types20.default.string,
      values: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number, import_prop_types20.default.string]).isRequired)
    }), import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["point"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["log"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["pow"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["sqrt"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["time"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["utc"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  }), import_prop_types20.default.shape({
    axis: import_prop_types20.default.oneOf(["y"]),
    classes: import_prop_types20.default.object,
    colorMap: import_prop_types20.default.oneOfType([import_prop_types20.default.shape({
      color: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.string.isRequired), import_prop_types20.default.func]).isRequired,
      max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
      type: import_prop_types20.default.oneOf(["continuous"]).isRequired
    }), import_prop_types20.default.shape({
      colors: import_prop_types20.default.arrayOf(import_prop_types20.default.string).isRequired,
      thresholds: import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]).isRequired).isRequired,
      type: import_prop_types20.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types20.default.array,
    dataKey: import_prop_types20.default.string,
    disableLine: import_prop_types20.default.bool,
    disableTicks: import_prop_types20.default.bool,
    domainLimit: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["nice", "strict"]), import_prop_types20.default.func]),
    fill: import_prop_types20.default.string,
    hideTooltip: import_prop_types20.default.bool,
    id: import_prop_types20.default.oneOfType([import_prop_types20.default.number, import_prop_types20.default.string]),
    ignoreTooltip: import_prop_types20.default.bool,
    label: import_prop_types20.default.string,
    labelStyle: import_prop_types20.default.object,
    max: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    min: import_prop_types20.default.oneOfType([import_prop_types20.default.instanceOf(Date), import_prop_types20.default.number]),
    offset: import_prop_types20.default.number,
    position: import_prop_types20.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types20.default.bool,
    scaleType: import_prop_types20.default.oneOf(["linear"]),
    slotProps: import_prop_types20.default.object,
    slots: import_prop_types20.default.object,
    stroke: import_prop_types20.default.string,
    sx: import_prop_types20.default.oneOfType([import_prop_types20.default.arrayOf(import_prop_types20.default.oneOfType([import_prop_types20.default.func, import_prop_types20.default.object, import_prop_types20.default.bool])), import_prop_types20.default.func, import_prop_types20.default.object]),
    tickInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.array, import_prop_types20.default.func]),
    tickLabelInterval: import_prop_types20.default.oneOfType([import_prop_types20.default.oneOf(["auto"]), import_prop_types20.default.func]),
    tickLabelPlacement: import_prop_types20.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types20.default.object,
    tickMaxStep: import_prop_types20.default.number,
    tickMinStep: import_prop_types20.default.number,
    tickNumber: import_prop_types20.default.number,
    tickPlacement: import_prop_types20.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types20.default.number,
    valueFormatter: import_prop_types20.default.func,
    width: import_prop_types20.default.number
  })])
} : void 0;

// node_modules/@mui/x-charts/esm/Gauge/Gauge.js
var React33 = __toESM(require_react(), 1);
var import_prop_types24 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/Gauge/GaugeContainer.js
var React29 = __toESM(require_react(), 1);
var import_prop_types21 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/Gauge/GaugeProvider.js
var React28 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/Gauge/utils.js
function getPoint(angle) {
  const radAngle = deg2rad(angle);
  return [Math.sin(radAngle), -Math.cos(radAngle)];
}
function getArcRatios(startAngle, endAngle) {
  const points = [[0, 0], getPoint(startAngle), getPoint(endAngle)];
  const minAngle = Math.min(startAngle, endAngle);
  const maxAngle = Math.max(startAngle, endAngle);
  const initialAngle = Math.floor(minAngle / 90) * 90;
  for (let step = 1; step <= 4; step += 1) {
    const cardinalAngle = initialAngle + step * 90;
    if (cardinalAngle < maxAngle) {
      points.push(getPoint(cardinalAngle));
    }
  }
  const minX = Math.min(...points.map(([x]) => x));
  const maxX = Math.max(...points.map(([x]) => x));
  const minY = Math.min(...points.map(([, y]) => y));
  const maxY = Math.max(...points.map(([, y]) => y));
  return {
    cx: -minX / (maxX - minX),
    cy: -minY / (maxY - minY),
    minX,
    maxX,
    minY,
    maxY
  };
}
function getAvailableRadius(cx, cy, width, height, {
  minX,
  maxX,
  minY,
  maxY
}) {
  return Math.min(...[{
    ratio: Math.abs(minX),
    space: cx
  }, {
    ratio: Math.abs(maxX),
    space: width - cx
  }, {
    ratio: Math.abs(minY),
    space: cy
  }, {
    ratio: Math.abs(maxY),
    space: height - cy
  }].map(({
    ratio,
    space
  }) => {
    if (ratio < 1e-5) {
      return Infinity;
    }
    return space / ratio;
  }));
}

// node_modules/@mui/x-charts/esm/Gauge/GaugeProvider.js
var import_jsx_runtime22 = __toESM(require_jsx_runtime(), 1);
var GaugeContext = React28.createContext({
  value: null,
  valueMin: 0,
  valueMax: 0,
  startAngle: 0,
  endAngle: 0,
  innerRadius: 0,
  outerRadius: 0,
  cornerRadius: 0,
  cx: 0,
  cy: 0,
  maxRadius: 0,
  valueAngle: null
});
if (true) GaugeContext.displayName = "GaugeContext";
function GaugeProvider(props) {
  const {
    value = null,
    valueMin = 0,
    valueMax = 100,
    startAngle = 0,
    endAngle = 360,
    outerRadius: outerRadiusParam,
    innerRadius: innerRadiusParam,
    cornerRadius: cornerRadiusParam,
    cx: cxParam,
    cy: cyParam,
    children
  } = props;
  const {
    left,
    top,
    width,
    height
  } = useDrawingArea();
  const ratios = getArcRatios(startAngle, endAngle);
  const innerCx = cxParam ? getPercentageValue(cxParam, width) : ratios.cx * width;
  const innerCy = cyParam ? getPercentageValue(cyParam, height) : ratios.cy * height;
  let cx = left + innerCx;
  let cy = top + innerCy;
  const maxRadius = getAvailableRadius(innerCx, innerCy, width, height, ratios);
  if (cxParam === void 0) {
    const usedWidth = maxRadius * (ratios.maxX - ratios.minX);
    cx = left + (width - usedWidth) / 2 + ratios.cx * usedWidth;
  }
  if (cyParam === void 0) {
    const usedHeight = maxRadius * (ratios.maxY - ratios.minY);
    cy = top + (height - usedHeight) / 2 + ratios.cy * usedHeight;
  }
  const outerRadius = getPercentageValue(outerRadiusParam ?? maxRadius, maxRadius);
  const innerRadius = getPercentageValue(innerRadiusParam ?? "80%", maxRadius);
  const cornerRadius = getPercentageValue(cornerRadiusParam ?? 0, outerRadius - innerRadius);
  const contextValue = React28.useMemo(() => {
    const startAngleRad = deg2rad(startAngle);
    const endAngleRad = deg2rad(endAngle);
    return {
      value,
      valueMin,
      valueMax,
      startAngle: startAngleRad,
      endAngle: endAngleRad,
      outerRadius,
      innerRadius,
      cornerRadius,
      cx,
      cy,
      maxRadius,
      valueAngle: value === null ? null : startAngleRad + (endAngleRad - startAngleRad) * (value - valueMin) / (valueMax - valueMin)
    };
  }, [value, valueMin, valueMax, startAngle, endAngle, outerRadius, innerRadius, cornerRadius, cx, cy, maxRadius]);
  return (0, import_jsx_runtime22.jsx)(GaugeContext.Provider, {
    value: contextValue,
    children
  });
}
function useGaugeState() {
  return React28.useContext(GaugeContext);
}

// node_modules/@mui/x-charts/esm/Gauge/GaugeContainer.js
var import_jsx_runtime23 = __toESM(require_jsx_runtime(), 1);
var _excluded17 = ["width", "height", "margin", "title", "desc", "value", "valueMin", "valueMax", "startAngle", "endAngle", "outerRadius", "innerRadius", "cornerRadius", "cx", "cy", "children"];
var GStyled = styled_default("g")(({
  theme
}) => ({
  "& text": {
    fill: (theme.vars || theme).palette.text.primary
  }
}));
var GaugeContainer = React29.forwardRef(function GaugeContainer2(props, ref) {
  const {
    width: inWidth,
    height: inHeight,
    margin,
    title,
    desc,
    value,
    valueMin = 0,
    valueMax = 100,
    startAngle,
    endAngle,
    outerRadius,
    innerRadius,
    cornerRadius,
    cx,
    cy,
    children
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded17);
  return (0, import_jsx_runtime23.jsx)(ChartProvider, {
    pluginParams: {
      width: inWidth,
      height: inHeight,
      margin: defaultizeMargin(margin, {
        left: 10,
        right: 10,
        top: 10,
        bottom: 10
      })
    },
    plugins: [],
    children: (0, import_jsx_runtime23.jsx)(GaugeProvider, {
      value,
      valueMin,
      valueMax,
      startAngle,
      endAngle,
      outerRadius,
      innerRadius,
      cornerRadius,
      cx,
      cy,
      children: (0, import_jsx_runtime23.jsx)(ChartsSurface, _extends({
        title,
        desc,
        role: "meter",
        "aria-valuenow": value === null ? void 0 : value,
        "aria-valuemin": valueMin,
        "aria-valuemax": valueMax
      }, other, {
        ref,
        children: (0, import_jsx_runtime23.jsx)(GStyled, {
          "aria-hidden": "true",
          children
        })
      }))
    })
  });
});
if (true) GaugeContainer.displayName = "GaugeContainer";
true ? GaugeContainer.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  children: import_prop_types21.default.node,
  className: import_prop_types21.default.string,
  /**
   * The radius applied to arc corners (similar to border radius).
   * Set it to '50%' to get rounded arc.
   * @default 0
   */
  cornerRadius: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.string]),
  /**
   * The x coordinate of the arc center.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the width the drawing area.
   */
  cx: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.string]),
  /**
   * The y coordinate of the arc center.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the height the drawing area.
   */
  cy: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.string]),
  desc: import_prop_types21.default.string,
  /**
   * The end angle (deg).
   * @default 360
   */
  endAngle: import_prop_types21.default.number,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types21.default.number,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types21.default.string,
  /**
   * The radius between circle center and the beginning of the arc.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the maximal radius that fit into the drawing area.
   * @default '80%'
   */
  innerRadius: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.string]),
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.shape({
    bottom: import_prop_types21.default.number,
    left: import_prop_types21.default.number,
    right: import_prop_types21.default.number,
    top: import_prop_types21.default.number
  })]),
  /**
   * The radius between circle center and the end of the arc.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the maximal radius that fit into the drawing area.
   * @default '100%'
   */
  outerRadius: import_prop_types21.default.oneOfType([import_prop_types21.default.number, import_prop_types21.default.string]),
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types21.default.bool,
  /**
   * The start angle (deg).
   * @default 0
   */
  startAngle: import_prop_types21.default.number,
  sx: import_prop_types21.default.oneOfType([import_prop_types21.default.arrayOf(import_prop_types21.default.oneOfType([import_prop_types21.default.func, import_prop_types21.default.object, import_prop_types21.default.bool])), import_prop_types21.default.func, import_prop_types21.default.object]),
  title: import_prop_types21.default.string,
  /**
   * The value of the gauge.
   * Set to `null` to not display a value.
   */
  value: import_prop_types21.default.number,
  /**
   * The maximal value of the gauge.
   * @default 100
   */
  valueMax: import_prop_types21.default.number,
  /**
   * The minimal value of the gauge.
   * @default 0
   */
  valueMin: import_prop_types21.default.number,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types21.default.number
} : void 0;

// node_modules/@mui/x-charts/esm/Gauge/GaugeValueArc.js
var React30 = __toESM(require_react(), 1);
var import_prop_types22 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/hooks/animation/useAnimateGaugeValueArc.js
function gaugeValueArcPropsInterpolator(from, to) {
  const interpolateStartAngle = number_default(from.startAngle, to.startAngle);
  const interpolateEndAngle = number_default(from.endAngle, to.endAngle);
  const interpolateInnerRadius = number_default(from.innerRadius, to.innerRadius);
  const interpolateOuterRadius = number_default(from.outerRadius, to.outerRadius);
  const interpolateCornerRadius = number_default(from.cornerRadius, to.cornerRadius);
  return (t) => {
    return {
      startAngle: interpolateStartAngle(t),
      endAngle: interpolateEndAngle(t),
      innerRadius: interpolateInnerRadius(t),
      outerRadius: interpolateOuterRadius(t),
      cornerRadius: interpolateCornerRadius(t)
    };
  };
}
function useAnimateGaugeValueArc(props) {
  return useAnimate({
    startAngle: props.startAngle,
    endAngle: props.endAngle,
    innerRadius: props.innerRadius,
    outerRadius: props.outerRadius,
    cornerRadius: props.cornerRadius
  }, {
    createInterpolator: gaugeValueArcPropsInterpolator,
    transformProps: (p) => ({
      d: arc_default().cornerRadius(p.cornerRadius)({
        innerRadius: p.innerRadius,
        outerRadius: p.outerRadius,
        startAngle: p.startAngle,
        endAngle: p.endAngle
      })
    }),
    applyProps(element, p) {
      element.setAttribute("d", p.d);
    },
    initialProps: {
      startAngle: props.startAngle,
      endAngle: props.startAngle,
      innerRadius: props.innerRadius,
      outerRadius: props.outerRadius,
      cornerRadius: props.cornerRadius
    },
    skip: props.skipAnimation,
    ref: props.ref
  });
}

// node_modules/@mui/x-charts/esm/Gauge/GaugeValueArc.js
var import_jsx_runtime24 = __toESM(require_jsx_runtime(), 1);
var _excluded18 = ["cx", "cy", "startAngle", "endAngle", "cornerRadius", "innerRadius", "outerRadius", "skipAnimation"];
var StyledPath = styled_default("path", {
  name: "MuiGauge",
  slot: "ReferenceArc"
})(({
  theme
}) => ({
  fill: (theme.vars || theme).palette.primary.main
}));
function GaugeValueArc(props) {
  const {
    value,
    valueMin,
    valueMax,
    startAngle,
    endAngle,
    outerRadius,
    innerRadius,
    cornerRadius,
    cx,
    cy
  } = useGaugeState();
  if (value === null) {
    return null;
  }
  const valueAngle = startAngle + (value - valueMin) / (valueMax - valueMin) * (endAngle - startAngle);
  return (0, import_jsx_runtime24.jsx)(AnimatedGaugeValueArc, _extends({}, props, {
    cx,
    cy,
    startAngle,
    endAngle: valueAngle,
    cornerRadius,
    innerRadius,
    outerRadius
  }));
}
true ? GaugeValueArc.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  skipAnimation: import_prop_types22.default.bool
} : void 0;
function AnimatedGaugeValueArc(_ref) {
  let {
    cx,
    cy,
    startAngle,
    endAngle,
    cornerRadius,
    innerRadius,
    outerRadius,
    skipAnimation: inSkipAnimation
  } = _ref, other = _objectWithoutPropertiesLoose(_ref, _excluded18);
  const skipAnimation = useSkipAnimation(inSkipAnimation);
  const animatedProps = useAnimateGaugeValueArc({
    startAngle,
    endAngle,
    cornerRadius,
    innerRadius,
    outerRadius,
    skipAnimation
  });
  return (0, import_jsx_runtime24.jsx)(StyledPath, _extends({}, animatedProps, {
    transform: `translate(${cx}, ${cy})`
  }, other));
}
true ? AnimatedGaugeValueArc.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  cornerRadius: import_prop_types22.default.number.isRequired,
  cx: import_prop_types22.default.number.isRequired,
  cy: import_prop_types22.default.number.isRequired,
  endAngle: import_prop_types22.default.number.isRequired,
  innerRadius: import_prop_types22.default.number.isRequired,
  outerRadius: import_prop_types22.default.number.isRequired,
  skipAnimation: import_prop_types22.default.bool,
  startAngle: import_prop_types22.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/Gauge/GaugeReferenceArc.js
var React31 = __toESM(require_react(), 1);
var import_jsx_runtime25 = __toESM(require_jsx_runtime(), 1);
var StyledPath2 = styled_default("path", {
  name: "MuiGauge",
  slot: "ReferenceArc"
})(({
  theme
}) => ({
  fill: (theme.vars || theme).palette.divider
}));
function GaugeReferenceArc(props) {
  const {
    startAngle,
    endAngle,
    outerRadius,
    innerRadius,
    cornerRadius,
    cx,
    cy
  } = useGaugeState();
  return (0, import_jsx_runtime25.jsx)(StyledPath2, _extends({
    transform: `translate(${cx}, ${cy})`,
    d: arc_default().cornerRadius(cornerRadius)({
      startAngle,
      endAngle,
      innerRadius,
      outerRadius
    })
  }, props));
}

// node_modules/@mui/x-charts/esm/Gauge/gaugeClasses.js
function getGaugeUtilityClass(slot) {
  return generateUtilityClass("MuiGauge", slot);
}
var gaugeClasses = generateUtilityClasses("MuiGauge", ["root", "valueArc", "referenceArc", "valueText"]);

// node_modules/@mui/x-charts/esm/Gauge/GaugeValueText.js
var React32 = __toESM(require_react(), 1);
var import_prop_types23 = __toESM(require_prop_types(), 1);
var import_jsx_runtime26 = __toESM(require_jsx_runtime(), 1);
var _excluded19 = ["text", "className"];
function defaultFormatter({
  value
}) {
  return value === null ? null : value.toLocaleString();
}
function GaugeValueText(props) {
  const {
    text = defaultFormatter,
    className
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded19);
  const {
    value,
    valueMin,
    valueMax,
    cx,
    cy
  } = useGaugeState();
  const formattedText = typeof text === "function" ? text({
    value,
    valueMin,
    valueMax
  }) : text;
  if (formattedText === null) {
    return null;
  }
  return (0, import_jsx_runtime26.jsx)("g", {
    className,
    children: (0, import_jsx_runtime26.jsx)(ChartsText, _extends({
      x: cx,
      y: cy,
      text: formattedText,
      style: {
        textAnchor: "middle",
        dominantBaseline: "central"
      }
    }, other))
  });
}
true ? GaugeValueText.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Height of a text line (in `em`).
   */
  lineHeight: import_prop_types23.default.number,
  /**
   * If `true`, the line width is computed.
   * @default false
   */
  needsComputation: import_prop_types23.default.bool,
  ownerState: import_prop_types23.default.any,
  /**
   * Style applied to text elements.
   */
  style: import_prop_types23.default.object,
  text: import_prop_types23.default.oneOfType([import_prop_types23.default.func, import_prop_types23.default.string])
} : void 0;

// node_modules/@mui/x-charts/esm/Gauge/Gauge.js
var import_jsx_runtime27 = __toESM(require_jsx_runtime(), 1);
var _excluded20 = ["text", "children", "classes", "className", "skipAnimation"];
var useUtilityClasses7 = (props) => {
  const {
    classes
  } = props;
  const slots = {
    root: ["root"],
    valueArc: ["valueArc"],
    referenceArc: ["referenceArc"],
    valueText: ["valueText"]
  };
  return composeClasses(slots, getGaugeUtilityClass, classes);
};
var Gauge = React33.forwardRef(function Gauge2(props, ref) {
  const {
    text,
    children,
    className,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded20);
  const classes = useUtilityClasses7(props);
  return (0, import_jsx_runtime27.jsxs)(GaugeContainer, _extends({}, other, {
    className: clsx_default(classes.root, className),
    ref,
    children: [(0, import_jsx_runtime27.jsx)(GaugeReferenceArc, {
      className: classes.referenceArc
    }), (0, import_jsx_runtime27.jsx)(GaugeValueArc, {
      className: classes.valueArc,
      skipAnimation
    }), (0, import_jsx_runtime27.jsx)(GaugeValueText, {
      className: classes.valueText,
      text
    }), children]
  }));
});
if (true) Gauge.displayName = "Gauge";
true ? Gauge.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  children: import_prop_types24.default.node,
  classes: import_prop_types24.default.object,
  className: import_prop_types24.default.string,
  /**
   * The radius applied to arc corners (similar to border radius).
   * Set it to '50%' to get rounded arc.
   * @default 0
   */
  cornerRadius: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.string]),
  /**
   * The x coordinate of the arc center.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the width the drawing area.
   */
  cx: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.string]),
  /**
   * The y coordinate of the arc center.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the height the drawing area.
   */
  cy: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.string]),
  desc: import_prop_types24.default.string,
  /**
   * The end angle (deg).
   * @default 360
   */
  endAngle: import_prop_types24.default.number,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types24.default.number,
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types24.default.string,
  /**
   * The radius between circle center and the beginning of the arc.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the maximal radius that fit into the drawing area.
   * @default '80%'
   */
  innerRadius: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.string]),
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.shape({
    bottom: import_prop_types24.default.number,
    left: import_prop_types24.default.number,
    right: import_prop_types24.default.number,
    top: import_prop_types24.default.number
  })]),
  /**
   * The radius between circle center and the end of the arc.
   * Can be a number (in px) or a string with a percentage such as '50%'.
   * The '100%' is the maximal radius that fit into the drawing area.
   * @default '100%'
   */
  outerRadius: import_prop_types24.default.oneOfType([import_prop_types24.default.number, import_prop_types24.default.string]),
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types24.default.bool,
  /**
   * The start angle (deg).
   * @default 0
   */
  startAngle: import_prop_types24.default.number,
  sx: import_prop_types24.default.oneOfType([import_prop_types24.default.arrayOf(import_prop_types24.default.oneOfType([import_prop_types24.default.func, import_prop_types24.default.object, import_prop_types24.default.bool])), import_prop_types24.default.func, import_prop_types24.default.object]),
  text: import_prop_types24.default.oneOfType([import_prop_types24.default.func, import_prop_types24.default.string]),
  title: import_prop_types24.default.string,
  /**
   * The value of the gauge.
   * Set to `null` to not display a value.
   */
  value: import_prop_types24.default.number,
  /**
   * The maximal value of the gauge.
   * @default 100
   */
  valueMax: import_prop_types24.default.number,
  /**
   * The minimal value of the gauge.
   * @default 0
   */
  valueMin: import_prop_types24.default.number,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types24.default.number
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarChart.js
var React46 = __toESM(require_react(), 1);
var import_prop_types30 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/useRadarChartProps.js
var _excluded21 = ["apiRef", "series", "radar", "width", "height", "margin", "colors", "sx", "children", "slots", "slotProps", "skipAnimation", "loading", "highlightedItem", "onHighlightChange", "hideLegend", "divisions", "shape", "stripeColor", "highlight", "showToolbar", "onAxisClick", "onAreaClick", "onMarkClick"];
var useRadarChartProps = (props) => {
  const {
    apiRef,
    series,
    radar,
    width,
    height,
    margin,
    colors,
    sx,
    children,
    slots,
    slotProps,
    skipAnimation,
    loading,
    highlightedItem,
    onHighlightChange,
    divisions,
    shape,
    stripeColor,
    highlight = "axis",
    onAxisClick,
    onAreaClick,
    onMarkClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded21);
  const radarDataProviderProps = {
    apiRef,
    series,
    radar,
    highlight,
    width,
    height,
    margin,
    colors,
    highlightedItem,
    onHighlightChange,
    skipAnimation,
    onAxisClick
  };
  const overlayProps = {
    slots,
    slotProps,
    loading
  };
  const legendProps = {
    slots,
    slotProps
  };
  const chartsWrapperProps = {
    sx
  };
  const radarGrid = {
    divisions,
    shape,
    stripeColor
  };
  const radarSeriesAreaProps = {
    onItemClick: onAreaClick
  };
  const radarSeriesMarksProps = {
    onItemClick: onMarkClick
  };
  const chartsSurfaceProps = other;
  return {
    highlight,
    chartsWrapperProps,
    chartsSurfaceProps,
    radarDataProviderProps,
    radarGrid,
    radarSeriesAreaProps,
    radarSeriesMarksProps,
    overlayProps,
    legendProps,
    children
  };
};

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/RadarGrid.js
var React38 = __toESM(require_react(), 1);
var import_prop_types25 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/useRadarGridData.js
function useRadarGridData() {
  const {
    instance
  } = useChartContext();
  const rotationScale = useRotationScale();
  const {
    radiusAxis
  } = useRadiusAxes();
  const drawingArea = useDrawingArea();
  const cx = drawingArea.left + drawingArea.width / 2;
  const cy = drawingArea.top + drawingArea.height / 2;
  if (!rotationScale || rotationScale.domain().length === 0) {
    return null;
  }
  const metrics = rotationScale.domain();
  const angles = metrics.map((key) => rotationScale(key));
  return {
    center: {
      x: cx,
      y: cy
    },
    corners: metrics.map((metric, dataIndex) => {
      const radiusScale = radiusAxis[metric].scale;
      const r = radiusScale.range()[1];
      const angle = angles[dataIndex];
      const [x, y] = instance.polar2svg(r, angle);
      return {
        x,
        y
      };
    }),
    radius: radiusAxis[metrics[0]].scale.range()[1]
  };
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/SharpRadarGrid.js
var React34 = __toESM(require_react(), 1);
var import_jsx_runtime28 = __toESM(require_jsx_runtime(), 1);
function SharpRadarGrid(props) {
  const {
    center,
    corners,
    divisions,
    strokeColor,
    classes
  } = props;
  const divisionRatio = Array.from({
    length: divisions
  }, (_, index) => (index + 1) / divisions);
  return (0, import_jsx_runtime28.jsxs)(React34.Fragment, {
    children: [corners.map(({
      x,
      y
    }, i) => (0, import_jsx_runtime28.jsx)("path", {
      d: `M ${center.x} ${center.y} L ${x} ${y}`,
      stroke: strokeColor,
      strokeWidth: 1,
      strokeOpacity: 0.3,
      fill: "none",
      className: classes == null ? void 0 : classes.radial
    }, i)), divisionRatio.map((ratio) => (0, import_jsx_runtime28.jsx)("path", {
      d: `M ${corners.map(({
        x,
        y
      }) => `${center.x * (1 - ratio) + ratio * x} ${center.y * (1 - ratio) + ratio * y}`).join(" L ")} Z`,
      stroke: strokeColor,
      strokeWidth: 1,
      strokeOpacity: 0.3,
      fill: "none",
      className: classes == null ? void 0 : classes.divider
    }, ratio))]
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/CircularRadarGrid.js
var React35 = __toESM(require_react(), 1);
var import_jsx_runtime29 = __toESM(require_jsx_runtime(), 1);
function CircularRadarGrid(props) {
  const {
    center,
    corners,
    divisions,
    radius,
    strokeColor,
    classes
  } = props;
  const divisionRadius = Array.from({
    length: divisions
  }, (_, index) => radius * (index + 1) / divisions);
  return (0, import_jsx_runtime29.jsxs)(React35.Fragment, {
    children: [corners.map(({
      x,
      y
    }, i) => (0, import_jsx_runtime29.jsx)("path", {
      d: `M ${center.x} ${center.y} L ${x} ${y}`,
      stroke: strokeColor,
      strokeWidth: 1,
      strokeOpacity: 0.3,
      fill: "none",
      className: classes == null ? void 0 : classes.radial
    }, i)), divisionRadius.map((r) => (0, import_jsx_runtime29.jsx)("circle", {
      cx: center.x,
      cy: center.y,
      r,
      stroke: strokeColor,
      strokeWidth: 1,
      strokeOpacity: 0.3,
      fill: "none",
      className: classes == null ? void 0 : classes.divider
    }, r))]
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/SharpRadarStripes.js
var React36 = __toESM(require_react(), 1);
var import_jsx_runtime30 = __toESM(require_jsx_runtime(), 1);
var getPath = (corners, center, outerRatio, innerRatio) => ["M", [...corners, corners[0]].map(({
  x,
  y
}) => `${center.x * (1 - outerRatio) + outerRatio * x} ${center.y * (1 - outerRatio) + outerRatio * y}`).join(" L "), "L", [...corners, corners[0]].reverse().map(({
  x,
  y
}) => `${center.x * (1 - innerRatio) + innerRatio * x} ${center.y * (1 - innerRatio) + innerRatio * y}`).join(" L "), "Z"].join(" ");
function SharpRadarStripes(props) {
  const {
    center,
    corners,
    divisions,
    stripeColor,
    classes
  } = props;
  const divisionRatio = Array.from({
    length: divisions
  }, (_, index) => (index + 1) / divisions);
  return (0, import_jsx_runtime30.jsx)(React36.Fragment, {
    children: divisionRatio.map((ratio, index) => {
      const smallerRatio = divisionRatio[index - 1] ?? 0;
      return (0, import_jsx_runtime30.jsx)("path", {
        d: getPath(corners, center, ratio, smallerRatio),
        stroke: "none",
        fill: (stripeColor == null ? void 0 : stripeColor(index)) ?? "none",
        fillOpacity: 0.1,
        className: classes == null ? void 0 : classes.stripe
      }, ratio);
    })
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/CircularRadarStripes.js
var React37 = __toESM(require_react(), 1);
var import_jsx_runtime31 = __toESM(require_jsx_runtime(), 1);
var getPath2 = (center, outerRadius, innerRadius) => [`M ${center.x - outerRadius} ${center.y}`, `A ${outerRadius} ${outerRadius} 0 1 0 ${center.x + outerRadius} ${center.y}`, `A ${outerRadius} ${outerRadius} 0 1 0 ${center.x - outerRadius} ${center.y} Z`, `M ${center.x - innerRadius} ${center.y}`, `A ${innerRadius} ${innerRadius} 0 1 0 ${center.x + innerRadius} ${center.y}`, `A ${innerRadius} ${innerRadius} 0 1 0 ${center.x - innerRadius} ${center.y} Z`].join("");
function CircularRadarStripes(props) {
  const {
    center,
    divisions,
    radius,
    stripeColor,
    classes
  } = props;
  const divisionRadius = Array.from({
    length: divisions
  }, (_, index) => radius * (index + 1) / divisions);
  return (0, import_jsx_runtime31.jsx)(React37.Fragment, {
    children: divisionRadius.map((r, index) => {
      const smallerRadius = divisionRadius[index - 1] ?? 0;
      return (0, import_jsx_runtime31.jsx)("path", {
        d: getPath2(center, r, smallerRadius),
        fillRule: "evenodd",
        fill: (stripeColor == null ? void 0 : stripeColor(index)) ?? "none",
        fillOpacity: 0.1,
        className: classes == null ? void 0 : classes.stripe
      }, r);
    })
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/radarGridClasses.js
function getRadarGridUtilityClass(slot) {
  return generateUtilityClass("MuiRadarGrid", slot);
}
var chartsGridClasses2 = generateUtilityClasses("MuiRadarGrid", ["radial", "divider", "stripe"]);
var useUtilityClasses8 = (classes) => {
  const slots = {
    radial: ["radial"],
    divider: ["divider"],
    stripe: ["stripe"]
  };
  return composeClasses(slots, getRadarGridUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/RadarChart/RadarGrid/RadarGrid.js
var import_jsx_runtime32 = __toESM(require_jsx_runtime(), 1);
function RadarGrid(props) {
  const theme = useTheme();
  const {
    divisions = 5,
    shape = "sharp",
    stripeColor = (index) => index % 2 === 1 ? (theme.vars || theme).palette.text.secondary : "none"
  } = props;
  const gridData = useRadarGridData();
  const classes = useUtilityClasses8(props.classes);
  if (gridData === null) {
    return null;
  }
  const {
    center,
    corners,
    radius
  } = gridData;
  return shape === "sharp" ? (0, import_jsx_runtime32.jsxs)(React38.Fragment, {
    children: [stripeColor && (0, import_jsx_runtime32.jsx)(SharpRadarStripes, {
      divisions,
      corners,
      center,
      radius,
      stripeColor,
      classes
    }), (0, import_jsx_runtime32.jsx)(SharpRadarGrid, {
      divisions,
      corners,
      center,
      radius,
      strokeColor: (theme.vars || theme).palette.text.primary,
      classes
    })]
  }) : (0, import_jsx_runtime32.jsxs)(React38.Fragment, {
    children: [stripeColor && (0, import_jsx_runtime32.jsx)(CircularRadarStripes, {
      divisions,
      corners,
      center,
      radius,
      stripeColor,
      classes
    }), (0, import_jsx_runtime32.jsx)(CircularRadarGrid, {
      divisions,
      corners,
      center,
      radius,
      strokeColor: (theme.vars || theme).palette.text.primary,
      classes
    })]
  });
}
true ? RadarGrid.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types25.default.object,
  /**
   * The number of divisions in the radar grid.
   * @default 5
   */
  divisions: import_prop_types25.default.number,
  /**
   * The grid shape.
   * @default 'sharp'
   */
  shape: import_prop_types25.default.oneOf(["circular", "sharp"]),
  /**
   * Get stripe fill color. Set it to `null` to remove stripes
   * @param {number} index The index of the stripe band.
   * @returns {string} The color to fill the stripe.
   * @default (index) => index % 2 === 1 ? (theme.vars || theme).palette.text.secondary : 'none'
   */
  stripeColor: import_prop_types25.default.func
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarDataProvider/RadarDataProvider.js
var React39 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarChart.plugins.js
var RADAR_PLUGINS = [useChartPolarAxis, useChartInteraction, useChartHighlight];

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/formatter.js
var formatter = (params) => {
  const {
    seriesOrder,
    series
  } = params;
  return {
    seriesOrder,
    series: defaultizeValueFormatter(series, (v) => v == null ? "" : v.toLocaleString())
  };
};
var formatter_default = formatter;

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/getColor.js
var getColor = (series) => {
  return () => series.color;
};
var getColor_default2 = getColor;

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/extremums.js
var radiusExtremumGetter = ({
  series,
  axisIndex
}) => {
  return Object.keys(series).filter((seriesId) => series[seriesId].type === "radar").reduce((acc, seriesId) => {
    const {
      data
    } = series[seriesId];
    return [Math.min(data[axisIndex], acc[0]), Math.max(data[axisIndex], acc[1])];
  }, [Infinity, -Infinity]);
};
var rotationExtremumGetter = ({
  axis
}) => {
  const min = Math.min(...axis.data ?? []);
  const max = Math.max(...axis.data ?? []);
  return [min, max];
};

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/legend.js
var legendGetter = (params) => {
  const {
    seriesOrder,
    series
  } = params;
  return seriesOrder.reduce((acc, seriesId) => {
    const formattedLabel = getLabel(series[seriesId].label, "legend");
    if (formattedLabel === void 0) {
      return acc;
    }
    acc.push({
      id: seriesId,
      seriesId,
      color: series[seriesId].color,
      label: formattedLabel,
      markType: series[seriesId].labelMarkType ?? "square"
    });
    return acc;
  }, []);
};
var legend_default = legendGetter;

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/tooltip.js
var tooltipGetter = (params) => {
  const {
    series,
    axesConfig,
    getColor: getColor2,
    identifier
  } = params;
  const rotationAxis = axesConfig.rotation;
  if (!identifier || !rotationAxis) {
    return null;
  }
  const label = getLabel(series.label, "tooltip");
  const formatter2 = (v) => {
    var _a;
    return ((_a = rotationAxis.valueFormatter) == null ? void 0 : _a.call(rotationAxis, v, {
      location: "tooltip",
      scale: rotationAxis.scale
    })) ?? (v == null ? "" : v.toLocaleString());
  };
  return {
    identifier,
    color: getColor2(),
    label,
    markType: series.labelMarkType,
    values: series.data.map((value, dataIndex) => {
      var _a;
      return {
        value,
        formattedValue: series.valueFormatter(value, {
          dataIndex
        }),
        markType: series.labelMarkType,
        label: formatter2((_a = rotationAxis == null ? void 0 : rotationAxis.data) == null ? void 0 : _a[dataIndex])
      };
    })
  };
};
var axisTooltipGetter = (series) => {
  return Object.values(series).map(() => ({
    direction: "rotation",
    axisId: void 0
  }));
};
var tooltip_default = tooltipGetter;

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/getSeriesWithDefaultValues.js
var getSeriesWithDefaultValues = (seriesData, seriesIndex, colors) => {
  return _extends({
    id: seriesData.id ?? `auto-generated-id-${seriesIndex}`,
    color: colors[seriesIndex % colors.length]
  }, seriesData);
};
var getSeriesWithDefaultValues_default = getSeriesWithDefaultValues;

// node_modules/@mui/x-charts/esm/RadarChart/seriesConfig/index.js
var radarSeriesConfig = {
  colorProcessor: getColor_default2,
  seriesProcessor: formatter_default,
  legendGetter: legend_default,
  tooltipGetter: tooltip_default,
  axisTooltipGetter,
  getSeriesWithDefaultValues: getSeriesWithDefaultValues_default,
  radiusExtremumGetter,
  rotationExtremumGetter
};

// node_modules/@mui/x-charts/esm/RadarChart/RadarDataProvider/RadarDataProvider.js
var import_jsx_runtime33 = __toESM(require_jsx_runtime(), 1);
var _excluded22 = ["series", "children", "width", "height", "colors", "skipAnimation", "margin", "radar", "highlight", "plugins"];
var RADAR_SERIES_CONFIG = {
  radar: radarSeriesConfig
};
var DEFAULT_RADAR_MARGIN = {
  top: 30,
  bottom: 30,
  left: 50,
  right: 50
};
function RadarDataProvider(props) {
  const {
    series,
    children,
    width,
    height,
    colors,
    skipAnimation,
    margin,
    radar,
    highlight,
    plugins
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded22);
  const rotationAxes = React39.useMemo(() => [{
    id: "radar-rotation-axis",
    scaleType: "point",
    data: radar.metrics.map((metric) => typeof metric === "string" ? metric : metric.name),
    startAngle: radar.startAngle,
    endAngle: radar.startAngle !== void 0 ? radar.startAngle + 360 : void 0,
    labelGap: radar.labelGap,
    valueFormatter: (name, {
      location
    }) => {
      var _a;
      return ((_a = radar.labelFormatter) == null ? void 0 : _a.call(radar, name, {
        location
      })) ?? name;
    }
  }], [radar]);
  const radiusAxis = React39.useMemo(() => radar.metrics.map((m) => {
    const {
      name,
      min = 0,
      max = radar.max
    } = typeof m === "string" ? {
      name: m
    } : m;
    return {
      id: name,
      label: name,
      scaleType: "linear",
      min,
      max
    };
  }), [radar]);
  const defaultizedSeries = React39.useMemo(() => series.map((s) => _extends({
    type: "radar",
    highlightScope: s.highlightScope ?? (highlight === "series" ? {
      highlight: "series",
      fade: "global"
    } : void 0)
  }, s)), [series, highlight]);
  const defaultizedMargin = React39.useMemo(() => defaultizeMargin(margin, DEFAULT_RADAR_MARGIN), [margin]);
  return (0, import_jsx_runtime33.jsx)(ChartDataProvider, _extends({}, other, {
    series: defaultizedSeries,
    width,
    height,
    margin: defaultizedMargin,
    colors,
    skipAnimation,
    plugins: plugins ?? RADAR_PLUGINS,
    rotationAxis: rotationAxes,
    radiusAxis,
    seriesConfig: RADAR_SERIES_CONFIG,
    children
  }));
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/RadarSeriesPlot.js
var React43 = __toESM(require_react(), 1);
var import_prop_types28 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/useRadarSeriesData.js
function useRadarSeriesData(querySeriesId) {
  const {
    instance
  } = useChartContext();
  const rotationScale = useRotationScale();
  const {
    radiusAxis
  } = useRadiusAxes();
  const radarSeries = useRadarSeries(querySeriesId === void 0 ? void 0 : [querySeriesId]);
  const {
    isFaded: isItemFaded,
    isHighlighted: isItemHighlighted
  } = useItemHighlightedGetter();
  const metrics = (rotationScale == null ? void 0 : rotationScale.domain()) ?? [];
  const angles = metrics.map((key) => rotationScale == null ? void 0 : rotationScale(key));
  return radarSeries.map((series) => {
    const seriesId = series.id;
    const isSeriesHighlighted = isItemHighlighted({
      seriesId
    });
    const isSeriesFaded = !isSeriesHighlighted && isItemFaded({
      seriesId
    });
    return _extends({}, series, {
      seriesId: series.id,
      isSeriesHighlighted,
      isSeriesFaded,
      points: series.data.map((value, dataIndex) => {
        const highlighted = isItemHighlighted({
          seriesId,
          dataIndex
        });
        const faded = !highlighted && isItemFaded({
          seriesId,
          dataIndex
        });
        const r = radiusAxis[metrics[dataIndex]].scale(value);
        const angle = angles[dataIndex];
        const [x, y] = instance.polar2svg(r, angle);
        return {
          x,
          y,
          isItemHighlighted: highlighted,
          isItemFaded: faded,
          dataIndex
        };
      })
    });
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/radarSeriesPlotClasses.js
function getRadarSeriesPlotUtilityClass(slot) {
  return generateUtilityClass("MuiRadarSeriesPlot", slot);
}
var radarSeriesPlotClasses = generateUtilityClasses("MuiRadarSeriesPlot", ["root", "area", "mark", "highlighted", "faded"]);
var useUtilityClasses9 = (classes) => {
  const slots = {
    root: ["root"],
    area: ["area"],
    mark: ["mark"],
    highlighted: ["highlighted"],
    faded: ["faded"]
  };
  return composeClasses(slots, getRadarSeriesPlotUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/RadarSeriesArea.js
var React41 = __toESM(require_react(), 1);
var import_prop_types26 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/getAreaPath.js
function getAreaPath(points) {
  return `M ${points.map((p) => `${p.x} ${p.y}`).join("L")} Z`;
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/useRadarRotationIndex.js
var React40 = __toESM(require_react(), 1);
function useRadarRotationIndex() {
  const svgRef = useSvgRef();
  const store = useStore();
  const rotationAxis = useRotationAxis();
  const center = useSelector(store, selectorChartPolarCenter);
  const rotationIndexGetter = React40.useCallback(function rotationIndexGetter2(event) {
    const element = svgRef.current;
    if (!element || !rotationAxis) {
      throw new Error(`MUI X Charts: The ${!element ? "SVG" : "rotation axis"} was not found to compute radar dataIndex.`);
    }
    const svgPoint = getSVGPoint(element, event);
    const rotation = generateSvg2rotation(center)(svgPoint.x, svgPoint.y);
    const rotationIndex = getAxisIndex(rotationAxis, rotation);
    return rotationIndex;
  }, [center, rotationAxis, svgRef]);
  return rotationIndexGetter;
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/RadarSeriesArea.js
var import_jsx_runtime34 = __toESM(require_jsx_runtime(), 1);
var _excluded23 = ["seriesId", "onItemClick"];
function getPathProps(params) {
  const {
    isHighlighted,
    isFaded,
    seriesId,
    classes,
    points,
    fillArea,
    color
  } = params;
  const isItemHighlighted = isHighlighted({
    seriesId
  });
  const isItemFaded = !isItemHighlighted && isFaded({
    seriesId
  });
  return {
    d: getAreaPath(points),
    fill: fillArea ? color : "transparent",
    stroke: color,
    className: clsx_default(classes.area, isItemHighlighted && classes.highlighted || isItemFaded && classes.faded),
    strokeOpacity: isItemFaded ? 0.5 : 1,
    fillOpacity: isItemHighlighted && 0.4 || isItemFaded && 0.1 || 0.2,
    strokeWidth: !fillArea && isItemHighlighted ? 2 : 1
  };
}
function RadarSeriesArea(props) {
  const {
    seriesId,
    onItemClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded23);
  const seriesCoordinates = useRadarSeriesData(seriesId);
  const getRotationIndex = useRadarRotationIndex();
  const interactionProps = useInteractionAllItemProps(seriesCoordinates);
  const {
    isFaded,
    isHighlighted
  } = useItemHighlightedGetter();
  const classes = useUtilityClasses9(props.classes);
  return (0, import_jsx_runtime34.jsx)(React41.Fragment, {
    children: seriesCoordinates == null ? void 0 : seriesCoordinates.map(({
      seriesId: id,
      points,
      color,
      fillArea
    }, seriesIndex) => {
      return (0, import_jsx_runtime34.jsx)("path", _extends({}, getPathProps({
        seriesId: id,
        points,
        color,
        fillArea,
        isFaded,
        isHighlighted,
        classes
      }), {
        onClick: (event) => onItemClick == null ? void 0 : onItemClick(event, {
          type: "radar",
          seriesId: id,
          dataIndex: getRotationIndex(event)
        }),
        cursor: onItemClick ? "pointer" : "unset"
      }, interactionProps[seriesIndex], other), id);
    })
  });
}
true ? RadarSeriesArea.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types26.default.object,
  /**
   * Callback fired when an area is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onItemClick: import_prop_types26.default.func,
  /**
   * The id of the series to display.
   * If undefined all series are displayed.
   */
  seriesId: import_prop_types26.default.string
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/RadarSeriesMarks.js
var React42 = __toESM(require_react(), 1);
var import_prop_types27 = __toESM(require_prop_types(), 1);
var import_jsx_runtime35 = __toESM(require_jsx_runtime(), 1);
var _excluded24 = ["seriesId", "onItemClick"];
function getCircleProps(params) {
  const {
    isHighlighted,
    isFaded,
    seriesId,
    classes,
    point,
    fillArea,
    color
  } = params;
  const isItemHighlighted = isHighlighted({
    seriesId
  });
  const isItemFaded = !isItemHighlighted && isFaded({
    seriesId
  });
  return {
    cx: point.x,
    cy: point.y,
    r: 3,
    fill: color,
    stroke: color,
    opacity: fillArea && isItemFaded ? 0.5 : 1,
    className: clsx(classes.mark, isItemHighlighted && classes.highlighted || isItemFaded && classes.faded)
  };
}
function RadarSeriesMarks(props) {
  const {
    onItemClick
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded24);
  const seriesCoordinates = useRadarSeriesData(props.seriesId);
  const classes = useUtilityClasses9(props.classes);
  const {
    isFaded,
    isHighlighted
  } = useItemHighlightedGetter();
  return (0, import_jsx_runtime35.jsx)(React42.Fragment, {
    children: seriesCoordinates == null ? void 0 : seriesCoordinates.map(({
      seriesId: id,
      points,
      color,
      hideMark,
      fillArea
    }) => {
      if (hideMark) {
        return null;
      }
      return (0, import_jsx_runtime35.jsx)("g", {
        children: points.map((point, index) => (0, import_jsx_runtime35.jsx)("circle", _extends({}, getCircleProps({
          seriesId: id,
          point,
          color,
          fillArea,
          isFaded,
          isHighlighted,
          classes
        }), {
          pointerEvents: onItemClick ? void 0 : "none",
          onClick: (event) => onItemClick == null ? void 0 : onItemClick(event, {
            type: "radar",
            seriesId: id,
            dataIndex: index
          }),
          cursor: onItemClick ? "pointer" : "unset"
        }, other), index))
      }, id);
    })
  });
}
true ? RadarSeriesMarks.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types27.default.object,
  /**
   * Callback fired when a mark is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onItemClick: import_prop_types27.default.func,
  /**
   * The id of the series to display.
   * If undefined all series are displayed.
   */
  seriesId: import_prop_types27.default.string
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarSeriesPlot/RadarSeriesPlot.js
var import_jsx_runtime36 = __toESM(require_jsx_runtime(), 1);
function RadarSeriesPlot(props) {
  const {
    seriesId: inSeriesId,
    classes: inClasses,
    onAreaClick,
    onMarkClick
  } = props;
  const seriesCoordinates = useRadarSeriesData(inSeriesId);
  const getRotationIndex = useRadarRotationIndex();
  const interactionProps = useInteractionAllItemProps(seriesCoordinates);
  const {
    isFaded,
    isHighlighted
  } = useItemHighlightedGetter();
  const classes = useUtilityClasses9(inClasses);
  return (0, import_jsx_runtime36.jsx)("g", {
    className: classes.root,
    children: seriesCoordinates == null ? void 0 : seriesCoordinates.map(({
      seriesId,
      points,
      color,
      hideMark,
      fillArea
    }, seriesIndex) => {
      return (0, import_jsx_runtime36.jsxs)("g", {
        children: [(0, import_jsx_runtime36.jsx)("path", _extends({}, getPathProps({
          seriesId,
          points,
          color,
          fillArea,
          isFaded,
          isHighlighted,
          classes
        }), {
          onClick: (event) => onAreaClick == null ? void 0 : onAreaClick(event, {
            type: "radar",
            seriesId,
            dataIndex: getRotationIndex(event)
          }),
          cursor: onAreaClick ? "pointer" : "unset"
        }, interactionProps[seriesIndex]), seriesId), !hideMark && points.map((point, index) => (0, import_jsx_runtime36.jsx)("circle", _extends({}, getCircleProps({
          seriesId,
          point,
          color,
          fillArea,
          isFaded,
          isHighlighted,
          classes
        }), {
          onClick: (event) => onMarkClick == null ? void 0 : onMarkClick(event, {
            type: "radar",
            seriesId,
            dataIndex: index
          }),
          cursor: onMarkClick ? "pointer" : "unset"
        }), index))]
      }, seriesId);
    })
  });
}
true ? RadarSeriesPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types28.default.object,
  /**
   * Callback fired when an area is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onAreaClick: import_prop_types28.default.func,
  /**
   * Callback fired when a mark is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onMarkClick: import_prop_types28.default.func,
  /**
   * The id of the series to display.
   * If undefined all series are displayed.
   */
  seriesId: import_prop_types28.default.string
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarAxisHighlight/RadarAxisHighlight.js
var React44 = __toESM(require_react(), 1);
var import_prop_types29 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarAxisHighlight/useRadarAxisHighlight.js
function useRadarAxisHighlight() {
  const radarSeries = useRadarSeries();
  const rotationScale = useRotationScale();
  const {
    radiusAxis,
    radiusAxisIds
  } = useRadiusAxes();
  const {
    instance
  } = useChartContext();
  const store = useStore();
  const rotationAxisIndex = useSelector(store, selectorChartsInteractionRotationAxisIndex);
  const rotationAxisValue = useSelector(store, selectorChartsInteractionRotationAxisValue);
  const center = useSelector(store, selectorChartPolarCenter);
  const highlightedIndex = rotationAxisIndex;
  if (!rotationScale) {
    return null;
  }
  if (highlightedIndex === null || highlightedIndex === -1) {
    return null;
  }
  if (radarSeries === void 0 || radarSeries.length === 0) {
    return null;
  }
  const metric = radiusAxisIds[highlightedIndex];
  const radiusScale = radiusAxis[metric].scale;
  const angle = rotationScale(rotationAxisValue);
  const radius = radiusScale.range()[1];
  return {
    center,
    radius,
    instance,
    highlightedIndex,
    highlightedMetric: metric,
    highlightedAngle: angle,
    series: radarSeries,
    points: radarSeries.map((series) => {
      const value = series.data[highlightedIndex];
      const r = radiusScale(value);
      const [x, y] = instance.polar2svg(r, angle);
      const returnedValue = {
        x,
        y,
        r,
        angle,
        value
      };
      return returnedValue;
    })
  };
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarAxisHighlight/radarAxisHighlightClasses.js
function getRadarAxisHighlightUtilityClass(slot) {
  return generateUtilityClass("MuiRadarAxisHighlight", slot);
}
var chartsAxisHighlightClasses2 = generateUtilityClasses("MuiRadarAxisHighlight", ["root", "line", "dot"]);

// node_modules/@mui/x-charts/esm/RadarChart/RadarAxisHighlight/RadarAxisHighlight.js
var import_jsx_runtime37 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses10 = (classes) => {
  const slots = {
    root: ["root"],
    line: ["line"],
    dot: ["dot"]
  };
  return composeClasses(slots, getRadarAxisHighlightUtilityClass, classes);
};
var highlightMarkShadow = {
  r: 7,
  opacity: 0.3
};
var highlightMark = {
  r: 3,
  opacity: 1
};
function RadarAxisHighlight(props) {
  const classes = useUtilityClasses10(props.classes);
  const theme = useTheme();
  const data = useRadarAxisHighlight();
  if (data === null) {
    return null;
  }
  const {
    center,
    series,
    points,
    radius,
    highlightedAngle,
    instance
  } = data;
  const [x, y] = instance.polar2svg(radius, highlightedAngle);
  return (0, import_jsx_runtime37.jsxs)("g", {
    className: classes.root,
    children: [(0, import_jsx_runtime37.jsx)("path", {
      d: `M ${center.cx} ${center.cy} L ${x} ${y}`,
      stroke: (theme.vars || theme).palette.text.primary,
      strokeWidth: 1,
      className: classes.line,
      pointerEvents: "none",
      strokeDasharray: "4 4"
    }), points.map((point, seriesIndex) => {
      return (0, import_jsx_runtime37.jsx)("circle", _extends({
        fill: series[seriesIndex].color,
        cx: point.x,
        cy: point.y,
        className: classes.dot,
        pointerEvents: "none"
      }, series[seriesIndex].hideMark ? highlightMark : highlightMarkShadow), series[seriesIndex].id);
    })]
  });
}
true ? RadarAxisHighlight.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types29.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/RadarChart/RadarMetricLabels/RadarMetricLabels.js
var React45 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/RadarChart/RadarMetricLabels/useRadarMetricData.js
function useRadarMetricData() {
  const rotationAxis = useRotationAxis();
  const {
    scale: rotationScale,
    valueFormatter,
    labelGap = 10
  } = rotationAxis;
  const {
    radiusAxis
  } = useRadiusAxes();
  const drawingArea = useDrawingArea();
  const cx = drawingArea.left + drawingArea.width / 2;
  const cy = drawingArea.top + drawingArea.height / 2;
  const metrics = rotationScale.domain();
  const angles = metrics.map((key) => rotationScale(key));
  return {
    corners: metrics.map((metric, dataIndex) => {
      const radiusScale = radiusAxis[metric].scale;
      const r = radiusScale.range()[1] + labelGap;
      const angle = angles[dataIndex];
      const defaultTickLabel = metric;
      return {
        x: cx + r * Math.sin(angle),
        y: cy - r * Math.cos(angle),
        angle: rad2deg(angle),
        label: (valueFormatter == null ? void 0 : valueFormatter(metric, {
          location: "tick",
          scale: rotationScale,
          defaultTickLabel
        })) ?? defaultTickLabel
      };
    })
  };
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarMetricLabels/RadarMetricLabels.js
var import_jsx_runtime38 = __toESM(require_jsx_runtime(), 1);
function RadarMetricLabels() {
  const {
    corners
  } = useRadarMetricData();
  const theme = useTheme();
  return (0, import_jsx_runtime38.jsx)(React45.Fragment, {
    children: corners.map(({
      x,
      y,
      angle,
      label
    }, i) => (0, import_jsx_runtime38.jsx)(ChartsText, {
      x,
      y,
      fontSize: 14,
      fill: (theme.vars || theme).palette.text.primary,
      stroke: "none",
      text: label,
      style: _extends({}, theme.typography.caption, {
        fontSize: 12,
        lineHeight: 1.25,
        textAnchor: getDefaultTextAnchor(180 + angle),
        dominantBaseline: getDefaultBaseline(180 + angle)
      })
    }, i))
  });
}

// node_modules/@mui/x-charts/esm/RadarChart/RadarChart.js
var import_jsx_runtime39 = __toESM(require_jsx_runtime(), 1);
var RadarChart = React46.forwardRef(function RadarChart2(inProps, ref) {
  var _a, _b, _c, _d;
  const props = useThemeProps({
    props: inProps,
    name: "MuiRadarChart"
  });
  const {
    chartsWrapperProps,
    chartsSurfaceProps,
    radarDataProviderProps,
    radarGrid,
    radarSeriesAreaProps,
    radarSeriesMarksProps,
    overlayProps,
    legendProps,
    highlight,
    children
  } = useRadarChartProps(props);
  const Tooltip = ((_a = props.slots) == null ? void 0 : _a.tooltip) ?? ChartsTooltip;
  const Toolbar3 = (_b = props.slots) == null ? void 0 : _b.toolbar;
  return (0, import_jsx_runtime39.jsx)(RadarDataProvider, _extends({}, radarDataProviderProps, {
    children: (0, import_jsx_runtime39.jsxs)(ChartsWrapper, _extends({}, chartsWrapperProps, {
      children: [props.showToolbar && Toolbar3 ? (0, import_jsx_runtime39.jsx)(Toolbar3, _extends({}, (_c = props.slotProps) == null ? void 0 : _c.toolbar)) : null, !props.hideLegend && (0, import_jsx_runtime39.jsx)(ChartsLegend, _extends({}, legendProps)), (0, import_jsx_runtime39.jsxs)(ChartsSurface, _extends({}, chartsSurfaceProps, {
        ref,
        children: [(0, import_jsx_runtime39.jsx)(RadarGrid, _extends({}, radarGrid)), (0, import_jsx_runtime39.jsx)(RadarMetricLabels, {}), (0, import_jsx_runtime39.jsx)(RadarSeriesArea, _extends({}, radarSeriesAreaProps)), highlight === "axis" && (0, import_jsx_runtime39.jsx)(RadarAxisHighlight, {}), (0, import_jsx_runtime39.jsx)(RadarSeriesMarks, _extends({}, radarSeriesMarksProps)), (0, import_jsx_runtime39.jsx)(ChartsOverlay, _extends({}, overlayProps)), children]
      })), !props.loading && (0, import_jsx_runtime39.jsx)(Tooltip, _extends({}, (_d = props.slotProps) == null ? void 0 : _d.tooltip))]
    }))
  }));
});
if (true) RadarChart.displayName = "RadarChart";
true ? RadarChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types30.default.shape({
    current: import_prop_types30.default.object
  }),
  className: import_prop_types30.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types30.default.oneOfType([import_prop_types30.default.arrayOf(import_prop_types30.default.string), import_prop_types30.default.func]),
  desc: import_prop_types30.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types30.default.bool,
  /**
   * The number of divisions in the radar grid.
   * @default 5
   */
  divisions: import_prop_types30.default.number,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types30.default.number,
  /**
   * If `true`, the legend is not rendered.
   */
  hideLegend: import_prop_types30.default.bool,
  /**
   * Indicates if the chart should highlight items per axis or per series.
   * @default 'axis'
   */
  highlight: import_prop_types30.default.oneOf(["axis", "none", "series"]),
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types30.default.shape({
    dataIndex: import_prop_types30.default.number,
    seriesId: import_prop_types30.default.oneOfType([import_prop_types30.default.number, import_prop_types30.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types30.default.string,
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types30.default.bool,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types30.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types30.default.oneOfType([import_prop_types30.default.number, import_prop_types30.default.shape({
    bottom: import_prop_types30.default.number,
    left: import_prop_types30.default.number,
    right: import_prop_types30.default.number,
    top: import_prop_types30.default.number
  })]),
  /**
   * Callback fired when an area is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onAreaClick: import_prop_types30.default.func,
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types30.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types30.default.func,
  /**
   * Callback fired when a mark is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {RadarItemIdentifier} radarItemIdentifier The radar item identifier.
   */
  onMarkClick: import_prop_types30.default.func,
  /**
   * The configuration of the radar scales.
   */
  radar: import_prop_types30.default.shape({
    labelFormatter: import_prop_types30.default.func,
    labelGap: import_prop_types30.default.number,
    max: import_prop_types30.default.number,
    metrics: import_prop_types30.default.oneOfType([import_prop_types30.default.arrayOf(import_prop_types30.default.string), import_prop_types30.default.arrayOf(import_prop_types30.default.shape({
      max: import_prop_types30.default.number,
      min: import_prop_types30.default.number,
      name: import_prop_types30.default.string.isRequired
    }))]).isRequired,
    startAngle: import_prop_types30.default.number
  }).isRequired,
  /**
   * The series to display in the bar chart.
   * An array of [[RadarSeries]] objects.
   */
  series: import_prop_types30.default.arrayOf(import_prop_types30.default.object).isRequired,
  /**
   * The grid shape.
   * @default 'sharp'
   */
  shape: import_prop_types30.default.oneOf(["circular", "sharp"]),
  /**
   * If true, shows the default chart toolbar.
   * @default false
   */
  showToolbar: import_prop_types30.default.bool,
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types30.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types30.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types30.default.object,
  /**
   * Get stripe fill color. Set it to `null` to remove stripes
   * @param {number} index The index of the stripe band.
   * @returns {string} The color to fill the stripe.
   * @default (index) => index % 2 === 1 ? (theme.vars || theme).palette.text.secondary : 'none'
   */
  stripeColor: import_prop_types30.default.func,
  sx: import_prop_types30.default.oneOfType([import_prop_types30.default.arrayOf(import_prop_types30.default.oneOfType([import_prop_types30.default.func, import_prop_types30.default.object, import_prop_types30.default.bool])), import_prop_types30.default.func, import_prop_types30.default.object]),
  theme: import_prop_types30.default.oneOf(["dark", "light"]),
  title: import_prop_types30.default.string,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types30.default.number
} : void 0;

// node_modules/@mui/x-charts/esm/ChartContainer/ChartContainer.js
var React47 = __toESM(require_react(), 1);
var import_prop_types31 = __toESM(require_prop_types(), 1);
var import_jsx_runtime40 = __toESM(require_jsx_runtime(), 1);
var ChartContainer = React47.forwardRef(function ChartContainer2(props, ref) {
  const {
    chartDataProviderProps,
    children,
    chartsSurfaceProps
  } = useChartContainerProps(props, ref);
  return (0, import_jsx_runtime40.jsx)(ChartDataProvider, _extends({}, chartDataProviderProps, {
    children: (0, import_jsx_runtime40.jsx)(ChartsSurface, _extends({}, chartsSurfaceProps, {
      children
    }))
  }));
});
if (true) ChartContainer.displayName = "ChartContainer";
true ? ChartContainer.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types31.default.shape({
    current: import_prop_types31.default.object
  }),
  children: import_prop_types31.default.node,
  className: import_prop_types31.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string), import_prop_types31.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types31.default.arrayOf(import_prop_types31.default.object),
  desc: import_prop_types31.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types31.default.bool,
  /**
   * If true, the voronoi interaction are ignored.
   */
  disableVoronoi: import_prop_types31.default.bool,
  /**
   * Options to enable features planned for the next major.
   */
  experimentalFeatures: import_prop_types31.default.shape({
    preferStrictDomainInLineCharts: import_prop_types31.default.bool
  }),
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types31.default.number,
  /**
   * The controlled axis highlight.
   * Identified by the axis id, and data index.
   */
  highlightedAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.shape({
    axisId: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    dataIndex: import_prop_types31.default.number.isRequired
  })),
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types31.default.shape({
    dataIndex: import_prop_types31.default.number,
    seriesId: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types31.default.string,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types31.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.shape({
    bottom: import_prop_types31.default.number,
    left: import_prop_types31.default.number,
    right: import_prop_types31.default.number,
    top: import_prop_types31.default.number
  })]),
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types31.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types31.default.func,
  /**
   * The function called when the pointer position corresponds to a new axis data item.
   * This update can either be caused by a pointer movement, or an axis update.
   * In case of multiple axes, the function is called if at least one axis is updated.
   * The argument contains the identifier for all axes with a `data` property.
   * @param {AxisItemIdentifier[]} axisItems The array of axes item identifiers.
   */
  onHighlightedAxisChange: import_prop_types31.default.func,
  /**
   * Callback fired when clicking close to an item.
   * This is only available for scatter plot for now.
   * @param {MouseEvent} event Mouse event caught at the svg level
   * @param {ScatterItemIdentifier} scatterItemIdentifier Identify which item got clicked
   */
  onItemClick: import_prop_types31.default.func,
  /**
   * The configuration of the radial-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  radiusAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    maxRadius: import_prop_types31.default.number,
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    minRadius: import_prop_types31.default.number,
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["linear"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  })),
  /**
   * The configuration of the rotation-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  rotationAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
    barGapRatio: import_prop_types31.default.number,
    categoryGapRatio: import_prop_types31.default.number,
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["band"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["point"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["log"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["pow"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["sqrt"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["time"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["utc"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    endAngle: import_prop_types31.default.number,
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]).isRequired,
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelGap: import_prop_types31.default.number,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["linear"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    startAngle: import_prop_types31.default.number,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  })]).isRequired),
  /**
   * The array of series to display.
   * Each type of series has its own specificity.
   * Please refer to the appropriate docs page to learn more about it.
   */
  series: import_prop_types31.default.arrayOf(import_prop_types31.default.object),
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types31.default.bool,
  /**
   * The props for the slots.
   */
  slotProps: import_prop_types31.default.object,
  /**
   * Slots to customize charts' components.
   */
  slots: import_prop_types31.default.object,
  sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
  theme: import_prop_types31.default.oneOf(["dark", "light"]),
  title: import_prop_types31.default.string,
  /**
   * Defines the maximal distance between a scatter point and the pointer that triggers the interaction.
   * If `undefined`, the radius is assumed to be infinite.
   */
  voronoiMaxRadius: import_prop_types31.default.number,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types31.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  xAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    barGapRatio: import_prop_types31.default.number,
    categoryGapRatio: import_prop_types31.default.number,
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["band"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["point"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["log"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["pow"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["sqrt"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["time"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["utc"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["x"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    height: import_prop_types31.default.number,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["linear"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelMinGap: import_prop_types31.default.number,
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func
  })]).isRequired),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  yAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    barGapRatio: import_prop_types31.default.number,
    categoryGapRatio: import_prop_types31.default.number,
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["band"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["point"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["log"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["pow"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["sqrt"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["time"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["utc"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  }), import_prop_types31.default.shape({
    axis: import_prop_types31.default.oneOf(["y"]),
    classes: import_prop_types31.default.object,
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    disableLine: import_prop_types31.default.bool,
    disableTicks: import_prop_types31.default.bool,
    domainLimit: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["nice", "strict"]), import_prop_types31.default.func]),
    fill: import_prop_types31.default.string,
    hideTooltip: import_prop_types31.default.bool,
    id: import_prop_types31.default.oneOfType([import_prop_types31.default.number, import_prop_types31.default.string]),
    ignoreTooltip: import_prop_types31.default.bool,
    label: import_prop_types31.default.string,
    labelStyle: import_prop_types31.default.object,
    max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
    offset: import_prop_types31.default.number,
    position: import_prop_types31.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types31.default.bool,
    scaleType: import_prop_types31.default.oneOf(["linear"]),
    slotProps: import_prop_types31.default.object,
    slots: import_prop_types31.default.object,
    stroke: import_prop_types31.default.string,
    sx: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.func, import_prop_types31.default.object, import_prop_types31.default.bool])), import_prop_types31.default.func, import_prop_types31.default.object]),
    tickInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.array, import_prop_types31.default.func]),
    tickLabelInterval: import_prop_types31.default.oneOfType([import_prop_types31.default.oneOf(["auto"]), import_prop_types31.default.func]),
    tickLabelPlacement: import_prop_types31.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types31.default.object,
    tickMaxStep: import_prop_types31.default.number,
    tickMinStep: import_prop_types31.default.number,
    tickNumber: import_prop_types31.default.number,
    tickPlacement: import_prop_types31.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types31.default.number,
    valueFormatter: import_prop_types31.default.func,
    width: import_prop_types31.default.number
  })]).isRequired),
  /**
   * The configuration of the z-axes.
   */
  zAxis: import_prop_types31.default.arrayOf(import_prop_types31.default.shape({
    colorMap: import_prop_types31.default.oneOfType([import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      type: import_prop_types31.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types31.default.string,
      values: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number, import_prop_types31.default.string]).isRequired)
    }), import_prop_types31.default.shape({
      color: import_prop_types31.default.oneOfType([import_prop_types31.default.arrayOf(import_prop_types31.default.string.isRequired), import_prop_types31.default.func]).isRequired,
      max: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      min: import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]),
      type: import_prop_types31.default.oneOf(["continuous"]).isRequired
    }), import_prop_types31.default.shape({
      colors: import_prop_types31.default.arrayOf(import_prop_types31.default.string).isRequired,
      thresholds: import_prop_types31.default.arrayOf(import_prop_types31.default.oneOfType([import_prop_types31.default.instanceOf(Date), import_prop_types31.default.number]).isRequired).isRequired,
      type: import_prop_types31.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types31.default.array,
    dataKey: import_prop_types31.default.string,
    id: import_prop_types31.default.string,
    max: import_prop_types31.default.number,
    min: import_prop_types31.default.number
  }))
} : void 0;

// node_modules/@mui/x-charts/esm/Toolbar/Toolbar.js
var React51 = __toESM(require_react(), 1);
var import_prop_types32 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-internals/esm/ToolbarContext/ToolbarContext.js
var React48 = __toESM(require_react(), 1);
var import_jsx_runtime41 = __toESM(require_jsx_runtime(), 1);
var ToolbarContext = React48.createContext(void 0);
if (true) ToolbarContext.displayName = "ToolbarContext";
function useToolbarContext() {
  const context = React48.useContext(ToolbarContext);
  if (context === void 0) {
    throw new Error("MUI X: Missing context. Toolbar subcomponents must be placed within a <Toolbar /> component.");
  }
  return context;
}
function ToolbarContextProvider({
  children
}) {
  const [focusableItemId, setFocusableItemId] = React48.useState(null);
  const focusableItemIdRef = React48.useRef(focusableItemId);
  const [items, setItems] = React48.useState([]);
  const getSortedItems = React48.useCallback(() => items.sort(sortByDocumentPosition), [items]);
  const findEnabledItem = React48.useCallback((startIndex, step, wrap = true) => {
    var _a, _b;
    let index = startIndex;
    const sortedItems = getSortedItems();
    const itemCount = sortedItems.length;
    for (let i = 0; i < itemCount; i += 1) {
      index += step;
      if (index >= itemCount) {
        if (!wrap) {
          return -1;
        }
        index = 0;
      } else if (index < 0) {
        if (!wrap) {
          return -1;
        }
        index = itemCount - 1;
      }
      if (!((_a = sortedItems[index].ref.current) == null ? void 0 : _a.disabled) && ((_b = sortedItems[index].ref.current) == null ? void 0 : _b.ariaDisabled) !== "true") {
        return index;
      }
    }
    return -1;
  }, [getSortedItems]);
  const registerItem = React48.useCallback((id, itemRef) => {
    setItems((prevItems) => [...prevItems, {
      id,
      ref: itemRef
    }]);
  }, []);
  const unregisterItem = React48.useCallback((id) => {
    setItems((prevItems) => prevItems.filter((i) => i.id !== id));
  }, []);
  const onItemKeyDown = React48.useCallback((event) => {
    var _a;
    if (!focusableItemId) {
      return;
    }
    const sortedItems = getSortedItems();
    const focusableItemIndex = sortedItems.findIndex((item) => item.id === focusableItemId);
    let newIndex = -1;
    if (event.key === "ArrowRight") {
      event.preventDefault();
      newIndex = findEnabledItem(focusableItemIndex, 1);
    } else if (event.key === "ArrowLeft") {
      event.preventDefault();
      newIndex = findEnabledItem(focusableItemIndex, -1);
    } else if (event.key === "Home") {
      event.preventDefault();
      newIndex = findEnabledItem(-1, 1, false);
    } else if (event.key === "End") {
      event.preventDefault();
      newIndex = findEnabledItem(sortedItems.length, -1, false);
    }
    if (newIndex >= 0 && newIndex < sortedItems.length) {
      const item = sortedItems[newIndex];
      setFocusableItemId(item.id);
      (_a = item.ref.current) == null ? void 0 : _a.focus();
    }
  }, [getSortedItems, focusableItemId, findEnabledItem]);
  const onItemFocus = React48.useCallback((id) => {
    if (focusableItemId !== id) {
      setFocusableItemId(id);
    }
  }, [focusableItemId, setFocusableItemId]);
  const onItemDisabled = React48.useCallback((id) => {
    var _a;
    const sortedItems = getSortedItems();
    const currentIndex = sortedItems.findIndex((item) => item.id === id);
    const newIndex = findEnabledItem(currentIndex, 1);
    if (newIndex >= 0 && newIndex < sortedItems.length) {
      const item = sortedItems[newIndex];
      setFocusableItemId(item.id);
      (_a = item.ref.current) == null ? void 0 : _a.focus();
    }
  }, [getSortedItems, findEnabledItem]);
  React48.useEffect(() => {
    focusableItemIdRef.current = focusableItemId;
  }, [focusableItemId]);
  React48.useEffect(() => {
    var _a, _b;
    const sortedItems = getSortedItems();
    if (sortedItems.length > 0) {
      if (!focusableItemIdRef.current) {
        setFocusableItemId(sortedItems[0].id);
        return;
      }
      const focusableItemIndex = sortedItems.findIndex((item) => item.id === focusableItemIdRef.current);
      if (!sortedItems[focusableItemIndex]) {
        const item = sortedItems[sortedItems.length - 1];
        if (item) {
          setFocusableItemId(item.id);
          (_a = item.ref.current) == null ? void 0 : _a.focus();
        }
      } else if (focusableItemIndex === -1) {
        const item = sortedItems[focusableItemIndex];
        if (item) {
          setFocusableItemId(item.id);
          (_b = item.ref.current) == null ? void 0 : _b.focus();
        }
      }
    }
  }, [getSortedItems, findEnabledItem]);
  const contextValue = React48.useMemo(() => ({
    focusableItemId,
    registerItem,
    unregisterItem,
    onItemKeyDown,
    onItemFocus,
    onItemDisabled
  }), [focusableItemId, registerItem, unregisterItem, onItemKeyDown, onItemFocus, onItemDisabled]);
  return (0, import_jsx_runtime41.jsx)(ToolbarContext.Provider, {
    value: contextValue,
    children
  });
}
function sortByDocumentPosition(a, b) {
  if (!a.ref.current || !b.ref.current) {
    return 0;
  }
  const position = a.ref.current.compareDocumentPosition(b.ref.current);
  if (!position) {
    return 0;
  }
  if (position & Node.DOCUMENT_POSITION_FOLLOWING || position & Node.DOCUMENT_POSITION_CONTAINED_BY) {
    return -1;
  }
  if (position & Node.DOCUMENT_POSITION_PRECEDING || position & Node.DOCUMENT_POSITION_CONTAINS) {
    return 1;
  }
  return 0;
}

// node_modules/@mui/x-internals/esm/ToolbarContext/useRegisterToolbarButton.js
var React50 = __toESM(require_react(), 1);

// node_modules/@mui/x-internals/node_modules/@mui/utils/esm/useId/useId.js
var React49 = __toESM(require_react(), 1);
var globalId = 0;
function useGlobalId(idOverride) {
  const [defaultId, setDefaultId] = React49.useState(idOverride);
  const id = idOverride || defaultId;
  React49.useEffect(() => {
    if (defaultId == null) {
      globalId += 1;
      setDefaultId(`mui-${globalId}`);
    }
  }, [defaultId]);
  return id;
}
var safeReact = {
  ...React49
};
var maybeReactUseId = safeReact.useId;
function useId2(idOverride) {
  if (maybeReactUseId !== void 0) {
    const reactId = maybeReactUseId();
    return idOverride ?? reactId;
  }
  return useGlobalId(idOverride);
}

// node_modules/@mui/x-internals/esm/ToolbarContext/useRegisterToolbarButton.js
function useRegisterToolbarButton(props, ref) {
  const {
    onKeyDown,
    onFocus,
    disabled,
    "aria-disabled": ariaDisabled
  } = props;
  const id = useId2();
  const {
    focusableItemId,
    registerItem,
    unregisterItem,
    onItemKeyDown,
    onItemFocus,
    onItemDisabled
  } = useToolbarContext();
  const handleKeyDown = (event) => {
    onItemKeyDown(event);
    onKeyDown == null ? void 0 : onKeyDown(event);
  };
  const handleFocus = (event) => {
    onItemFocus(id);
    onFocus == null ? void 0 : onFocus(event);
  };
  React50.useEffect(() => {
    registerItem(id, ref);
    return () => unregisterItem(id);
  }, [id, ref, registerItem, unregisterItem]);
  const previousDisabled = React50.useRef(disabled);
  React50.useEffect(() => {
    if (previousDisabled.current !== disabled && disabled === true) {
      onItemDisabled(id, disabled);
    }
    previousDisabled.current = disabled;
  }, [disabled, id, onItemDisabled]);
  const previousAriaDisabled = React50.useRef(ariaDisabled);
  React50.useEffect(() => {
    if (previousAriaDisabled.current !== ariaDisabled && ariaDisabled === true) {
      onItemDisabled(id, true);
    }
    previousAriaDisabled.current = ariaDisabled;
  }, [ariaDisabled, id, onItemDisabled]);
  return {
    tabIndex: focusableItemId === id ? 0 : -1,
    disabled,
    "aria-disabled": ariaDisabled,
    onKeyDown: handleKeyDown,
    onFocus: handleFocus
  };
}

// node_modules/@mui/x-charts/esm/Toolbar/chartToolbarClasses.js
var chartsToolbarClasses = generateUtilityClasses("MuiChartsToolbar", ["root"]);

// node_modules/@mui/x-charts/esm/Toolbar/Toolbar.js
var import_jsx_runtime42 = __toESM(require_jsx_runtime(), 1);
var _excluded25 = ["className", "render"];
var ToolbarRoot = styled_default("div", {
  name: "MuiChartsToolbar",
  slot: "Root"
})(({
  theme
}) => ({
  flex: 0,
  display: "flex",
  alignItems: "center",
  justifyContent: "end",
  gap: theme.spacing(0.25),
  padding: theme.spacing(0.5),
  marginBottom: theme.spacing(1.5),
  minHeight: 44,
  boxSizing: "border-box",
  border: `1px solid ${(theme.vars || theme).palette.divider}`,
  borderRadius: 4
}));
var Toolbar = React51.forwardRef(function Toolbar2(_ref, ref) {
  let {
    className,
    render
  } = _ref, other = _objectWithoutPropertiesLoose(_ref, _excluded25);
  const element = useComponentRenderer(ToolbarRoot, render, _extends({
    role: "toolbar",
    "aria-orientation": "horizontal",
    className: clsx_default(chartsToolbarClasses.root, className)
  }, other, {
    ref
  }));
  return (0, import_jsx_runtime42.jsx)(ToolbarContextProvider, {
    children: element
  });
});
if (true) Toolbar.displayName = "Toolbar";
true ? Toolbar.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  className: import_prop_types32.default.string,
  /**
   * A function to customize rendering of the component.
   */
  render: import_prop_types32.default.oneOfType([import_prop_types32.default.element, import_prop_types32.default.func])
} : void 0;

// node_modules/@mui/x-charts/esm/Toolbar/ToolbarButton.js
var import_prop_types33 = __toESM(require_prop_types(), 1);
var React52 = __toESM(require_react(), 1);
var import_jsx_runtime43 = __toESM(require_jsx_runtime(), 1);
var _excluded26 = ["render", "onKeyDown", "onFocus", "disabled", "aria-disabled"];
var _excluded27 = ["tabIndex"];
var ToolbarButton = React52.forwardRef(function ToolbarButton2(props, ref) {
  const {
    render
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded26);
  const {
    slots,
    slotProps
  } = useChartsSlots();
  const buttonRef = React52.useRef(null);
  const handleRef = useForkRef(buttonRef, ref);
  const _useRegisterToolbarBu = useRegisterToolbarButton(props, buttonRef), {
    tabIndex
  } = _useRegisterToolbarBu, toolbarButtonProps = _objectWithoutPropertiesLoose(_useRegisterToolbarBu, _excluded27);
  const element = useComponentRenderer(slots.baseIconButton, render, _extends({}, slotProps == null ? void 0 : slotProps.baseIconButton, {
    tabIndex
  }, other, toolbarButtonProps, {
    ref: handleRef
  }));
  return (0, import_jsx_runtime43.jsx)(React52.Fragment, {
    children: element
  });
});
if (true) ToolbarButton.displayName = "ToolbarButton";
true ? ToolbarButton.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  className: import_prop_types33.default.string,
  disabled: import_prop_types33.default.bool,
  id: import_prop_types33.default.string,
  /**
   * A function to customize the rendering of the component.
   */
  render: import_prop_types33.default.oneOfType([import_prop_types33.default.element, import_prop_types33.default.func]),
  size: import_prop_types33.default.oneOf(["large", "medium", "small"]),
  style: import_prop_types33.default.object,
  tabIndex: import_prop_types33.default.number
} : void 0;
export {
  AXIS_LABEL_DEFAULT_HEIGHT,
  AnimatedArea,
  AnimatedLine,
  AreaElement,
  AreaPlot,
  BarChart,
  BarElement,
  BarLabel,
  BarPlot,
  ChartContainer,
  ChartDataProvider,
  ChartsAxis,
  ChartsAxisHighlight,
  ChartsAxisHighlightPath,
  ChartsAxisTooltipContent,
  ChartsClipPath,
  ChartsGrid,
  ChartsItemTooltipContent,
  ChartsLabel,
  ChartsLabelMark,
  ChartsLegend,
  ChartsLocalizationProvider,
  ChartsReferenceLine,
  ChartsSurface,
  ChartsText,
  ChartsTooltip,
  ChartsTooltipCell,
  ChartsTooltipContainer,
  ChartsTooltipPaper,
  ChartsTooltipRow,
  ChartsTooltipTable,
  ChartsXAxis,
  ChartsYAxis,
  ContinuousColorLegend,
  DEFAULT_AXIS_SIZE_HEIGHT,
  DEFAULT_AXIS_SIZE_WIDTH,
  DEFAULT_MARGINS,
  DEFAULT_RADIUS_AXIS_KEY,
  DEFAULT_ROTATION_AXIS_KEY,
  DEFAULT_X_AXIS_KEY,
  DEFAULT_Y_AXIS_KEY,
  Gauge,
  GaugeContainer,
  GaugeReferenceArc,
  GaugeValueArc,
  GaugeValueText,
  LineChart,
  LineElement,
  LineHighlightElement,
  LineHighlightPlot,
  LinePlot,
  MarkElement,
  MarkPlot,
  PieArc,
  PieArcLabel,
  PieArcLabelPlot,
  PieArcPlot,
  PieChart,
  PiePlot,
  PiecewiseColorLegend,
  RadarAxisHighlight,
  RadarChart,
  RadarDataProvider,
  RadarGrid,
  RadarMetricLabels,
  RadarSeriesArea,
  RadarSeriesMarks,
  RadarSeriesPlot,
  Scatter,
  ScatterChart,
  ScatterMarker,
  ScatterPlot,
  SparkLineChart,
  Toolbar,
  ToolbarButton,
  RadarChart as Unstable_RadarChart,
  RadarDataProvider as Unstable_RadarDataProvider,
  areaElementClasses,
  axisClasses,
  barClasses,
  barElementClasses,
  barLabelClasses,
  bluePalette,
  bluePaletteDark,
  bluePaletteLight,
  blueberryTwilightPalette,
  blueberryTwilightPaletteDark,
  blueberryTwilightPaletteLight,
  chartsAxisHighlightClasses,
  chartsGridClasses,
  chartsToolbarClasses,
  chartsTooltipClasses,
  cheerfulFiestaPalette,
  cheerfulFiestaPaletteDark,
  cheerfulFiestaPaletteLight,
  continuousColorLegendClasses,
  cyanPalette,
  cyanPaletteDark,
  cyanPaletteLight,
  gaugeClasses,
  getAreaElementUtilityClass,
  getAxisHighlightUtilityClass,
  getAxisUtilityClass,
  getBarElementUtilityClass,
  getBarLabelUtilityClass,
  getBarUtilityClass,
  getChartsGridUtilityClass,
  getChartsTooltipUtilityClass,
  getGaugeUtilityClass,
  getHighlightElementUtilityClass,
  getLineElementUtilityClass,
  getMarkElementUtilityClass,
  getPieArcLabelUtilityClass,
  getPieArcUtilityClass,
  getPieCoordinates,
  getReferenceLineUtilityClass,
  getValueToPositionMapper,
  greenPalette,
  greenPaletteDark,
  greenPaletteLight,
  isBarSeries,
  isDefaultizedBarSeries,
  labelClasses,
  labelGradientClasses,
  labelMarkClasses,
  legendClasses,
  lineElementClasses,
  lineHighlightElementClasses,
  mangoFusionPalette,
  mangoFusionPaletteDark,
  mangoFusionPaletteLight,
  markElementClasses,
  orangePalette,
  orangePaletteDark,
  orangePaletteLight,
  pieArcClasses,
  pieArcLabelClasses,
  pieClasses,
  piecewiseColorDefaultLabelFormatter,
  piecewiseColorLegendClasses,
  pinkPalette,
  pinkPaletteDark,
  pinkPaletteLight,
  purplePalette,
  purplePaletteDark,
  purplePaletteLight,
  radarSeriesPlotClasses,
  rainbowSurgePalette,
  rainbowSurgePaletteDark,
  rainbowSurgePaletteLight,
  redPalette,
  redPaletteDark,
  redPaletteLight,
  referenceLineClasses,
  scatterClasses,
  strawberrySkyPalette,
  strawberrySkyPaletteDark,
  strawberrySkyPaletteLight,
  useAnimate,
  useAnimateArea,
  useAnimateBar,
  useAnimateBarLabel,
  useAnimateLine,
  useAnimatePieArc,
  useAnimatePieArcLabel,
  useAxesTooltip,
  useAxisTooltip,
  useBarSeries,
  useBarSeriesContext,
  useChartApiContext,
  useChartGradientId,
  useChartGradientIdObjectBound,
  useChartId,
  useChartRootRef,
  useChartsLocalization,
  useDrawingArea,
  useGaugeState,
  useItemHighlighted,
  useItemHighlightedGetter,
  useItemTooltip,
  useLegend,
  useLineSeries,
  useLineSeriesContext,
  useMouseTracker,
  usePieSeries,
  usePieSeriesContext,
  useRadarItemTooltip,
  useRadarSeries,
  useRadarSeriesContext,
  useRadiusAxes,
  useRadiusAxis,
  useRadiusScale,
  useRotationAxes,
  useRotationAxis,
  useRotationScale,
  useScatterSeries,
  useScatterSeriesContext,
  useSeries,
  useSvgRef,
  useUtilityClasses,
  useXAxes,
  useXAxis,
  useXColorScale,
  useXScale,
  useYAxes,
  useYAxis,
  useYColorScale,
  useYScale,
  useZAxes,
  useZAxis,
  useZColorScale,
  yellowPalette,
  yellowPaletteDark,
  yellowPaletteLight
};
/*! Bundled license information:

@mui/x-charts/esm/index.js:
  (**
   * @mui/x-charts v8.9.0
   *
   * @license MIT
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=@mui_x-charts.js.map
