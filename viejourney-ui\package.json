{"name": "wanderlog-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fingerprintjs/fingerprintjs": "^4.6.2", "@floating-ui/react": "^0.27.12", "@mui/icons-material": "^6.4.8", "@mui/material": "^6.4.8", "@mui/x-charts": "^8.7.0", "@mui/x-data-grid-premium": "^8.5.1", "@mui/x-date-pickers-pro": "^8.5.0", "@mui/x-license": "^8.5.0", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.0.15", "@tiptap/extension-highlight": "^2.14.1", "@tiptap/extension-image": "^2.14.1", "@tiptap/extension-link": "^2.14.1", "@tiptap/extension-subscript": "^2.14.1", "@tiptap/extension-superscript": "^2.14.1", "@tiptap/extension-task-item": "^2.14.1", "@tiptap/extension-task-list": "^2.14.1", "@tiptap/extension-text-align": "^2.14.1", "@tiptap/extension-typography": "^2.14.1", "@tiptap/extension-underline": "^2.14.1", "@tiptap/pm": "^2.14.1", "@tiptap/react": "^2.14.1", "@tiptap/starter-kit": "^2.14.1", "@typebot.io/react": "^0.8.5", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.8.4", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "motion": "^12.5.0", "notistack": "^3.0.2", "randomcolor": "^0.6.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-number-format": "^5.4.4", "react-router-dom": "^7.4.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.6", "tailwindcss": "^4.0.15", "zustand": "^5.0.5"}, "overrides": {"react-helmet-async": {"react": "^19.0.0", "react-dom": "^19.0.0"}}, "lint-staged": {"src/**/*.{js,ts,tsx}": "eslint --fix"}, "devDependencies": {"@eslint/js": "^9.21.0", "@swc/core": "^1.13.1", "@types/node": "^22.13.10", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/swiper": "^5.4.3", "@vitejs/plugin-react-swc": "^3.8.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "react-error-overlay": "^6.0.9", "sass-embedded": "^1.89.2", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}, "optionalDependencies": {"@rollup/rollup-darwin-arm64": "^4.32.0", "@rollup/rollup-darwin-x64": "^4.32.0", "@rollup/rollup-linux-arm64-gnu": "^4.32.0", "@rollup/rollup-linux-x64-gnu": "^4.32.0", "@rollup/rollup-win32-arm64-msvc": "^4.32.0", "@rollup/rollup-win32-x64-msvc": "^4.32.0"}}