"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminModule = void 0;
const common_1 = require("@nestjs/common");
const admin_service_1 = require("./admin.service");
const admin_controller_1 = require("./admin.controller");
const mongoose_1 = require("@nestjs/mongoose");
const auth_module_1 = require("../auth/auth.module");
const assets_module_1 = require("../assets/assets.module");
const user_module_1 = require("../userinfo/user.module");
const account_schema_1 = require("../../infrastructure/database/account.schema");
const blog_schema_1 = require("../../infrastructure/database/blog.schema");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const comment_schema_1 = require("../../infrastructure/database/comment.schema");
const asset_schema_1 = require("../../infrastructure/database/asset.schema");
const account_entity_1 = require("../../common/entities/account.entity");
const blog_entity_1 = require("../../common/entities/blog.entity");
const comment_entity_1 = require("../../common/entities/comment.entity");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const asset_entity_1 = require("../../common/entities/asset.entity");
const trip_schema_1 = require("../../infrastructure/database/trip.schema");
let AdminModule = class AdminModule {
};
exports.AdminModule = AdminModule;
exports.AdminModule = AdminModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: account_entity_1.Account.name, schema: account_schema_1.AccountSchema },
                { name: blog_entity_1.Blog.name, schema: blog_schema_1.BlogSchema },
                { name: comment_entity_1.Comment.name, schema: comment_schema_1.CommentSchema },
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: asset_entity_1.Asset.name, schema: asset_schema_1.AssetSchema },
                { name: trip_schema_1.Trip.name, schema: trip_schema_1.TripSchema },
            ]),
            (0, common_1.forwardRef)(() => auth_module_1.AuthModule),
            assets_module_1.AssetsModule,
            user_module_1.UserModule,
        ],
        controllers: [admin_controller_1.AdminController],
        providers: [admin_service_1.AdminService],
        exports: [admin_service_1.AdminService],
    })
], AdminModule);
//# sourceMappingURL=admin.module.js.map