import { CommentService } from './comment.service';
export declare class CommentController {
    private readonly commentService;
    constructor(commentService: CommentService);
    createComment(blogId: string, content: string, req: any): Promise<Omit<import("mongoose").Document<unknown, {}, import("../../common/entities/comment.entity").Comment, {}> & import("../../common/entities/comment.entity").Comment & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }, never>>;
    getComments(blogId: string, limit?: string, skip?: string): Promise<import("../../common/entities/comment.entity").Comment[]>;
    editComment(commentId: string, content: string, req: any): Promise<import("mongoose").Document<unknown, {}, import("../../common/entities/comment.entity").Comment, {}> & import("../../common/entities/comment.entity").Comment & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
    deleteComment(commentId: string, req: any): Promise<{
        success: boolean;
    }>;
    likeComment(commentId: string, req: any): Promise<{
        likes: string[];
        likesCount: number;
    }>;
    unlikeComment(commentId: string, req: any): Promise<{
        likes: string[];
        likesCount: number;
    }>;
}
