import {
  ChartDataProvider,
  ChartsLegend,
  ChartsOverlay,
  ChartsSurface,
  ChartsTooltip,
  ChartsWrapper,
  DEFAULT_X_AXIS_KEY,
  DEFAULT_Y_AXIS_KEY,
  clampAngle,
  composeClasses,
  deg2rad,
  generateUtilityClass,
  generateUtilityClasses,
  getColor_default,
  getValueToPositionMapper,
  isBandScale,
  isBandScaleConfig,
  isPointScaleConfig,
  number_default,
  selectorChartXAxis,
  selectorChartYAxis,
  selectorChartZoomIsInteracting,
  selectorChartsHighlightXAxisValue,
  selectorChartsHighlightYAxisValue,
  useAnimate,
  useAnimateBar,
  useAnimateBarLabel,
  useBarSeriesContext,
  useChartCartesianAxis,
  useChartContainerProps,
  useChartContext,
  useChartHighlight,
  useChartId,
  useChartInteraction,
  useChartZAxis,
  useDrawingArea,
  useEnhancedEffect_default,
  useId,
  useInteractionItemProps,
  useItemHighlighted,
  useSelector,
  useSkipAnimation,
  useSlotProps_default,
  useStore,
  useXAxes,
  useYAxes
} from "./chunk-ZSDK7NHJ.js";
import {
  useRtl
} from "./chunk-T7G2LBTZ.js";
import {
  warnOnce
} from "./chunk-TTGKUZR6.js";
import {
  useThemeProps
} from "./chunk-ZZOFVYJW.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-FD6QZ34J.js";
import {
  styled_default,
  useTheme
} from "./chunk-2364KQYK.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_prop_types
} from "./chunk-CCWQ57J5.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-charts/esm/BarChart/BarChart.js
var React23 = __toESM(require_react(), 1);
var import_prop_types12 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/BarChart/BarPlot.js
var React7 = __toESM(require_react(), 1);
var import_prop_types4 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/BarChart/barElementClasses.js
function getBarElementUtilityClass(slot) {
  return generateUtilityClass("MuiBarElement", slot);
}
var barElementClasses = generateUtilityClasses("MuiBarElement", ["root", "highlighted", "faded", "series"]);
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    id,
    isHighlighted,
    isFaded
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getBarElementUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/BarChart/BarElement.js
var React2 = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/BarChart/AnimatedBarElement.js
var React = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var _excluded = ["ownerState", "skipAnimation", "id", "dataIndex", "xOrigin", "yOrigin"];
function AnimatedBarElement(props) {
  const {
    ownerState
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const animatedProps = useAnimateBar(props);
  return (0, import_jsx_runtime.jsx)("rect", _extends({}, other, {
    filter: ownerState.isHighlighted ? "brightness(120%)" : void 0,
    opacity: ownerState.isFaded ? 0.3 : 1,
    "data-highlighted": ownerState.isHighlighted || void 0,
    "data-faded": ownerState.isFaded || void 0
  }, animatedProps));
}

// node_modules/@mui/x-charts/esm/BarChart/BarElement.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var _excluded2 = ["id", "dataIndex", "classes", "color", "slots", "slotProps", "style", "onClick", "skipAnimation", "layout", "x", "xOrigin", "y", "yOrigin", "width", "height"];
function BarElement(props) {
  const {
    id,
    dataIndex,
    classes: innerClasses,
    color,
    slots,
    slotProps,
    style,
    onClick,
    skipAnimation,
    layout,
    x,
    xOrigin,
    y,
    yOrigin,
    width,
    height
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const interactionProps = useInteractionItemProps({
    type: "bar",
    seriesId: id,
    dataIndex
  });
  const {
    isFaded,
    isHighlighted
  } = useItemHighlighted({
    seriesId: id,
    dataIndex
  });
  const ownerState = {
    id,
    dataIndex,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses(ownerState);
  const Bar = (slots == null ? void 0 : slots.bar) ?? AnimatedBarElement;
  const barProps = useSlotProps_default({
    elementType: Bar,
    externalSlotProps: slotProps == null ? void 0 : slotProps.bar,
    externalForwardedProps: other,
    additionalProps: _extends({}, interactionProps, {
      id,
      dataIndex,
      color,
      x,
      xOrigin,
      y,
      yOrigin,
      width,
      height,
      style,
      onClick,
      cursor: onClick ? "pointer" : "unset",
      stroke: "none",
      fill: color,
      skipAnimation,
      layout
    }),
    className: classes.root,
    ownerState
  });
  return (0, import_jsx_runtime2.jsx)(Bar, _extends({}, barProps));
}
true ? BarElement.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types.default.object,
  dataIndex: import_prop_types.default.number.isRequired,
  id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]).isRequired,
  layout: import_prop_types.default.oneOf(["horizontal", "vertical"]).isRequired,
  skipAnimation: import_prop_types.default.bool.isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types.default.object,
  xOrigin: import_prop_types.default.number.isRequired,
  yOrigin: import_prop_types.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/BarChart/BarClipPath.js
var React3 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/BarChart/getRadius.js
var getRadius = (corner, {
  hasNegative,
  hasPositive,
  borderRadius,
  layout
}) => {
  if (!borderRadius) {
    return 0;
  }
  const isVertical = layout === "vertical";
  if (corner === "top-left" && (isVertical && hasPositive || !isVertical && hasNegative)) {
    return borderRadius;
  }
  if (corner === "top-right" && (isVertical && hasPositive || !isVertical && hasPositive)) {
    return borderRadius;
  }
  if (corner === "bottom-right" && (isVertical && hasNegative || !isVertical && hasPositive)) {
    return borderRadius;
  }
  if (corner === "bottom-left" && (isVertical && hasNegative || !isVertical && hasNegative)) {
    return borderRadius;
  }
  return 0;
};

// node_modules/@mui/x-charts/esm/BarChart/BarClipPath.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var _excluded3 = ["maskId", "x", "y", "width", "height", "skipAnimation"];
function buildClipPath(size, borderRadius, ownerState) {
  const radiusData = _extends({}, ownerState, {
    borderRadius
  });
  const topLeft = Math.min(size, getRadius("top-left", radiusData));
  const topRight = Math.min(size, getRadius("top-right", radiusData));
  const bottomRight = Math.min(size, getRadius("bottom-right", radiusData));
  const bottomLeft = Math.min(size, getRadius("bottom-left", radiusData));
  return `inset(0px round ${topLeft}px ${topRight}px ${bottomRight}px ${bottomLeft}px)`;
}
function barClipRectPropsInterpolator(from, to) {
  const interpolateX = number_default(from.x, to.x);
  const interpolateY = number_default(from.y, to.y);
  const interpolateWidth = number_default(from.width, to.width);
  const interpolateHeight = number_default(from.height, to.height);
  const interpolateBorderRadius = number_default(from.borderRadius, to.borderRadius);
  return (t) => {
    return {
      x: interpolateX(t),
      y: interpolateY(t),
      width: interpolateWidth(t),
      height: interpolateHeight(t),
      borderRadius: interpolateBorderRadius(t)
    };
  };
}
function useAnimateBarClipRect(props) {
  const initialProps = {
    x: props.x,
    y: props.y + (props.ownerState.layout === "vertical" ? props.height : 0),
    width: props.ownerState.layout === "vertical" ? props.width : 0,
    height: props.ownerState.layout === "vertical" ? 0 : props.height,
    borderRadius: props.borderRadius
  };
  return useAnimate({
    x: props.x,
    y: props.y,
    width: props.width,
    height: props.height,
    borderRadius: props.borderRadius
  }, {
    createInterpolator: barClipRectPropsInterpolator,
    transformProps: (p) => ({
      x: p.x,
      y: p.y,
      width: p.width,
      height: p.height,
      style: {
        clipPath: buildClipPath(props.ownerState.layout === "vertical" ? p.height : p.width, p.borderRadius, props.ownerState)
      }
    }),
    applyProps(element, animatedProps) {
      element.setAttribute("x", animatedProps.x.toString());
      element.setAttribute("y", animatedProps.y.toString());
      element.setAttribute("width", animatedProps.width.toString());
      element.setAttribute("height", animatedProps.height.toString());
      element.style.clipPath = animatedProps.style.clipPath;
    },
    initialProps,
    skip: props.skipAnimation,
    ref: props.ref
  });
}
function BarClipRect(props) {
  const animatedProps = useAnimateBarClipRect(_extends({}, props, {
    borderRadius: props.ownerState.borderRadius ?? 0
  }));
  return (0, import_jsx_runtime3.jsx)("rect", _extends({}, animatedProps));
}
function BarClipPath(props) {
  const {
    maskId,
    x,
    y,
    width,
    height,
    skipAnimation
  } = props, rest = _objectWithoutPropertiesLoose(props, _excluded3);
  if (!props.borderRadius || props.borderRadius <= 0) {
    return null;
  }
  return (0, import_jsx_runtime3.jsx)("clipPath", {
    id: maskId,
    children: (0, import_jsx_runtime3.jsx)(BarClipRect, {
      ownerState: rest,
      x,
      y,
      width,
      height,
      skipAnimation
    })
  });
}

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/BarLabelPlot.js
var React6 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/BarLabelItem.js
var React5 = __toESM(require_react(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/barLabelClasses.js
function getBarLabelUtilityClass(slot) {
  return generateUtilityClass("MuiBarLabel", slot);
}
var barLabelClasses = generateUtilityClasses("MuiBarLabel", ["root", "highlighted", "faded", "animate"]);
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    seriesId,
    isFaded,
    isHighlighted,
    skipAnimation
  } = ownerState;
  const slots = {
    root: ["root", `series-${seriesId}`, isHighlighted && "highlighted", isFaded && "faded", !skipAnimation && "animate"]
  };
  return composeClasses(slots, getBarLabelUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/getBarLabel.js
var getBarLabel = (options) => {
  const {
    barLabel,
    value,
    dataIndex,
    seriesId,
    height,
    width
  } = options;
  if (barLabel === "value") {
    return value ? value == null ? void 0 : value.toString() : null;
  }
  return barLabel({
    seriesId,
    dataIndex,
    value
  }, {
    bar: {
      height,
      width
    }
  });
};

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/BarLabel.js
var React4 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var _excluded4 = ["seriesId", "dataIndex", "color", "isFaded", "isHighlighted", "classes", "skipAnimation", "layout", "xOrigin", "yOrigin"];
var BarLabelComponent = styled_default("text", {
  name: "MuiBarLabel",
  slot: "Root",
  overridesResolver: (_, styles) => [{
    [`&.${barLabelClasses.faded}`]: styles.faded
  }, {
    [`&.${barLabelClasses.highlighted}`]: styles.highlighted
  }, styles.root]
})(({
  theme
}) => {
  var _a, _b, _c, _d;
  return _extends({}, (_a = theme == null ? void 0 : theme.typography) == null ? void 0 : _a.body2, {
    stroke: "none",
    fill: (_d = (_c = (_b = theme.vars || theme) == null ? void 0 : _b.palette) == null ? void 0 : _c.text) == null ? void 0 : _d.primary,
    transition: "opacity 0.2s ease-in, fill 0.2s ease-in",
    textAnchor: "middle",
    dominantBaseline: "central",
    pointerEvents: "none",
    opacity: 1,
    [`&.${barLabelClasses.faded}`]: {
      opacity: 0.3
    }
  });
});
function BarLabel(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiBarLabel"
  });
  const otherProps = _objectWithoutPropertiesLoose(props, _excluded4);
  const animatedProps = useAnimateBarLabel(props);
  return (0, import_jsx_runtime4.jsx)(BarLabelComponent, _extends({}, otherProps, animatedProps));
}
true ? BarLabel.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types2.default.object,
  dataIndex: import_prop_types2.default.number.isRequired,
  /**
   * Height of the bar this label belongs to.
   */
  height: import_prop_types2.default.number.isRequired,
  isFaded: import_prop_types2.default.bool.isRequired,
  isHighlighted: import_prop_types2.default.bool.isRequired,
  layout: import_prop_types2.default.oneOf(["horizontal", "vertical"]).isRequired,
  seriesId: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]).isRequired,
  skipAnimation: import_prop_types2.default.bool.isRequired,
  /**
   * Width of the bar this label belongs to.
   */
  width: import_prop_types2.default.number.isRequired,
  /**
   * Position in the x-axis of the bar this label belongs to.
   */
  x: import_prop_types2.default.number.isRequired,
  /**
   * The x-coordinate of the stack this bar label belongs to.
   */
  xOrigin: import_prop_types2.default.number.isRequired,
  /**
   * Position in the y-axis of the bar this label belongs to.
   */
  y: import_prop_types2.default.number.isRequired,
  /**
   * The y-coordinate of the stack this bar label belongs to.
   */
  yOrigin: import_prop_types2.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/BarLabelItem.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
var _excluded5 = ["seriesId", "classes", "color", "dataIndex", "barLabel", "slots", "slotProps", "xOrigin", "yOrigin", "x", "y", "width", "height", "value", "skipAnimation", "layout"];
var _excluded22 = ["ownerState"];
function BarLabelItem(props) {
  const {
    seriesId,
    classes: innerClasses,
    color,
    dataIndex,
    barLabel,
    slots,
    slotProps,
    xOrigin,
    yOrigin,
    x,
    y,
    width,
    height,
    value,
    skipAnimation,
    layout
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const {
    isFaded,
    isHighlighted
  } = useItemHighlighted({
    seriesId,
    dataIndex
  });
  const ownerState = {
    seriesId,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted,
    dataIndex,
    skipAnimation,
    layout
  };
  const classes = useUtilityClasses2(ownerState);
  const Component = (slots == null ? void 0 : slots.barLabel) ?? BarLabel;
  const _useSlotProps = useSlotProps_default({
    elementType: Component,
    externalSlotProps: slotProps == null ? void 0 : slotProps.barLabel,
    additionalProps: _extends({}, other, {
      xOrigin,
      yOrigin,
      x,
      y,
      width,
      height,
      className: classes.root
    }),
    ownerState
  }), {
    ownerState: barLabelOwnerState
  } = _useSlotProps, barLabelProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded22);
  if (!barLabel) {
    return null;
  }
  const formattedLabelText = getBarLabel({
    barLabel,
    value,
    dataIndex,
    seriesId,
    height,
    width
  });
  if (!formattedLabelText) {
    return null;
  }
  return (0, import_jsx_runtime5.jsx)(Component, _extends({}, barLabelProps, barLabelOwnerState, {
    children: formattedLabelText
  }));
}
true ? BarLabelItem.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * If provided, the function will be used to format the label of the bar.
   * It can be set to 'value' to display the current value.
   * @param {BarItem} item The item to format.
   * @param {BarLabelContext} context data about the bar.
   * @returns {string} The formatted label.
   */
  barLabel: import_prop_types3.default.oneOfType([import_prop_types3.default.oneOf(["value"]), import_prop_types3.default.func]),
  classes: import_prop_types3.default.object,
  color: import_prop_types3.default.string.isRequired,
  dataIndex: import_prop_types3.default.number.isRequired,
  /**
   * The height of the bar.
   */
  height: import_prop_types3.default.number.isRequired,
  seriesId: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]).isRequired,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types3.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types3.default.object,
  /**
   * The value of the data point.
   */
  value: import_prop_types3.default.number,
  /**
   * The width of the bar.
   */
  width: import_prop_types3.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/BarChart/barClasses.js
function getBarUtilityClass(slot) {
  return generateUtilityClass("MuiBar", slot);
}
var barClasses = generateUtilityClasses("MuiBar", ["root", "series", "seriesLabels"]);
var useUtilityClasses3 = (classes) => {
  const slots = {
    root: ["root"],
    series: ["series"],
    seriesLabels: ["seriesLabels"]
  };
  return composeClasses(slots, getBarUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/BarChart/BarLabel/BarLabelPlot.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var _excluded6 = ["bars", "skipAnimation"];
function BarLabelPlot(props) {
  const {
    bars,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded6);
  const classes = useUtilityClasses3();
  return (0, import_jsx_runtime6.jsx)(React6.Fragment, {
    children: bars.flatMap(({
      seriesId,
      data
    }) => (0, import_jsx_runtime6.jsx)("g", {
      className: classes.seriesLabels,
      "data-series": seriesId,
      children: data.map(({
        xOrigin,
        yOrigin,
        x,
        y,
        dataIndex,
        color,
        value,
        width,
        height,
        layout
      }) => (0, import_jsx_runtime6.jsx)(BarLabelItem, _extends({
        seriesId,
        dataIndex,
        value,
        color,
        xOrigin,
        yOrigin,
        x,
        y,
        width,
        height,
        skipAnimation: skipAnimation ?? false,
        layout: layout ?? "vertical"
      }, other), dataIndex))
    }, seriesId))
  });
}

// node_modules/@mui/x-charts/esm/internals/plugins/featurePlugins/useChartCartesianAxis/useInternalIsZoomInteracting.js
function useInternalIsZoomInteracting() {
  const store = useStore();
  const isInteracting = useSelector(store, selectorChartZoomIsInteracting);
  return isInteracting;
}

// node_modules/@mui/x-charts/esm/BarChart/checkScaleErrors.js
var getAxisMessage = (axisDirection, axisId) => {
  const axisName = `${axisDirection}-axis`;
  const axisIdName = `${axisDirection}Axis`;
  const axisDefaultKey = axisDirection === "x" ? DEFAULT_X_AXIS_KEY : DEFAULT_Y_AXIS_KEY;
  return axisId === axisDefaultKey ? `The first \`${axisIdName}\`` : `The ${axisName} with id "${axisId}"`;
};
function checkScaleErrors(verticalLayout, seriesId, series, xAxisId, xAxis, yAxisId, yAxis) {
  const xAxisConfig = xAxis[xAxisId];
  const yAxisConfig = yAxis[yAxisId];
  const discreteAxisConfig = verticalLayout ? xAxisConfig : yAxisConfig;
  const continuousAxisConfig = verticalLayout ? yAxisConfig : xAxisConfig;
  const discreteAxisId = verticalLayout ? xAxisId : yAxisId;
  const continuousAxisId = verticalLayout ? yAxisId : xAxisId;
  const discreteAxisDirection = verticalLayout ? "x" : "y";
  const continuousAxisDirection = verticalLayout ? "y" : "x";
  if (!isBandScaleConfig(discreteAxisConfig)) {
    throw new Error(`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} should be of type "band" to display the bar series of id "${seriesId}".`);
  }
  if (discreteAxisConfig.data === void 0) {
    throw new Error(`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} should have data property.`);
  }
  if (isBandScaleConfig(continuousAxisConfig) || isPointScaleConfig(continuousAxisConfig)) {
    throw new Error(`MUI X Charts: ${getAxisMessage(continuousAxisDirection, continuousAxisId)} should be a continuous type to display the bar series of id "${seriesId}".`);
  }
  if (true) {
    if (discreteAxisConfig.data.length < series.stackedData.length) {
      warnOnce([`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} has less data (${discreteAxisConfig.data.length} values) than the bar series of id "${seriesId}" (${series.stackedData.length} values).`, "The axis data should have at least the same length than the series using it."], "error");
    }
  }
}

// node_modules/@mui/x-charts/esm/BarChart/useBarPlotData.js
function useBarPlotData(drawingArea, xAxes, yAxes) {
  const seriesData = useBarSeriesContext() ?? {
    series: {},
    stackingGroups: [],
    seriesOrder: []
  };
  const defaultXAxisId = useXAxes().xAxisIds[0];
  const defaultYAxisId = useYAxes().yAxisIds[0];
  const chartId = useChartId();
  const {
    series,
    stackingGroups
  } = seriesData;
  const masks = {};
  const data = stackingGroups.flatMap(({
    ids: seriesIds
  }, groupIndex) => {
    const xMin = drawingArea.left;
    const xMax = drawingArea.left + drawingArea.width;
    const yMin = drawingArea.top;
    const yMax = drawingArea.top + drawingArea.height;
    return seriesIds.map((seriesId) => {
      const xAxisId = series[seriesId].xAxisId ?? defaultXAxisId;
      const yAxisId = series[seriesId].yAxisId ?? defaultYAxisId;
      const xAxisConfig = xAxes[xAxisId];
      const yAxisConfig = yAxes[yAxisId];
      const verticalLayout = series[seriesId].layout === "vertical";
      checkScaleErrors(verticalLayout, seriesId, series[seriesId], xAxisId, xAxes, yAxisId, yAxes);
      const baseScaleConfig = verticalLayout ? xAxisConfig : yAxisConfig;
      const xScale = xAxisConfig.scale;
      const yScale = yAxisConfig.scale;
      const colorGetter = getColor_default(series[seriesId], xAxes[xAxisId], yAxes[yAxisId]);
      const bandWidth = baseScaleConfig.scale.bandwidth();
      const {
        barWidth,
        offset
      } = getBandSize({
        bandWidth,
        numberOfGroups: stackingGroups.length,
        gapRatio: baseScaleConfig.barGapRatio
      });
      const barOffset = groupIndex * (barWidth + offset);
      const {
        stackedData,
        data: currentSeriesData,
        layout,
        minBarSize
      } = series[seriesId];
      const seriesDataPoints = baseScaleConfig.data.map((baseValue, dataIndex) => {
        if (currentSeriesData[dataIndex] == null) {
          return null;
        }
        const values = stackedData[dataIndex];
        const valueCoordinates = values.map((v) => verticalLayout ? yScale(v) : xScale(v));
        const minValueCoord = Math.round(Math.min(...valueCoordinates));
        const maxValueCoord = Math.round(Math.max(...valueCoordinates));
        const stackId = series[seriesId].stack;
        const {
          barSize,
          startCoordinate
        } = getValueCoordinate(verticalLayout, minValueCoord, maxValueCoord, currentSeriesData[dataIndex], minBarSize);
        const result = {
          seriesId,
          dataIndex,
          layout,
          x: verticalLayout ? xScale(baseValue) + barOffset : startCoordinate,
          y: verticalLayout ? startCoordinate : yScale(baseValue) + barOffset,
          xOrigin: xScale(0) ?? 0,
          yOrigin: yScale(0) ?? 0,
          height: verticalLayout ? barSize : barWidth,
          width: verticalLayout ? barWidth : barSize,
          color: colorGetter(dataIndex),
          value: currentSeriesData[dataIndex],
          maskId: `${chartId}_${stackId || seriesId}_${groupIndex}_${dataIndex}`
        };
        if (result.x > xMax || result.x + result.width < xMin || result.y > yMax || result.y + result.height < yMin) {
          return null;
        }
        if (!masks[result.maskId]) {
          masks[result.maskId] = {
            id: result.maskId,
            width: 0,
            height: 0,
            hasNegative: false,
            hasPositive: false,
            layout: result.layout,
            xOrigin: xScale(0),
            yOrigin: yScale(0),
            x: 0,
            y: 0
          };
        }
        const mask = masks[result.maskId];
        mask.width = result.layout === "vertical" ? result.width : mask.width + result.width;
        mask.height = result.layout === "vertical" ? mask.height + result.height : result.height;
        mask.x = Math.min(mask.x === 0 ? Infinity : mask.x, result.x);
        mask.y = Math.min(mask.y === 0 ? Infinity : mask.y, result.y);
        mask.hasNegative = mask.hasNegative || (result.value ?? 0) < 0;
        mask.hasPositive = mask.hasPositive || (result.value ?? 0) > 0;
        return result;
      }).filter((rectangle) => rectangle !== null);
      return {
        seriesId,
        data: seriesDataPoints
      };
    });
  });
  return {
    completedData: data,
    masksData: Object.values(masks)
  };
}
function getBandSize({
  bandWidth: W,
  numberOfGroups: N,
  gapRatio: r
}) {
  if (r === 0) {
    return {
      barWidth: W / N,
      offset: 0
    };
  }
  const barWidth = W / (N + (N - 1) * r);
  const offset = r * barWidth;
  return {
    barWidth,
    offset
  };
}
function getValueCoordinate(isVertical, minValueCoord, maxValueCoord, baseValue, minBarSize) {
  if (baseValue === 0 || baseValue == null) {
    return {
      barSize: 0,
      startCoordinate: minValueCoord
    };
  }
  const isSizeLessThanMin = maxValueCoord - minValueCoord < minBarSize;
  const barSize = isSizeLessThanMin ? minBarSize : maxValueCoord - minValueCoord;
  const isVerticalAndPositive = isVertical && baseValue >= 0;
  const isHorizontalAndNegative = !isVertical && baseValue < 0;
  if (isSizeLessThanMin && (isVerticalAndPositive || isHorizontalAndNegative)) {
    return {
      barSize,
      startCoordinate: maxValueCoord - barSize
    };
  }
  return {
    barSize,
    startCoordinate: minValueCoord
  };
}

// node_modules/@mui/x-charts/esm/BarChart/BarPlot.js
var import_jsx_runtime7 = __toESM(require_jsx_runtime(), 1);
var _excluded7 = ["skipAnimation", "onItemClick", "borderRadius", "barLabel"];
var BarPlotRoot = styled_default("g", {
  name: "MuiBarPlot",
  slot: "Root"
})({
  [`& .${barElementClasses.root}`]: {
    transition: "opacity 0.2s ease-in, fill 0.2s ease-in"
  }
});
function BarPlot(props) {
  const {
    skipAnimation: inSkipAnimation,
    onItemClick,
    borderRadius,
    barLabel
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded7);
  const isZoomInteracting = useInternalIsZoomInteracting();
  const skipAnimation = useSkipAnimation(isZoomInteracting || inSkipAnimation);
  const {
    xAxis: xAxes
  } = useXAxes();
  const {
    yAxis: yAxes
  } = useYAxes();
  const {
    completedData,
    masksData
  } = useBarPlotData(useDrawingArea(), xAxes, yAxes);
  const withoutBorderRadius = !borderRadius || borderRadius <= 0;
  const classes = useUtilityClasses3();
  return (0, import_jsx_runtime7.jsxs)(BarPlotRoot, {
    className: classes.root,
    children: [!withoutBorderRadius && masksData.map(({
      id,
      x,
      y,
      width,
      height,
      hasPositive,
      hasNegative,
      layout
    }) => {
      return (0, import_jsx_runtime7.jsx)(BarClipPath, {
        maskId: id,
        borderRadius,
        hasNegative,
        hasPositive,
        layout,
        x,
        y,
        width,
        height,
        skipAnimation: skipAnimation ?? false
      }, id);
    }), completedData.map(({
      seriesId,
      data
    }) => {
      return (0, import_jsx_runtime7.jsx)("g", {
        "data-series": seriesId,
        className: classes.series,
        children: data.map(({
          dataIndex,
          color,
          maskId,
          layout,
          x,
          xOrigin,
          y,
          yOrigin,
          width,
          height
        }) => {
          const barElement = (0, import_jsx_runtime7.jsx)(BarElement, _extends({
            id: seriesId,
            dataIndex,
            color,
            skipAnimation: skipAnimation ?? false,
            layout: layout ?? "vertical",
            x,
            xOrigin,
            y,
            yOrigin,
            width,
            height
          }, other, {
            onClick: onItemClick && ((event) => {
              onItemClick(event, {
                type: "bar",
                seriesId,
                dataIndex
              });
            })
          }), dataIndex);
          if (withoutBorderRadius) {
            return barElement;
          }
          return (0, import_jsx_runtime7.jsx)("g", {
            clipPath: `url(#${maskId})`,
            children: barElement
          }, dataIndex);
        })
      }, seriesId);
    }), barLabel && (0, import_jsx_runtime7.jsx)(BarLabelPlot, _extends({
      bars: completedData,
      skipAnimation,
      barLabel
    }, other))]
  });
}
true ? BarPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * If provided, the function will be used to format the label of the bar.
   * It can be set to 'value' to display the current value.
   * @param {BarItem} item The item to format.
   * @param {BarLabelContext} context data about the bar.
   * @returns {string} The formatted label.
   */
  barLabel: import_prop_types4.default.oneOfType([import_prop_types4.default.oneOf(["value"]), import_prop_types4.default.func]),
  /**
   * Defines the border radius of the bar element.
   */
  borderRadius: import_prop_types4.default.number,
  /**
   * Callback fired when a bar item is clicked.
   * @param {React.MouseEvent<SVGElement, MouseEvent>} event The event source of the callback.
   * @param {BarItemIdentifier} barItemIdentifier The bar item identifier.
   */
  onItemClick: import_prop_types4.default.func,
  /**
   * If `true`, animations are skipped.
   * @default undefined
   */
  skipAnimation: import_prop_types4.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types4.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types4.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsAxis/ChartsAxis.js
var React14 = __toESM(require_react(), 1);
var import_prop_types8 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsXAxis/ChartsXAxis.js
var React12 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/hooks/useIsHydrated.js
var React8 = __toESM(require_react(), 1);
function useIsHydrated() {
  const [isHydrated, setIsHydrated] = React8.useState(typeof window !== "undefined" || false);
  React8.useEffect(() => {
    setIsHydrated(true);
  }, []);
  return isHydrated;
}

// node_modules/@mui/x-charts/esm/internals/domUtils.js
function isSsr() {
  return typeof window === "undefined";
}
var stringCache = /* @__PURE__ */ new Map();
var MAX_CACHE_NUM = 2e3;
var SPAN_STYLE = {
  position: "absolute",
  top: "-20000px",
  left: 0,
  padding: 0,
  margin: 0,
  border: "none",
  whiteSpace: "pre"
};
var STYLE_LIST = ["minWidth", "maxWidth", "width", "minHeight", "maxHeight", "height", "top", "left", "fontSize", "padding", "margin", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom", "marginLeft", "marginRight", "marginTop", "marginBottom"];
var MEASUREMENT_SPAN_ID = "mui_measurement_span";
function autoCompleteStyle(name, value) {
  if (STYLE_LIST.indexOf(name) >= 0 && value === +value) {
    return `${value}px`;
  }
  return value;
}
function camelToMiddleLine(text) {
  const strs = text.split("");
  const formatStrs = strs.reduce((result, entry) => {
    if (entry === entry.toUpperCase()) {
      return [...result, "-", entry.toLowerCase()];
    }
    return [...result, entry];
  }, []);
  return formatStrs.join("");
}
var getStyleString = (style) => Object.keys(style).sort().reduce((result, s) => `${result}${camelToMiddleLine(s)}:${autoCompleteStyle(s, style[s])};`, "");
var domCleanTimeout;
var getStringSize = (text, style = {}) => {
  if (text === void 0 || text === null || isSsr()) {
    return {
      width: 0,
      height: 0
    };
  }
  const str = `${text}`;
  const styleString = getStyleString(style);
  const cacheKey = `${str}-${styleString}`;
  const size = stringCache.get(cacheKey);
  if (size) {
    return size;
  }
  try {
    let measurementSpan = document.getElementById(MEASUREMENT_SPAN_ID);
    if (measurementSpan === null) {
      measurementSpan = document.createElement("span");
      measurementSpan.setAttribute("id", MEASUREMENT_SPAN_ID);
      measurementSpan.setAttribute("aria-hidden", "true");
      document.body.appendChild(measurementSpan);
    }
    const measurementSpanStyle = _extends({}, SPAN_STYLE, style);
    Object.keys(measurementSpanStyle).map((styleKey) => {
      measurementSpan.style[camelToMiddleLine(styleKey)] = autoCompleteStyle(styleKey, measurementSpanStyle[styleKey]);
      return styleKey;
    });
    measurementSpan.textContent = str;
    const rect = measurementSpan.getBoundingClientRect();
    const result = {
      width: rect.width,
      height: rect.height
    };
    stringCache.set(cacheKey, result);
    if (stringCache.size + 1 > MAX_CACHE_NUM) {
      stringCache.clear();
    }
    if (false) {
      measurementSpan.textContent = "";
    } else {
      if (domCleanTimeout) {
        clearTimeout(domCleanTimeout);
      }
      domCleanTimeout = setTimeout(() => {
        measurementSpan.textContent = "";
      }, 0);
    }
    return result;
  } catch {
    return {
      width: 0,
      height: 0
    };
  }
};

// node_modules/@mui/x-charts/esm/hooks/useTicks.js
var React9 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/internals/isInfinity.js
function isInfinity(v) {
  return typeof v === "number" && !Number.isFinite(v);
}

// node_modules/@mui/x-charts/esm/hooks/useTicks.js
var offsetRatio = {
  start: 0,
  extremities: 0,
  end: 1,
  middle: 0.5
};
function getTicks(options) {
  const {
    scale,
    tickNumber,
    valueFormatter,
    tickInterval,
    tickPlacement = "extremities",
    tickLabelPlacement: tickLabelPlacementProp,
    isInside
  } = options;
  if (isBandScale(scale)) {
    const domain2 = scale.domain();
    const tickLabelPlacement2 = tickLabelPlacementProp ?? "middle";
    if (scale.bandwidth() > 0) {
      const filteredDomain2 = typeof tickInterval === "function" && domain2.filter(tickInterval) || typeof tickInterval === "object" && tickInterval || domain2;
      return [...filteredDomain2.map((value) => {
        const defaultTickLabel = `${value}`;
        return {
          value,
          formattedValue: (valueFormatter == null ? void 0 : valueFormatter(value, {
            location: "tick",
            scale,
            tickNumber,
            defaultTickLabel
          })) ?? defaultTickLabel,
          offset: scale(value) - (scale.step() - scale.bandwidth()) / 2 + offsetRatio[tickPlacement] * scale.step(),
          labelOffset: tickLabelPlacement2 === "tick" ? 0 : scale.step() * (offsetRatio[tickLabelPlacement2] - offsetRatio[tickPlacement])
        };
      }), ...tickPlacement === "extremities" ? [{
        formattedValue: void 0,
        offset: scale.range()[1],
        labelOffset: 0
      }] : []];
    }
    const filteredDomain = typeof tickInterval === "function" && domain2.filter(tickInterval) || typeof tickInterval === "object" && tickInterval || domain2;
    return filteredDomain.map((value) => {
      const defaultTickLabel = `${value}`;
      return {
        value,
        formattedValue: (valueFormatter == null ? void 0 : valueFormatter(value, {
          location: "tick",
          scale,
          tickNumber,
          defaultTickLabel
        })) ?? defaultTickLabel,
        offset: scale(value),
        labelOffset: 0
      };
    });
  }
  const domain = scale.domain();
  if (domain.some(isInfinity)) {
    return [];
  }
  const tickLabelPlacement = tickLabelPlacementProp;
  const ticks = typeof tickInterval === "object" ? tickInterval : scale.ticks(tickNumber);
  const visibleTicks = [];
  for (let i = 0; i < ticks.length; i += 1) {
    const value = ticks[i];
    const offset = scale(value);
    if (isInside(offset)) {
      const defaultTickLabel = scale.tickFormat(tickNumber)(value);
      visibleTicks.push({
        value,
        formattedValue: (valueFormatter == null ? void 0 : valueFormatter(value, {
          location: "tick",
          scale,
          tickNumber,
          defaultTickLabel
        })) ?? defaultTickLabel,
        offset,
        // Allowing the label to be placed in the middle of a continuous scale is weird.
        // But it is useful in some cases, like funnel categories with a linear scale.
        labelOffset: tickLabelPlacement === "middle" ? scale(ticks[i - 1] ?? 0) - (offset + scale(ticks[i - 1] ?? 0)) / 2 : 0
      });
    }
  }
  return visibleTicks;
}
function useTicks(options) {
  const {
    scale,
    tickNumber,
    valueFormatter,
    tickInterval,
    tickPlacement = "extremities",
    tickLabelPlacement,
    direction
  } = options;
  const {
    instance
  } = useChartContext();
  const isInside = direction === "x" ? instance.isXInside : instance.isYInside;
  return React9.useMemo(() => getTicks({
    scale,
    tickNumber,
    tickPlacement,
    tickInterval,
    tickLabelPlacement,
    valueFormatter,
    isInside
  }), [scale, tickNumber, tickPlacement, tickInterval, tickLabelPlacement, valueFormatter, isInside]);
}

// node_modules/@mui/x-charts/esm/ChartsAxis/axisClasses.js
function getAxisUtilityClass(slot) {
  return generateUtilityClass("MuiChartsAxis", slot);
}
var axisClasses = generateUtilityClasses("MuiChartsAxis", ["root", "line", "tickContainer", "tick", "tickLabel", "label", "directionX", "directionY", "top", "bottom", "left", "right", "id"]);

// node_modules/@mui/x-charts/esm/internals/components/AxisSharedComponents.js
var AxisRoot = styled_default("g", {
  name: "MuiChartsAxis",
  slot: "Root"
})(({
  theme
}) => ({
  [`& .${axisClasses.tickLabel}`]: _extends({}, theme.typography.caption, {
    fill: (theme.vars || theme).palette.text.primary
  }),
  [`& .${axisClasses.label}`]: {
    fill: (theme.vars || theme).palette.text.primary
  },
  [`& .${axisClasses.line}`]: {
    stroke: (theme.vars || theme).palette.text.primary,
    shapeRendering: "crispEdges",
    strokeWidth: 1
  },
  [`& .${axisClasses.tick}`]: {
    stroke: (theme.vars || theme).palette.text.primary,
    shapeRendering: "crispEdges"
  }
}));

// node_modules/@mui/x-charts/esm/ChartsText/ChartsText.js
var React10 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/internals/getWordsByLines.js
function getWordsByLines({
  style,
  needsComputation,
  text
}) {
  return text.split("\n").map((subText) => _extends({
    text: subText
  }, needsComputation ? getStringSize(subText, style) : {
    width: 0,
    height: 0
  }));
}

// node_modules/@mui/x-charts/esm/ChartsText/ChartsText.js
var import_jsx_runtime8 = __toESM(require_jsx_runtime(), 1);
var _excluded8 = ["x", "y", "style", "text", "ownerState"];
var _excluded23 = ["angle", "textAnchor", "dominantBaseline"];
function ChartsText(props) {
  const {
    x,
    y,
    style: styleProps,
    text
  } = props, textProps = _objectWithoutPropertiesLoose(props, _excluded8);
  const _ref = styleProps ?? {}, {
    angle,
    textAnchor,
    dominantBaseline
  } = _ref, style = _objectWithoutPropertiesLoose(_ref, _excluded23);
  const isHydrated = useIsHydrated();
  const wordsByLines = React10.useMemo(() => getWordsByLines({
    style,
    needsComputation: isHydrated && text.includes("\n"),
    text
  }), [style, text, isHydrated]);
  let startDy;
  switch (dominantBaseline) {
    case "hanging":
    case "text-before-edge":
      startDy = 0;
      break;
    case "central":
      startDy = (wordsByLines.length - 1) / 2 * -wordsByLines[0].height;
      break;
    default:
      startDy = (wordsByLines.length - 1) * -wordsByLines[0].height;
      break;
  }
  return (0, import_jsx_runtime8.jsx)("text", _extends({}, textProps, {
    transform: angle ? `rotate(${angle}, ${x}, ${y})` : void 0,
    x,
    y,
    textAnchor,
    dominantBaseline,
    style,
    children: wordsByLines.map((line, index) => (0, import_jsx_runtime8.jsx)("tspan", {
      x,
      dy: `${index === 0 ? startDy : wordsByLines[0].height}px`,
      dominantBaseline,
      children: line.text
    }, index))
  }));
}
true ? ChartsText.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Height of a text line (in `em`).
   */
  lineHeight: import_prop_types5.default.number,
  /**
   * If `true`, the line width is computed.
   * @default false
   */
  needsComputation: import_prop_types5.default.bool,
  ownerState: import_prop_types5.default.any,
  /**
   * Style applied to text elements.
   */
  style: import_prop_types5.default.object,
  /**
   * Text displayed.
   */
  text: import_prop_types5.default.string.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/hooks/useMounted.js
var React11 = __toESM(require_react(), 1);
function useMounted(defer = false) {
  const [mountedState, setMountedState] = React11.useState(false);
  useEnhancedEffect_default(() => {
    if (!defer) {
      setMountedState(true);
    }
  }, [defer]);
  React11.useEffect(() => {
    if (defer) {
      setMountedState(true);
    }
  }, [defer]);
  return mountedState;
}

// node_modules/@mui/x-charts/esm/ChartsText/defaultTextPlacement.js
function getDefaultTextAnchor(angle) {
  const adjustedAngle = clampAngle(angle);
  if (adjustedAngle <= 30 || adjustedAngle >= 330) {
    return "middle";
  }
  if (adjustedAngle <= 210 && adjustedAngle >= 150) {
    return "middle";
  }
  if (adjustedAngle <= 180) {
    return "end";
  }
  return "start";
}
function getDefaultBaseline(angle) {
  const adjustedAngle = clampAngle(angle);
  if (adjustedAngle <= 30 || adjustedAngle >= 330) {
    return "hanging";
  }
  if (adjustedAngle <= 210 && adjustedAngle >= 150) {
    return "auto";
  }
  return "central";
}

// node_modules/@mui/x-charts/esm/internals/invertTextAnchor.js
function invertTextAnchor(textAnchor) {
  switch (textAnchor) {
    case "start":
      return "end";
    case "end":
      return "start";
    default:
      return textAnchor;
  }
}

// node_modules/@mui/x-charts/esm/internals/getGraphemeCount.js
var segmenter = typeof window !== "undefined" && "Intl" in window && "Segmenter" in Intl ? new Intl.Segmenter(void 0, {
  granularity: "grapheme"
}) : null;
function getGraphemeCountFallback(text) {
  return text.length;
}
function getGraphemeCountModern(text) {
  const segments = segmenter.segment(text);
  let count = 0;
  for (const _unused of segments) {
    count += 1;
  }
  return count;
}
var getGraphemeCount = segmenter ? getGraphemeCountModern : getGraphemeCountFallback;

// node_modules/@mui/x-charts/esm/internals/degToRad.js
function degToRad(degrees) {
  return degrees * (Math.PI / 180);
}

// node_modules/@mui/x-charts/esm/internals/sliceUntil.js
var segmenter2 = typeof window !== "undefined" && "Intl" in window && "Segmenter" in Intl ? new Intl.Segmenter(void 0, {
  granularity: "grapheme"
}) : null;
function sliceUntilFallback(text, endIndex) {
  return text.slice(0, endIndex);
}
function sliceUntilModern(text, endIndex) {
  const segments = segmenter2.segment(text);
  let newText = "";
  let i = 0;
  for (const segment of segments) {
    newText += segment.segment;
    i += 1;
    if (i >= endIndex) {
      break;
    }
  }
  return newText;
}
var sliceUntil = segmenter2 ? sliceUntilModern : sliceUntilFallback;

// node_modules/@mui/x-charts/esm/internals/ellipsize.js
var ELLIPSIS = "…";
function doesTextFitInRect(text, config) {
  const {
    width,
    height,
    measureText
  } = config;
  const angle = degToRad(config.angle);
  const textSize = measureText(text);
  const angledWidth = Math.abs(textSize.width * Math.cos(angle)) + Math.abs(textSize.height * Math.sin(angle));
  const angledHeight = Math.abs(textSize.width * Math.sin(angle)) + Math.abs(textSize.height * Math.cos(angle));
  return angledWidth <= width && angledHeight <= height;
}
function ellipsize(text, doesTextFit) {
  if (doesTextFit(text)) {
    return text;
  }
  let shortenedText = text;
  let step = 1;
  let by = 1 / 2;
  const graphemeCount = getGraphemeCount(text);
  let newLength = graphemeCount;
  let lastLength = graphemeCount;
  let longestFittingText = null;
  do {
    lastLength = newLength;
    newLength = Math.floor(graphemeCount * by);
    if (newLength === 0) {
      break;
    }
    shortenedText = sliceUntil(text, newLength).trim();
    const fits = doesTextFit(shortenedText + ELLIPSIS);
    step += 1;
    if (fits) {
      longestFittingText = shortenedText;
      by += 1 / 2 ** step;
    } else {
      by -= 1 / 2 ** step;
    }
  } while (Math.abs(newLength - lastLength) !== 1);
  return longestFittingText ? longestFittingText + ELLIPSIS : "";
}

// node_modules/@mui/x-charts/esm/ChartsXAxis/shortenLabels.js
function shortenLabels(visibleLabels, drawingArea, maxHeight, isRtl, tickLabelStyle) {
  const shortenedLabels = /* @__PURE__ */ new Map();
  const angle = clampAngle((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0);
  let leftBoundFactor = 1;
  let rightBoundFactor = 1;
  if ((tickLabelStyle == null ? void 0 : tickLabelStyle.textAnchor) === "start") {
    leftBoundFactor = Infinity;
    rightBoundFactor = 1;
  } else if ((tickLabelStyle == null ? void 0 : tickLabelStyle.textAnchor) === "end") {
    leftBoundFactor = 1;
    rightBoundFactor = Infinity;
  } else {
    leftBoundFactor = 2;
    rightBoundFactor = 2;
  }
  if (angle > 90 && angle < 270) {
    [leftBoundFactor, rightBoundFactor] = [rightBoundFactor, leftBoundFactor];
  }
  if (isRtl) {
    [leftBoundFactor, rightBoundFactor] = [rightBoundFactor, leftBoundFactor];
  }
  for (const item of visibleLabels) {
    if (item.formattedValue) {
      const width = Math.min((item.offset + item.labelOffset) * leftBoundFactor, (drawingArea.left + drawingArea.width + drawingArea.right - item.offset - item.labelOffset) * rightBoundFactor);
      const doesTextFit = (text) => doesTextFitInRect(text, {
        width,
        height: maxHeight,
        angle,
        measureText: (string) => getStringSize(string, tickLabelStyle)
      });
      shortenedLabels.set(item, ellipsize(item.formattedValue.toString(), doesTextFit));
    }
  }
  return shortenedLabels;
}

// node_modules/@mui/x-charts/esm/internals/geometry.js
var ANGLE_APPROX = 5;
function getMinXTranslation(width, height, angle = 0) {
  if (true) {
    if (angle > 90 && angle < -90) {
      warnOnce([`MUI X Charts: It seems you applied an angle larger than 90° or smaller than -90° to an axis text.`, `This could cause some text overlapping.`, `If you encounter a use case where it's needed, please open an issue.`]);
    }
  }
  const standardAngle = Math.min(Math.abs(angle) % 180, Math.abs(Math.abs(angle) % 180 - 180) % 180);
  if (standardAngle < ANGLE_APPROX) {
    return width;
  }
  if (standardAngle > 90 - ANGLE_APPROX) {
    return height;
  }
  const radAngle = deg2rad(standardAngle);
  const angleSwich = Math.atan2(height, width);
  if (radAngle < angleSwich) {
    return width / Math.cos(radAngle);
  }
  return height / Math.sin(radAngle);
}

// node_modules/@mui/x-charts/esm/ChartsXAxis/getVisibleLabels.js
function getVisibleLabels(xTicks, {
  tickLabelStyle: style,
  tickLabelInterval,
  tickLabelMinGap,
  reverse,
  isMounted,
  isXInside
}) {
  const getTickLabelSize = (tick) => {
    if (!isMounted || tick.formattedValue === void 0) {
      return {
        width: 0,
        height: 0
      };
    }
    const tickSizes = getWordsByLines({
      style,
      needsComputation: true,
      text: tick.formattedValue
    });
    return {
      width: Math.max(...tickSizes.map((size) => size.width)),
      height: Math.max(tickSizes.length * tickSizes[0].height)
    };
  };
  if (typeof tickLabelInterval === "function") {
    return new Set(xTicks.filter((item, index) => tickLabelInterval(item.value, index)));
  }
  let previousTextLimit = 0;
  const direction = reverse ? -1 : 1;
  return new Set(xTicks.filter((item, labelIndex) => {
    const {
      offset,
      labelOffset
    } = item;
    const textPosition = offset + labelOffset;
    if (labelIndex > 0 && direction * textPosition < direction * (previousTextLimit + tickLabelMinGap)) {
      return false;
    }
    if (!isXInside(textPosition)) {
      return false;
    }
    const {
      width,
      height
    } = getTickLabelSize(item);
    const distance = getMinXTranslation(width, height, style == null ? void 0 : style.angle);
    const currentTextLimit = textPosition - direction * distance / 2;
    if (labelIndex > 0 && direction * currentTextLimit < direction * (previousTextLimit + tickLabelMinGap)) {
      return false;
    }
    previousTextLimit = textPosition + direction * distance / 2;
    return true;
  }));
}

// node_modules/@mui/x-charts/esm/ChartsXAxis/ChartsXAxis.js
var import_jsx_runtime9 = __toESM(require_jsx_runtime(), 1);
var _excluded9 = ["scale", "tickNumber", "reverse"];
var useUtilityClasses4 = (ownerState) => {
  const {
    classes,
    position,
    id
  } = ownerState;
  const slots = {
    root: ["root", "directionX", position, `id-${id}`],
    line: ["line"],
    tickContainer: ["tickContainer"],
    tick: ["tick"],
    tickLabel: ["tickLabel"],
    label: ["label"]
  };
  return composeClasses(slots, getAxisUtilityClass, classes);
};
var TICK_LABEL_GAP = 3;
var AXIS_LABEL_TICK_LABEL_GAP = 4;
var XAxisRoot = styled_default(AxisRoot, {
  name: "MuiChartsXAxis",
  slot: "Root"
})({});
var defaultProps = {
  disableLine: false,
  disableTicks: false,
  tickSize: 6,
  tickLabelMinGap: 4
};
function ChartsXAxis(inProps) {
  const {
    xAxis,
    xAxisIds
  } = useXAxes();
  const _xAxis = xAxis[inProps.axisId ?? xAxisIds[0]], {
    scale: xScale,
    tickNumber,
    reverse
  } = _xAxis, settings = _objectWithoutPropertiesLoose(_xAxis, _excluded9);
  const isMounted = useMounted();
  const themedProps = useThemeProps({
    props: _extends({}, settings, inProps),
    name: "MuiChartsXAxis"
  });
  const defaultizedProps = _extends({}, defaultProps, themedProps);
  const {
    position,
    disableLine,
    disableTicks,
    tickLabelStyle,
    label,
    labelStyle,
    tickSize: tickSizeProp,
    valueFormatter,
    slots,
    slotProps,
    tickInterval,
    tickLabelInterval,
    tickPlacement,
    tickLabelPlacement,
    tickLabelMinGap,
    sx,
    offset,
    height: axisHeight
  } = defaultizedProps;
  const theme = useTheme();
  const isRtl = useRtl();
  const classes = useUtilityClasses4(defaultizedProps);
  const drawingArea = useDrawingArea();
  const {
    left,
    top,
    width,
    height
  } = drawingArea;
  const {
    instance
  } = useChartContext();
  const isHydrated = useIsHydrated();
  const tickSize = disableTicks ? 4 : tickSizeProp;
  const positionSign = position === "bottom" ? 1 : -1;
  const Line = (slots == null ? void 0 : slots.axisLine) ?? "line";
  const Tick = (slots == null ? void 0 : slots.axisTick) ?? "line";
  const TickLabel = (slots == null ? void 0 : slots.axisTickLabel) ?? ChartsText;
  const Label = (slots == null ? void 0 : slots.axisLabel) ?? ChartsText;
  const defaultTextAnchor = getDefaultTextAnchor((position === "bottom" ? 0 : 180) - ((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0));
  const defaultDominantBaseline = getDefaultBaseline((position === "bottom" ? 0 : 180) - ((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0));
  const axisTickLabelProps = useSlotProps_default({
    elementType: TickLabel,
    externalSlotProps: slotProps == null ? void 0 : slotProps.axisTickLabel,
    additionalProps: {
      style: _extends({}, theme.typography.caption, {
        fontSize: 12,
        lineHeight: 1.25,
        textAnchor: isRtl ? invertTextAnchor(defaultTextAnchor) : defaultTextAnchor,
        dominantBaseline: defaultDominantBaseline
      }, tickLabelStyle)
    },
    className: classes.tickLabel,
    ownerState: {}
  });
  const xTicks = useTicks({
    scale: xScale,
    tickNumber,
    valueFormatter,
    tickInterval,
    tickPlacement,
    tickLabelPlacement,
    direction: "x"
  });
  const visibleLabels = getVisibleLabels(xTicks, {
    tickLabelStyle: axisTickLabelProps.style,
    tickLabelInterval,
    tickLabelMinGap,
    reverse,
    isMounted,
    isXInside: instance.isXInside
  });
  const axisLabelProps = useSlotProps_default({
    elementType: Label,
    externalSlotProps: slotProps == null ? void 0 : slotProps.axisLabel,
    additionalProps: {
      style: _extends({}, theme.typography.body1, {
        lineHeight: 1,
        fontSize: 14,
        textAnchor: "middle",
        dominantBaseline: position === "bottom" ? "text-after-edge" : "text-before-edge"
      }, labelStyle)
    },
    ownerState: {}
  });
  const domain = xScale.domain();
  const ordinalAxis = isBandScale(xScale);
  if (ordinalAxis && domain.length === 0 || !ordinalAxis && domain.some(isInfinity) || position === "none") {
    return null;
  }
  const labelHeight = label ? getStringSize(label, axisLabelProps.style).height : 0;
  const labelRefPoint = {
    x: left + width / 2,
    y: positionSign * axisHeight
  };
  const tickLabelsMaxHeight = Math.max(0, axisHeight - (label ? labelHeight + AXIS_LABEL_TICK_LABEL_GAP : 0) - tickSize - TICK_LABEL_GAP);
  const tickLabels = isHydrated ? shortenLabels(visibleLabels, drawingArea, tickLabelsMaxHeight, isRtl, axisTickLabelProps.style) : new Map(Array.from(visibleLabels).map((item) => [item, item.formattedValue]));
  return (0, import_jsx_runtime9.jsxs)(XAxisRoot, {
    transform: `translate(0, ${position === "bottom" ? top + height + offset : top - offset})`,
    className: classes.root,
    sx,
    children: [!disableLine && (0, import_jsx_runtime9.jsx)(Line, _extends({
      x1: left,
      x2: left + width,
      className: classes.line
    }, slotProps == null ? void 0 : slotProps.axisLine)), xTicks.map((item, index) => {
      const {
        offset: tickOffset,
        labelOffset
      } = item;
      const xTickLabel = labelOffset ?? 0;
      const yTickLabel = positionSign * (tickSize + TICK_LABEL_GAP);
      const showTick = instance.isXInside(tickOffset);
      const tickLabel = tickLabels.get(item);
      const showTickLabel = visibleLabels.has(item);
      return (0, import_jsx_runtime9.jsxs)("g", {
        transform: `translate(${tickOffset}, 0)`,
        className: classes.tickContainer,
        children: [!disableTicks && showTick && (0, import_jsx_runtime9.jsx)(Tick, _extends({
          y2: positionSign * tickSize,
          className: classes.tick
        }, slotProps == null ? void 0 : slotProps.axisTick)), tickLabel !== void 0 && showTickLabel && (0, import_jsx_runtime9.jsx)(TickLabel, _extends({
          x: xTickLabel,
          y: yTickLabel
        }, axisTickLabelProps, {
          text: tickLabel
        }))]
      }, index);
    }), label && (0, import_jsx_runtime9.jsx)("g", {
      className: classes.label,
      children: (0, import_jsx_runtime9.jsx)(Label, _extends({}, labelRefPoint, axisLabelProps, {
        text: label
      }))
    })]
  });
}
true ? ChartsXAxis.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  axis: import_prop_types6.default.oneOf(["x"]),
  /**
   * The id of the axis to render.
   * If undefined, it will be the first defined axis.
   */
  axisId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types6.default.object,
  /**
   * If true, the axis line is disabled.
   * @default false
   */
  disableLine: import_prop_types6.default.bool,
  /**
   * If true, the ticks are disabled.
   * @default false
   */
  disableTicks: import_prop_types6.default.bool,
  /**
   * The fill color of the axis text.
   * @default 'currentColor'
   */
  fill: import_prop_types6.default.string,
  /**
   * The label of the axis.
   */
  label: import_prop_types6.default.string,
  /**
   * The style applied to the axis label.
   */
  labelStyle: import_prop_types6.default.object,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types6.default.object,
  /**
   * The stroke color of the axis line.
   * @default 'currentColor'
   */
  stroke: import_prop_types6.default.string,
  sx: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.object, import_prop_types6.default.bool])), import_prop_types6.default.func, import_prop_types6.default.object]),
  /**
   * Defines which ticks are displayed.
   * Its value can be:
   * - 'auto' In such case the ticks are computed based on axis scale and other parameters.
   * - a filtering function of the form `(value, index) => boolean` which is available only if the axis has "point" scale.
   * - an array containing the values where ticks should be displayed.
   * @see See {@link https://mui.com/x/react-charts/axis/#fixed-tick-positions}
   * @default 'auto'
   */
  tickInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.array, import_prop_types6.default.func]),
  /**
   * Defines which ticks get its label displayed. Its value can be:
   * - 'auto' In such case, labels are displayed if they do not overlap with the previous one.
   * - a filtering function of the form (value, index) => boolean. Warning: the index is tick index, not data ones.
   * @default 'auto'
   */
  tickLabelInterval: import_prop_types6.default.oneOfType([import_prop_types6.default.oneOf(["auto"]), import_prop_types6.default.func]),
  /**
   * The minimum gap in pixels between two tick labels.
   * If two tick labels are closer than this minimum gap, one of them will be hidden.
   * @default 4
   */
  tickLabelMinGap: import_prop_types6.default.number,
  /**
   * The placement of ticks label. Can be the middle of the band, or the tick position.
   * Only used if scale is 'band'.
   * @default 'middle'
   */
  tickLabelPlacement: import_prop_types6.default.oneOf(["middle", "tick"]),
  /**
   * The style applied to ticks text.
   */
  tickLabelStyle: import_prop_types6.default.object,
  /**
   * Maximal step between two ticks.
   * When using time data, the value is assumed to be in ms.
   * Not supported by categorical axis (band, points).
   */
  tickMaxStep: import_prop_types6.default.number,
  /**
   * Minimal step between two ticks.
   * When using time data, the value is assumed to be in ms.
   * Not supported by categorical axis (band, points).
   */
  tickMinStep: import_prop_types6.default.number,
  /**
   * The number of ticks. This number is not guaranteed.
   * Not supported by categorical axis (band, points).
   */
  tickNumber: import_prop_types6.default.number,
  /**
   * The placement of ticks in regard to the band interval.
   * Only used if scale is 'band'.
   * @default 'extremities'
   */
  tickPlacement: import_prop_types6.default.oneOf(["end", "extremities", "middle", "start"]),
  /**
   * The size of the ticks.
   * @default 6
   */
  tickSize: import_prop_types6.default.number
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsYAxis/ChartsYAxis.js
var React13 = __toESM(require_react(), 1);
var import_prop_types7 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsYAxis/shortenLabels.js
function shortenLabels2(visibleLabels, drawingArea, maxWidth, isRtl, tickLabelStyle) {
  const shortenedLabels = /* @__PURE__ */ new Map();
  const angle = clampAngle((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0);
  let topBoundFactor = 1;
  let bottomBoundFactor = 1;
  if ((tickLabelStyle == null ? void 0 : tickLabelStyle.textAnchor) === "start") {
    topBoundFactor = Infinity;
    bottomBoundFactor = 1;
  } else if ((tickLabelStyle == null ? void 0 : tickLabelStyle.textAnchor) === "end") {
    topBoundFactor = 1;
    bottomBoundFactor = Infinity;
  } else {
    topBoundFactor = 2;
    bottomBoundFactor = 2;
  }
  if (angle > 180) {
    [topBoundFactor, bottomBoundFactor] = [bottomBoundFactor, topBoundFactor];
  }
  if (isRtl) {
    [topBoundFactor, bottomBoundFactor] = [bottomBoundFactor, topBoundFactor];
  }
  for (const item of visibleLabels) {
    if (item.formattedValue) {
      const height = Math.min((item.offset + item.labelOffset) * topBoundFactor, (drawingArea.top + drawingArea.height + drawingArea.bottom - item.offset - item.labelOffset) * bottomBoundFactor);
      const doesTextFit = (text) => doesTextFitInRect(text, {
        width: maxWidth,
        height,
        angle,
        measureText: (string) => getStringSize(string, tickLabelStyle)
      });
      shortenedLabels.set(item, ellipsize(item.formattedValue.toString(), doesTextFit));
    }
  }
  return shortenedLabels;
}

// node_modules/@mui/x-charts/esm/ChartsYAxis/ChartsYAxis.js
var import_jsx_runtime10 = __toESM(require_jsx_runtime(), 1);
var _excluded10 = ["scale", "tickNumber"];
var useUtilityClasses5 = (ownerState) => {
  const {
    classes,
    position,
    id
  } = ownerState;
  const slots = {
    root: ["root", "directionY", position, `id-${id}`],
    line: ["line"],
    tickContainer: ["tickContainer"],
    tick: ["tick"],
    tickLabel: ["tickLabel"],
    label: ["label"]
  };
  return composeClasses(slots, getAxisUtilityClass, classes);
};
var TICK_LABEL_GAP2 = 2;
var AXIS_LABEL_TICK_LABEL_GAP2 = 2;
var YAxisRoot = styled_default(AxisRoot, {
  name: "MuiChartsYAxis",
  slot: "Root"
})({});
var defaultProps2 = {
  disableLine: false,
  disableTicks: false,
  tickSize: 6
};
function ChartsYAxis(inProps) {
  const {
    yAxisIds,
    yAxis
  } = useYAxes();
  const _yAxis = yAxis[inProps.axisId ?? yAxisIds[0]], {
    scale: yScale,
    tickNumber
  } = _yAxis, settings = _objectWithoutPropertiesLoose(_yAxis, _excluded10);
  const themedProps = useThemeProps({
    props: _extends({}, settings, inProps),
    name: "MuiChartsYAxis"
  });
  const defaultizedProps = _extends({}, defaultProps2, themedProps);
  const {
    position,
    disableLine,
    disableTicks,
    label,
    labelStyle,
    tickLabelStyle,
    tickSize: tickSizeProp,
    valueFormatter,
    slots,
    slotProps,
    tickPlacement,
    tickLabelPlacement,
    tickInterval,
    tickLabelInterval,
    sx,
    offset,
    width: axisWidth
  } = defaultizedProps;
  const theme = useTheme();
  const isRtl = useRtl();
  const isHydrated = useIsHydrated();
  const classes = useUtilityClasses5(defaultizedProps);
  const {
    instance
  } = useChartContext();
  const drawingArea = useDrawingArea();
  const {
    left,
    top,
    width,
    height
  } = drawingArea;
  const tickSize = disableTicks ? 4 : tickSizeProp;
  const yTicks = useTicks({
    scale: yScale,
    tickNumber,
    valueFormatter,
    tickPlacement,
    tickLabelPlacement,
    tickInterval,
    direction: "y"
  });
  const positionSign = position === "right" ? 1 : -1;
  const tickFontSize = typeof (tickLabelStyle == null ? void 0 : tickLabelStyle.fontSize) === "number" ? tickLabelStyle.fontSize : 12;
  const Line = (slots == null ? void 0 : slots.axisLine) ?? "line";
  const Tick = (slots == null ? void 0 : slots.axisTick) ?? "line";
  const TickLabel = (slots == null ? void 0 : slots.axisTickLabel) ?? ChartsText;
  const Label = (slots == null ? void 0 : slots.axisLabel) ?? ChartsText;
  const defaultTextAnchor = getDefaultTextAnchor((position === "right" ? -90 : 90) - ((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0));
  const defaultDominantBaseline = getDefaultBaseline((position === "right" ? -90 : 90) - ((tickLabelStyle == null ? void 0 : tickLabelStyle.angle) ?? 0));
  const axisTickLabelProps = useSlotProps_default({
    elementType: TickLabel,
    externalSlotProps: slotProps == null ? void 0 : slotProps.axisTickLabel,
    additionalProps: {
      style: _extends({}, theme.typography.caption, {
        fontSize: tickFontSize,
        textAnchor: isRtl ? invertTextAnchor(defaultTextAnchor) : defaultTextAnchor,
        dominantBaseline: defaultDominantBaseline
      }, tickLabelStyle)
    },
    className: classes.tickLabel,
    ownerState: {}
  });
  const axisLabelProps = useSlotProps_default({
    elementType: Label,
    externalSlotProps: slotProps == null ? void 0 : slotProps.axisLabel,
    additionalProps: {
      style: _extends({}, theme.typography.body1, {
        lineHeight: 1,
        fontSize: 14,
        angle: positionSign * 90,
        textAnchor: "middle",
        dominantBaseline: "text-before-edge"
      }, labelStyle)
    },
    ownerState: {}
  });
  const lineSlotProps = useSlotProps_default({
    elementType: Line,
    externalSlotProps: slotProps == null ? void 0 : slotProps.axisLine,
    additionalProps: {
      strokeLinecap: "square"
    },
    ownerState: {}
  });
  const domain = yScale.domain();
  const ordinalAxis = isBandScale(yScale);
  if (ordinalAxis && domain.length === 0 || !ordinalAxis && domain.some(isInfinity) || position === "none") {
    return null;
  }
  const labelRefPoint = {
    x: positionSign * axisWidth,
    y: top + height / 2
  };
  const tickLabelsMaxWidth = Math.max(0, axisWidth - (label ? getStringSize(label, axisLabelProps.style).height + AXIS_LABEL_TICK_LABEL_GAP2 : 0) - tickSize - TICK_LABEL_GAP2);
  const tickLabels = isHydrated ? shortenLabels2(yTicks, drawingArea, tickLabelsMaxWidth, isRtl, axisTickLabelProps.style) : new Map(Array.from(yTicks).map((item) => [item, item.formattedValue]));
  return (0, import_jsx_runtime10.jsxs)(YAxisRoot, {
    transform: `translate(${position === "right" ? left + width + offset : left - offset}, 0)`,
    className: classes.root,
    sx,
    children: [!disableLine && (0, import_jsx_runtime10.jsx)(Line, _extends({
      y1: top,
      y2: top + height,
      className: classes.line
    }, lineSlotProps)), yTicks.map((item, index) => {
      const {
        offset: tickOffset,
        labelOffset,
        value
      } = item;
      const xTickLabel = positionSign * (tickSize + TICK_LABEL_GAP2);
      const yTickLabel = labelOffset;
      const skipLabel = typeof tickLabelInterval === "function" && !(tickLabelInterval == null ? void 0 : tickLabelInterval(value, index));
      const showLabel = instance.isYInside(tickOffset);
      const tickLabel = tickLabels.get(item);
      if (!showLabel) {
        return null;
      }
      return (0, import_jsx_runtime10.jsxs)("g", {
        transform: `translate(0, ${tickOffset})`,
        className: classes.tickContainer,
        children: [!disableTicks && (0, import_jsx_runtime10.jsx)(Tick, _extends({
          x2: positionSign * tickSize,
          className: classes.tick
        }, slotProps == null ? void 0 : slotProps.axisTick)), tickLabel !== void 0 && !skipLabel && (0, import_jsx_runtime10.jsx)(TickLabel, _extends({
          x: xTickLabel,
          y: yTickLabel,
          text: tickLabel
        }, axisTickLabelProps))]
      }, index);
    }), label && isHydrated && (0, import_jsx_runtime10.jsx)("g", {
      className: classes.label,
      children: (0, import_jsx_runtime10.jsx)(Label, _extends({}, labelRefPoint, axisLabelProps, {
        text: label
      }))
    })]
  });
}
true ? ChartsYAxis.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  axis: import_prop_types7.default.oneOf(["y"]),
  /**
   * The id of the axis to render.
   * If undefined, it will be the first defined axis.
   */
  axisId: import_prop_types7.default.oneOfType([import_prop_types7.default.number, import_prop_types7.default.string]),
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types7.default.object,
  /**
   * If true, the axis line is disabled.
   * @default false
   */
  disableLine: import_prop_types7.default.bool,
  /**
   * If true, the ticks are disabled.
   * @default false
   */
  disableTicks: import_prop_types7.default.bool,
  /**
   * The fill color of the axis text.
   * @default 'currentColor'
   */
  fill: import_prop_types7.default.string,
  /**
   * The label of the axis.
   */
  label: import_prop_types7.default.string,
  /**
   * The style applied to the axis label.
   */
  labelStyle: import_prop_types7.default.object,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types7.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types7.default.object,
  /**
   * The stroke color of the axis line.
   * @default 'currentColor'
   */
  stroke: import_prop_types7.default.string,
  sx: import_prop_types7.default.oneOfType([import_prop_types7.default.arrayOf(import_prop_types7.default.oneOfType([import_prop_types7.default.func, import_prop_types7.default.object, import_prop_types7.default.bool])), import_prop_types7.default.func, import_prop_types7.default.object]),
  /**
   * Defines which ticks are displayed.
   * Its value can be:
   * - 'auto' In such case the ticks are computed based on axis scale and other parameters.
   * - a filtering function of the form `(value, index) => boolean` which is available only if the axis has "point" scale.
   * - an array containing the values where ticks should be displayed.
   * @see See {@link https://mui.com/x/react-charts/axis/#fixed-tick-positions}
   * @default 'auto'
   */
  tickInterval: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["auto"]), import_prop_types7.default.array, import_prop_types7.default.func]),
  /**
   * Defines which ticks get its label displayed. Its value can be:
   * - 'auto' In such case, labels are displayed if they do not overlap with the previous one.
   * - a filtering function of the form (value, index) => boolean. Warning: the index is tick index, not data ones.
   * @default 'auto'
   */
  tickLabelInterval: import_prop_types7.default.oneOfType([import_prop_types7.default.oneOf(["auto"]), import_prop_types7.default.func]),
  /**
   * The placement of ticks label. Can be the middle of the band, or the tick position.
   * Only used if scale is 'band'.
   * @default 'middle'
   */
  tickLabelPlacement: import_prop_types7.default.oneOf(["middle", "tick"]),
  /**
   * The style applied to ticks text.
   */
  tickLabelStyle: import_prop_types7.default.object,
  /**
   * Maximal step between two ticks.
   * When using time data, the value is assumed to be in ms.
   * Not supported by categorical axis (band, points).
   */
  tickMaxStep: import_prop_types7.default.number,
  /**
   * Minimal step between two ticks.
   * When using time data, the value is assumed to be in ms.
   * Not supported by categorical axis (band, points).
   */
  tickMinStep: import_prop_types7.default.number,
  /**
   * The number of ticks. This number is not guaranteed.
   * Not supported by categorical axis (band, points).
   */
  tickNumber: import_prop_types7.default.number,
  /**
   * The placement of ticks in regard to the band interval.
   * Only used if scale is 'band'.
   * @default 'extremities'
   */
  tickPlacement: import_prop_types7.default.oneOf(["end", "extremities", "middle", "start"]),
  /**
   * The size of the ticks.
   * @default 6
   */
  tickSize: import_prop_types7.default.number
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsAxis/ChartsAxis.js
var import_jsx_runtime11 = __toESM(require_jsx_runtime(), 1);
function ChartsAxis(props) {
  const {
    slots,
    slotProps
  } = props;
  const {
    xAxisIds,
    xAxis
  } = useXAxes();
  const {
    yAxisIds,
    yAxis
  } = useYAxes();
  return (0, import_jsx_runtime11.jsxs)(React14.Fragment, {
    children: [xAxisIds.map((axisId) => {
      if (!xAxis[axisId].position || xAxis[axisId].position === "none") {
        return null;
      }
      return (0, import_jsx_runtime11.jsx)(ChartsXAxis, {
        slots,
        slotProps,
        axisId
      }, axisId);
    }), yAxisIds.map((axisId) => {
      if (!yAxis[axisId].position || yAxis[axisId].position === "none") {
        return null;
      }
      return (0, import_jsx_runtime11.jsx)(ChartsYAxis, {
        slots,
        slotProps,
        axisId
      }, axisId);
    })]
  });
}
true ? ChartsAxis.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types8.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types8.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsAxisHighlight.js
var React17 = __toESM(require_react(), 1);
var import_prop_types9 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/chartsAxisHighlightClasses.js
function getAxisHighlightUtilityClass(slot) {
  return generateUtilityClass("MuiChartsAxisHighlight", slot);
}
var chartsAxisHighlightClasses = generateUtilityClasses("MuiChartsAxisHighlight", ["root"]);

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsYAxisHighlight.js
var React15 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsAxisHighlightPath.js
var ChartsAxisHighlightPath = styled_default("path", {
  name: "MuiChartsAxisHighlight",
  slot: "Root"
})(({
  theme
}) => ({
  pointerEvents: "none",
  variants: [{
    props: {
      axisHighlight: "band"
    },
    style: _extends({
      fill: "white",
      fillOpacity: 0.1
    }, theme.applyStyles("light", {
      fill: "gray"
    }))
  }, {
    props: {
      axisHighlight: "line"
    },
    style: _extends({
      strokeDasharray: "5 2",
      stroke: "#ffffff"
    }, theme.applyStyles("light", {
      stroke: "#000000"
    }))
  }]
}));

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsYAxisHighlight.js
var import_jsx_runtime12 = __toESM(require_jsx_runtime(), 1);
function ChartsYHighlight(props) {
  const {
    type,
    classes
  } = props;
  const {
    left,
    width
  } = useDrawingArea();
  const store = useStore();
  const axisYValues = useSelector(store, selectorChartsHighlightYAxisValue);
  const yAxes = useSelector(store, selectorChartYAxis);
  if (axisYValues.length === 0) {
    return null;
  }
  return axisYValues.map(({
    axisId,
    value
  }) => {
    const yAxis = yAxes.axis[axisId];
    const yScale = yAxis.scale;
    const getYPosition = getValueToPositionMapper(yScale);
    const isBandScaleY = type === "band" && value !== null && isBandScale(yScale);
    if (true) {
      const isError = isBandScaleY && yScale(value) === void 0;
      if (isError) {
        console.error([`MUI X Charts: The position value provided for the axis is not valid for the current scale.`, `This probably means something is wrong with the data passed to the chart.`, `The ChartsAxisHighlight component will not be displayed.`].join("\n"));
      }
    }
    return (0, import_jsx_runtime12.jsxs)(React15.Fragment, {
      children: [isBandScaleY && yScale(value) !== void 0 && (0, import_jsx_runtime12.jsx)(ChartsAxisHighlightPath, {
        d: `M ${left} ${// @ts-expect-error, yScale value is checked in the statement above
        yScale(value) - (yScale.step() - yScale.bandwidth()) / 2} l 0 ${yScale.step()} l ${width} 0 l 0 ${-yScale.step()} Z`,
        className: classes.root,
        ownerState: {
          axisHighlight: "band"
        }
      }), type === "line" && value !== null && (0, import_jsx_runtime12.jsx)(ChartsAxisHighlightPath, {
        d: `M ${left} ${getYPosition(value)} L ${left + width} ${getYPosition(value)}`,
        className: classes.root,
        ownerState: {
          axisHighlight: "line"
        }
      })]
    }, `${axisId}-${value}`);
  });
}

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsXAxisHighlight.js
var React16 = __toESM(require_react(), 1);
var import_jsx_runtime13 = __toESM(require_jsx_runtime(), 1);
function ChartsXHighlight(props) {
  const {
    type,
    classes
  } = props;
  const {
    top,
    height
  } = useDrawingArea();
  const store = useStore();
  const axisXValues = useSelector(store, selectorChartsHighlightXAxisValue);
  const xAxes = useSelector(store, selectorChartXAxis);
  if (axisXValues.length === 0) {
    return null;
  }
  return axisXValues.map(({
    axisId,
    value
  }) => {
    const xAxis = xAxes.axis[axisId];
    const xScale = xAxis.scale;
    const getXPosition = getValueToPositionMapper(xScale);
    const isBandScaleX = type === "band" && value !== null && isBandScale(xScale);
    if (true) {
      const isError = isBandScaleX && xScale(value) === void 0;
      if (isError) {
        console.error([`MUI X Charts: The position value provided for the axis is not valid for the current scale.`, `This probably means something is wrong with the data passed to the chart.`, `The ChartsAxisHighlight component will not be displayed.`].join("\n"));
      }
    }
    return (0, import_jsx_runtime13.jsxs)(React16.Fragment, {
      children: [isBandScaleX && xScale(value) !== void 0 && (0, import_jsx_runtime13.jsx)(
        ChartsAxisHighlightPath,
        {
          d: `M ${xScale(value) - (xScale.step() - xScale.bandwidth()) / 2} ${top} l ${xScale.step()} 0 l 0 ${height} l ${-xScale.step()} 0 Z`,
          className: classes.root,
          ownerState: {
            axisHighlight: "band"
          }
        }
      ), type === "line" && value !== null && (0, import_jsx_runtime13.jsx)(ChartsAxisHighlightPath, {
        d: `M ${getXPosition(value)} ${top} L ${getXPosition(value)} ${top + height}`,
        className: classes.root,
        ownerState: {
          axisHighlight: "line"
        }
      })]
    }, `${axisId}-${value}`);
  });
}

// node_modules/@mui/x-charts/esm/ChartsAxisHighlight/ChartsAxisHighlight.js
var import_jsx_runtime14 = __toESM(require_jsx_runtime(), 1);
var useUtilityClasses6 = () => {
  const slots = {
    root: ["root"]
  };
  return composeClasses(slots, getAxisHighlightUtilityClass);
};
function ChartsAxisHighlight(props) {
  const {
    x: xAxisHighlight,
    y: yAxisHighlight
  } = props;
  const classes = useUtilityClasses6();
  return (0, import_jsx_runtime14.jsxs)(React17.Fragment, {
    children: [xAxisHighlight && (0, import_jsx_runtime14.jsx)(ChartsXHighlight, {
      type: xAxisHighlight,
      classes
    }), yAxisHighlight && (0, import_jsx_runtime14.jsx)(ChartsYHighlight, {
      type: yAxisHighlight,
      classes
    })]
  });
}
true ? ChartsAxisHighlight.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  x: import_prop_types9.default.oneOf(["band", "line", "none"]),
  y: import_prop_types9.default.oneOf(["band", "line", "none"])
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsClipPath/ChartsClipPath.js
var React18 = __toESM(require_react(), 1);
var import_prop_types10 = __toESM(require_prop_types(), 1);
var import_jsx_runtime15 = __toESM(require_jsx_runtime(), 1);
function ChartsClipPath(props) {
  const {
    id,
    offset: offsetProps
  } = props;
  const {
    left,
    top,
    width,
    height
  } = useDrawingArea();
  const offset = _extends({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  }, offsetProps);
  return (0, import_jsx_runtime15.jsx)("clipPath", {
    id,
    children: (0, import_jsx_runtime15.jsx)("rect", {
      x: left - offset.left,
      y: top - offset.top,
      width: width + offset.left + offset.right,
      height: height + offset.top + offset.bottom
    })
  });
}
true ? ChartsClipPath.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The id of the clip path.
   */
  id: import_prop_types10.default.string.isRequired,
  /**
   * Offset, in pixels, of the clip path rectangle from the drawing area.
   *
   * A positive value will move the rectangle outside the drawing area.
   */
  offset: import_prop_types10.default.shape({
    bottom: import_prop_types10.default.number,
    left: import_prop_types10.default.number,
    right: import_prop_types10.default.number,
    top: import_prop_types10.default.number
  })
} : void 0;

// node_modules/@mui/x-charts/esm/ChartsGrid/ChartsGrid.js
var React21 = __toESM(require_react(), 1);
var import_prop_types11 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/ChartsGrid/chartsGridClasses.js
function getChartsGridUtilityClass(slot) {
  return generateUtilityClass("MuiChartsGrid", slot);
}
var chartsGridClasses = generateUtilityClasses("MuiChartsGrid", ["root", "line", "horizontalLine", "verticalLine"]);

// node_modules/@mui/x-charts/esm/ChartsGrid/styledComponents.js
var GridRoot = styled_default("g", {
  name: "MuiChartsGrid",
  slot: "Root",
  overridesResolver: (props, styles) => [{
    [`&.${chartsGridClasses.verticalLine}`]: styles.verticalLine
  }, {
    [`&.${chartsGridClasses.horizontalLine}`]: styles.horizontalLine
  }, styles.root]
})({});
var GridLine = styled_default("line", {
  name: "MuiChartsGrid",
  slot: "Line"
})(({
  theme
}) => ({
  stroke: (theme.vars || theme).palette.divider,
  shapeRendering: "crispEdges",
  strokeWidth: 1
}));

// node_modules/@mui/x-charts/esm/ChartsGrid/ChartsVerticalGrid.js
var React19 = __toESM(require_react(), 1);
var import_jsx_runtime16 = __toESM(require_jsx_runtime(), 1);
function ChartsGridVertical(props) {
  const {
    axis,
    start,
    end,
    classes
  } = props;
  const {
    scale,
    tickNumber,
    tickInterval
  } = axis;
  const xTicks = useTicks({
    scale,
    tickNumber,
    tickInterval,
    direction: "x"
  });
  return (0, import_jsx_runtime16.jsx)(React19.Fragment, {
    children: xTicks.map(({
      value,
      offset
    }) => {
      var _a;
      return (0, import_jsx_runtime16.jsx)(GridLine, {
        y1: start,
        y2: end,
        x1: offset,
        x2: offset,
        className: classes.verticalLine
      }, `vertical-${((_a = value == null ? void 0 : value.getTime) == null ? void 0 : _a.call(value)) ?? value}`);
    })
  });
}

// node_modules/@mui/x-charts/esm/ChartsGrid/ChartsHorizontalGrid.js
var React20 = __toESM(require_react(), 1);
var import_jsx_runtime17 = __toESM(require_jsx_runtime(), 1);
function ChartsGridHorizontal(props) {
  const {
    axis,
    start,
    end,
    classes
  } = props;
  const {
    scale,
    tickNumber,
    tickInterval
  } = axis;
  const yTicks = useTicks({
    scale,
    tickNumber,
    tickInterval,
    direction: "y"
  });
  return (0, import_jsx_runtime17.jsx)(React20.Fragment, {
    children: yTicks.map(({
      value,
      offset
    }) => {
      var _a;
      return (0, import_jsx_runtime17.jsx)(GridLine, {
        y1: offset,
        y2: offset,
        x1: start,
        x2: end,
        className: classes.horizontalLine
      }, `horizontal-${((_a = value == null ? void 0 : value.getTime) == null ? void 0 : _a.call(value)) ?? value}`);
    })
  });
}

// node_modules/@mui/x-charts/esm/ChartsGrid/ChartsGrid.js
var import_jsx_runtime18 = __toESM(require_jsx_runtime(), 1);
var _excluded11 = ["vertical", "horizontal"];
var useUtilityClasses7 = ({
  classes
}) => {
  const slots = {
    root: ["root"],
    verticalLine: ["line", "verticalLine"],
    horizontalLine: ["line", "horizontalLine"]
  };
  return composeClasses(slots, getChartsGridUtilityClass, classes);
};
function ChartsGrid(inProps) {
  const props = useThemeProps({
    props: inProps,
    name: "MuiChartsGrid"
  });
  const drawingArea = useDrawingArea();
  const {
    vertical,
    horizontal
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded11);
  const {
    xAxis,
    xAxisIds
  } = useXAxes();
  const {
    yAxis,
    yAxisIds
  } = useYAxes();
  const classes = useUtilityClasses7(props);
  const horizontalAxis = yAxis[yAxisIds[0]];
  const verticalAxis = xAxis[xAxisIds[0]];
  return (0, import_jsx_runtime18.jsxs)(GridRoot, _extends({}, other, {
    className: classes.root,
    children: [vertical && (0, import_jsx_runtime18.jsx)(ChartsGridVertical, {
      axis: verticalAxis,
      start: drawingArea.top,
      end: drawingArea.height + drawingArea.top,
      classes
    }), horizontal && (0, import_jsx_runtime18.jsx)(ChartsGridHorizontal, {
      axis: horizontalAxis,
      start: drawingArea.left,
      end: drawingArea.width + drawingArea.left,
      classes
    })]
  }));
}
true ? ChartsGrid.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types11.default.object,
  /**
   * Displays horizontal grid.
   */
  horizontal: import_prop_types11.default.bool,
  /**
   * Displays vertical grid.
   */
  vertical: import_prop_types11.default.bool
} : void 0;

// node_modules/@mui/x-charts/esm/BarChart/useBarChartProps.js
var React22 = __toESM(require_react(), 1);

// node_modules/@mui/x-charts/esm/BarChart/BarChart.plugins.js
var BAR_CHART_PLUGINS = [useChartZAxis, useChartCartesianAxis, useChartInteraction, useChartHighlight];

// node_modules/@mui/x-charts/esm/BarChart/useBarChartProps.js
var _excluded12 = ["xAxis", "yAxis", "series", "width", "height", "margin", "colors", "dataset", "sx", "axisHighlight", "grid", "children", "slots", "slotProps", "skipAnimation", "loading", "layout", "onItemClick", "highlightedItem", "onHighlightChange", "borderRadius", "barLabel", "className", "hideLegend", "showToolbar"];
var useBarChartProps = (props) => {
  var _a, _b, _c, _d, _e;
  const {
    xAxis,
    yAxis,
    series,
    width,
    height,
    margin,
    colors,
    dataset,
    sx,
    axisHighlight,
    grid,
    children,
    slots,
    slotProps,
    skipAnimation,
    loading,
    layout,
    onItemClick,
    highlightedItem,
    onHighlightChange,
    borderRadius,
    barLabel,
    className
  } = props, rest = _objectWithoutPropertiesLoose(props, _excluded12);
  const id = useId();
  const clipPathId = `${id}-clip-path`;
  const hasHorizontalSeries = layout === "horizontal" || layout === void 0 && series.some((item) => item.layout === "horizontal");
  const defaultBandXAxis = React22.useMemo(() => [{
    id: DEFAULT_X_AXIS_KEY,
    scaleType: "band",
    data: Array.from({
      length: Math.max(...series.map((s) => (s.data ?? dataset ?? []).length))
    }, (_, index) => index)
  }], [dataset, series]);
  const defaultBandYAxis = React22.useMemo(() => [{
    id: DEFAULT_Y_AXIS_KEY,
    scaleType: "band",
    data: Array.from({
      length: Math.max(...series.map((s) => (s.data ?? dataset ?? []).length))
    }, (_, index) => index)
  }], [dataset, series]);
  const seriesWithDefault = React22.useMemo(() => series.map((s) => _extends({
    type: "bar"
  }, s, {
    layout: hasHorizontalSeries ? "horizontal" : "vertical"
  })), [hasHorizontalSeries, series]);
  const defaultXAxis = hasHorizontalSeries ? void 0 : defaultBandXAxis;
  const processedXAxis = React22.useMemo(() => {
    if (!xAxis) {
      return defaultXAxis;
    }
    return hasHorizontalSeries ? xAxis : xAxis.map((axis) => _extends({
      scaleType: "band"
    }, axis));
  }, [defaultXAxis, hasHorizontalSeries, xAxis]);
  const defaultYAxis = hasHorizontalSeries ? defaultBandYAxis : void 0;
  const processedYAxis = React22.useMemo(() => {
    if (!yAxis) {
      return defaultYAxis;
    }
    return hasHorizontalSeries ? yAxis.map((axis) => _extends({
      scaleType: "band"
    }, axis)) : yAxis;
  }, [defaultYAxis, hasHorizontalSeries, yAxis]);
  const chartContainerProps = _extends({}, rest, {
    series: seriesWithDefault,
    width,
    height,
    margin,
    colors,
    dataset,
    xAxis: processedXAxis,
    yAxis: processedYAxis,
    highlightedItem,
    onHighlightChange,
    disableAxisListener: ((_a = slotProps == null ? void 0 : slotProps.tooltip) == null ? void 0 : _a.trigger) !== "axis" && (axisHighlight == null ? void 0 : axisHighlight.x) === "none" && (axisHighlight == null ? void 0 : axisHighlight.y) === "none",
    className,
    skipAnimation,
    plugins: BAR_CHART_PLUGINS
  });
  const barPlotProps = {
    onItemClick,
    slots,
    slotProps,
    borderRadius,
    barLabel
  };
  const gridProps = {
    vertical: grid == null ? void 0 : grid.vertical,
    horizontal: grid == null ? void 0 : grid.horizontal
  };
  const clipPathGroupProps = {
    clipPath: `url(#${clipPathId})`
  };
  const clipPathProps = {
    id: clipPathId
  };
  const overlayProps = {
    slots,
    slotProps,
    loading
  };
  const chartsAxisProps = {
    slots,
    slotProps
  };
  const axisHighlightProps = _extends({}, hasHorizontalSeries ? {
    y: "band"
  } : {
    x: "band"
  }, axisHighlight);
  const legendProps = {
    slots,
    slotProps
  };
  const chartsWrapperProps = {
    sx,
    legendPosition: (_c = (_b = props.slotProps) == null ? void 0 : _b.legend) == null ? void 0 : _c.position,
    legendDirection: (_e = (_d = props.slotProps) == null ? void 0 : _d.legend) == null ? void 0 : _e.direction
  };
  return {
    chartsWrapperProps,
    chartContainerProps,
    barPlotProps,
    gridProps,
    clipPathProps,
    clipPathGroupProps,
    overlayProps,
    chartsAxisProps,
    axisHighlightProps,
    legendProps,
    children
  };
};

// node_modules/@mui/x-charts/esm/BarChart/BarChart.js
var import_jsx_runtime19 = __toESM(require_jsx_runtime(), 1);
var BarChart = React23.forwardRef(function BarChart2(inProps, ref) {
  var _a, _b, _c, _d;
  const props = useThemeProps({
    props: inProps,
    name: "MuiBarChart"
  });
  const {
    chartsWrapperProps,
    chartContainerProps,
    barPlotProps,
    gridProps,
    clipPathProps,
    clipPathGroupProps,
    overlayProps,
    chartsAxisProps,
    axisHighlightProps,
    legendProps,
    children
  } = useBarChartProps(props);
  const {
    chartDataProviderProps,
    chartsSurfaceProps
  } = useChartContainerProps(chartContainerProps, ref);
  const Tooltip = ((_a = props.slots) == null ? void 0 : _a.tooltip) ?? ChartsTooltip;
  const Toolbar = (_b = props.slots) == null ? void 0 : _b.toolbar;
  return (0, import_jsx_runtime19.jsx)(ChartDataProvider, _extends({}, chartDataProviderProps, {
    children: (0, import_jsx_runtime19.jsxs)(ChartsWrapper, _extends({}, chartsWrapperProps, {
      children: [props.showToolbar && Toolbar ? (0, import_jsx_runtime19.jsx)(Toolbar, _extends({}, (_c = props.slotProps) == null ? void 0 : _c.toolbar)) : null, !props.hideLegend && (0, import_jsx_runtime19.jsx)(ChartsLegend, _extends({}, legendProps)), (0, import_jsx_runtime19.jsxs)(ChartsSurface, _extends({}, chartsSurfaceProps, {
        children: [(0, import_jsx_runtime19.jsx)(ChartsGrid, _extends({}, gridProps)), (0, import_jsx_runtime19.jsxs)("g", _extends({}, clipPathGroupProps, {
          children: [(0, import_jsx_runtime19.jsx)(BarPlot, _extends({}, barPlotProps)), (0, import_jsx_runtime19.jsx)(ChartsOverlay, _extends({}, overlayProps)), (0, import_jsx_runtime19.jsx)(ChartsAxisHighlight, _extends({}, axisHighlightProps))]
        })), (0, import_jsx_runtime19.jsx)(ChartsAxis, _extends({}, chartsAxisProps)), (0, import_jsx_runtime19.jsx)(ChartsClipPath, _extends({}, clipPathProps)), children]
      })), !props.loading && (0, import_jsx_runtime19.jsx)(Tooltip, _extends({}, (_d = props.slotProps) == null ? void 0 : _d.tooltip))]
    }))
  }));
});
if (true) BarChart.displayName = "BarChart";
true ? BarChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types12.default.shape({
    current: import_prop_types12.default.object
  }),
  /**
   * The configuration of axes highlight.
   * Default is set to 'band' in the bar direction.
   * Depends on `layout` prop.
   * @see See {@link https://mui.com/x/react-charts/highlighting/ highlighting docs} for more details.
   */
  axisHighlight: import_prop_types12.default.shape({
    x: import_prop_types12.default.oneOf(["band", "line", "none"]),
    y: import_prop_types12.default.oneOf(["band", "line", "none"])
  }),
  /**
   * If provided, the function will be used to format the label of the bar.
   * It can be set to 'value' to display the current value.
   * @param {BarItem} item The item to format.
   * @param {BarLabelContext} context data about the bar.
   * @returns {string} The formatted label.
   */
  barLabel: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["value"]), import_prop_types12.default.func]),
  /**
   * Defines the border radius of the bar element.
   */
  borderRadius: import_prop_types12.default.number,
  children: import_prop_types12.default.node,
  className: import_prop_types12.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string), import_prop_types12.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types12.default.arrayOf(import_prop_types12.default.object),
  desc: import_prop_types12.default.string,
  /**
   * If `true`, the charts will not listen to the mouse move event.
   * It might break interactive features, but will improve performance.
   * @default false
   */
  disableAxisListener: import_prop_types12.default.bool,
  /**
   * Option to display a cartesian grid in the background.
   */
  grid: import_prop_types12.default.shape({
    horizontal: import_prop_types12.default.bool,
    vertical: import_prop_types12.default.bool
  }),
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types12.default.number,
  /**
   * If `true`, the legend is not rendered.
   */
  hideLegend: import_prop_types12.default.bool,
  /**
   * The controlled axis highlight.
   * Identified by the axis id, and data index.
   */
  highlightedAxis: import_prop_types12.default.arrayOf(import_prop_types12.default.shape({
    axisId: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]).isRequired,
    dataIndex: import_prop_types12.default.number.isRequired
  })),
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types12.default.shape({
    dataIndex: import_prop_types12.default.number,
    seriesId: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types12.default.string,
  /**
   * The direction of the bar elements.
   * @default 'vertical'
   */
  layout: import_prop_types12.default.oneOf(["horizontal", "vertical"]),
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types12.default.bool,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types12.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.shape({
    bottom: import_prop_types12.default.number,
    left: import_prop_types12.default.number,
    right: import_prop_types12.default.number,
    top: import_prop_types12.default.number
  })]),
  /**
   * The function called for onClick events.
   * The second argument contains information about all line/bar elements at the current mouse position.
   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.
   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.
   */
  onAxisClick: import_prop_types12.default.func,
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types12.default.func,
  /**
   * The function called when the pointer position corresponds to a new axis data item.
   * This update can either be caused by a pointer movement, or an axis update.
   * In case of multiple axes, the function is called if at least one axis is updated.
   * The argument contains the identifier for all axes with a `data` property.
   * @param {AxisItemIdentifier[]} axisItems The array of axes item identifiers.
   */
  onHighlightedAxisChange: import_prop_types12.default.func,
  /**
   * Callback fired when a bar item is clicked.
   * @param {React.MouseEvent<SVGElement, MouseEvent>} event The event source of the callback.
   * @param {BarItemIdentifier} barItemIdentifier The bar item identifier.
   */
  onItemClick: import_prop_types12.default.func,
  /**
   * The series to display in the bar chart.
   * An array of [[BarSeries]] objects.
   */
  series: import_prop_types12.default.arrayOf(import_prop_types12.default.object).isRequired,
  /**
   * If true, shows the default chart toolbar.
   * @default false
   */
  showToolbar: import_prop_types12.default.bool,
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types12.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types12.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types12.default.object,
  sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
  theme: import_prop_types12.default.oneOf(["dark", "light"]),
  title: import_prop_types12.default.string,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types12.default.number,
  /**
   * The configuration of the x-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  xAxis: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    barGapRatio: import_prop_types12.default.number,
    categoryGapRatio: import_prop_types12.default.number,
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      type: import_prop_types12.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types12.default.string,
      values: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number, import_prop_types12.default.string]).isRequired)
    }), import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["band"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      type: import_prop_types12.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types12.default.string,
      values: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number, import_prop_types12.default.string]).isRequired)
    }), import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["point"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["log"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["pow"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["sqrt"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["time"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["utc"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["x"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    height: import_prop_types12.default.number,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["bottom", "none", "top"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["linear"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelMinGap: import_prop_types12.default.number,
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func
  })]).isRequired),
  /**
   * The configuration of the y-axes.
   * If not provided, a default axis config is used.
   * An array of [[AxisConfig]] objects.
   */
  yAxis: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    barGapRatio: import_prop_types12.default.number,
    categoryGapRatio: import_prop_types12.default.number,
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      type: import_prop_types12.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types12.default.string,
      values: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number, import_prop_types12.default.string]).isRequired)
    }), import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["band"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      type: import_prop_types12.default.oneOf(["ordinal"]).isRequired,
      unknownColor: import_prop_types12.default.string,
      values: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number, import_prop_types12.default.string]).isRequired)
    }), import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["point"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["log"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["pow"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["sqrt"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["time"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["utc"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  }), import_prop_types12.default.shape({
    axis: import_prop_types12.default.oneOf(["y"]),
    classes: import_prop_types12.default.object,
    colorMap: import_prop_types12.default.oneOfType([import_prop_types12.default.shape({
      color: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.string.isRequired), import_prop_types12.default.func]).isRequired,
      max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
      type: import_prop_types12.default.oneOf(["continuous"]).isRequired
    }), import_prop_types12.default.shape({
      colors: import_prop_types12.default.arrayOf(import_prop_types12.default.string).isRequired,
      thresholds: import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]).isRequired).isRequired,
      type: import_prop_types12.default.oneOf(["piecewise"]).isRequired
    })]),
    data: import_prop_types12.default.array,
    dataKey: import_prop_types12.default.string,
    disableLine: import_prop_types12.default.bool,
    disableTicks: import_prop_types12.default.bool,
    domainLimit: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["nice", "strict"]), import_prop_types12.default.func]),
    fill: import_prop_types12.default.string,
    hideTooltip: import_prop_types12.default.bool,
    id: import_prop_types12.default.oneOfType([import_prop_types12.default.number, import_prop_types12.default.string]),
    ignoreTooltip: import_prop_types12.default.bool,
    label: import_prop_types12.default.string,
    labelStyle: import_prop_types12.default.object,
    max: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    min: import_prop_types12.default.oneOfType([import_prop_types12.default.instanceOf(Date), import_prop_types12.default.number]),
    offset: import_prop_types12.default.number,
    position: import_prop_types12.default.oneOf(["left", "none", "right"]),
    reverse: import_prop_types12.default.bool,
    scaleType: import_prop_types12.default.oneOf(["linear"]),
    slotProps: import_prop_types12.default.object,
    slots: import_prop_types12.default.object,
    stroke: import_prop_types12.default.string,
    sx: import_prop_types12.default.oneOfType([import_prop_types12.default.arrayOf(import_prop_types12.default.oneOfType([import_prop_types12.default.func, import_prop_types12.default.object, import_prop_types12.default.bool])), import_prop_types12.default.func, import_prop_types12.default.object]),
    tickInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.array, import_prop_types12.default.func]),
    tickLabelInterval: import_prop_types12.default.oneOfType([import_prop_types12.default.oneOf(["auto"]), import_prop_types12.default.func]),
    tickLabelPlacement: import_prop_types12.default.oneOf(["middle", "tick"]),
    tickLabelStyle: import_prop_types12.default.object,
    tickMaxStep: import_prop_types12.default.number,
    tickMinStep: import_prop_types12.default.number,
    tickNumber: import_prop_types12.default.number,
    tickPlacement: import_prop_types12.default.oneOf(["end", "extremities", "middle", "start"]),
    tickSize: import_prop_types12.default.number,
    valueFormatter: import_prop_types12.default.func,
    width: import_prop_types12.default.number
  })]).isRequired)
} : void 0;

export {
  ChartsClipPath,
  ChartsText,
  getAxisUtilityClass,
  axisClasses,
  getDefaultTextAnchor,
  getDefaultBaseline,
  ChartsXAxis,
  ChartsYAxis,
  ChartsAxis,
  getChartsGridUtilityClass,
  chartsGridClasses,
  ChartsGrid,
  getAxisHighlightUtilityClass,
  chartsAxisHighlightClasses,
  ChartsAxisHighlightPath,
  ChartsAxisHighlight,
  getBarElementUtilityClass,
  barElementClasses,
  useUtilityClasses,
  BarElement,
  getBarLabelUtilityClass,
  barLabelClasses,
  BarLabel,
  getBarUtilityClass,
  barClasses,
  useInternalIsZoomInteracting,
  BarPlot,
  BarChart
};
//# sourceMappingURL=chunk-C6M2VKWF.js.map
