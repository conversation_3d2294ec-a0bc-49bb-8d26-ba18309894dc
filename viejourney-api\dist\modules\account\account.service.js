"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("mongoose");
const mongoose_2 = require("@nestjs/mongoose");
const assets_service_1 = require("../assets/assets.service");
let AccountService = class AccountService {
    accountModel;
    userInfosModel;
    assetModel;
    assetsService;
    constructor(accountModel, userInfosModel, assetModel, assetsService) {
        this.accountModel = accountModel;
        this.userInfosModel = userInfosModel;
        this.assetModel = assetModel;
        this.assetsService = assetsService;
    }
    async activateUser(userId) {
        const user = await this.accountModel.findByIdAndUpdate(userId, { active: true }, { new: true });
        if (!user)
            throw new common_1.NotFoundException('User not found');
    }
    async create(createAccountDto) {
        const createdAccount = new this.accountModel(createAccountDto);
        return createdAccount.save();
    }
    async findAll() {
        return this.accountModel.find().exec();
    }
    async findOne(id) {
        const account = await this.accountModel.findById(id);
        if (!account) {
            throw new common_1.HttpException(`Account with id ${id} not found`, 404);
        }
        return {
            _id: account._id,
            email: account.email,
            status: account.status,
            role: account.role,
        };
    }
    async findByEmail(email) {
        const account = await this.accountModel.findOne({ email });
        if (!account) {
            throw new common_1.NotFoundException(`Account with email ${email} not found`);
        }
        return account;
    }
    async update(id, updateAccountDto) {
        const updatedAccount = await this.accountModel
            .findByIdAndUpdate(id, updateAccountDto, {
            new: true,
            runValidators: true,
        })
            .exec();
        if (!updatedAccount) {
            throw new common_1.NotFoundException(`Account with id ${id} not found`);
        }
        return updatedAccount;
    }
    async remove(id) {
        const deletedAccount = await this.accountModel.findByIdAndDelete(id).exec();
        if (!deletedAccount) {
            throw new common_1.NotFoundException(`Account with id ${id} not found`);
        }
        return deletedAccount;
    }
    async editInfos(file, editProfile, userId) {
        try {
            const existingInfo = await this.userInfosModel
                .findOne({ userId: userId })
                .populate('avatar');
            let uploadResult = null;
            let assetId;
            if (file) {
                if (existingInfo?.avatar?.publicId) {
                    await this.assetsService.deleteImage(existingInfo.avatar.publicId);
                }
                uploadResult = await this.assetsService.uploadImage(file, {
                    public_id: `users/${userId}/AVATAR/${file.filename}`,
                });
                const assetData = {
                    userId: new mongoose_1.Types.ObjectId(userId),
                    type: 'AVATAR',
                    url: uploadResult?.secure_url,
                    publicId: uploadResult?.public_id,
                };
                if (existingInfo?.avatar?._id) {
                    await this.assetModel.updateOne({ _id: existingInfo.avatar._id }, { $set: assetData });
                    assetId = existingInfo.avatar._id;
                }
                else {
                    const asset = await this.assetModel.create(assetData);
                    assetId = asset._id;
                }
            }
            if (!existingInfo) {
                const userInfoData = {
                    ...editProfile,
                    userId: new mongoose_1.Types.ObjectId(userId),
                    ...(assetId ? { avatar: assetId } : {}),
                };
                const userInfo = await this.userInfosModel.create(userInfoData);
                return userInfo;
            }
            else {
                const updateData = {
                    ...editProfile,
                    ...(assetId ? { avatar: assetId } : {}),
                    updatedAt: new Date(),
                };
                await this.userInfosModel.updateOne({ userId: new mongoose_1.Types.ObjectId(userId) }, { $set: updateData });
                return this.userInfosModel
                    .findOne({ userId: userId })
                    .populate('avatar');
            }
        }
        catch (error) {
            console.error('Error updating user info:', error);
            throw new Error(`Failed to update user info: ${error.message}`);
        }
    }
};
exports.AccountService = AccountService;
exports.AccountService = AccountService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_2.InjectModel)('Account')),
    __param(1, (0, mongoose_2.InjectModel)('UserInfos')),
    __param(2, (0, mongoose_2.InjectModel)('Asset')),
    __metadata("design:paramtypes", [mongoose_1.Model,
        mongoose_1.Model,
        mongoose_1.Model,
        assets_service_1.AssetsService])
], AccountService);
//# sourceMappingURL=account.service.js.map