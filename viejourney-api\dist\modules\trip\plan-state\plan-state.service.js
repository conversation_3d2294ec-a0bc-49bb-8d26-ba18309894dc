"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanStateService = void 0;
const common_1 = require("@nestjs/common");
const trip_service_1 = require("../trip.service");
const crypto_1 = require("crypto");
let PlanStateService = class PlanStateService {
    tripService;
    saveDelayMillis = 20000;
    planStates = new Map();
    constructor(tripService) {
        this.tripService = tripService;
    }
    addItem(tripId, section, item, user) {
        const plan = this.getOrCreatePlan(tripId);
        if (section === 'budget') {
            if (typeof item == 'number' || typeof item === 'string') {
                plan.budget = item;
                this.scheduleSave(tripId);
                return 'budget';
            }
            else {
                throw new Error('Invalid budget payload');
            }
        }
        if (section === 'notes' && user) {
            item.by = user.fullName || user.email;
        }
        if (section === 'itineraries' && user) {
            if (item.place) {
                item.place.createdBy = {
                    id: user.id,
                    email: user?.email || '',
                    fullname: user?.fullName || '',
                };
            }
            item.createdAt = new Date().toISOString();
            delete item.createdBy;
            delete item.isEditing;
        }
        if (!Array.isArray(plan[section])) {
            throw new Error(`Section ${section} is not an array and cannot add items to it`);
        }
        const newItem = {
            ...item,
            id: (0, crypto_1.randomUUID)().toString(),
        };
        plan[section].push(newItem);
        this.scheduleSave(tripId);
        return newItem.id;
    }
    updateItem(tripId, section, item, user) {
        const plan = this.getOrCreatePlan(tripId);
        if (Array.isArray(plan[section])) {
            const index = plan[section].findIndex((i) => i.id === item.id);
            if (index !== -1) {
                plan[section][index] = {
                    ...plan[section][index],
                    ...item,
                };
            }
            this.scheduleSave(tripId);
        }
        else {
            throw new Error(`Update not supported for section: ${section}`);
        }
    }
    deleteItem(tripId, section, itemId) {
        const plan = this.getOrCreatePlan(tripId);
        if (Array.isArray(plan[section])) {
            const index = plan[section].findIndex((i) => i.id === itemId);
            if (index !== -1) {
                console.log('first', plan);
                plan[section].splice(index, 1);
                console.log('then', plan);
            }
            this.scheduleSave(tripId);
        }
    }
    savingStatus = new Map();
    isSavingPlan(tripId) {
        return this.savingStatus.get(tripId) === true;
    }
    async savePlan(tripId) {
        console.log(`Saving plan for trip: ${tripId}`);
        const state = this.planStates.get(tripId);
        if (!state)
            return;
        console.log(JSON.stringify(state.plan, null, 1));
        try {
            this.savingStatus.set(tripId, true);
            this.emitSaveStatus(tripId, 'saving');
            await this.tripService.updatePlan(tripId, state.plan);
            this.savingStatus.set(tripId, false);
            console.log(`Plan saved for trip: ${tripId}`);
            this.emitSaveStatus(tripId, 'saved');
        }
        catch (error) {
            this.savingStatus.set(tripId, false);
            console.error(`Failed to save plan for trip: ${tripId}`, error);
            this.emitSaveStatus(tripId, 'error', error.message);
        }
    }
    notifySaveStatus;
    emitSaveStatus(tripId, status, errorMessage) {
        this.notifySaveStatus?.(tripId, status, errorMessage);
    }
    scheduleSave(tripId) {
        const state = this.planStates.get(tripId);
        if (!state)
            return;
        if (state.timeout)
            clearTimeout(state.timeout);
        state.timeout = setTimeout(() => this.savePlan(tripId), this.saveDelayMillis);
    }
    getOrCreatePlan(tripId) {
        let state = this.planStates.get(tripId);
        if (!state) {
            state = {
                plan: {
                    notes: [],
                    places: [],
                    transits: [],
                    itineraries: [],
                    budget: 0,
                    expenses: [],
                },
            };
            this.planStates.set(tripId, state);
        }
        return state.plan;
    }
    initializePlan(tripId, plan) {
        this.planStates.set(tripId, {
            plan,
            timeout: undefined,
        });
    }
    async forceSave(tripId) {
        const state = this.planStates.get(tripId);
        if (!state)
            return;
        if (state.timeout) {
            clearTimeout(state.timeout);
            state.timeout = undefined;
        }
        console.log(`[FORCE SAVE] Force saving plan for trip ${tripId}:`);
        await this.savePlan(tripId);
    }
};
exports.PlanStateService = PlanStateService;
exports.PlanStateService = PlanStateService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [trip_service_1.TripService])
], PlanStateService);
//# sourceMappingURL=plan-state.service.js.map