import { Model, Types } from 'mongoose';
import { AssetsService } from '../assets/assets.service';
import { Asset } from 'src/common/entities/asset.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { Account } from 'src/common/entities/account.entity';
import { CreateAccountDto } from 'src/common/dtos/create-account.dto';
import { EditProfileDto } from 'src/common/dtos/editProfile.dto';
import { UpdateAccountDto } from 'src/common/dtos/update-account.dto';
export declare class AccountService {
    private readonly accountModel;
    private readonly userInfosModel;
    private readonly assetModel;
    private readonly assetsService;
    constructor(accountModel: Model<Account>, userInfosModel: Model<UserInfos>, assetModel: Model<Asset>, assetsService: AssetsService);
    activateUser(userId: Types.ObjectId): Promise<void>;
    create(createAccountDto: CreateAccountDto): Promise<Account>;
    findAll(): Promise<Account[]>;
    findOne(id: string): Promise<{
        _id: string;
        email: string;
        status: import("../../common/enums/status.enum").Status;
        role: import("../../common/enums/role.enum").Role;
    }>;
    findByEmail(email: string): Promise<Account>;
    update(id: string, updateAccountDto: UpdateAccountDto): Promise<Account>;
    remove(id: string): Promise<Account>;
    editInfos(file: Express.Multer.File, editProfile: EditProfileDto, userId: string): Promise<(import("mongoose").Document<unknown, {}, UserInfos, {}> & UserInfos & Required<{
        _id: Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
}
