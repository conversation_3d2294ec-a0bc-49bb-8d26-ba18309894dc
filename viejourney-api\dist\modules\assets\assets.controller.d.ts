import { AssetsService } from './assets.service';
import { Request } from 'express';
export declare class AssetsController {
    private readonly assetsService;
    constructor(assetsService: AssetsService);
    getAllBannersBySubsection(): Promise<Record<string, any[]>>;
    getSubsection(): Promise<(string | undefined)[]>;
    getAssetsByType(type: string, subsection?: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/asset.entity").Asset, {}> & import("../../common/entities/asset.entity").Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    })[]>;
    deleteAssetById(id: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/asset.entity").Asset, {}> & import("../../common/entities/asset.entity").Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
    updateAsset(file: Express.Multer.File, publicId: string): Promise<import("mongoose").Document<unknown, {}, import("../../common/entities/asset.entity").Asset, {}> & import("../../common/entities/asset.entity").Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }>;
    addModularAsset(file: Express.Multer.File, req: Request, type: string, subsection?: string): Promise<import("mongoose").Document<unknown, {}, import("../../common/entities/asset.entity").Asset, {}> & import("../../common/entities/asset.entity").Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }>;
    uploadImage(file: any): Promise<{
        url: string;
        publicId: string;
        width: number;
        height: number;
        format: string;
        size: number;
    }>;
    uploadMultipleImages(files: any[]): Promise<{
        url: string;
        publicId: string;
        width: number;
        height: number;
        format: string;
        size: number;
    }[]>;
    getAllUserContentAssets(req: Request): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/asset.entity").Asset, {}> & import("../../common/entities/asset.entity").Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    })[]>;
}
