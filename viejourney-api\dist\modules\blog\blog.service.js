"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("mongoose");
const mongoose_2 = require("@nestjs/mongoose");
const status_enum_1 = require("../../common/enums/status.enum");
const assets_service_1 = require("../assets/assets.service");
const uuid_1 = require("uuid");
let BlogService = class BlogService {
    blogModel;
    accountModel;
    userInfosModel;
    likeModel;
    assetsService;
    constructor(blogModel, accountModel, userInfosModel, likeModel, assetsService) {
        this.blogModel = blogModel;
        this.accountModel = accountModel;
        this.userInfosModel = userInfosModel;
        this.likeModel = likeModel;
        this.assetsService = assetsService;
    }
    async hasUserLikedBlog(userId, blogId) {
        const like = await this.likeModel.findOne({ userId, blogId }).exec();
        return !!like;
    }
    async postLikeBlog(req, blogId) {
        try {
            const userId = req.user?.['userId'];
            if (!userId)
                throw new common_1.BadRequestException('User ID not found');
            const like = await this.likeModel.create({ userId, blogId });
            await this.blogModel.findByIdAndUpdate(blogId, {
                $addToSet: { likes: like._id },
                $inc: { 'metrics.likeCount': 1 },
            }, { new: true });
            return {
                like: like,
                message: 'Blog liked successfully',
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Error liking blog: ' + error.message);
        }
    }
    async getRelatedBlogs(blogId, tags, destination) {
        const query = {
            status: 'APPROVED',
        };
        if (tags && tags.length > 0) {
            query.tags = { $in: tags };
        }
        if (blogId) {
            query._id = { $ne: blogId };
        }
        if (destination && destination.trim() !== '') {
            query.destination = { $regex: destination, $options: 'i' };
        }
        const relatedBlogs = await this.blogModel
            .find(query)
            .populate({
            path: 'createdBy',
            select: 'fullName avatar',
            populate: [{ path: 'userId', model: 'Account', select: 'email' }],
        })
            .limit(3)
            .exec();
        if (relatedBlogs.length === 0) {
            console.warn('No related blogs found for the given criteria');
            return [];
        }
        return relatedBlogs;
    }
    async unlikeBlog(req, blogId) {
        const userId = req.user?.['userId'];
        const like = await this.likeModel.findOneAndDelete({ userId, blogId });
        if (like) {
            await this.blogModel.findByIdAndUpdate(blogId, {
                $pull: { likes: like._id },
                $inc: { 'metrics.likeCount': -1 },
            });
        }
        return { message: 'Blog unliked successfully' };
    }
    async findAll(paginationDto) {
        const page = paginationDto.page ?? 1;
        const pageSize = paginationDto.pageSize ?? 10;
        const status = paginationDto.status?.trim();
        const viewCountRange = paginationDto.viewCountRange;
        const search = paginationDto.search?.trim();
        const sort = paginationDto.sort || 'desc';
        const filter = {};
        if (search) {
            filter.title = { $regex: search, $options: 'i' };
        }
        const skip = (page - 1) * pageSize;
        if (search) {
            filter.title = { $regex: search, $options: 'i' };
        }
        if (status) {
            filter.status = status;
        }
        if (viewCountRange) {
            if (viewCountRange === 'lt100') {
                filter['metrics.viewCount'] = { $lt: 100 };
            }
            else if (viewCountRange === '100to1000') {
                filter['metrics.viewCount'] = { $gte: 100, $lte: 1000 };
            }
            else if (viewCountRange === 'gt1000') {
                filter['metrics.viewCount'] = { $gt: 1000 };
            }
        }
        const sortOption = sort === 'asc' ? 1 : -1;
        const [blogs, totalItems] = await Promise.all([
            this.blogModel
                .find(filter)
                .sort({ updatedAt: sortOption })
                .skip(skip)
                .limit(pageSize)
                .populate('createdBy')
                .populate({
                path: 'createdBy',
                populate: [
                    {
                        path: 'avatar',
                        model: 'Asset',
                        select: 'url',
                    },
                    {
                        path: 'userId',
                        model: 'Account',
                        select: 'email',
                    },
                ],
            })
                .exec(),
            this.blogModel.countDocuments(filter).exec(),
        ]);
        const totalPages = Math.ceil(totalItems / pageSize);
        if (page > totalPages && totalItems > 0) {
            return {
                data: [],
                totalPages,
                currentPage: page,
                pageSize: pageSize,
                totalItems,
                message: 'Page exceeds total number of pages, no blogs found for this page.',
            };
        }
        if (totalItems === 0) {
            return {
                data: [],
                totalPages: 0,
                currentPage: page,
                pageSize: pageSize,
                totalItems: 0,
                message: 'No blogs found in the system.',
            };
        }
        const listBlogs = blogs.map((blog) => {
            return {
                _id: blog._id,
                title: blog.title,
                createdBy: {
                    _id: blog.createdBy._id,
                    fullName: blog.createdBy.fullName,
                    email: blog.createdBy.userId?.email,
                },
                avatarUser: blog?.createdBy?.avatar?.url || null,
                summary: blog.summary,
                destination: blog?.destination || null,
                viewCount: blog.metrics?.viewCount || 0,
                likeCount: blog.metrics?.likeCount || 0,
                commentCount: blog.metrics?.commentCount || 0,
                status: blog.status,
                flags: blog.flags?.length || 0,
                createdAt: blog.createdAt,
                updatedAt: blog.updatedAt,
            };
        });
        return {
            data: listBlogs,
            Total_Blogs: totalItems,
            currentPage: page,
            pageSize: pageSize,
            totalPages,
        };
    }
    async getAllApprovedBlogs(page, limit, search) {
        try {
            const pageNum = page || 1;
            const limitNum = limit || 10;
            const skip = (pageNum - 1) * limitNum;
            let query = { status: 'APPROVED' };
            if (search && search.trim() !== '') {
                query.$or = [
                    { title: { $regex: search, $options: 'i' } },
                    { summary: { $regex: search, $options: 'i' } },
                    { tags: { $regex: search, $options: 'i' } },
                    { destination: { $regex: search, $options: 'i' } },
                ];
            }
            const totalBlogs = await this.blogModel.countDocuments(query);
            const totalPages = Math.ceil(totalBlogs / limitNum);
            const blogs = await this.blogModel
                .find(query)
                .sort({ updatedAt: -1, createdAt: -1 })
                .skip(skip)
                .limit(limitNum)
                .exec();
            const populateAuthors = await Promise.all(blogs.map(async (blog) => {
                const createBy = await this.userInfosModel
                    .findOne({ userId: blog.createdBy })
                    .populate('userId', 'email')
                    .exec();
                const updatedBy = await this.userInfosModel
                    .findOne({ userId: blog.updatedBy })
                    .populate('userId', 'email')
                    .exec();
                return {
                    ...blog.toObject(),
                    createdBy: {
                        _id: createBy?._id,
                        fullName: createBy?.fullName || 'Unknown',
                        email: createBy?.userId?.email || '',
                        avatar: createBy?.avatar?.url || null,
                    },
                    updatedBy: {
                        _id: updatedBy?._id,
                        fullName: updatedBy?.fullName || 'Unknown',
                        email: updatedBy?.userId?.email || '',
                        avatar: updatedBy?.avatar?.url || null,
                    },
                };
            }));
            return {
                status: 'success',
                data: {
                    blogs: populateAuthors.map((blog) => ({
                        ...blog,
                        author: {
                            _id: blog?._id,
                            fullName: blog?.createdBy?.fullName || 'Unknown',
                            email: blog?.createdBy?.email || '',
                            avatar: blog?.createdBy?.avatar || null,
                        },
                        metrics: {
                            viewCount: blog.metrics?.viewCount || 0,
                            likeCount: blog.metrics?.likeCount || 0,
                            commentCount: blog.metrics?.commentCount || 0,
                        },
                        createdAt: blog.createdAt,
                        updatedAt: blog.updatedAt,
                    })),
                    pagination: {
                        currentPage: pageNum,
                        totalPages: totalPages,
                        totalItems: totalBlogs,
                        itemsPerPage: limitNum,
                        hasNext: pageNum < totalPages,
                        hasPrev: pageNum > 1,
                    },
                },
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error retrieving approved blogs: ' + error.message);
        }
    }
    async findOneBlogById(blogId) {
        const blog = await this.blogModel
            .findById(blogId)
            .populate({
            path: 'createdBy',
            select: 'fullName avatar',
            populate: {
                path: 'avatar',
                select: 'url',
            },
        })
            .exec();
        const [userBlogCount, userEmail] = await Promise.all([
            this.blogModel.countDocuments({ createdBy: blog?.createdBy._id }),
            this.userInfosModel
                .findById(blog?.createdBy._id)
                .populate({
                path: 'userId',
                select: 'email',
            })
                .exec(),
        ]);
        await this.blogModel.updateOne({ _id: blogId }, { $inc: { 'metrics.viewCount': 1 } });
        const plainBlog = blog?.toObject();
        const blogWithMetrics = {
            ...plainBlog,
            createdBy: {
                ...blog?.toObject().createdBy,
                email: userEmail?.userId?.email || 'Unknown',
                totalBlogs: userBlogCount,
                likesCount: blog?.metrics?.likeCount || 0,
            },
        };
        if (!blogWithMetrics)
            throw new common_1.NotFoundException('Blog not found');
        return blogWithMetrics;
    }
    async updateStatus(blogId, status) {
        const blog = await this.blogModel.findById(blogId).exec();
        if (!blog)
            throw new common_1.NotFoundException('Blog not found');
        blog.status = status;
        blog.updatedAt = new Date();
        await blog.save();
        return {
            _id: blog._id,
            title: blog.title,
            status: blog.status,
        };
    }
    async cleanFlags(blogId) {
        const blog = await this.blogModel.findById(blogId).exec();
        if (!blog)
            throw new common_1.NotFoundException('Blog not found');
        blog.flags = [];
        await blog.save();
        return {
            _id: blog._id,
            title: blog.title,
            flags: blog.flags,
        };
    }
    async banAuthor(blogId, reason) {
        const blog = await this.blogModel
            .findById(blogId)
            .populate({
            path: 'createdBy',
            populate: {
                path: 'userId',
                model: 'Account',
                select: 'role',
            },
        })
            .exec();
        if (!blog) {
            throw new common_1.NotFoundException('Blog not found');
        }
        if (blog.createdBy &&
            blog.createdBy.userId &&
            (blog.createdBy.userId.role === 'ADMIN' ||
                blog.createdBy.userId.role === 'MANAGER')) {
            throw new common_1.BadRequestException('Cannot ban admin or manager author');
        }
        else if (blog.createdBy &&
            blog.createdBy.userId &&
            blog.createdBy.userId.role === 'USER') {
            const account = await this.accountModel.findById(blog.createdBy.userId._id);
            if (account) {
                account.status = status_enum_1.Status.banned;
                await account.save();
            }
            const userInfo = await this.userInfosModel.findById(blog.createdBy._id);
            if (userInfo) {
                userInfo.banReason = reason;
                userInfo.bannedAt = new Date();
                await userInfo.save();
            }
            return {
                _id: blog._id,
                reasonBan: userInfo ? userInfo.banReason : blog.createdBy.banReason,
                bannedAt: userInfo ? userInfo.bannedAt : blog.createdBy.bannedAt,
                status: account ? account.status : blog.createdBy.userId.status,
            };
        }
        throw new common_1.BadRequestException('Invalid blog author');
    }
    async deleteBlogById(blogId) {
        const blog = await this.blogModel.findById(blogId).exec();
        if (!blog)
            throw new common_1.NotFoundException('Blog not found');
        if (blog.coverImage) {
            const publicId = this.assetsService.getPublicIdFromUrl(blog.coverImage);
            if (publicId) {
                await this.assetsService.deleteImage(publicId);
            }
        }
        await this.blogModel.deleteOne({ _id: blogId }).exec();
        return { message: 'Blog deleted successfully' };
    }
    async createBlog(createBlogDto, file, userId) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            console.log(user);
            if (!user)
                throw new common_1.NotFoundException('User not found');
            let uploadResult = null;
            uploadResult = await this.assetsService.uploadImage(file, {
                public_id: `manager/${userId}/${(0, uuid_1.v4)()}`,
                folder: `vie-journey/blogs`,
            });
            const newBlog = new this.blogModel({
                ...createBlogDto,
                createdBy: new mongoose_1.Types.ObjectId(user._id),
                updatedBy: new mongoose_1.Types.ObjectId(user._id),
                coverImage: uploadResult?.secure_url || '',
                destination: createBlogDto.destination || null,
                places: createBlogDto.places || [],
                status: 'APPROVED',
                metrics: {
                    likeCount: 0,
                    commentCount: 0,
                    viewCount: 0,
                },
                flags: [],
                comments: [],
            });
            const createdBlog = await newBlog.save();
            return createdBlog;
        }
        catch (error) {
            throw new common_1.NotFoundException('Error creating blog');
        }
    }
    async createFlag(blogId, reason, req) {
        const userId = req.user?.['userId'];
        const blog = await this.blogModel.findById(blogId).exec();
        if (!blog)
            throw new common_1.NotFoundException('Blog not found');
        blog.flags = blog.flags || [];
        blog.flags.push({
            reason,
            userId: new mongoose_1.Types.ObjectId(userId),
            date: new Date(),
        });
        await blog.save();
        return { message: 'Flag added successfully', flags: blog.flags };
    }
    async startBlog(location, userId, file) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            console.log(user);
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const title = `${location} Guide`;
            let coverImageUrl = '';
            if (file) {
                const uploadResult = await this.assetsService.uploadImage(file, {
                    public_id: `users/${userId}/BLOG_COVERS/${(0, uuid_1.v4)()}`,
                });
                coverImageUrl = uploadResult?.secure_url || '';
            }
            const newBlog = new this.blogModel({
                title,
                content: 'Write your content',
                summary: '',
                tags: [],
                coverImage: coverImageUrl,
                destination: location,
                createdBy: user._id,
                updatedBy: user._id,
                likes: [],
                status: 'DRAFT',
                metrics: {
                    likeCount: 0,
                    commentCount: 0,
                    viewCount: 0,
                },
                flags: [],
                comments: [],
            });
            const createdBlog = await newBlog.save();
            return {
                blogId: createdBlog._id,
                title: createdBlog.title,
                destination: createdBlog.destination,
                coverImage: createdBlog.coverImage,
                status: createdBlog.status,
                message: 'Blog draft created successfully. You can now start writing.',
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error starting blog: ' + error.message);
        }
    }
    async getDraftBlog(blogId, userId) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const blog = await this.blogModel
                .findOne({
                _id: blogId,
                createdBy: user._id,
                status: 'DRAFT',
            })
                .populate('createdBy')
                .exec();
            if (!blog) {
                throw new common_1.NotFoundException('Draft blog not found or you do not have permission to edit this blog');
            }
            return {
                _id: blog._id,
                title: blog.title,
                content: blog.content,
                summary: blog.summary,
                tags: blog.tags,
                coverImage: blog.coverImage,
                destination: blog.destination,
                places: blog.places || [],
                status: blog.status,
                createdAt: blog.createdAt,
                updatedAt: blog.updatedAt,
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error retrieving draft blog: ' + error.message);
        }
    }
    async getPublishedBlog(blogId, userId) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const blog = await this.blogModel
                .findOne({
                _id: blogId,
                createdBy: user._id,
                status: { $in: ['PENDING', 'APPROVED', 'REJECTED'] },
            })
                .populate('createdBy')
                .exec();
            if (!blog) {
                throw new common_1.NotFoundException('Published blog not found or you do not have permission to view this blog');
            }
            return {
                _id: blog._id,
                title: blog.title,
                content: blog.content,
                summary: blog.summary,
                tags: blog.tags,
                coverImage: blog.coverImage,
                destination: blog.destination,
                places: blog.places || [],
                status: blog.status,
                metrics: {
                    viewCount: blog.metrics?.viewCount || 0,
                    likeCount: blog.metrics?.likeCount || 0,
                    commentCount: blog.metrics?.commentCount || 0,
                },
                createdAt: blog.createdAt,
                updatedAt: blog.updatedAt,
                message: `Blog is currently ${blog.status}. You can edit this blog if needed.`,
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error retrieving published blog: ' + error.message);
        }
    }
    async updateBlogDraft(blogId, updateData, userId, file) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const blog = await this.blogModel
                .findOne({
                _id: blogId,
                createdBy: user._id,
                status: 'DRAFT',
            })
                .exec();
            if (!blog) {
                throw new common_1.NotFoundException('Draft blog not found or you do not have permission to edit this blog');
            }
            if (file) {
                if (blog.coverImage) {
                    const publicId = this.assetsService.getPublicIdFromUrl(blog.coverImage);
                    if (publicId) {
                        await this.assetsService.deleteImage(publicId);
                    }
                }
                const uploadResult = await this.assetsService.uploadImage(file, {
                    public_id: `users/${userId}/BLOG_COVERS/${blogId}_${(0, uuid_1.v4)()}`,
                });
                blog.coverImage = uploadResult?.secure_url || '';
            }
            if (updateData.title !== undefined)
                blog.title = updateData.title;
            if (updateData.content !== undefined)
                blog.content = updateData.content;
            if (updateData.summary !== undefined)
                blog.summary = updateData.summary;
            if (updateData.tags !== undefined)
                blog.tags = updateData.tags;
            if (updateData.destination !== undefined)
                blog.destination = updateData.destination;
            if (updateData.places !== undefined)
                blog.places = updateData.places;
            blog.updatedBy = user._id;
            blog.updatedAt = new Date();
            const updatedBlog = await blog.save();
            return {
                _id: updatedBlog._id,
                title: updatedBlog.title,
                content: updatedBlog.content,
                summary: updatedBlog.summary,
                tags: updatedBlog.tags,
                coverImage: updatedBlog.coverImage,
                destination: updatedBlog.destination,
                places: updatedBlog.places,
                status: updatedBlog.status,
                updatedAt: updatedBlog.updatedAt,
                message: 'Blog draft updated successfully',
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error updating blog draft: ' + error.message);
        }
    }
    async publishBlog(blogId, userId, role) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const blog = await this.blogModel
                .findOne({
                _id: blogId,
                createdBy: user._id,
                status: 'DRAFT',
            })
                .exec();
            if (!blog) {
                throw new common_1.NotFoundException('Draft blog not found or you do not have permission to publish this blog');
            }
            if (!blog.title || blog.title.trim().length === 0) {
                throw new common_1.BadRequestException('Title is required to publish the blog');
            }
            if (!blog.content || blog.content.trim().length < 20) {
                throw new common_1.BadRequestException('Content must be at least 20 characters to publish the blog');
            }
            if (role === 'USER') {
                blog.status = 'PENDING';
                blog.updatedBy = user._id;
                blog.updatedAt = new Date();
            }
            else {
                blog.status = 'APPROVED';
                blog.updatedBy = user._id;
                blog.updatedAt = new Date();
            }
            const publishedBlog = await blog.save();
            return {
                blogId: publishedBlog._id,
                title: publishedBlog.title,
                status: publishedBlog.status,
                publishedAt: publishedBlog.updatedAt,
                message: 'Blog published successfully and is now pending admin approval',
            };
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException ||
                error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.NotFoundException('Error publishing blog: ' + error.message);
        }
    }
    async editPublishedBlog(blogId, updateData, userId, file) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const blog = await this.blogModel
                .findOne({
                _id: blogId,
                createdBy: user._id,
                status: { $in: ['PENDING', 'APPROVED', 'REJECTED'] },
            })
                .exec();
            if (!blog) {
                throw new common_1.NotFoundException('Blog not found or you do not have permission to edit this blog');
            }
            if (file) {
                if (blog.coverImage) {
                    const publicId = this.assetsService.getPublicIdFromUrl(blog.coverImage);
                    if (publicId) {
                        await this.assetsService.deleteImage(publicId);
                    }
                }
                const uploadResult = await this.assetsService.uploadImage(file, {
                    public_id: `users/${userId}/BLOG_COVERS/${blogId}_${(0, uuid_1.v4)()}`,
                });
                blog.coverImage = uploadResult?.secure_url || '';
            }
            if (updateData.title !== undefined)
                blog.title = updateData.title;
            if (updateData.content !== undefined)
                blog.content = updateData.content;
            if (updateData.summary !== undefined)
                blog.summary = updateData.summary;
            if (updateData.tags !== undefined)
                blog.tags = updateData.tags;
            blog.status = 'DRAFT';
            blog.updatedBy = user._id;
            blog.updatedAt = new Date();
            const updatedBlog = await blog.save();
            return {
                _id: updatedBlog._id,
                title: updatedBlog.title,
                content: updatedBlog.content,
                summary: updatedBlog.summary,
                tags: updatedBlog.tags,
                coverImage: updatedBlog.coverImage,
                destination: updatedBlog.destination,
                places: updatedBlog.places,
                status: updatedBlog.status,
                updatedAt: updatedBlog.updatedAt,
                message: 'Blog has been edited and converted back to DRAFT status. You can publish it again after review.',
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error editing published blog: ' + error.message);
        }
    }
    async getUserBlogs(userId, status) {
        try {
            const user = await this.userInfosModel
                .findOne({ userId: new mongoose_1.Types.ObjectId(userId) })
                .exec();
            if (!user)
                throw new common_1.NotFoundException('User not found');
            const filter = { createdBy: user._id };
            if (status) {
                filter.status = status;
            }
            const blogs = await this.blogModel
                .find(filter)
                .sort({ updatedAt: -1 })
                .exec();
            return {
                blogs: blogs,
                total: blogs.length,
            };
        }
        catch (error) {
            throw new common_1.NotFoundException('Error retrieving user blogs: ' + error.message);
        }
    }
};
exports.BlogService = BlogService;
exports.BlogService = BlogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_2.InjectModel)('Blog')),
    __param(1, (0, mongoose_2.InjectModel)('Account')),
    __param(2, (0, mongoose_2.InjectModel)('UserInfos')),
    __param(3, (0, mongoose_2.InjectModel)('Like')),
    __metadata("design:paramtypes", [mongoose_1.Model,
        mongoose_1.Model,
        mongoose_1.Model,
        mongoose_1.Model,
        assets_service_1.AssetsService])
], BlogService);
//# sourceMappingURL=blog.service.js.map