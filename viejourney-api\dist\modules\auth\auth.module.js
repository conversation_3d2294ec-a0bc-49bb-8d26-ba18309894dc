"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("@nestjs/mongoose");
const passport_1 = require("@nestjs/passport");
const account_module_1 = require("../account/account.module");
const auth_controller_1 = require("./auth.controller");
const auth_service_1 = require("./auth.service");
const user_module_1 = require("../userinfo/user.module");
const account_entity_1 = require("../../common/entities/account.entity");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const account_schema_1 = require("../../infrastructure/database/account.schema");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const jwt_strategy_1 = require("../../common/strategies/jwt.strategy");
const google_strategy_1 = require("../../common/strategies/google.strategy");
const asset_entity_1 = require("../../common/entities/asset.entity");
const asset_schema_1 = require("../../infrastructure/database/asset.schema");
const assets_module_1 = require("../assets/assets.module");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            assets_module_1.AssetsModule,
            passport_1.PassportModule.register({ defaultStrategy: 'jwt', session: true }),
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'secret',
                signOptions: { expiresIn: '60m' },
            }),
            mongoose_1.MongooseModule.forFeature([
                { name: account_entity_1.Account.name, schema: account_schema_1.AccountSchema },
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: asset_entity_1.Asset.name, schema: asset_schema_1.AssetSchema },
            ]),
            user_module_1.UserModule,
            account_module_1.AccountModule,
            assets_module_1.AssetsModule,
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [auth_service_1.AuthService, jwt_strategy_1.JwtStrategy, google_strategy_1.GoogleStrategy, common_1.Logger],
        exports: [auth_service_1.AuthService, jwt_strategy_1.JwtStrategy, passport_1.PassportModule],
    })
], AuthModule);
//# sourceMappingURL=auth.module.js.map