{"version": 3, "sources": ["../../@mui/x-charts/esm/PieChart/PieChart.js", "../../@mui/x-charts/esm/PieChart/PiePlot.js", "../../@mui/x-charts/esm/PieChart/PieArcPlot.js", "../../@mui/x-charts/esm/PieChart/PieArc.js", "../../@mui/x-charts/esm/PieChart/dataTransform/useTransformData.js", "../../@mui/x-charts/esm/PieChart/PieArcLabelPlot.js", "../../@mui/x-charts/esm/PieChart/PieArcLabel.js", "../../@mui/x-charts/esm/internals/getPercentageValue.js", "../../@mui/x-charts/esm/PieChart/getPieCoordinates.js", "../../@mui/x-charts/esm/PieChart/pieClasses.js", "../../@mui/x-charts/esm/PieChart/PieChart.plugins.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"series\", \"width\", \"height\", \"margin\", \"colors\", \"sx\", \"skipAnimation\", \"hideLegend\", \"children\", \"slots\", \"slotProps\", \"onItemClick\", \"loading\", \"highlightedItem\", \"onHighlightChange\", \"className\", \"showToolbar\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '@mui/material/styles';\nimport { DEFAULT_PIE_CHART_MARGIN } from \"../internals/constants.js\";\nimport { ChartsTooltip } from \"../ChartsTooltip/index.js\";\nimport { ChartsLegend } from \"../ChartsLegend/index.js\";\nimport { PiePlot } from \"./PiePlot.js\";\nimport { ChartsOverlay } from \"../ChartsOverlay/index.js\";\nimport { ChartsSurface } from \"../ChartsSurface/index.js\";\nimport { ChartDataProvider } from \"../ChartDataProvider/index.js\";\nimport { useChartContainerProps } from \"../ChartContainer/useChartContainerProps.js\";\nimport { ChartsWrapper } from \"../internals/components/ChartsWrapper/index.js\";\nimport { PIE_CHART_PLUGINS } from \"./PieChart.plugins.js\";\nimport { defaultizeMargin } from \"../internals/defaultizeMargin.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Pie](https://mui.com/x/react-charts/pie/)\n * - [Pie demonstration](https://mui.com/x/react-charts/pie-demo/)\n *\n * API:\n *\n * - [PieChart API](https://mui.com/x/api/charts/pie-chart/)\n */\nconst PieChart = /*#__PURE__*/React.forwardRef(function PieChart(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPieChart'\n  });\n  const {\n      series,\n      width,\n      height,\n      margin: marginProps,\n      colors,\n      sx,\n      skipAnimation,\n      hideLegend,\n      children,\n      slots,\n      slotProps,\n      onItemClick,\n      loading,\n      highlightedItem,\n      onHighlightChange,\n      className,\n      showToolbar\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const margin = defaultizeMargin(marginProps, DEFAULT_PIE_CHART_MARGIN);\n  const {\n    chartDataProviderProps,\n    chartsSurfaceProps\n  } = useChartContainerProps(_extends({}, other, {\n    series: series.map(s => _extends({\n      type: 'pie'\n    }, s)),\n    width,\n    height,\n    margin,\n    colors,\n    highlightedItem,\n    onHighlightChange,\n    className,\n    skipAnimation,\n    plugins: PIE_CHART_PLUGINS\n  }), ref);\n  const Tooltip = slots?.tooltip ?? ChartsTooltip;\n  const Toolbar = props.slots?.toolbar;\n  return /*#__PURE__*/_jsx(ChartDataProvider, _extends({}, chartDataProviderProps, {\n    children: /*#__PURE__*/_jsxs(ChartsWrapper, {\n      legendPosition: props.slotProps?.legend?.position,\n      legendDirection: props?.slotProps?.legend?.direction ?? 'vertical',\n      sx: sx,\n      children: [showToolbar && Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, props.slotProps?.toolbar)) : null, !hideLegend && /*#__PURE__*/_jsx(ChartsLegend, {\n        direction: props?.slotProps?.legend?.direction ?? 'vertical',\n        slots: slots,\n        slotProps: slotProps\n      }), /*#__PURE__*/_jsxs(ChartsSurface, _extends({}, chartsSurfaceProps, {\n        children: [/*#__PURE__*/_jsx(PiePlot, {\n          slots: slots,\n          slotProps: slotProps,\n          onItemClick: onItemClick\n        }), /*#__PURE__*/_jsx(ChartsOverlay, {\n          loading: loading,\n          slots: slots,\n          slotProps: slotProps\n        }), children]\n      })), !loading && /*#__PURE__*/_jsx(Tooltip, _extends({\n        trigger: \"item\"\n      }, slotProps?.tooltip))]\n    })\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") PieChart.displayName = \"PieChart\";\nprocess.env.NODE_ENV !== \"production\" ? PieChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object\n  }),\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default rainbowSurgePalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   */\n  height: PropTypes.number,\n  /**\n   * If `true`, the legend is not rendered.\n   */\n  hideLegend: PropTypes.bool,\n  /**\n   * The highlighted item.\n   * Used when the highlight is controlled.\n   */\n  highlightedItem: PropTypes.shape({\n    dataIndex: PropTypes.number,\n    seriesId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n  }),\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, a loading overlay is displayed.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Localized text for chart components.\n   */\n  localeText: PropTypes.object,\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   *\n   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   */\n  margin: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  })]),\n  /**\n   * The callback fired when the highlighted item changes.\n   *\n   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when a pie arc is clicked.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * The series to display in the pie chart.\n   * An array of [[PieSeries]] objects.\n   */\n  series: PropTypes.arrayOf(PropTypes.object).isRequired,\n  /**\n   * If true, shows the default chart toolbar.\n   * @default false\n   */\n  showToolbar: PropTypes.bool,\n  /**\n   * If `true`, animations are skipped.\n   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  theme: PropTypes.oneOf(['dark', 'light']),\n  title: PropTypes.string,\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   */\n  width: PropTypes.number\n} : void 0;\nexport { PieChart };", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { PieArcPlot } from \"./PieArcPlot.js\";\nimport { PieArcLabelPlot } from \"./PieArcLabelPlot.js\";\nimport { getPercentageValue } from \"../internals/getPercentageValue.js\";\nimport { getPieCoordinates } from \"./getPieCoordinates.js\";\nimport { usePieSeriesContext } from \"../hooks/usePieSeries.js\";\nimport { useSkipAnimation } from \"../hooks/useSkipAnimation.js\";\nimport { useDrawingArea } from \"../hooks/index.js\";\nimport { useUtilityClasses } from \"./pieClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Pie](https://mui.com/x/react-charts/pie/)\n * - [Pie demonstration](https://mui.com/x/react-charts/pie-demo/)\n *\n * API:\n *\n * - [PiePlot API](https://mui.com/x/api/charts/pie-plot/)\n */\nfunction PiePlot(props) {\n  const {\n    skipAnimation: inSkipAnimation,\n    slots,\n    slotProps,\n    onItemClick\n  } = props;\n  const seriesData = usePieSeriesContext();\n  const {\n    left,\n    top,\n    width,\n    height\n  } = useDrawingArea();\n  const skipAnimation = useSkipAnimation(inSkipAnimation);\n  const classes = useUtilityClasses();\n  if (seriesData === undefined) {\n    return null;\n  }\n  const {\n    series,\n    seriesOrder\n  } = seriesData;\n  return /*#__PURE__*/_jsxs(\"g\", {\n    children: [seriesOrder.map(seriesId => {\n      const {\n        innerRadius: innerRadiusParam,\n        outerRadius: outerRadiusParam,\n        cornerRadius,\n        paddingAngle,\n        data,\n        cx: cxParam,\n        cy: cyParam,\n        highlighted,\n        faded\n      } = series[seriesId];\n      const {\n        cx,\n        cy,\n        availableRadius\n      } = getPieCoordinates({\n        cx: cxParam,\n        cy: cyParam\n      }, {\n        width,\n        height\n      });\n      const outerRadius = getPercentageValue(outerRadiusParam ?? availableRadius, availableRadius);\n      const innerRadius = getPercentageValue(innerRadiusParam ?? 0, availableRadius);\n      return /*#__PURE__*/_jsx(\"g\", {\n        className: classes.series,\n        transform: `translate(${left + cx}, ${top + cy})`,\n        \"data-series\": seriesId,\n        children: /*#__PURE__*/_jsx(PieArcPlot, {\n          innerRadius: innerRadius,\n          outerRadius: outerRadius,\n          cornerRadius: cornerRadius,\n          paddingAngle: paddingAngle,\n          id: seriesId,\n          data: data,\n          skipAnimation: skipAnimation,\n          highlighted: highlighted,\n          faded: faded,\n          onItemClick: onItemClick,\n          slots: slots,\n          slotProps: slotProps\n        })\n      }, seriesId);\n    }), seriesOrder.map(seriesId => {\n      const {\n        innerRadius: innerRadiusParam,\n        outerRadius: outerRadiusParam,\n        arcLabelRadius: arcLabelRadiusParam,\n        cornerRadius,\n        paddingAngle,\n        arcLabel,\n        arcLabelMinAngle,\n        data,\n        cx: cxParam,\n        cy: cyParam\n      } = series[seriesId];\n      const {\n        cx,\n        cy,\n        availableRadius\n      } = getPieCoordinates({\n        cx: cxParam,\n        cy: cyParam\n      }, {\n        width,\n        height\n      });\n      const outerRadius = getPercentageValue(outerRadiusParam ?? availableRadius, availableRadius);\n      const innerRadius = getPercentageValue(innerRadiusParam ?? 0, availableRadius);\n      const arcLabelRadius = arcLabelRadiusParam === undefined ? (outerRadius + innerRadius) / 2 : getPercentageValue(arcLabelRadiusParam, availableRadius);\n      return /*#__PURE__*/_jsx(\"g\", {\n        className: classes.seriesLabels,\n        transform: `translate(${left + cx}, ${top + cy})`,\n        \"data-series\": seriesId,\n        children: /*#__PURE__*/_jsx(PieArcLabelPlot, {\n          innerRadius: innerRadius,\n          outerRadius: outerRadius ?? availableRadius,\n          arcLabelRadius: arcLabelRadius,\n          cornerRadius: cornerRadius,\n          paddingAngle: paddingAngle,\n          id: seriesId,\n          data: data,\n          skipAnimation: skipAnimation,\n          arcLabel: arcLabel,\n          arcLabelMinAngle: arcLabelMinAngle,\n          slots: slots,\n          slotProps: slotProps\n        })\n      }, seriesId);\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? PiePlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Callback fired when a pie item is clicked.\n   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.\n   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.\n   * @param {DefaultizedPieValueType} item The pie item.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * If `true`, animations are skipped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PiePlot };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"slots\", \"slotProps\", \"innerRadius\", \"outerRadius\", \"cornerRadius\", \"paddingAngle\", \"id\", \"highlighted\", \"faded\", \"data\", \"onItemClick\", \"skipAnimation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { PieArc } from \"./PieArc.js\";\nimport { useTransformData } from \"./dataTransform/useTransformData.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction PieArcPlot(props) {\n  const {\n      slots,\n      slotProps,\n      innerRadius = 0,\n      outerRadius,\n      cornerRadius = 0,\n      paddingAngle = 0,\n      id,\n      highlighted,\n      faded = {\n        additionalRadius: -5\n      },\n      data,\n      onItemClick,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const transformedData = useTransformData({\n    innerRadius,\n    outerRadius,\n    cornerRadius,\n    paddingAngle,\n    id,\n    highlighted,\n    faded,\n    data\n  });\n  if (data.length === 0) {\n    return null;\n  }\n  const Arc = slots?.pieArc ?? PieArc;\n  return /*#__PURE__*/_jsx(\"g\", _extends({}, other, {\n    children: transformedData.map((item, index) => /*#__PURE__*/_jsx(Arc, _extends({\n      startAngle: item.startAngle,\n      endAngle: item.endAngle,\n      paddingAngle: item.paddingAngle,\n      innerRadius: item.innerRadius,\n      outerRadius: item.outerRadius,\n      cornerRadius: item.cornerRadius,\n      skipAnimation: skipAnimation ?? false,\n      id: id,\n      color: item.color,\n      dataIndex: index,\n      isFaded: item.isFaded,\n      isHighlighted: item.isHighlighted,\n      onClick: onItemClick && (event => {\n        onItemClick(event, {\n          type: 'pie',\n          seriesId: id,\n          dataIndex: index\n        }, item);\n      })\n    }, slotProps?.pieArc), item.dataIndex))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArcPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The radius between circle center and the arc label in px.\n   * @default (innerRadius - outerRadius) / 2\n   */\n  arcLabelRadius: PropTypes.number,\n  /**\n   * The radius applied to arc corners (similar to border radius).\n   * @default 0\n   */\n  cornerRadius: PropTypes.number,\n  data: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string.isRequired,\n    endAngle: PropTypes.number.isRequired,\n    formattedValue: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    index: PropTypes.number.isRequired,\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n    labelMarkType: PropTypes.oneOfType([PropTypes.oneOf(['circle', 'line', 'square']), PropTypes.func]),\n    padAngle: PropTypes.number.isRequired,\n    startAngle: PropTypes.number.isRequired,\n    value: PropTypes.number.isRequired\n  })).isRequired,\n  /**\n   * Override the arc attributes when it is faded.\n   * @default { additionalRadius: -5 }\n   */\n  faded: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * Override the arc attributes when it is highlighted.\n   */\n  highlighted: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * The id of this series.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The radius between circle center and the beginning of the arc.\n   * @default 0\n   */\n  innerRadius: PropTypes.number,\n  /**\n   * Callback fired when a pie item is clicked.\n   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.\n   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.\n   * @param {DefaultizedPieValueType} item The pie item.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * The radius between circle center and the end of the arc.\n   */\n  outerRadius: PropTypes.number.isRequired,\n  /**\n   * The padding angle (deg) between two arcs.\n   * @default 0\n   */\n  paddingAngle: PropTypes.number,\n  /**\n   * If `true`, animations are skipped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PieArcPlot };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"classes\", \"color\", \"dataIndex\", \"id\", \"isFaded\", \"isHighlighted\", \"onClick\", \"cornerRadius\", \"startAngle\", \"endAngle\", \"innerRadius\", \"outerRadius\", \"paddingAngle\", \"skipAnimation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport { useAnimatePieArc } from \"../hooks/index.js\";\nimport { ANIMATION_DURATION_MS, ANIMATION_TIMING_FUNCTION } from \"../internals/animation/animation.js\";\nimport { useInteractionItemProps } from \"../hooks/useInteractionItemProps.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getPieArcUtilityClass(slot) {\n  return generateUtilityClass('MuiPieArc', slot);\n}\nexport const pieArcClasses = generateUtilityClasses('MuiPieArc', ['root', 'highlighted', 'faded', 'series']);\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id,\n    isFaded,\n    isHighlighted,\n    dataIndex\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`, `data-index-${dataIndex}`, isHighlighted && 'highlighted', isFaded && 'faded']\n  };\n  return composeClasses(slots, getPieArcUtilityClass, classes);\n};\nconst PieArcRoot = styled('path', {\n  name: 'MuiPieArc',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.arc // FIXME: Inconsistent naming with slot\n})(({\n  theme\n}) => ({\n  // Got to move stroke to an element prop instead of style.\n  stroke: (theme.vars || theme).palette.background.paper,\n  transitionProperty: 'opacity, fill, filter',\n  transitionDuration: `${ANIMATION_DURATION_MS}ms`,\n  transitionTimingFunction: ANIMATION_TIMING_FUNCTION\n}));\nconst PieArc = /*#__PURE__*/React.forwardRef(function PieArc(props, ref) {\n  const {\n      classes: innerClasses,\n      color,\n      dataIndex,\n      id,\n      isFaded,\n      isHighlighted,\n      onClick,\n      cornerRadius,\n      startAngle,\n      endAngle,\n      innerRadius,\n      outerRadius,\n      paddingAngle,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    id,\n    dataIndex,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted\n  };\n  const classes = useUtilityClasses(ownerState);\n  const interactionProps = useInteractionItemProps({\n    type: 'pie',\n    seriesId: id,\n    dataIndex\n  });\n  const animatedProps = useAnimatePieArc({\n    cornerRadius,\n    startAngle,\n    endAngle,\n    innerRadius,\n    outerRadius,\n    paddingAngle,\n    skipAnimation,\n    ref\n  });\n  return /*#__PURE__*/_jsx(PieArcRoot, _extends({\n    onClick: onClick,\n    cursor: onClick ? 'pointer' : 'unset',\n    ownerState: ownerState,\n    className: classes.root,\n    fill: ownerState.color,\n    opacity: ownerState.isFaded ? 0.3 : 1,\n    filter: ownerState.isHighlighted ? 'brightness(120%)' : 'none',\n    strokeWidth: 1,\n    strokeLinejoin: \"round\",\n    \"data-highlighted\": ownerState.isHighlighted || undefined,\n    \"data-faded\": ownerState.isFaded || undefined\n  }, other, interactionProps, animatedProps));\n});\nif (process.env.NODE_ENV !== \"production\") PieArc.displayName = \"PieArc\";\nprocess.env.NODE_ENV !== \"production\" ? PieArc.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  cornerRadius: PropTypes.number.isRequired,\n  dataIndex: PropTypes.number.isRequired,\n  endAngle: PropTypes.number.isRequired,\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  innerRadius: PropTypes.number.isRequired,\n  isFaded: PropTypes.bool.isRequired,\n  isHighlighted: PropTypes.bool.isRequired,\n  outerRadius: PropTypes.number.isRequired,\n  paddingAngle: PropTypes.number.isRequired,\n  /**\n   * @default false\n   */\n  skipAnimation: PropTypes.bool.isRequired,\n  startAngle: PropTypes.number.isRequired\n} : void 0;\nexport { PieArc };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useItemHighlightedGetter } from \"../../hooks/useItemHighlightedGetter.js\";\nimport { deg2rad } from \"../../internals/angleConversion.js\";\nexport function useTransformData(series) {\n  const {\n    id: seriesId,\n    data,\n    faded,\n    highlighted,\n    paddingAngle: basePaddingAngle = 0,\n    innerRadius: baseInnerRadius = 0,\n    arcLabelRadius: baseArcLabelRadius,\n    outerRadius: baseOuterRadius,\n    cornerRadius: baseCornerRadius = 0\n  } = series;\n  const {\n    isFaded: isItemFaded,\n    isHighlighted: isItemHighlighted\n  } = useItemHighlightedGetter();\n  const dataWithHighlight = React.useMemo(() => data.map((item, itemIndex) => {\n    const currentItem = {\n      seriesId,\n      dataIndex: itemIndex\n    };\n    const isHighlighted = isItemHighlighted(currentItem);\n    const isFaded = !isHighlighted && isItemFaded(currentItem);\n    const attributesOverride = _extends({\n      additionalRadius: 0\n    }, isFaded && faded || isHighlighted && highlighted || {});\n    const paddingAngle = Math.max(0, deg2rad(attributesOverride.paddingAngle ?? basePaddingAngle));\n    const innerRadius = Math.max(0, attributesOverride.innerRadius ?? baseInnerRadius);\n    const outerRadius = Math.max(0, attributesOverride.outerRadius ?? baseOuterRadius + attributesOverride.additionalRadius);\n    const cornerRadius = attributesOverride.cornerRadius ?? baseCornerRadius;\n    const arcLabelRadius = attributesOverride.arcLabelRadius ?? baseArcLabelRadius ?? (innerRadius + outerRadius) / 2;\n    return _extends({}, item, attributesOverride, {\n      dataIndex: itemIndex,\n      isFaded,\n      isHighlighted,\n      paddingAngle,\n      innerRadius,\n      outerRadius,\n      cornerRadius,\n      arcLabelRadius\n    });\n  }), [baseCornerRadius, baseInnerRadius, baseOuterRadius, basePaddingAngle, baseArcLabelRadius, data, faded, highlighted, isItemFaded, isItemHighlighted, seriesId]);\n  return dataWithHighlight;\n}", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"arcLabel\", \"arcLabelMinAngle\", \"arcLabelRadius\", \"cornerRadius\", \"data\", \"faded\", \"highlighted\", \"id\", \"innerRadius\", \"outerRadius\", \"paddingAngle\", \"skipAnimation\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useTransformData } from \"./dataTransform/useTransformData.js\";\nimport { PieArcLabel } from \"./PieArcLabel.js\";\nimport { getLabel } from \"../internals/getLabel.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst RATIO = 180 / Math.PI;\nfunction getItemLabel(arcLabel, arcLabelMinAngle, item) {\n  if (!arcLabel) {\n    return null;\n  }\n  const angle = (item.endAngle - item.startAngle) * RATIO;\n  if (angle < arcLabelMinAngle) {\n    return null;\n  }\n  switch (arcLabel) {\n    case 'label':\n      return getLabel(item.label, 'arc');\n    case 'value':\n      return item.value?.toString();\n    case 'formattedValue':\n      return item.formattedValue;\n    default:\n      return arcLabel(_extends({}, item, {\n        label: getLabel(item.label, 'arc')\n      }));\n  }\n}\nfunction PieArcLabelPlot(props) {\n  const {\n      arcLabel,\n      arcLabelMinAngle = 0,\n      arcLabelRadius,\n      cornerRadius = 0,\n      data,\n      faded = {\n        additionalRadius: -5\n      },\n      highlighted,\n      id,\n      innerRadius,\n      outerRadius,\n      paddingAngle = 0,\n      skipAnimation,\n      slotProps,\n      slots\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const transformedData = useTransformData({\n    innerRadius,\n    outerRadius,\n    arcLabelRadius,\n    cornerRadius,\n    paddingAngle,\n    id,\n    highlighted,\n    faded,\n    data\n  });\n  if (data.length === 0) {\n    return null;\n  }\n  const ArcLabel = slots?.pieArcLabel ?? PieArcLabel;\n  return /*#__PURE__*/_jsx(\"g\", _extends({}, other, {\n    children: transformedData.map(item => /*#__PURE__*/_jsx(ArcLabel, _extends({\n      startAngle: item.startAngle,\n      endAngle: item.endAngle,\n      paddingAngle: item.paddingAngle,\n      innerRadius: item.innerRadius,\n      outerRadius: item.outerRadius,\n      arcLabelRadius: item.arcLabelRadius,\n      cornerRadius: item.cornerRadius,\n      id: id,\n      color: item.color,\n      isFaded: item.isFaded,\n      isHighlighted: item.isHighlighted,\n      formattedArcLabel: getItemLabel(arcLabel, arcLabelMinAngle, item),\n      skipAnimation: skipAnimation ?? false\n    }, slotProps?.pieArcLabel), item.id ?? item.dataIndex))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? PieArcLabelPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The label displayed into the arc.\n   */\n  arcLabel: PropTypes.oneOfType([PropTypes.oneOf(['formattedValue', 'label', 'value']), PropTypes.func]),\n  /**\n   * The minimal angle required to display the arc label.\n   * @default 0\n   */\n  arcLabelMinAngle: PropTypes.number,\n  /**\n   * The radius between circle center and the arc label in px.\n   * @default (innerRadius - outerRadius) / 2\n   */\n  arcLabelRadius: PropTypes.number,\n  /**\n   * The radius applied to arc corners (similar to border radius).\n   * @default 0\n   */\n  cornerRadius: PropTypes.number,\n  data: PropTypes.arrayOf(PropTypes.shape({\n    color: PropTypes.string.isRequired,\n    endAngle: PropTypes.number.isRequired,\n    formattedValue: PropTypes.string.isRequired,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    index: PropTypes.number.isRequired,\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.string]),\n    labelMarkType: PropTypes.oneOfType([PropTypes.oneOf(['circle', 'line', 'square']), PropTypes.func]),\n    padAngle: PropTypes.number.isRequired,\n    startAngle: PropTypes.number.isRequired,\n    value: PropTypes.number.isRequired\n  })).isRequired,\n  /**\n   * Override the arc attributes when it is faded.\n   * @default { additionalRadius: -5 }\n   */\n  faded: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * Override the arc attributes when it is highlighted.\n   */\n  highlighted: PropTypes.shape({\n    additionalRadius: PropTypes.number,\n    arcLabelRadius: PropTypes.number,\n    color: PropTypes.string,\n    cornerRadius: PropTypes.number,\n    innerRadius: PropTypes.number,\n    outerRadius: PropTypes.number,\n    paddingAngle: PropTypes.number\n  }),\n  /**\n   * The id of this series.\n   */\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The radius between circle center and the beginning of the arc.\n   * @default 0\n   */\n  innerRadius: PropTypes.number,\n  /**\n   * The radius between circle center and the end of the arc.\n   */\n  outerRadius: PropTypes.number.isRequired,\n  /**\n   * The padding angle (deg) between two arcs.\n   * @default 0\n   */\n  paddingAngle: PropTypes.number,\n  /**\n   * If `true`, animations are skipped.\n   * @default false\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { PieArcLabelPlot };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"classes\", \"color\", \"startAngle\", \"endAngle\", \"paddingAngle\", \"arcLabelRadius\", \"innerRadius\", \"outerRadius\", \"cornerRadius\", \"formattedArcLabel\", \"isHighlighted\", \"isFaded\", \"style\", \"skipAnimation\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport { styled } from '@mui/material/styles';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport { ANIMATION_DURATION_MS, ANIMATION_TIMING_FUNCTION } from \"../internals/animation/animation.js\";\nimport { useAnimatePieArcLabel } from \"../hooks/animation/useAnimatePieArcLabel.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function getPieArcLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiPieArcLabel', slot);\n}\nexport const pieArcLabelClasses = generateUtilityClasses('MuiPieArcLabel', ['root', 'highlighted', 'faded', 'animate', 'series']);\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id,\n    isFaded,\n    isHighlighted,\n    skipAnimation\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`, isHighlighted && 'highlighted', isFaded && 'faded', !skipAnimation && 'animate']\n  };\n  return composeClasses(slots, getPieArcLabelUtilityClass, classes);\n};\nconst PieArcLabelRoot = styled('text', {\n  name: 'MuiPieArcLabel',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  fill: (theme.vars || theme).palette.text.primary,\n  textAnchor: 'middle',\n  dominantBaseline: 'middle',\n  pointerEvents: 'none',\n  animationName: 'animate-opacity',\n  animationDuration: '0s',\n  animationTimingFunction: ANIMATION_TIMING_FUNCTION,\n  [`&.${pieArcLabelClasses.animate}`]: {\n    animationDuration: `${ANIMATION_DURATION_MS}ms`\n  },\n  '@keyframes animate-opacity': {\n    from: {\n      opacity: 0\n    }\n  }\n}));\nconst PieArcLabel = /*#__PURE__*/React.forwardRef(function PieArcLabel(props, ref) {\n  const {\n      id,\n      classes: innerClasses,\n      color,\n      startAngle,\n      endAngle,\n      paddingAngle,\n      arcLabelRadius,\n      cornerRadius,\n      formattedArcLabel,\n      isHighlighted,\n      isFaded,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = {\n    id,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted,\n    skipAnimation\n  };\n  const classes = useUtilityClasses(ownerState);\n  const animatedProps = useAnimatePieArcLabel({\n    cornerRadius,\n    startAngle,\n    endAngle,\n    innerRadius: arcLabelRadius,\n    outerRadius: arcLabelRadius,\n    paddingAngle,\n    skipAnimation,\n    ref\n  });\n  return /*#__PURE__*/_jsx(PieArcLabelRoot, _extends({\n    className: classes.root\n  }, other, animatedProps, {\n    children: formattedArcLabel\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") PieArcLabel.displayName = \"PieArcLabel\";\nprocess.env.NODE_ENV !== \"production\" ? PieArcLabel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  arcLabelRadius: PropTypes.number.isRequired,\n  classes: PropTypes.object,\n  color: PropTypes.string.isRequired,\n  cornerRadius: PropTypes.number.isRequired,\n  endAngle: PropTypes.number.isRequired,\n  formattedArcLabel: PropTypes.string,\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  innerRadius: PropTypes.number.isRequired,\n  isFaded: PropTypes.bool.isRequired,\n  isHighlighted: PropTypes.bool.isRequired,\n  outerRadius: PropTypes.number.isRequired,\n  paddingAngle: PropTypes.number.isRequired,\n  skipAnimation: PropTypes.bool.isRequired,\n  startAngle: PropTypes.number.isRequired\n} : void 0;\nexport { PieArcLabel };", "/**\n * Helper that converts values and percentages into values.\n * @param value The value provided by the developer. Can either be a number or a string with '%' or 'px'.\n * @param refValue The numerical value associated to 100%.\n * @returns The numerical value associated to the provided value.\n */\nexport function getPercentageValue(value, refValue) {\n  if (typeof value === 'number') {\n    return value;\n  }\n  if (value === '100%') {\n    // Avoid potential rounding issues\n    return refValue;\n  }\n  if (value.endsWith('%')) {\n    const percentage = Number.parseFloat(value.slice(0, value.length - 1));\n    if (!Number.isNaN(percentage)) {\n      return percentage * refValue / 100;\n    }\n  }\n  if (value.endsWith('px')) {\n    const val = Number.parseFloat(value.slice(0, value.length - 2));\n    if (!Number.isNaN(val)) {\n      return val;\n    }\n  }\n  throw new Error(`MUI X Charts: Received an unknown value \"${value}\". It should be a number, or a string with a percentage value.`);\n}", "import { getPercentageValue } from \"../internals/getPercentageValue.js\";\nexport function getPieCoordinates(series, drawing) {\n  const {\n    height,\n    width\n  } = drawing;\n  const {\n    cx: cxParam,\n    cy: cyParam\n  } = series;\n  const availableRadius = Math.min(width, height) / 2;\n  const cx = getPercentageValue(cxParam ?? '50%', width);\n  const cy = getPercentageValue(cyParam ?? '50%', height);\n  return {\n    cx,\n    cy,\n    availableRadius\n  };\n}", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getPieUtilityClass(slot) {\n  return generateUtilityClass('Mui<PERSON><PERSON><PERSON><PERSON>', slot);\n}\nexport const pieClasses = generateUtilityClasses('MuiPie<PERSON>hart', ['root', 'series', 'seriesLabels']);\nexport const useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    series: ['series'],\n    seriesLabels: ['seriesLabels']\n  };\n  return composeClasses(slots, getPieUtilityClass, classes);\n};", "import { useChartInteraction } from \"../internals/plugins/featurePlugins/useChartInteraction/index.js\";\nimport { useChartHighlight } from \"../internals/plugins/featurePlugins/useChartHighlight/index.js\";\nexport const PIE_CHART_PLUGINS = [useChartInteraction, useChartHighlight];"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAAA,SAAuB;AACvB,IAAAC,qBAAsB;;;ACJtB,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACEtB,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACDtB,YAAuB;AACvB,wBAAsB;AAQtB,yBAA4B;AAV5B,IAAM,YAAY,CAAC,WAAW,SAAS,aAAa,MAAM,WAAW,iBAAiB,WAAW,gBAAgB,cAAc,YAAY,eAAe,eAAe,gBAAgB,eAAe;AAWjM,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACO,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,eAAe,SAAS,QAAQ,CAAC;AAC3G,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,IAAI,cAAc,SAAS,IAAI,iBAAiB,eAAe,WAAW,OAAO;AAAA,EAC9G;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AACA,IAAM,aAAa,eAAO,QAAQ;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,GAAG,WAAW,OAAO;AAAA;AAC3C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA;AAAA,EAEL,SAAS,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,EACjD,oBAAoB;AAAA,EACpB,oBAAoB,GAAG,qBAAqB;AAAA,EAC5C,0BAA0B;AAC5B,EAAE;AACF,IAAM,SAA4B,iBAAW,SAASC,QAAO,OAAO,KAAK;AACvE,QAAM;AAAA,IACF,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,mBAAmB,wBAAwB;AAAA,IAC/C,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,iBAAiB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,KAAK,YAAY,SAAS;AAAA,IAC5C;AAAA,IACA,QAAQ,UAAU,YAAY;AAAA,IAC9B;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,MAAM,WAAW;AAAA,IACjB,SAAS,WAAW,UAAU,MAAM;AAAA,IACpC,QAAQ,WAAW,gBAAgB,qBAAqB;AAAA,IACxD,aAAa;AAAA,IACb,gBAAgB;AAAA,IAChB,oBAAoB,WAAW,iBAAiB;AAAA,IAChD,cAAc,WAAW,WAAW;AAAA,EACtC,GAAG,OAAO,kBAAkB,aAAa,CAAC;AAC5C,CAAC;AACD,IAAI,KAAuC,QAAO,cAAc;AAChE,OAAwC,OAAO,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzD,SAAS,kBAAAC,QAAU;AAAA,EACnB,cAAc,kBAAAA,QAAU,OAAO;AAAA,EAC/B,WAAW,kBAAAA,QAAU,OAAO;AAAA,EAC5B,UAAU,kBAAAA,QAAU,OAAO;AAAA,EAC3B,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAC9D,aAAa,kBAAAA,QAAU,OAAO;AAAA,EAC9B,SAAS,kBAAAA,QAAU,KAAK;AAAA,EACxB,eAAe,kBAAAA,QAAU,KAAK;AAAA,EAC9B,aAAa,kBAAAA,QAAU,OAAO;AAAA,EAC9B,cAAc,kBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI/B,eAAe,kBAAAA,QAAU,KAAK;AAAA,EAC9B,YAAY,kBAAAA,QAAU,OAAO;AAC/B,IAAI;;;ACvHJ,IAAAC,SAAuB;AAGhB,SAAS,iBAAiB,QAAQ;AACvC,QAAM;AAAA,IACJ,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc,mBAAmB;AAAA,IACjC,aAAa,kBAAkB;AAAA,IAC/B,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,cAAc,mBAAmB;AAAA,EACnC,IAAI;AACJ,QAAM;AAAA,IACJ,SAAS;AAAA,IACT,eAAe;AAAA,EACjB,IAAI,yBAAyB;AAC7B,QAAM,oBAA0B,eAAQ,MAAM,KAAK,IAAI,CAAC,MAAM,cAAc;AAC1E,UAAM,cAAc;AAAA,MAClB;AAAA,MACA,WAAW;AAAA,IACb;AACA,UAAM,gBAAgB,kBAAkB,WAAW;AACnD,UAAM,UAAU,CAAC,iBAAiB,YAAY,WAAW;AACzD,UAAM,qBAAqB,SAAS;AAAA,MAClC,kBAAkB;AAAA,IACpB,GAAG,WAAW,SAAS,iBAAiB,eAAe,CAAC,CAAC;AACzD,UAAM,eAAe,KAAK,IAAI,GAAG,QAAQ,mBAAmB,gBAAgB,gBAAgB,CAAC;AAC7F,UAAM,cAAc,KAAK,IAAI,GAAG,mBAAmB,eAAe,eAAe;AACjF,UAAM,cAAc,KAAK,IAAI,GAAG,mBAAmB,eAAe,kBAAkB,mBAAmB,gBAAgB;AACvH,UAAM,eAAe,mBAAmB,gBAAgB;AACxD,UAAM,iBAAiB,mBAAmB,kBAAkB,uBAAuB,cAAc,eAAe;AAChH,WAAO,SAAS,CAAC,GAAG,MAAM,oBAAoB;AAAA,MAC5C,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,CAAC,kBAAkB,iBAAiB,iBAAiB,kBAAkB,oBAAoB,MAAM,OAAO,aAAa,aAAa,mBAAmB,QAAQ,CAAC;AAClK,SAAO;AACT;;;AFxCA,IAAAC,sBAA4B;AAL5B,IAAMC,aAAY,CAAC,SAAS,aAAa,eAAe,eAAe,gBAAgB,gBAAgB,MAAM,eAAe,SAAS,QAAQ,eAAe,eAAe;AAM3K,SAAS,WAAW,OAAO;AACzB,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA,eAAe;AAAA,IACf,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOA,UAAS;AACxD,QAAM,kBAAkB,iBAAiB;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,OAAM,+BAAO,WAAU;AAC7B,aAAoB,oBAAAC,KAAK,KAAK,SAAS,CAAC,GAAG,OAAO;AAAA,IAChD,UAAU,gBAAgB,IAAI,CAAC,MAAM,cAAuB,oBAAAA,KAAK,KAAK,SAAS;AAAA,MAC7E,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,cAAc,KAAK;AAAA,MACnB,eAAe,iBAAiB;AAAA,MAChC;AAAA,MACA,OAAO,KAAK;AAAA,MACZ,WAAW;AAAA,MACX,SAAS,KAAK;AAAA,MACd,eAAe,KAAK;AAAA,MACpB,SAAS,gBAAgB,WAAS;AAChC,oBAAY,OAAO;AAAA,UACjB,MAAM;AAAA,UACN,UAAU;AAAA,UACV,WAAW;AAAA,QACb,GAAG,IAAI;AAAA,MACT;AAAA,IACF,GAAG,uCAAW,MAAM,GAAG,KAAK,SAAS,CAAC;AAAA,EACxC,CAAC,CAAC;AACJ;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7D,gBAAgB,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,cAAc,mBAAAA,QAAU;AAAA,EACxB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtC,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,gBAAgB,mBAAAA,QAAU,OAAO;AAAA,IACjC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,eAAe,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClG,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,YAAY,mBAAAA,QAAU,OAAO;AAAA,IAC7B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,aAAa,mBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AGzJJ,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACDtB,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AAOtB,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,MAAM,WAAW,SAAS,cAAc,YAAY,gBAAgB,kBAAkB,eAAe,eAAe,gBAAgB,qBAAqB,iBAAiB,WAAW,SAAS,eAAe;AAUzN,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACO,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,QAAQ,eAAe,SAAS,WAAW,QAAQ,CAAC;AAChI,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,IAAI,iBAAiB,eAAe,WAAW,SAAS,CAAC,iBAAiB,SAAS;AAAA,EAChH;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACA,IAAM,kBAAkB,eAAO,QAAQ;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EACzC,YAAY;AAAA,EACZ,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,CAAC,KAAK,mBAAmB,OAAO,EAAE,GAAG;AAAA,IACnC,mBAAmB,GAAG,qBAAqB;AAAA,EAC7C;AAAA,EACA,8BAA8B;AAAA,IAC5B,MAAM;AAAA,MACJ,SAAS;AAAA,IACX;AAAA,EACF;AACF,EAAE;AACF,IAAM,cAAiC,kBAAW,SAASC,aAAY,OAAO,KAAK;AACjF,QAAM;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOF,UAAS;AACxD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAUC,mBAAkB,UAAU;AAC5C,QAAM,gBAAgB,sBAAsB;AAAA,IAC1C;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAE,KAAK,iBAAiB,SAAS;AAAA,IACjD,WAAW,QAAQ;AAAA,EACrB,GAAG,OAAO,eAAe;AAAA,IACvB,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,aAAY,cAAc;AACrE,OAAwC,YAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,gBAAgB,mBAAAC,QAAU,OAAO;AAAA,EACjC,SAAS,mBAAAA,QAAU;AAAA,EACnB,OAAO,mBAAAA,QAAU,OAAO;AAAA,EACxB,cAAc,mBAAAA,QAAU,OAAO;AAAA,EAC/B,UAAU,mBAAAA,QAAU,OAAO;AAAA,EAC3B,mBAAmB,mBAAAA,QAAU;AAAA,EAC7B,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAC9D,aAAa,mBAAAA,QAAU,OAAO;AAAA,EAC9B,SAAS,mBAAAA,QAAU,KAAK;AAAA,EACxB,eAAe,mBAAAA,QAAU,KAAK;AAAA,EAC9B,aAAa,mBAAAA,QAAU,OAAO;AAAA,EAC9B,cAAc,mBAAAA,QAAU,OAAO;AAAA,EAC/B,eAAe,mBAAAA,QAAU,KAAK;AAAA,EAC9B,YAAY,mBAAAA,QAAU,OAAO;AAC/B,IAAI;;;ADxGJ,IAAAC,sBAA4B;AAN5B,IAAMC,aAAY,CAAC,YAAY,oBAAoB,kBAAkB,gBAAgB,QAAQ,SAAS,eAAe,MAAM,eAAe,eAAe,gBAAgB,iBAAiB,aAAa,OAAO;AAO9M,IAAM,QAAQ,MAAM,KAAK;AACzB,SAAS,aAAa,UAAU,kBAAkB,MAAM;AAZxD;AAaE,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,WAAW,KAAK,cAAc;AAClD,MAAI,QAAQ,kBAAkB;AAC5B,WAAO;AAAA,EACT;AACA,UAAQ,UAAU;AAAA,IAChB,KAAK;AACH,aAAO,SAAS,KAAK,OAAO,KAAK;AAAA,IACnC,KAAK;AACH,cAAO,UAAK,UAAL,mBAAY;AAAA,IACrB,KAAK;AACH,aAAO,KAAK;AAAA,IACd;AACE,aAAO,SAAS,SAAS,CAAC,GAAG,MAAM;AAAA,QACjC,OAAO,SAAS,KAAK,OAAO,KAAK;AAAA,MACnC,CAAC,CAAC;AAAA,EACN;AACF;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM;AAAA,IACF;AAAA,IACA,mBAAmB;AAAA,IACnB;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,kBAAkB;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOA,UAAS;AACxD,QAAM,kBAAkB,iBAAiB;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AACA,QAAM,YAAW,+BAAO,gBAAe;AACvC,aAAoB,oBAAAC,KAAK,KAAK,SAAS,CAAC,GAAG,OAAO;AAAA,IAChD,UAAU,gBAAgB,IAAI,cAAqB,oBAAAA,KAAK,UAAU,SAAS;AAAA,MACzE,YAAY,KAAK;AAAA,MACjB,UAAU,KAAK;AAAA,MACf,cAAc,KAAK;AAAA,MACnB,aAAa,KAAK;AAAA,MAClB,aAAa,KAAK;AAAA,MAClB,gBAAgB,KAAK;AAAA,MACrB,cAAc,KAAK;AAAA,MACnB;AAAA,MACA,OAAO,KAAK;AAAA,MACZ,SAAS,KAAK;AAAA,MACd,eAAe,KAAK;AAAA,MACpB,mBAAmB,aAAa,UAAU,kBAAkB,IAAI;AAAA,MAChE,eAAe,iBAAiB;AAAA,IAClC,GAAG,uCAAW,WAAW,GAAG,KAAK,MAAM,KAAK,SAAS,CAAC;AAAA,EACxD,CAAC,CAAC;AACJ;AACA,OAAwC,gBAAgB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlE,UAAU,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,kBAAkB,SAAS,OAAO,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrG,kBAAkB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1B,cAAc,mBAAAA,QAAU;AAAA,EACxB,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACtC,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,gBAAgB,mBAAAA,QAAU,OAAO;AAAA,IACjC,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,OAAO,mBAAAA,QAAU,OAAO;AAAA,IACxB,OAAO,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,eAAe,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,IAClG,UAAU,mBAAAA,QAAU,OAAO;AAAA,IAC3B,YAAY,mBAAAA,QAAU,OAAO;AAAA,IAC7B,OAAO,mBAAAA,QAAU,OAAO;AAAA,EAC1B,CAAC,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKJ,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,aAAa,mBAAAA,QAAU,MAAM;AAAA,IAC3B,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,gBAAgB,mBAAAA,QAAU;AAAA,IAC1B,OAAO,mBAAAA,QAAU;AAAA,IACjB,cAAc,mBAAAA,QAAU;AAAA,IACxB,aAAa,mBAAAA,QAAU;AAAA,IACvB,aAAa,mBAAAA,QAAU;AAAA,IACvB,cAAc,mBAAAA,QAAU;AAAA,EAC1B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AE9KG,SAAS,mBAAmB,OAAO,UAAU;AAClD,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;AAAA,EACT;AACA,MAAI,UAAU,QAAQ;AAEpB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,SAAS,GAAG,GAAG;AACvB,UAAM,aAAa,OAAO,WAAW,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,CAAC;AACrE,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,aAAO,aAAa,WAAW;AAAA,IACjC;AAAA,EACF;AACA,MAAI,MAAM,SAAS,IAAI,GAAG;AACxB,UAAM,MAAM,OAAO,WAAW,MAAM,MAAM,GAAG,MAAM,SAAS,CAAC,CAAC;AAC9D,QAAI,CAAC,OAAO,MAAM,GAAG,GAAG;AACtB,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,IAAI,MAAM,4CAA4C,KAAK,gEAAgE;AACnI;;;AC1BO,SAAS,kBAAkB,QAAQ,SAAS;AACjD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,IAAI;AAAA,IACJ,IAAI;AAAA,EACN,IAAI;AACJ,QAAM,kBAAkB,KAAK,IAAI,OAAO,MAAM,IAAI;AAClD,QAAM,KAAK,mBAAmB,WAAW,OAAO,KAAK;AACrD,QAAM,KAAK,mBAAmB,WAAW,OAAO,MAAM;AACtD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACfO,SAAS,mBAAmB,MAAM;AACvC,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACO,IAAM,aAAa,uBAAuB,eAAe,CAAC,QAAQ,UAAU,cAAc,CAAC;AAC3F,IAAMC,qBAAoB,aAAW;AAC1C,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,QAAQ,CAAC,QAAQ;AAAA,IACjB,cAAc,CAAC,cAAc;AAAA,EAC/B;AACA,SAAO,eAAe,OAAO,oBAAoB,OAAO;AAC1D;;;ARFA,IAAAC,sBAA2C;AAW3C,SAAS,QAAQ,OAAO;AACtB,QAAM;AAAA,IACJ,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,aAAa,oBAAoB;AACvC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,gBAAgB,iBAAiB,eAAe;AACtD,QAAM,UAAUC,mBAAkB;AAClC,MAAI,eAAe,QAAW;AAC5B,WAAO;AAAA,EACT;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,aAAoB,oBAAAC,MAAM,KAAK;AAAA,IAC7B,UAAU,CAAC,YAAY,IAAI,cAAY;AACrC,YAAM;AAAA,QACJ,aAAa;AAAA,QACb,aAAa;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,kBAAkB;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI;AAAA,MACN,GAAG;AAAA,QACD;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,cAAc,mBAAmB,oBAAoB,iBAAiB,eAAe;AAC3F,YAAM,cAAc,mBAAmB,oBAAoB,GAAG,eAAe;AAC7E,iBAAoB,oBAAAC,KAAK,KAAK;AAAA,QAC5B,WAAW,QAAQ;AAAA,QACnB,WAAW,aAAa,OAAO,EAAE,KAAK,MAAM,EAAE;AAAA,QAC9C,eAAe;AAAA,QACf,cAAuB,oBAAAA,KAAK,YAAY;AAAA,UACtC;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC,GAAG,YAAY,IAAI,cAAY;AAC9B,YAAM;AAAA,QACJ,aAAa;AAAA,QACb,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,IAAI;AAAA,QACJ,IAAI;AAAA,MACN,IAAI,OAAO,QAAQ;AACnB,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,kBAAkB;AAAA,QACpB,IAAI;AAAA,QACJ,IAAI;AAAA,MACN,GAAG;AAAA,QACD;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,cAAc,mBAAmB,oBAAoB,iBAAiB,eAAe;AAC3F,YAAM,cAAc,mBAAmB,oBAAoB,GAAG,eAAe;AAC7E,YAAM,iBAAiB,wBAAwB,UAAa,cAAc,eAAe,IAAI,mBAAmB,qBAAqB,eAAe;AACpJ,iBAAoB,oBAAAA,KAAK,KAAK;AAAA,QAC5B,WAAW,QAAQ;AAAA,QACnB,WAAW,aAAa,OAAO,EAAE,KAAK,MAAM,EAAE;AAAA,QAC9C,eAAe;AAAA,QACf,cAAuB,oBAAAA,KAAK,iBAAiB;AAAA,UAC3C;AAAA,UACA,aAAa,eAAe;AAAA,UAC5B;AAAA,UACA;AAAA,UACA;AAAA,UACA,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAW1D,aAAa,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;ASrKG,IAAM,oBAAoB,CAAC,qBAAqB,iBAAiB;;;AViBxE,IAAAC,sBAA2C;AAf3C,IAAMC,aAAY,CAAC,UAAU,SAAS,UAAU,UAAU,UAAU,MAAM,iBAAiB,cAAc,YAAY,SAAS,aAAa,eAAe,WAAW,mBAAmB,qBAAqB,aAAa,aAAa;AA0BvO,IAAM,WAA8B,kBAAW,SAASC,UAAS,SAAS,KAAK;AA9B/E;AA+BE,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM,SAAS,iBAAiB,aAAa,wBAAwB;AACrE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,uBAAuB,SAAS,CAAC,GAAG,OAAO;AAAA,IAC7C,QAAQ,OAAO,IAAI,OAAK,SAAS;AAAA,MAC/B,MAAM;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,CAAC,GAAG,GAAG;AACP,QAAM,WAAU,+BAAO,YAAW;AAClC,QAAM,WAAU,WAAM,UAAN,mBAAa;AAC7B,aAAoB,oBAAAE,KAAK,mBAAmB,SAAS,CAAC,GAAG,wBAAwB;AAAA,IAC/E,cAAuB,oBAAAC,MAAM,eAAe;AAAA,MAC1C,iBAAgB,iBAAM,cAAN,mBAAiB,WAAjB,mBAAyB;AAAA,MACzC,mBAAiB,0CAAO,cAAP,mBAAkB,WAAlB,mBAA0B,cAAa;AAAA,MACxD;AAAA,MACA,UAAU,CAAC,eAAe,cAAuB,oBAAAD,KAAK,SAAS,SAAS,CAAC,IAAG,WAAM,cAAN,mBAAiB,OAAO,CAAC,IAAI,MAAM,CAAC,kBAA2B,oBAAAA,KAAK,cAAc;AAAA,QAC5J,aAAW,0CAAO,cAAP,mBAAkB,WAAlB,mBAA0B,cAAa;AAAA,QAClD;AAAA,QACA;AAAA,MACF,CAAC,OAAgB,oBAAAC,MAAM,eAAe,SAAS,CAAC,GAAG,oBAAoB;AAAA,QACrE,UAAU,KAAc,oBAAAD,KAAK,SAAS;AAAA,UACpC;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,OAAgB,oBAAAA,KAAK,eAAe;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,QACF,CAAC,GAAG,QAAQ;AAAA,MACd,CAAC,CAAC,GAAG,CAAC,eAAwB,oBAAAA,KAAK,SAAS,SAAS;AAAA,QACnD,SAAS;AAAA,MACX,GAAG,uCAAW,OAAO,CAAC,CAAC;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,UAAS,cAAc;AAClE,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,QAAQ,mBAAAE,QAAU,MAAM;AAAA,IACtB,SAAS,mBAAAA,QAAU;AAAA,EACrB,CAAC;AAAA,EACD,UAAU,mBAAAA,QAAU;AAAA,EACpB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,SAAS,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,QAAQ,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,iBAAiB,mBAAAA,QAAU,MAAM;AAAA,IAC/B,WAAW,mBAAAA,QAAU;AAAA,IACrB,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EACtE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,IAAI,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtB,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IAC7D,QAAQ,mBAAAA,QAAU;AAAA,IAClB,MAAM,mBAAAA,QAAU;AAAA,IAChB,OAAO,mBAAAA,QAAU;AAAA,IACjB,KAAK,mBAAAA,QAAU;AAAA,EACjB,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMH,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,QAAQ,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5C,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA,EACjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,mBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,EACxC,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,mBAAAA,QAAU;AACnB,IAAI;", "names": ["React", "import_prop_types", "React", "import_prop_types", "React", "import_prop_types", "PieArc", "_jsx", "PropTypes", "React", "import_jsx_runtime", "_excluded", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "import_jsx_runtime", "_excluded", "useUtilityClasses", "PieArcLabel", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "_jsx", "PropTypes", "useUtilityClasses", "import_jsx_runtime", "useUtilityClasses", "_jsxs", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "<PERSON><PERSON><PERSON>", "_jsx", "_jsxs", "PropTypes"]}