import { Document } from 'mongoose';
import * as mongoose from 'mongoose';
import { Asset } from './asset.schema';
import { Account } from './account.schema';
export declare class UserInfos extends Document {
    userId: Account;
    fullName: string;
    dob: string;
    phone: string;
    address: string;
    avatar: Asset;
    lastLoginAt: Date;
    flaggedCount: number;
    banReason: string;
    bannedAt: Date;
}
export declare const UserInfosSchema: mongoose.Schema<UserInfos, mongoose.Model<UserInfos, any, any, any, Document<unknown, any, UserInfos, any> & UserInfos & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, UserInfos, Document<unknown, {}, mongoose.FlatRecord<UserInfos>, {}> & mongoose.FlatRecord<UserInfos> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
