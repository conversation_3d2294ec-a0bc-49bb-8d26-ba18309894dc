"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateBlogDraftDto = void 0;
const class_validator_1 = require("class-validator");
class UpdateBlogDraftDto {
    title;
    content;
    summary;
    tags;
}
exports.UpdateBlogDraftDto = UpdateBlogDraftDto;
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là một chuỗi.' }),
    (0, class_validator_1.Length)(5, 100, {
        message: 'Tiêu đề phải có độ dài từ 5 đến 100 ký tự.',
    }),
    __metadata("design:type", String)
], UpdateBlogDraftDto.prototype, "title", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Nội dung phải là một chuỗi.' }),
    (0, class_validator_1.Length)(0, 50000, {
        message: 'Nội dung không được vượt quá 50000 ký tự.',
    }),
    __metadata("design:type", String)
], UpdateBlogDraftDto.prototype, "content", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Tóm tắt phải là một chuỗi.' }),
    (0, class_validator_1.MaxLength)(300, {
        message: 'Tóm tắt không được vượt quá 300 ký tự.',
    }),
    __metadata("design:type", String)
], UpdateBlogDraftDto.prototype, "summary", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Tags phải là một mảng chuỗi.' }),
    (0, class_validator_1.ArrayMaxSize)(10, {
        message: 'Tối đa chỉ được 10 tags.',
    }),
    (0, class_validator_1.IsString)({ each: true, message: 'Mỗi tag phải là một chuỗi.' }),
    __metadata("design:type", Array)
], UpdateBlogDraftDto.prototype, "tags", void 0);
//# sourceMappingURL=update-blog-draft.dto.js.map