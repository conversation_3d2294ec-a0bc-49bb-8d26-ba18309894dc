import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "@mui/icons-material";
import { Avatar, IconButton, Tooltip } from "@mui/material";
import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAuthStore } from "../../services/stores/useAuthStore";

interface NavProps {
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  menuItems: MenuItem[];
}

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  path: string;
}

export const Nav: React.FC<NavProps> = ({
  collapsed,
  setCollapsed,
  menuItems,
}) => {
  const { info, user, handleLogout } = useAuthStore();
  const navigate = useNavigate();
  const handleProfile = () => {
    navigate("/profile");
  };
  return (
    <div
      className={`fixed left-0 top-0 h-screen text-gray-800 flex flex-col transition-all duration-300 z-50 bg-white border-r border-gray-200 shadow-sm ${
        collapsed ? "w-20" : "w-64"
      }`}
    >
      <div
        className={`flex items-center ${
          collapsed ? "justify-center" : "justify-between"
        } px-4 py-4 border-b border-gray-100`}
      >
        {!collapsed && (
          <div className="flex items-center gap-2">
            <Link
              to="/"
              className="hover:scale-105 transition-all  duration-200 hover:shadow-md rounded-full"
            >
              <img
                src="/icons/icons8-around-the-globe-50.png"
                alt="Logo"
                className="h-8"
              />
            </Link>
            <h1 className="text-xl font-bold ">VieJourney</h1>
          </div>
        )}
        <IconButton
          onClick={() => setCollapsed(!collapsed)}
          className="text-gray-600 hover:bg-gray-100 w-fit"
        >
          {collapsed ? <MenuIcon /> : <ChevronLeft />}
        </IconButton>
      </div>

      {/* Navigation Menu */}
      <div className="flex flex-col space-y-1 mt-4 px-3 flex-1">
        {menuItems.map((item, index) => (
          <Tooltip
            key={index}
            title={collapsed ? item.label : ""}
            placement="right"
          >
            <div
              onClick={() => navigate(item.path)}
              className={`flex items-center px-3 py-3  hover:bg-blue-50 hover:text-blue-600 cursor-pointer transition-all group ${
                collapsed ? "justify-center" : ""
              }`}
            >
              <div className="text-gray-600 group-hover:text-blue-600">
                {item.icon}
              </div>
              {!collapsed && (
                <span className="ml-3 font-medium text-gray-700 group-hover:text-blue-600">
                  {item.label}
                </span>
              )}
            </div>
          </Tooltip>
        ))}
      </div>

      {/* Account Section */}
      <div className="mt-auto border-t border-gray-100">
        {!collapsed ? (
          <div className="p-4">
            <div className="flex items-center gap-3 mb-3">
              <Avatar
                src={info?.avatar}
                alt={info?.fullName}
                sx={{ width: 40, height: 40 }}
                className="border-2 border-gray-200"
              >
                {info?.fullName?.charAt(0)}
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {info?.fullName}
                </p>
                <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                <p className="text-xs text-blue-600 font-medium">
                  {user?.role}
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <Tooltip title="Profile Settings">
                <button
                  onClick={handleProfile}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50  transition-all"
                >
                  <Settings sx={{ fontSize: 16 }} />
                  Profile
                </button>
              </Tooltip>

              <Tooltip title="Sign Out">
                <button
                  onClick={handleLogout}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50  transition-all"
                >
                  <Logout sx={{ fontSize: 16 }} />
                  Logout
                </button>
              </Tooltip>
            </div>
          </div>
        ) : (
          <div className="p-2 flex flex-col gap-2">
            <Tooltip title={`${info?.fullName} - Profile`} placement="right">
              <div className="flex justify-center">
                <Avatar
                  src={info?.avatar}
                  alt={info?.fullName}
                  sx={{ width: 32, height: 32 }}
                  className="border-2 border-gray-200 cursor-pointer hover:border-blue-300 transition-all"
                  onClick={handleProfile}
                >
                  {info?.fullName?.charAt(0)}
                </Avatar>
              </div>
            </Tooltip>

            <Tooltip title="Profile Settings" placement="right">
              <IconButton
                onClick={handleProfile}
                size="small"
                className="text-gray-600 hover:text-blue-600 hover:bg-blue-50"
              >
                <Settings sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>

            <Tooltip title="Sign Out" placement="right">
              <IconButton
                onClick={handleLogout}
                size="small"
                className="text-gray-600 hover:text-red-600 hover:bg-red-50"
              >
                <Logout sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>
          </div>
        )}
      </div>
    </div>
  );
};
