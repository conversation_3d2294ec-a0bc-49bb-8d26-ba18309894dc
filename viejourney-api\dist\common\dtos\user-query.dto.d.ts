export declare enum UserRole {
    USER = "USER",
    ADMIN = "ADMIN",
    MANAGER = "MANAGER"
}
export declare enum UserStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    BANNED = "BANNED",
    DELETED = "DELETED"
}
export declare class UserQueryDto {
    role?: UserRole;
    status?: UserStatus;
    username?: string;
    userId?: string;
    email?: string;
    page?: number;
    pageSize?: number;
}
