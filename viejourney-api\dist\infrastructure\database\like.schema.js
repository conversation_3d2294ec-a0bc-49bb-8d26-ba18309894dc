"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LikeSchema = exports.Like = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const userinfo_schema_1 = require("./userinfo.schema");
const blog_schema_1 = require("./blog.schema");
let Like = class Like extends mongoose_2.Document {
    userId;
    blogId;
};
exports.Like = Like;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.default.Types.ObjectId, ref: 'UserInfos' }),
    __metadata("design:type", userinfo_schema_1.UserInfos)
], Like.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.default.Types.ObjectId, ref: 'Blog' }),
    __metadata("design:type", blog_schema_1.Blog)
], Like.prototype, "blogId", void 0);
exports.Like = Like = __decorate([
    (0, mongoose_1.Schema)({
        versionKey: false,
        timestamps: true,
    })
], Like);
exports.LikeSchema = mongoose_1.SchemaFactory.createForClass(Like);
exports.LikeSchema.index({ userId: 1, blogId: 1 }, { unique: true });
//# sourceMappingURL=like.schema.js.map