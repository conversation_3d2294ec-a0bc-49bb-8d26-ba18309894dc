"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssetsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const cloudinary_1 = require("cloudinary");
const mongoose_2 = require("mongoose");
const uuid_1 = require("uuid");
let AssetsService = class AssetsService {
    assetModel;
    userInfos;
    configService;
    constructor(assetModel, userInfos, configService) {
        this.assetModel = assetModel;
        this.userInfos = userInfos;
        this.configService = configService;
        cloudinary_1.v2.config({
            cloud_name: this.configService.get('CLOUDINARY_CLOUD_NAME'),
            api_key: this.configService.get('CLOUDINARY_API_KEY'),
            api_secret: this.configService.get('CLOUDINARY_API_SECRET'),
        });
    }
    async getAllUserContentAssets(userId) {
        try {
            const assets = await this.assetModel
                .find({
                assetOwner: 'USER',
                userId: new mongoose_2.Types.ObjectId(userId),
                type: { $regex: /^CONTENT$/i },
            })
                .exec();
            return assets;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Error fetching user content assets: ${error.message}`);
        }
    }
    async addAssetSystem(file, req, type, subsection) {
        const userId = req.user?.['userId'];
        const roles = req.user?.['role'];
        if (!file) {
            throw new common_1.BadRequestException('File upload is required');
        }
        const isManager = Array.isArray(roles)
            ? roles.includes('MANAGER')
            : roles === 'MANAGER';
        let assetOwner = isManager ? 'SYSTEM' : 'USER';
        let publicIdPrefix = isManager ? 'system' : 'user';
        if (type.toUpperCase() === 'BANNER' && isManager) {
            if (!subsection) {
                throw new common_1.BadRequestException('Subsection is required');
            }
            const count = await this.assetModel.countDocuments({
                subsection: { $regex: new RegExp(`^${subsection}$`, 'i') },
            });
            if ((subsection.toLowerCase() === 'hero' && count >= 1) ||
                (subsection.toLowerCase() === 'intro' && count >= 4) ||
                (subsection.toLowerCase() === 'destination' && count >= 3) ||
                (subsection.toLowerCase() === 'hotel' && count >= 4) ||
                (subsection.toLowerCase() === 'creator' && count >= 3)) {
                throw new common_1.BadRequestException(`The number of assets for subsection:${subsection} has exceeded the limit!`);
            }
        }
        const uploadResult = await this.uploadImage(file, {
            public_id: `${publicIdPrefix}/${userId}/${(0, uuid_1.v4)()}`,
            folder: `vie-journey/${type.toLocaleLowerCase()}/${subsection ? subsection.toLocaleLowerCase() : ''}`,
        });
        const newAsset = new this.assetModel({
            userId: new mongoose_2.Types.ObjectId(userId),
            type: type.toLocaleUpperCase(),
            assetOwner: assetOwner,
            subsection: subsection ? subsection.toLocaleUpperCase() : null,
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            location: uploadResult.public_id.split('/')[0],
            format: uploadResult.format.toLocaleUpperCase(),
            file_size: `${(uploadResult.bytes / 1024).toFixed(2)} KB`,
            dimensions: `${uploadResult.width} x ${uploadResult.height}`,
        });
        return newAsset.save();
    }
    async updateAssetById(publicId, file) {
        if (!file) {
            throw new common_1.BadRequestException('File upload is required');
        }
        const asset = await this.assetModel.findOne({ publicId: publicId }).exec();
        if (!asset) {
            throw new common_1.BadRequestException(`Asset with id '${publicId}' not found`);
        }
        await this.deleteImage(publicId);
        const uploadResult = await this.uploadImage(file, {
            public_id: `${asset.assetOwner.toLocaleLowerCase()}/${asset.userId}/${(0, uuid_1.v4)()}`,
            folder: `vie-journey/${asset.type.toLocaleLowerCase()}/${asset?.subsection?.toLocaleLowerCase() || ''}`,
        });
        if (!uploadResult || !uploadResult.secure_url) {
            throw new common_1.BadRequestException('Failed to upload image to Cloudinary');
        }
        asset.set({
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            location: uploadResult.public_id.split('/')[0],
            format: uploadResult.format.toLocaleUpperCase(),
            file_size: `${(uploadResult.bytes / 1024).toFixed(2)} KB`,
            dimensions: `${uploadResult.width} x ${uploadResult.height}`,
        });
        await asset.save();
        return asset;
    }
    async deleteAssetById(id) {
        const asset = await this.assetModel.findById(id).exec();
        if (!asset) {
            throw new common_1.BadRequestException(`Asset with id ${id} not found`);
        }
        if (asset.type === 'AVATAR') {
            await this.deleteImage(asset.publicId);
            const updatedAsset = await this.assetModel.findByIdAndDelete(asset._id);
            if (!updatedAsset) {
                throw new common_1.BadRequestException(`Failed to delete asset with id ${id}`);
            }
            await this.userInfos.findOneAndUpdate({ userId: asset.userId }, { avatar: null }, { new: true });
            return updatedAsset;
        }
        else {
            await this.deleteImage(asset.publicId);
            const deletedAsset = await this.assetModel.findOneAndDelete({
                publicId: asset.publicId,
            });
            return deletedAsset;
        }
    }
    async getSubsection() {
        const assets = await this.assetModel.find({ type: 'BANNER' });
        const subsection = [...new Set(assets.map((asset) => asset.subsection))];
        return subsection;
    }
    async getAssetsByType(type, subsection) {
        try {
            const filter = {};
            if (type) {
                filter.type = { $regex: new RegExp(`^${type}$`, 'i') };
            }
            if (subsection) {
                filter.subsection = { $regex: new RegExp(`^${subsection}$`, 'i') };
            }
            const assets = await this.assetModel.find(filter).exec();
            return assets;
        }
        catch (error) {
            throw new Error(`Error fetching assets of type ${type}: ${error.message}`);
        }
    }
    async fetchAllBannersBySubsection() {
        try {
            const banners = await this.assetModel.find({ type: 'BANNER' }).exec();
            const subsections = ['hero', 'intro', 'destination', 'hotel', 'creator'];
            const result = {};
            subsections.forEach((sub) => {
                result[sub] = banners
                    .filter((b) => b.subsection && b.subsection.toLowerCase() === sub.toLowerCase())
                    .map((b) => ({
                    url: b.url,
                }));
            });
            return result;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Error fetching landing banner: ${error.message}`);
        }
    }
    async uploadImage(file, options) {
        return new Promise((resolve, reject) => {
            cloudinary_1.v2.uploader
                .upload_stream({
                resource_type: 'image',
                transformation: [
                    { width: 1200, height: 800, crop: 'limit' },
                    { quality: 'auto' },
                ],
                ...options,
            }, (error, result) => {
                if (error) {
                    reject(error);
                }
                else if (result) {
                    resolve(result);
                }
                else {
                    reject(new Error('Upload failed: No result returned'));
                }
            })
                .end(file.buffer);
        });
    }
    async deleteImage(publicId) {
        return cloudinary_1.v2.uploader.destroy(publicId);
    }
    getPublicIdFromUrl(url) {
        const matches = url.match(/\/upload\/(?:v\d+\/)?(.+)\.[a-zA-Z0-9]+$/);
        return matches ? matches[1] : null;
    }
    async uploadImageFromUrl(imageUrl, options) {
        try {
            const result = await cloudinary_1.v2.uploader.upload(imageUrl, {
                resource_type: 'image',
                transformation: [
                    { width: 1200, height: 800, crop: 'limit' },
                    { quality: 'auto' },
                ],
                ...options,
            });
            return result;
        }
        catch (error) {
            throw new Error(`Failed to upload image from URL: ${error.message}`);
        }
    }
};
exports.AssetsService = AssetsService;
exports.AssetsService = AssetsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Asset')),
    __param(1, (0, mongoose_1.InjectModel)('UserInfos')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        config_1.ConfigService])
], AssetsService);
//# sourceMappingURL=assets.service.js.map