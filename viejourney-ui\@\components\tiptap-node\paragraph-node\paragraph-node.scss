.tiptap.ProseMirror {
  --blockquote-bg-color: var(--tt-gray-light-900);
  --link-text-color: var(--tt-brand-color-500);
  --separator-color: var(--tt-gray-light-a-200);
  --thread-text: var(--tt-gray-light-900);
  --placeholder-color: var(--tt-gray-light-a-400);

  // Mathematics variables
  --tiptap-mathematics-bg-color: var(--tt-gray-light-a-200);
  --tiptap-mathematics-border-color: var(--tt-brand-color-500);

  .dark & {
    --blockquote-bg-color: var(--tt-gray-dark-900);
    --link-text-color: var(--tt-brand-color-400);
    --separator-color: var(--tt-gray-dark-a-200);
    --thread-text: var(--tt-gray-dark-900);
    --placeholder-color: var(--tt-gray-dark-a-400);

    --tiptap-mathematics-bg-color: var(--tt-gray-dark-a-200);
    --tiptap-mathematics-border-color: var(--tt-brand-color-400);
  }
}

/* =====================
     CORE EDITOR STYLES
     ===================== */
.tiptap.ProseMirror {
  white-space: pre-wrap;
  outline: none;
  caret-color: var(--tt-cursor-color);

  // Paragraph spacing
  p:not(:first-child) {
    font-size: 1rem;
    line-height: 1.6;
    font-weight: normal;
    margin-top: 20px;
  }

  // Selection styles
  &:not(.readonly):not(.ProseMirror-hideselection) {
    ::selection {
      background-color: var(--tt-selection-color);
    }

    .selection::selection {
      background: transparent;
    }
  }

  .selection {
    display: inline;
    background-color: var(--tt-selection-color);
  }

  .ProseMirror-hideselection {
    caret-color: transparent;
  }

  // Placeholder
  > p.is-editor-empty::before {
    content: attr(data-placeholder);
    pointer-events: none;
    color: var(--placeholder-color);
    float: left;
    height: 0;
  }

  // Resize cursor
  &.resize-cursor {
    cursor: ew-resize;
    cursor: col-resize;
  }
}

/* =====================
     GAP CURSOR
     ===================== */
.tiptap.ProseMirror {
  .ProseMirror-gapcursor {
    display: none;
    pointer-events: none;
    position: absolute;

    &:after {
      content: "";
      display: block;
      position: absolute;
      top: 1em;
      width: 1.25em;
      border-top: 1px solid black;
      animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
    }
  }

  &.ProseMirror-focused,
  &.ProseMirror.ProseMirror-focused {
    .ProseMirror-gapcursor {
      display: block;
    }
  }
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

/* =====================
     TEXT DECORATION
     ===================== */
.tiptap.ProseMirror {
  // Text decoration inheritance for spans
  a span {
    text-decoration: underline;
  }

  s span {
    text-decoration: line-through;
  }

  u span {
    text-decoration: underline;
  }
}

/* =====================
     BLOCKQUOTE
     ===================== */
.tiptap.ProseMirror {
  blockquote {
    position: relative;
    padding-left: 1em;
    padding-top: 0.375em;
    padding-bottom: 0.375em;
    margin: 1.5rem 0;

    p {
      margin-top: 0;
    }

    &::before,
    &.is-empty::before {
      position: absolute;
      bottom: 0;
      left: 0;
      top: 0;
      height: 100%;
      width: 0.25em;
      background-color: var(--blockquote-bg-color);
      content: "";
      border-radius: 0;
    }
  }
}

/* =====================
     COLLABORATION
     ===================== */
.tiptap.ProseMirror {
  .collaboration-cursor {
    &__caret {
      border-right: 1px solid transparent;
      border-left: 1px solid transparent;
      pointer-events: none;
      margin-left: -1px;
      margin-right: -1px;
      position: relative;
      word-break: normal;
    }

    &__label {
      border-radius: 0.25rem;
      border-bottom-left-radius: 0;
      font-size: 0.75rem;
      font-weight: 600;
      left: -1px;
      line-height: 1;
      padding: 0.125rem 0.375rem;
      position: absolute;
      top: -1.3em;
      user-select: none;
      white-space: nowrap;
    }
  }
}

/* =====================
     EMOJI
     ===================== */
.tiptap.ProseMirror [data-type="emoji"] img {
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  cursor: text;
}

/* =====================
     HEADINGS
     ===================== */
.tiptap.ProseMirror {
  h1,
  h2,
  h3,
  h4 {
    position: relative;
    color: inherit;
    font-style: inherit;

    &:first-child {
      margin-top: 0;
    }
  }

  h1 {
    font-size: 1.5em;
    font-weight: 700;
    margin-top: 3em;
  }

  h2 {
    font-size: 1.25em;
    font-weight: 700;
    margin-top: 2.5em;
  }

  h3 {
    font-size: 1.125em;
    font-weight: 600;
    margin-top: 2em;
  }

  h4 {
    font-size: 1em;
    font-weight: 600;
    margin-top: 2em;
  }
}

/* =====================
     HORIZONTAL RULE
     ===================== */
.tiptap.ProseMirror {
  hr {
    margin-top: 3em;
    margin-bottom: 3em;
    border: none;
    height: 1px;
    background-color: var(--separator-color);
  }

  &.ProseMirror-focused {
    hr.ProseMirror-selectednode {
      border-radius: 9999px;
      outline: 3px solid var(--tt-brand-color-500);
      outline-offset: 2px;
    }
  }
}

/* =====================
     LINKS
     ===================== */
.tiptap.ProseMirror {
  a {
    color: var(--link-text-color);
    text-decoration: underline;
  }
}

/* =====================
     MENTION
     ===================== */
.tiptap.ProseMirror {
  [data-type="mention"] {
    display: inline-block;
    color: var(--tt-brand-color-500);
  }
}

/* =====================
     THREADS
     ===================== */
.tiptap.ProseMirror {
  // Base styles for inline threads
  .tiptap-thread.tiptap-thread--unresolved.tiptap-thread--inline {
    transition:
      color 0.2s ease-in-out,
      background-color 0.2s ease-in-out;
    color: var(--thread-text);
    border-bottom: 2px dashed var(--tt-color-yellow-base);
    font-weight: 600;

    &.tiptap-thread--selected,
    &.tiptap-thread--hovered {
      background-color: var(--tt-color-yellow-inc-2);
      border-bottom-color: transparent;
    }
  }

  // Block thread styles with images
  .tiptap-thread.tiptap-thread--unresolved.tiptap-thread--block {
    &:has(img) {
      outline: 0.125rem solid var(--tt-color-yellow-base);
      border-radius: var(--tt-radius-xs, 0.25rem);
      overflow: hidden;
      width: fit-content;

      &.tiptap-thread--selected {
        outline-width: 0.25rem;
        outline-color: var(--tt-color-yellow-base);
      }

      &.tiptap-thread--hovered {
        outline-width: 0.25rem;
      }
    }

    // Block thread styles without images
    &:not(:has(img)) {
      border-radius: 0.25rem;
      border-bottom: 0.125rem dashed var(--tt-color-yellow-base);
      padding-bottom: 0.5rem;
      outline: 0.25rem solid transparent;

      &.tiptap-thread--hovered,
      &.tiptap-thread--selected {
        background-color: var(--tt-color-yellow-base);
        outline-color: var(--tt-color-yellow-base);
      }
    }
  }

  // Resolved thread styles
  .tiptap-thread.tiptap-thread--resolved.tiptap-thread--inline.tiptap-thread--selected {
    background-color: var(--tt-color-yellow-base);
    border-color: transparent;
    opacity: 0.5;
  }

  // React renderer specific styles
  .tiptap-thread.tiptap-thread--block:has(.react-renderer) {
    margin-top: 3rem;
    margin-bottom: 3rem;
  }
}

/* =====================
     Mathematics
     ===================== */
.tiptap.ProseMirror {
  .Tiptap-mathematics-editor {
    padding: 0 0.25rem;
    margin: 0 0.25rem;
    border: 1px solid var(--tiptap-mathematics-border-color);
    font-family: monospace;
    font-size: 0.875rem;
  }

  .Tiptap-mathematics-render {
    padding: 0 0.25rem;

    &--editable {
      cursor: pointer;
      transition: background 0.2s;

      &:hover {
        background: var(--tiptap-mathematics-bg-color);
      }
    }
  }

  .Tiptap-mathematics-editor,
  .Tiptap-mathematics-render {
    border-radius: var(--tt-radius-xs);
    display: inline-block;
  }
}

/* =====================
     PLACEHOLDER
     ===================== */
.is-empty[data-placeholder]:has(
    > .ProseMirror-trailingBreak:only-child
  )::before {
  content: "Write, type '/' for commands…";
}

.is-empty[data-placeholder]:has(
    > .ProseMirror-trailingBreak:only-child
  ):before {
  pointer-events: none;
  height: 0;
  position: absolute;
  font-style: italic;
}

.is-empty[data-placeholder]:has(> .ProseMirror-trailingBreak):before {
  color: var(--placeholder-color);
}
