"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const bcrypt = require("bcrypt");
const mongoose_2 = require("mongoose");
const status_enum_1 = require("../../common/enums/status.enum");
const assets_service_1 = require("../assets/assets.service");
let AdminService = class AdminService {
    blogModel;
    commentModel;
    accountModel;
    userInfosModel;
    assetModel;
    tripModel;
    assetsService;
    constructor(blogModel, commentModel, accountModel, userInfosModel, assetModel, tripModel, assetsService) {
        this.blogModel = blogModel;
        this.commentModel = commentModel;
        this.accountModel = accountModel;
        this.userInfosModel = userInfosModel;
        this.assetModel = assetModel;
        this.tripModel = tripModel;
        this.assetsService = assetsService;
    }
    async getBlogsReport(minViews) {
        const query = minViews ? { views: { $gte: minViews } } : {};
        const totalBlogs = await this.blogModel.countDocuments(query);
        const totalViews = await this.blogModel.aggregate([
            { $match: query },
            { $group: { _id: null, total: { $sum: '$views' } } },
        ]);
        return {
            totalBlogs,
            totalViews: totalViews[0]?.total || 0,
        };
    }
    async getCommentsReport() {
        const totalComments = await this.commentModel.countDocuments();
        const commentsPerBlog = await this.commentModel.aggregate([
            { $group: { _id: '$blog_id', count: { $sum: 1 } } },
        ]);
        return {
            totalComments,
            commentsPerBlog,
        };
    }
    async getAllAccounts() {
        return this.accountModel.find({ role: { $ne: 'ADMIN' } }).exec();
    }
    async getAccountById(id) {
        const account = await this.accountModel.findById(id).exec();
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        return account;
    }
    async createAccount(createAccountDto) {
        const existUser = await this.accountModel.findOne({
            email: createAccountDto.email,
        });
        if (existUser) {
            throw new Error('Email already exists');
        }
        const hashedPassword = await bcrypt.hash(createAccountDto.password, 10);
        const newAccount = new this.accountModel({
            email: createAccountDto.email,
            password: hashedPassword,
            active: true,
        });
        return newAccount.save();
    }
    async deleteAccount(id) {
        const account = await this.accountModel.findByIdAndDelete(id).exec();
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        return account;
    }
    async updateActiveStatus(id, active) {
        const account = await this.accountModel
            .findByIdAndUpdate(id, {
            status: active ? status_enum_1.Status.active : status_enum_1.Status.inactive,
        })
            .exec();
        if (!account) {
            throw new Error(`Account with ID ${id} not found`);
        }
        return account;
    }
    async banUser(userId, reason) {
        const userInfo = await this.userInfosModel.findById(userId);
        if (!userInfo) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        const account = await this.accountModel.findById(userInfo.userId);
        if (!account) {
            throw new common_1.NotFoundException(`Account not found for user ${userId}`);
        }
        if (account.role !== 'USER') {
            throw new common_1.BadRequestException(`Cannot ban user with role '${account.role}'. Only users with role 'USER' can be banned.`);
        }
        if (account.status === status_enum_1.Status.banned) {
            throw new common_1.BadRequestException(`Account is already banned. Ban reason: ${userInfo.banReason}, Banned at: ${userInfo.bannedAt}`);
        }
        try {
            account.status = status_enum_1.Status.banned;
            await account.save();
            userInfo.banReason = reason;
            userInfo.bannedAt = new Date();
            userInfo.flaggedCount += 1;
            await userInfo.save();
            return {
                userId: userInfo._id.toString(),
                accountId: account._id.toString(),
                role: account.role,
                email: account.email,
                userName: userInfo.fullName,
                status: account.status,
                banReason: userInfo.banReason,
                flaggedCount: userInfo.flaggedCount,
                bannedAt: userInfo.bannedAt,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to ban user: ' + error.message);
        }
    }
    async unbanUser(userId) {
        const userInfo = await this.userInfosModel.findById(userId);
        if (!userInfo) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found`);
        }
        const account = await this.accountModel.findById(userInfo.userId);
        if (!account) {
            throw new common_1.NotFoundException(`Account not found for user ${userId}`);
        }
        if (account.status !== status_enum_1.Status.banned) {
            throw new common_1.BadRequestException(`Account is not banned. Current status: ${account.status}`);
        }
        try {
            account.status = status_enum_1.Status.active;
            await account.save();
            userInfo.banReason = null;
            userInfo.bannedAt = null;
            await userInfo.save();
            return {
                userId: userInfo._id.toString(),
                accountId: account._id.toString(),
                role: account.role,
                email: account.email,
                userName: userInfo.fullName,
                status: account.status,
                banReason: userInfo.banReason,
                flaggedCount: userInfo.flaggedCount,
                bannedAt: userInfo.bannedAt,
            };
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to unban user: ' + error.message);
        }
    }
    async bulkUpdateUserRoles(userIds, newRole) {
        const validRoles = ['USER', 'ADMIN', 'MANAGER'];
        if (!validRoles.includes(newRole)) {
            throw new common_1.BadRequestException(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
        }
        const success = [];
        const failed = [];
        for (const userId of userIds) {
            try {
                const userInfo = await this.userInfosModel.findById(userId);
                if (!userInfo) {
                    failed.push({
                        userId,
                        reason: 'User not found',
                    });
                    continue;
                }
                const account = await this.accountModel.findById(userInfo.userId);
                if (!account) {
                    failed.push({
                        userId,
                        reason: 'Account not found',
                    });
                    continue;
                }
                if (account.role === 'ADMIN' && newRole !== 'ADMIN') {
                    failed.push({
                        userId,
                        reason: 'Cannot change admin role to non-admin role',
                    });
                    continue;
                }
                const oldRole = account.role;
                account.role = newRole;
                await account.save();
                success.push({
                    userId: userInfo._id.toString(),
                    accountId: account._id.toString(),
                    email: account.email,
                    userName: userInfo.fullName || 'Unknown',
                    oldRole,
                    newRole,
                    status: account.status,
                });
            }
            catch (error) {
                failed.push({
                    userId,
                    reason: `Error updating user: ${error.message}`,
                });
            }
        }
        return {
            success,
            failed,
            summary: {
                totalRequested: userIds.length,
                successCount: success.length,
                failedCount: failed.length,
            },
        };
    }
    async getDashboardAnalytics(query) {
        const { timeRange } = query;
        if (!timeRange) {
            throw new common_1.BadRequestException('Time range is required');
        }
        const endDate = new Date();
        const startDate = this.getStartDate(timeRange, endDate);
        const todayStart = new Date();
        todayStart.setHours(0, 0, 0, 0);
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);
        const [totalUsers, totalTrips, totalBlogs, totalInteractions, usersToday, tripsToday, blogsToday, interactionsToday, userGrowthData, contentCreationData, engagementData, userActivityStats, contentStatusStats, topLocations,] = await Promise.all([
            this.accountModel.countDocuments({ status: { $ne: 'deleted' } }),
            this.tripModel.countDocuments({}),
            this.blogModel.countDocuments({}),
            this.getLikesAndCommentsCount(),
            this.accountModel.countDocuments({
                createdAt: { $gte: todayStart, $lte: todayEnd },
            }),
            this.tripModel.countDocuments({
                createdAt: { $gte: todayStart, $lte: todayEnd },
            }),
            this.blogModel.countDocuments({
                createdAt: { $gte: todayStart, $lte: todayEnd },
            }),
            this.getTodayInteractions(todayStart, todayEnd),
            this.getUserGrowthData(startDate, endDate),
            this.getContentCreationData(startDate, endDate),
            this.getEngagementData(),
            this.getUserActivityStats(),
            this.getContentStatusStats(),
            this.getTopLocations(),
        ]);
        return {
            totalUsers,
            totalTrips,
            totalBlogs,
            totalInteractions,
            usersToday,
            tripsToday,
            blogsToday,
            interactionsToday,
            userGrowthData,
            contentCreationData,
            engagementData,
            userActivityData: userActivityStats,
            contentStatusData: contentStatusStats,
            topLocationsData: topLocations,
        };
    }
    getStartDate(timeRange, endDate) {
        const startDate = new Date(endDate);
        switch (timeRange) {
            case '7d':
                startDate.setDate(startDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(startDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(startDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(startDate.getFullYear() - 1);
                break;
            default:
                startDate.setDate(startDate.getDate() - 30);
        }
        return startDate;
    }
    async getLikesAndCommentsCount() {
        const [likesCount, commentsCount] = await Promise.all([
            this.blogModel.aggregate([
                { $group: { _id: null, total: { $sum: '$metrics.likeCount' } } },
            ]),
            this.blogModel.aggregate([
                { $group: { _id: null, total: { $sum: '$metrics.commentCount' } } },
            ]),
        ]);
        const likes = likesCount[0]?.total || 0;
        const comments = commentsCount[0]?.total || 0;
        return likes + comments;
    }
    async getTodayInteractions(todayStart, todayEnd) {
        const likesCountToday = await this.blogModel.aggregate([
            {
                $match: {
                    'likes.createdAt': { $gte: todayStart, $lte: todayEnd },
                },
            },
            {
                $project: {
                    todayLikes: {
                        $size: {
                            $filter: {
                                input: '$likes',
                                cond: {
                                    $and: [
                                        { $gte: ['$$this.createdAt', todayStart] },
                                        { $lte: ['$$this.createdAt', todayEnd] },
                                    ],
                                },
                            },
                        },
                    },
                },
            },
            {
                $group: { _id: null, total: { $sum: '$todayLikes' } },
            },
        ]);
        const commentsCountToday = await this.commentModel.countDocuments({
            createdAt: { $gte: todayStart, $lte: todayEnd },
        });
        return (likesCountToday[0]?.total || 0) + commentsCountToday;
    }
    async getUserGrowthData(startDate, endDate) {
        const result = await this.accountModel.aggregate([
            {
                $match: {
                    createdAt: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$createdAt' },
                        month: { $month: '$createdAt' },
                    },
                    newUsers: { $sum: 1 },
                },
            },
            {
                $sort: { '_id.year': 1, '_id.month': 1 },
            },
        ]);
        const months = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
        ];
        let cumulativeUsers = await this.accountModel.countDocuments({
            createdAt: { $lt: startDate },
        });
        return result.map((item) => {
            cumulativeUsers += item.newUsers;
            return {
                month: months[item._id.month - 1],
                users: cumulativeUsers,
                newUsers: item.newUsers,
            };
        });
    }
    async getContentCreationData(startDate, endDate) {
        const [blogData, tripData] = await Promise.all([
            this.blogModel.aggregate([
                {
                    $match: {
                        createdAt: { $gte: startDate, $lte: endDate },
                    },
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' },
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
            ]),
            this.tripModel.aggregate([
                {
                    $match: {
                        createdAt: { $gte: startDate, $lte: endDate },
                    },
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' },
                        },
                        count: { $sum: 1 },
                    },
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 },
                },
            ]),
        ]);
        const months = [
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec',
        ];
        const result = [];
        const blogMap = new Map();
        const tripMap = new Map();
        blogData.forEach((item) => {
            const key = `${item._id.year}-${item._id.month}`;
            blogMap.set(key, item.count);
        });
        tripData.forEach((item) => {
            const key = `${item._id.year}-${item._id.month}`;
            tripMap.set(key, item.count);
        });
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            const key = `${date.getFullYear()}-${date.getMonth() + 1}`;
            const monthName = months[date.getMonth()];
            result.push({
                month: monthName,
                blogs: blogMap.get(key) || 0,
                trips: tripMap.get(key) || 0,
            });
        }
        return result;
    }
    async getEngagementData() {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 7);
        const result = await this.blogModel.aggregate([
            {
                $match: {
                    updatedAt: { $gte: startDate, $lte: endDate },
                },
            },
            {
                $group: {
                    _id: {
                        dayOfWeek: { $dayOfWeek: '$updatedAt' },
                        date: { $dateToString: { format: '%Y-%m-%d', date: '$updatedAt' } },
                    },
                    likes: { $sum: '$metrics.likeCount' },
                    comments: { $sum: '$metrics.commentCount' },
                    shares: { $sum: 0 },
                },
            },
            {
                $sort: { '_id.date': 1 },
            },
        ]);
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        const dayData = [];
        for (let i = 6; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = days[date.getDay()];
            const data = result.find((r) => r._id.date === dateStr);
            dayData.push({
                day: dayName,
                likes: data?.likes || 0,
                comments: data?.comments || 0,
                shares: data?.shares || 0,
            });
        }
        return dayData;
    }
    async getUserActivityStats() {
        const totalUsers = await this.accountModel.countDocuments({
            status: { $ne: 'deleted' },
        });
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const [activeUsers, newUsers] = await Promise.all([
            this.accountModel.countDocuments({
                status: 'ACTIVE',
                updatedAt: { $gte: thirtyDaysAgo },
            }),
            this.accountModel.countDocuments({
                createdAt: { $gte: thirtyDaysAgo },
            }),
        ]);
        const inactiveUsers = totalUsers - activeUsers;
        return [
            { id: 0, value: activeUsers, label: 'Active Users', color: '#10B981' },
            {
                id: 1,
                value: inactiveUsers,
                label: 'Inactive Users',
                color: '#F59E0B',
            },
            { id: 2, value: newUsers, label: 'New Users', color: '#3B82F6' },
        ];
    }
    async getContentStatusStats() {
        const [approved, draft, pending, rejected] = await Promise.all([
            this.blogModel.countDocuments({ status: 'APPROVED' }),
            this.blogModel.countDocuments({ status: 'DRAFT' }),
            this.blogModel.countDocuments({ status: 'PENDING' }),
            this.blogModel.countDocuments({ status: 'REJECTED' }),
        ]);
        return [
            { id: 0, value: approved, label: 'Approved', color: '#10B981' },
            { id: 1, value: draft, label: 'Draft', color: '#6B7280' },
            { id: 2, value: pending, label: 'Pending', color: '#F59E0B' },
            { id: 3, value: rejected, label: 'Rejected', color: '#EF4444' },
        ];
    }
    async getTopLocations() {
        const [tripLocations, blogLocations] = await Promise.all([
            this.tripModel.aggregate([
                {
                    $group: {
                        _id: '$destination.name',
                        trips: { $sum: 1 },
                    },
                },
                {
                    $sort: { trips: -1 },
                },
                {
                    $limit: 5,
                },
            ]),
            this.blogModel.aggregate([
                {
                    $match: {
                        destination: { $exists: true, $ne: null },
                    },
                },
                {
                    $group: {
                        _id: '$destination',
                        blogs: { $sum: 1 },
                    },
                },
                {
                    $sort: { blogs: -1 },
                },
                {
                    $limit: 5,
                },
            ]),
        ]);
        const locationMap = new Map();
        tripLocations.forEach((item) => {
            locationMap.set(item._id, {
                location: item._id,
                trips: item.trips,
                blogs: 0,
            });
        });
        blogLocations.forEach((item) => {
            if (locationMap.has(item._id)) {
                locationMap.get(item._id).blogs = item.blogs;
            }
            else {
                locationMap.set(item._id, {
                    location: item._id,
                    trips: 0,
                    blogs: item.blogs,
                });
            }
        });
        return Array.from(locationMap.values())
            .sort((a, b) => b.trips + b.blogs - (a.trips + a.blogs))
            .slice(0, 5);
    }
    async getRoleBasedCounts() {
        const [userCount, adminCount, managerCount] = await Promise.all([
            this.accountModel.countDocuments({ role: 'USER' }),
            this.accountModel.countDocuments({ role: 'ADMIN' }),
            this.accountModel.countDocuments({ role: 'MANAGER' }),
        ]);
        return {
            userCount,
            adminCount,
            managerCount,
        };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Blog')),
    __param(1, (0, mongoose_1.InjectModel)('Comment')),
    __param(2, (0, mongoose_1.InjectModel)('Account')),
    __param(3, (0, mongoose_1.InjectModel)('UserInfos')),
    __param(4, (0, mongoose_1.InjectModel)('Asset')),
    __param(5, (0, mongoose_1.InjectModel)('Trip')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        assets_service_1.AssetsService])
], AdminService);
//# sourceMappingURL=admin.service.js.map