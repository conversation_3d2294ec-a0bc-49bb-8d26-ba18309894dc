import mongoose, { Document } from 'mongoose';
declare class Note {
    id: string;
    text: string;
    by?: string;
}
declare class Location {
    lat: number;
    lng: number;
}
declare class Departure {
    datetime: string;
    location: string;
}
declare class Arrival {
    datetime: string;
    location: string;
}
declare class Transit {
    id: string;
    note: string;
    cost: number;
    currency: string;
    mode: string;
    departure: Departure;
    arrival: Arrival;
}
declare class Place {
    id: string;
    place: {
        placeId: String;
        displayName: String;
        types: [String];
        photo: String;
        editorialSummary: String;
        regularOpeningHours: mongoose.Schema.Types.Mixed;
        websiteURI: String;
        priceLevel: String;
        rating: Number;
        googleMapsURI: String;
        userRatingCount: Number;
    };
    note: String;
    visited: Boolean;
}
declare class PlaceCreatedBySchema {
    id: string;
    email?: string;
    fullname?: string;
}
declare class PlaceDetails {
    placeId?: string;
    displayName: string;
    types: string[];
    photo: string;
    editorialSummary?: string;
    location?: Location;
    time?: string;
    cost?: number;
    createdBy?: PlaceCreatedBySchema;
}
declare class Itinerary {
    id: string;
    date: string;
    place?: PlaceDetails;
    note: string;
    createdAt?: string;
    updatedAt?: string;
}
declare class Split {
    splitWith: string[];
    amount: number;
    isSettled: boolean;
}
declare class Expense {
    id: string;
    tripId: mongoose.Types.ObjectId;
    amount: number;
    currency: string;
    type: string;
    desc: string;
    payer: string;
    splits: Split;
}
declare class Plan {
    notes: Note[];
    transits: Transit[];
    places: Place[];
    itineraries: Itinerary[];
    budget: number;
    expenses: Expense[];
}
export declare class TripPlan extends Document {
    tripId: mongoose.Types.ObjectId;
    plan: Plan;
    lastUpdated: Date;
    lastUpdatedBy: mongoose.Types.ObjectId;
}
export declare const TripPlanSchema: mongoose.Schema<TripPlan, mongoose.Model<TripPlan, any, any, any, mongoose.Document<unknown, any, TripPlan, any> & TripPlan & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, TripPlan, mongoose.Document<unknown, {}, mongoose.FlatRecord<TripPlan>, {}> & mongoose.FlatRecord<TripPlan> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export {};
