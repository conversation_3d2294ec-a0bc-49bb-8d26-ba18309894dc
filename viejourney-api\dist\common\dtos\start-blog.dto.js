"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StartBlogDto = void 0;
const class_validator_1 = require("class-validator");
class StartBlogDto {
    location;
}
exports.StartBlogDto = StartBlogDto;
__decorate([
    (0, class_validator_1.IsString)({ message: 'Địa điểm phải là một chuỗi.' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Địa điểm không được để trống.' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Địa điểm không được vượt quá 100 ký tự.' }),
    __metadata("design:type", String)
], StartBlogDto.prototype, "location", void 0);
//# sourceMappingURL=start-blog.dto.js.map