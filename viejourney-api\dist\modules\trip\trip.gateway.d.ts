import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { TripService } from './trip.service';
import { AddMessage, DeleteMessage, PlanSection, PlanStateService, UpdateMessage } from './plan-state/plan-state.service';
export declare class TripGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly tripService;
    private readonly planService;
    server: Server;
    constructor(tripService: TripService, planService: PlanStateService);
    private notifySaveStatus;
    handleConnection(client: Socket): Promise<void>;
    handleDisconnect(client: Socket): void;
    handlePing(data: any, client: Socket): void;
    handlePlanItemAdded<T extends PlanSection>(data: AddMessage<T>, client: Socket): void;
    handlePlanItemUpdated<T extends PlanSection>(data: UpdateMessage<T>, client: Socket): void;
    handlePlanItemDeleted<T extends PlanSection>(data: DeleteMessage<T>, client: Socket): void;
    handleRequestSaveStatus(data: {
        forceSave?: boolean;
    } | undefined, client: Socket): Promise<void>;
}
