"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const blog_controller_1 = require("./blog.controller");
const blog_service_1 = require("./blog.service");
const auth_module_1 = require("../auth/auth.module");
const blog_entity_1 = require("../../common/entities/blog.entity");
const blog_schema_1 = require("../../infrastructure/database/blog.schema");
const account_entity_1 = require("../../common/entities/account.entity");
const account_schema_1 = require("../../infrastructure/database/account.schema");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const assets_module_1 = require("../assets/assets.module");
const like_entity_1 = require("../../common/entities/like.entity");
const like_schema_1 = require("../../infrastructure/database/like.schema");
let BlogModule = class BlogModule {
};
exports.BlogModule = BlogModule;
exports.BlogModule = BlogModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: blog_entity_1.Blog.name, schema: blog_schema_1.BlogSchema },
                { name: account_entity_1.Account.name, schema: account_schema_1.AccountSchema },
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: like_entity_1.Like.name, schema: like_schema_1.LikeSchema },
            ]),
            (0, common_1.forwardRef)(() => auth_module_1.AuthModule),
            assets_module_1.AssetsModule,
        ],
        controllers: [blog_controller_1.BlogController],
        providers: [blog_service_1.BlogService],
        exports: [blog_service_1.BlogService],
    })
], BlogModule);
//# sourceMappingURL=blog.module.js.map