"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripPlanSchema = exports.TripPlan = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let Note = class Note {
    id;
    text;
    by;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Note.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Note.prototype, "text", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Note.prototype, "by", void 0);
Note = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Note);
let Location = class Location {
    lat;
    lng;
};
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Location.prototype, "lat", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Location.prototype, "lng", void 0);
Location = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Location);
let Departure = class Departure {
    datetime;
    location;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Departure.prototype, "datetime", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Departure.prototype, "location", void 0);
Departure = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Departure);
let Arrival = class Arrival {
    datetime;
    location;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Arrival.prototype, "datetime", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Arrival.prototype, "location", void 0);
Arrival = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Arrival);
let Transit = class Transit {
    id;
    note;
    cost;
    currency;
    mode;
    departure;
    arrival;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Transit.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Transit.prototype, "note", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], Transit.prototype, "cost", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Transit.prototype, "currency", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        enum: ['Train', 'Flight', 'Car', 'Bus', 'Boat', 'Walk', 'Bike', 'Others'],
    }),
    __metadata("design:type", String)
], Transit.prototype, "mode", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Departure, required: true }),
    __metadata("design:type", Departure)
], Transit.prototype, "departure", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Arrival, required: true }),
    __metadata("design:type", Arrival)
], Transit.prototype, "arrival", void 0);
Transit = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Transit);
let Place = class Place {
    id;
    place;
    note;
    visited;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Place.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Object, required: true }),
    __metadata("design:type", Object)
], Place.prototype, "place", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Place.prototype, "note", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Boolean)
], Place.prototype, "visited", void 0);
Place = __decorate([
    (0, mongoose_1.Schema)({ _id: false, timestamps: true })
], Place);
let PlaceCreatedBySchema = class PlaceCreatedBySchema {
    id;
    email;
    fullname;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PlaceCreatedBySchema.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], PlaceCreatedBySchema.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], PlaceCreatedBySchema.prototype, "fullname", void 0);
PlaceCreatedBySchema = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], PlaceCreatedBySchema);
let PlaceDetails = class PlaceDetails {
    placeId;
    displayName;
    types;
    photo;
    editorialSummary;
    location;
    time;
    cost;
    createdBy;
};
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PlaceDetails.prototype, "placeId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PlaceDetails.prototype, "displayName", void 0);
__decorate([
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], PlaceDetails.prototype, "types", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PlaceDetails.prototype, "photo", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PlaceDetails.prototype, "editorialSummary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Location }),
    __metadata("design:type", Location)
], PlaceDetails.prototype, "location", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PlaceDetails.prototype, "time", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], PlaceDetails.prototype, "cost", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: PlaceCreatedBySchema, required: false }),
    __metadata("design:type", PlaceCreatedBySchema)
], PlaceDetails.prototype, "createdBy", void 0);
PlaceDetails = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], PlaceDetails);
let Itinerary = class Itinerary {
    id;
    date;
    place;
    note;
    createdAt;
    updatedAt;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Itinerary.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Itinerary.prototype, "date", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: PlaceDetails }),
    __metadata("design:type", PlaceDetails)
], Itinerary.prototype, "place", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Itinerary.prototype, "note", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Itinerary.prototype, "createdAt", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Itinerary.prototype, "updatedAt", void 0);
Itinerary = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Itinerary);
let Split = class Split {
    splitWith;
    amount;
    isSettled;
};
__decorate([
    (0, mongoose_1.Prop)([String]),
    __metadata("design:type", Array)
], Split.prototype, "splitWith", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], Split.prototype, "amount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Split.prototype, "isSettled", void 0);
Split = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Split);
let Expense = class Expense {
    id;
    tripId;
    amount;
    currency;
    type;
    desc;
    payer;
    splits;
};
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Expense.prototype, "id", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.default.Types.ObjectId, ref: 'Trip' }),
    __metadata("design:type", mongoose_2.default.Types.ObjectId)
], Expense.prototype, "tripId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], Expense.prototype, "amount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Expense.prototype, "currency", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        enum: [
            'Flights',
            'Lodging',
            'Car rental',
            'Transit',
            'Food',
            'Drinks',
            'Sightseeing',
            'Activities',
            'Shopping',
            'Gas',
            'Groceries',
            'Other',
        ],
    }),
    __metadata("design:type", String)
], Expense.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Expense.prototype, "desc", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Expense.prototype, "payer", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Split }),
    __metadata("design:type", Split)
], Expense.prototype, "splits", void 0);
Expense = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Expense);
let Plan = class Plan {
    notes;
    transits;
    places;
    itineraries;
    budget;
    expenses;
};
__decorate([
    (0, mongoose_1.Prop)({ type: [Note], default: [] }),
    __metadata("design:type", Array)
], Plan.prototype, "notes", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Transit], default: [] }),
    __metadata("design:type", Array)
], Plan.prototype, "transits", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Place], default: [] }),
    __metadata("design:type", Array)
], Plan.prototype, "places", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Itinerary], default: [] }),
    __metadata("design:type", Array)
], Plan.prototype, "itineraries", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Plan.prototype, "budget", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Expense], default: [] }),
    __metadata("design:type", Array)
], Plan.prototype, "expenses", void 0);
Plan = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Plan);
let TripPlan = class TripPlan extends mongoose_2.Document {
    tripId;
    plan;
    lastUpdated;
    lastUpdatedBy;
};
exports.TripPlan = TripPlan;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.default.Types.ObjectId, ref: 'Trip', required: true }),
    __metadata("design:type", mongoose_2.default.Types.ObjectId)
], TripPlan.prototype, "tripId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: Plan,
        default: {
            notes: [],
            transits: [],
            places: [],
            itineraries: [],
            budget: 0,
            expenses: [],
        },
    }),
    __metadata("design:type", Plan)
], TripPlan.prototype, "plan", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: Date.now }),
    __metadata("design:type", Date)
], TripPlan.prototype, "lastUpdated", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.default.Types.ObjectId, ref: 'User' }),
    __metadata("design:type", mongoose_2.default.Types.ObjectId)
], TripPlan.prototype, "lastUpdatedBy", void 0);
exports.TripPlan = TripPlan = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true, versionKey: false })
], TripPlan);
exports.TripPlanSchema = mongoose_1.SchemaFactory.createForClass(TripPlan);
//# sourceMappingURL=plan.schema.js.map