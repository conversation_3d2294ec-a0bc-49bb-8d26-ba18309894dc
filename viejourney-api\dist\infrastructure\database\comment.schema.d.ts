import mongoose, { Document } from 'mongoose';
import { UserInfos } from './userinfo.schema';
export declare class Comment extends Document {
    blogId: mongoose.Types.ObjectId;
    content: string;
    commentBy: UserInfos;
    likes: UserInfos[];
    edited: boolean;
    editedAt: Date | null;
}
export declare const CommentSchema: mongoose.Schema<Comment, mongoose.Model<Comment, any, any, any, mongoose.Document<unknown, any, Comment, any> & Comment & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Comment, mongoose.Document<unknown, {}, mongoose.FlatRecord<Comment>, {}> & mongoose.FlatRecord<Comment> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
