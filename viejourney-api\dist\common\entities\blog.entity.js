"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Blog = exports.PlaceData = void 0;
const mongoose_1 = require("mongoose");
class PlaceData {
    placeId;
    displayName;
    latitude;
    longitude;
    editorialSummary;
    types;
    photos;
    googleMapsURI;
    showDetails;
}
exports.PlaceData = PlaceData;
class Blog extends mongoose_1.Document {
    title;
    slug;
    content;
    summary;
    tags;
    coverImage;
    destination;
    places;
    createdBy;
    updatedBy;
    likes;
    status;
    flags;
    metrics;
    comments;
    createdAt;
    updatedAt;
}
exports.Blog = Blog;
//# sourceMappingURL=blog.entity.js.map