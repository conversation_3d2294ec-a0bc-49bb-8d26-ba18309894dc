import { CreateHotelDto } from 'src/common/dtos/create-hotel.dto';
import { UpdateHotelDto } from 'src/common/dtos/update-hotel.dto';
import { HotelService } from '../hotel/hotel.service';
export declare class ManagerController {
    private readonly hotelService;
    constructor(hotelService: HotelService);
    getHotelList(): Promise<(import("mongoose").Document<unknown, {}, import("../../infrastructure/database/hotel.schema").Hotel, {}> & import("../../infrastructure/database/hotel.schema").Hotel & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    createHotel(createHotelDto: CreateHotelDto): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    getHotelDetail(id: string): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    deleteHotel(id: string): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    updateHotel(id: string, updateHotelDto: UpdateHotelDto): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    importHotels(file: Express.Multer.File): Promise<{
        count: number;
    }>;
}
