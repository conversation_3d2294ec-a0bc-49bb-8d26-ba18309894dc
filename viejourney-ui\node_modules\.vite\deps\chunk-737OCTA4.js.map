{"version": 3, "sources": ["../../@mui/material/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nfunction stripDiacritics(string) {\n  return string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '');\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.startsWith(input) : candidate.includes(input);\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.parentElement?.contains(document.activeElement);\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction getInputValue(value, multiple, getOptionLabel) {\n  if (multiple || value == null) {\n    return '';\n  }\n  const optionLabel = getOptionLabel(value);\n  return typeof optionLabel === 'string' ? optionLabel : '';\n}\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => option.label ?? option,\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n\n  // Calculate the initial inputValue on mount only.\n  // useRef ensures it doesn't update dynamically with defaultValue or value props.\n  const initialInputValue = React.useRef(getInputValue(defaultValue ?? valueProp, multiple, getOptionLabel)).current;\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: initialInputValue,\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue, reason) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    const newInputValue = getInputValue(newValue, multiple, getOptionLabel);\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, reason);\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value, 'reset');\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange && ['mouse', 'keyboard', 'touch'].includes(reason)) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return filteredOptions.findIndex(option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && value.findIndex(val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = filteredOptions.findIndex(optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = newValue.findIndex(valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue, reason);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && !['ArrowLeft', 'ArrowRight'].includes(event.key)) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value, 'blur');\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => ({\n      ...other,\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => ({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1,\n      ...(!readOnly && {\n        onDelete: handleTagDelete(index)\n      })\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionKey?.(option) ?? getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;"], "mappings": ";;;;;;;;;;;;;;;AAGA,YAAuB;AAIvB,SAAS,gBAAgB,QAAQ;AAC/B,SAAO,OAAO,UAAU,KAAK,EAAE,QAAQ,oBAAoB,EAAE;AAC/D;AACO,SAAS,oBAAoB,SAAS,CAAC,GAAG;AAC/C,QAAM;AAAA,IACJ,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,EACT,IAAI;AACJ,SAAO,CAAC,SAAS;AAAA,IACf;AAAA,IACA;AAAA,EACF,MAAM;AACJ,QAAI,QAAQ,OAAO,WAAW,KAAK,IAAI;AACvC,QAAI,YAAY;AACd,cAAQ,MAAM,YAAY;AAAA,IAC5B;AACA,QAAI,eAAe;AACjB,cAAQ,gBAAgB,KAAK;AAAA,IAC/B;AACA,UAAM,kBAAkB,CAAC,QAAQ,UAAU,QAAQ,OAAO,YAAU;AAClE,UAAI,aAAa,aAAa,gBAAgB,MAAM;AACpD,UAAI,YAAY;AACd,oBAAY,UAAU,YAAY;AAAA,MACpC;AACA,UAAI,eAAe;AACjB,oBAAY,gBAAgB,SAAS;AAAA,MACvC;AACA,aAAO,cAAc,UAAU,UAAU,WAAW,KAAK,IAAI,UAAU,SAAS,KAAK;AAAA,IACvF,CAAC;AACD,WAAO,OAAO,UAAU,WAAW,gBAAgB,MAAM,GAAG,KAAK,IAAI;AAAA,EACvE;AACF;AACA,IAAM,uBAAuB,oBAAoB;AAGjD,IAAM,WAAW;AACjB,IAAM,kCAAkC,gBAAW;AA/CnD;AA+CsD,oBAAW,YAAY,UAAQ,gBAAW,QAAQ,kBAAnB,mBAAkC,SAAS,SAAS;AAAA;AACzI,IAAM,yBAAyB,CAAC;AAChC,SAAS,cAAc,OAAO,UAAU,gBAAgB;AACtD,MAAI,YAAY,SAAS,MAAM;AAC7B,WAAO;AAAA,EACT;AACA,QAAM,cAAc,eAAe,KAAK;AACxC,SAAO,OAAO,gBAAgB,WAAW,cAAc;AACzD;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM;AAAA;AAAA,IAEJ,oCAAoC;AAAA;AAAA,IAEpC,2BAA2B;AAAA,IAC3B,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,aAAa;AAAA,IACb,eAAe;AAAA,IACf,cAAc,CAAC,MAAM;AAAA,IACrB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB,eAAe,MAAM,WAAW,yBAAyB;AAAA,IACzD,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,UAAU;AAAA,IACV,yBAAyB;AAAA,IACzB,kBAAkB;AAAA,IAClB,gBAAgB;AAAA,IAChB,wBAAwB;AAAA,IACxB,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,gBAAgB,qBAAqB,YAAU,OAAO,SAAS;AAAA,IAC/D;AAAA,IACA,oBAAoB,CAAC,MAAM;AAAA,IAC3B,IAAI;AAAA,IACJ,qBAAqB;AAAA,IACrB,YAAY;AAAA,IACZ,uBAAuB,CAAC,QAAQA,WAAU,WAAWA;AAAA,IACrD,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,IACA,WAAW;AAAA,IACX,gBAAgB,CAAC,MAAM;AAAA,IACvB,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,KAAK,MAAM,MAAM;AACvB,MAAI,iBAAiB;AACrB,mBAAiB,YAAU;AACzB,UAAM,cAAc,mBAAmB,MAAM;AAC7C,QAAI,OAAO,gBAAgB,UAAU;AACnC,UAAI,MAAuC;AACzC,cAAM,kBAAkB,gBAAgB,SAAY,cAAc,GAAG,OAAO,WAAW,KAAK,WAAW;AACvG,gBAAQ,MAAM,yCAAyC,aAAa,aAAa,eAAe,4BAA4B,KAAK,UAAU,MAAM,CAAC,GAAG;AAAA,MACvJ;AACA,aAAO,OAAO,WAAW;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAoB,aAAO,KAAK;AACtC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,WAAiB,aAAO,IAAI;AAClC,QAAM,aAAmB,aAAO,IAAI;AACpC,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,IAAI;AACnD,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,EAAE;AACrD,QAAM,qBAAqB,gBAAgB,IAAI;AAC/C,QAAM,sBAA4B,aAAO,kBAAkB;AAI3D,QAAM,oBAA0B,aAAO,cAAc,gBAAgB,WAAW,UAAU,cAAc,CAAC,EAAE;AAC3G,QAAM,CAAC,OAAO,aAAa,IAAI,cAAc;AAAA,IAC3C,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,EACR,CAAC;AACD,QAAM,CAAC,YAAY,kBAAkB,IAAI,cAAc;AAAA,IACrD,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,QAAM,kBAAwB,kBAAY,CAAC,OAAO,UAAU,WAAW;AAGrE,UAAM,mBAAmB,WAAW,MAAM,SAAS,SAAS,SAAS,aAAa;AAClF,QAAI,CAAC,oBAAoB,CAAC,aAAa;AACrC;AAAA,IACF;AACA,UAAM,gBAAgB,cAAc,UAAU,UAAU,cAAc;AACtE,QAAI,eAAe,eAAe;AAChC;AAAA,IACF;AACA,uBAAmB,aAAa;AAChC,QAAI,eAAe;AACjB,oBAAc,OAAO,eAAe,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG,CAAC,gBAAgB,YAAY,UAAU,eAAe,oBAAoB,aAAa,KAAK,CAAC;AAChG,QAAM,CAAC,MAAM,YAAY,IAAI,cAAc;AAAA,IACzC,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM,CAAC,eAAe,gBAAgB,IAAU,eAAS,IAAI;AAC7D,QAAM,4BAA4B,CAAC,YAAY,SAAS,QAAQ,eAAe,eAAe,KAAK;AACnG,QAAM,YAAY,QAAQ,CAAC;AAC3B,QAAM,kBAAkB,YAAY;AAAA,IAAc,QAAQ,OAAO,YAAU;AACzE,UAAI,0BAA0B,WAAW,QAAQ,CAAC,KAAK,GAAG,KAAK,YAAU,WAAW,QAAQ,qBAAqB,QAAQ,MAAM,CAAC,GAAG;AACjI,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AAAA;AAAA;AAAA,IAGD;AAAA,MACE,YAAY,6BAA6B,gBAAgB,KAAK;AAAA,MAC9D;AAAA,IACF;AAAA,EAAC,IAAI,CAAC;AACN,QAAM,gBAAgB,yBAAiB;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,EAAM,gBAAU,MAAM;AACpB,UAAM,cAAc,UAAU,cAAc;AAC5C,QAAI,WAAW,CAAC,aAAa;AAC3B;AAAA,IACF;AAGA,QAAI,YAAY,CAAC,aAAa;AAC5B;AAAA,IACF;AACA,oBAAgB,MAAM,OAAO,OAAO;AAAA,EACtC,GAAG,CAAC,OAAO,iBAAiB,SAAS,cAAc,OAAO,QAAQ,CAAC;AACnE,QAAM,mBAAmB,QAAQ,gBAAgB,SAAS,KAAK,CAAC;AAChE,QAAM,WAAW,yBAAiB,gBAAc;AAC9C,QAAI,eAAe,IAAI;AACrB,eAAS,QAAQ,MAAM;AAAA,IACzB,OAAO;AACL,eAAS,cAAc,oBAAoB,UAAU,IAAI,EAAE,MAAM;AAAA,IACnE;AAAA,EACF,CAAC;AAGD,EAAM,gBAAU,MAAM;AACpB,QAAI,YAAY,aAAa,MAAM,SAAS,GAAG;AAC7C,oBAAc,EAAE;AAChB,eAAS,EAAE;AAAA,IACb;AAAA,EACF,GAAG,CAAC,OAAO,UAAU,YAAY,QAAQ,CAAC;AAC1C,WAAS,iBAAiB,OAAO,WAAW;AAC1C,QAAI,CAAC,WAAW,WAAW,QAAQ,KAAK,SAAS,gBAAgB,QAAQ;AACvE,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,WAAO,MAAM;AACX,YAAM,SAAS,WAAW,QAAQ,cAAc,uBAAuB,SAAS,IAAI;AAGpF,YAAM,oBAAoB,yBAAyB,QAAQ,CAAC,UAAU,OAAO,YAAY,OAAO,aAAa,eAAe,MAAM;AAClI,UAAI,UAAU,OAAO,aAAa,UAAU,KAAK,CAAC,mBAAmB;AAEnE,eAAO;AAAA,MACT;AAIA,UAAI,cAAc,QAAQ;AACxB,qBAAa,YAAY,KAAK,gBAAgB;AAAA,MAChD,OAAO;AACL,qBAAa,YAAY,IAAI,gBAAgB,UAAU,gBAAgB;AAAA,MACzE;AAIA,UAAI,cAAc,OAAO;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,sBAAsB,yBAAiB,CAAC;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,EACF,MAAM;AACJ,wBAAoB,UAAU;AAG9B,QAAI,UAAU,IAAI;AAChB,eAAS,QAAQ,gBAAgB,uBAAuB;AAAA,IAC1D,OAAO;AACL,eAAS,QAAQ,aAAa,yBAAyB,GAAG,EAAE,WAAW,KAAK,EAAE;AAAA,IAChF;AACA,QAAI,qBAAqB,CAAC,SAAS,YAAY,OAAO,EAAE,SAAS,MAAM,GAAG;AACxE,wBAAkB,OAAO,UAAU,KAAK,OAAO,gBAAgB,KAAK,GAAG,MAAM;AAAA,IAC/E;AACA,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AACA,UAAM,OAAO,WAAW,QAAQ,cAAc,mBAAmB,wBAAwB,UAAU;AACnG,QAAI,MAAM;AACR,WAAK,UAAU,OAAO,GAAG,wBAAwB,UAAU;AAC3D,WAAK,UAAU,OAAO,GAAG,wBAAwB,eAAe;AAAA,IAClE;AACA,QAAI,cAAc,WAAW;AAC7B,QAAI,WAAW,QAAQ,aAAa,MAAM,MAAM,WAAW;AACzD,oBAAc,WAAW,QAAQ,cAAc,cAAc,kBAAkB;AAAA,IACjF;AAGA,QAAI,CAAC,aAAa;AAChB;AAAA,IACF;AACA,QAAI,UAAU,IAAI;AAChB,kBAAY,YAAY;AACxB;AAAA,IACF;AACA,UAAM,SAAS,WAAW,QAAQ,cAAc,uBAAuB,KAAK,IAAI;AAChF,QAAI,CAAC,QAAQ;AACX;AAAA,IACF;AACA,WAAO,UAAU,IAAI,GAAG,wBAAwB,UAAU;AAC1D,QAAI,WAAW,YAAY;AACzB,aAAO,UAAU,IAAI,GAAG,wBAAwB,eAAe;AAAA,IACjE;AAOA,QAAI,YAAY,eAAe,YAAY,gBAAgB,WAAW,WAAW,WAAW,SAAS;AACnG,YAAM,UAAU;AAChB,YAAM,eAAe,YAAY,eAAe,YAAY;AAC5D,YAAM,gBAAgB,QAAQ,YAAY,QAAQ;AAClD,UAAI,gBAAgB,cAAc;AAChC,oBAAY,YAAY,gBAAgB,YAAY;AAAA,MACtD,WAAW,QAAQ,YAAY,QAAQ,gBAAgB,UAAU,MAAM,KAAK,YAAY,WAAW;AACjG,oBAAY,YAAY,QAAQ,YAAY,QAAQ,gBAAgB,UAAU,MAAM;AAAA,MACtF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,yBAAyB,yBAAiB,CAAC;AAAA,IAC/C;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,EACF,MAAM;AACJ,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AACA,UAAM,eAAe,MAAM;AACzB,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,SAAS;AACpB,eAAO;AAAA,MACT;AACA,UAAI,SAAS,OAAO;AAClB,eAAO;AAAA,MACT;AACA,YAAM,WAAW,oBAAoB,UAAU;AAC/C,UAAI,WAAW,GAAG;AAChB,YAAI,aAAa,MAAM,oBAAoB;AACzC,iBAAO;AAAA,QACT;AACA,YAAI,mBAAmB,oBAAoB,YAAY,MAAM,KAAK,IAAI,IAAI,IAAI,GAAG;AAC/E,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,UAAI,WAAW,UAAU;AACvB,YAAI,aAAa,WAAW,KAAK,oBAAoB;AACnD,iBAAO;AAAA,QACT;AACA,YAAI,mBAAmB,KAAK,IAAI,IAAI,IAAI,GAAG;AACzC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,YAAY,iBAAiB,aAAa,GAAG,SAAS;AAC5D,wBAAoB;AAAA,MAClB,OAAO;AAAA,MACP;AAAA,MACA;AAAA,IACF,CAAC;AAGD,QAAI,gBAAgB,SAAS,SAAS;AACpC,UAAI,cAAc,IAAI;AACpB,iBAAS,QAAQ,QAAQ;AAAA,MAC3B,OAAO;AACL,cAAM,SAAS,eAAe,gBAAgB,SAAS,CAAC;AACxD,iBAAS,QAAQ,QAAQ;AAIzB,cAAM,QAAQ,OAAO,YAAY,EAAE,QAAQ,WAAW,YAAY,CAAC;AACnE,YAAI,UAAU,KAAK,WAAW,SAAS,GAAG;AACxC,mBAAS,QAAQ,kBAAkB,WAAW,QAAQ,OAAO,MAAM;AAAA,QACrE;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,oCAAoC,MAAM;AAC9C,UAAM,cAAc,CAAC,QAAQ,WAAW;AACtC,YAAM,SAAS,SAAS,eAAe,MAAM,IAAI;AACjD,YAAM,SAAS,SAAS,eAAe,MAAM,IAAI;AACjD,aAAO,WAAW;AAAA,IACpB;AACA,QAAI,oBAAoB,YAAY,MAAM,cAAc,mBAAmB,cAAc,gBAAgB,WAAW,gBAAgB,UAAU,cAAc,eAAe,eAAe,WAAW,MAAM,WAAW,cAAc,MAAM,UAAU,cAAc,MAAM,MAAM,CAAC,KAAK,MAAM,eAAe,MAAM,CAAC,CAAC,MAAM,eAAe,GAAG,CAAC,IAAI,YAAY,cAAc,OAAO,KAAK,IAAI;AACtX,YAAM,4BAA4B,cAAc,gBAAgB,oBAAoB,OAAO;AAC3F,UAAI,2BAA2B;AAC7B,eAAO,gBAAgB,UAAU,YAAU;AACzC,iBAAO,eAAe,MAAM,MAAM,eAAe,yBAAyB;AAAA,QAC5E,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,QAAM,uBAA6B,kBAAY,MAAM;AACnD,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAIA,UAAM,iCAAiC,kCAAkC;AACzE,QAAI,mCAAmC,IAAI;AACzC,0BAAoB,UAAU;AAC9B;AAAA,IACF;AACA,UAAM,YAAY,WAAW,MAAM,CAAC,IAAI;AAGxC,QAAI,gBAAgB,WAAW,KAAK,aAAa,MAAM;AACrD,6BAAuB;AAAA,QACrB,MAAM;AAAA,MACR,CAAC;AACD;AAAA,IACF;AACA,QAAI,CAAC,WAAW,SAAS;AACvB;AAAA,IACF;AAGA,QAAI,aAAa,MAAM;AACrB,YAAM,gBAAgB,gBAAgB,oBAAoB,OAAO;AAGjE,UAAI,YAAY,iBAAiB,MAAM,UAAU,SAAO,qBAAqB,eAAe,GAAG,CAAC,MAAM,IAAI;AACxG;AAAA,MACF;AACA,YAAM,YAAY,gBAAgB,UAAU,gBAAc,qBAAqB,YAAY,SAAS,CAAC;AACrG,UAAI,cAAc,IAAI;AACpB,+BAAuB;AAAA,UACrB,MAAM;AAAA,QACR,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB;AAAA,UAClB,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA;AAAA,IACF;AAGA,QAAI,oBAAoB,WAAW,gBAAgB,SAAS,GAAG;AAC7D,0BAAoB;AAAA,QAClB,OAAO,gBAAgB,SAAS;AAAA,MAClC,CAAC;AACD;AAAA,IACF;AAGA,wBAAoB;AAAA,MAClB,OAAO,oBAAoB;AAAA,IAC7B,CAAC;AAAA,EAGH,GAAG;AAAA;AAAA,IAEH,gBAAgB;AAAA;AAAA;AAAA,IAGhB,WAAW,QAAQ;AAAA,IAAO;AAAA,IAAuB;AAAA,IAAwB;AAAA,IAAqB;AAAA,IAAW;AAAA,IAAY;AAAA,EAAQ,CAAC;AAC9H,QAAM,mBAAmB,yBAAiB,UAAQ;AAChD,WAAO,YAAY,IAAI;AACvB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,yBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,MAAuC;AAEzC,IAAM,gBAAU,MAAM;AACpB,UAAI,CAAC,SAAS,WAAW,SAAS,QAAQ,aAAa,SAAS;AAC9D,YAAI,SAAS,WAAW,SAAS,QAAQ,aAAa,YAAY;AAChE,kBAAQ,KAAK,CAAC,sCAAsC,aAAa,8BAA8B,8EAA8E,8GAA8G,mFAAmF,EAAE,KAAK,IAAI,CAAC;AAAA,QAC5X,OAAO;AACL,kBAAQ,MAAM,CAAC,6DAA6D,SAAS,OAAO,4CAA4C,YAAY,aAAa,8BAA8B,IAAI,kBAAkB,oBAAoB,qHAAqH,8DAA8D,EAAE,KAAK,IAAI,CAAC;AAAA,QAC1a;AAAA,MACF;AAAA,IACF,GAAG,CAAC,aAAa,CAAC;AAAA,EACpB;AACA,EAAM,gBAAU,MAAM;AACpB,yBAAqB;AAAA,EACvB,GAAG,CAAC,oBAAoB,CAAC;AACzB,QAAM,aAAa,WAAS;AAC1B,QAAI,MAAM;AACR;AAAA,IACF;AACA,iBAAa,IAAI;AACjB,qBAAiB,IAAI;AACrB,QAAI,QAAQ;AACV,aAAO,KAAK;AAAA,IACd;AAAA,EACF;AACA,QAAM,cAAc,CAAC,OAAO,WAAW;AACrC,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,iBAAa,KAAK;AAClB,QAAI,SAAS;AACX,cAAQ,OAAO,MAAM;AAAA,IACvB;AAAA,EACF;AACA,QAAM,cAAc,CAAC,OAAO,UAAU,QAAQ,YAAY;AACxD,QAAI,UAAU;AACZ,UAAI,MAAM,WAAW,SAAS,UAAU,MAAM,MAAM,CAAC,KAAK,MAAM,QAAQ,SAAS,CAAC,CAAC,GAAG;AACpF;AAAA,MACF;AAAA,IACF,WAAW,UAAU,UAAU;AAC7B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,eAAS,OAAO,UAAU,QAAQ,OAAO;AAAA,IAC3C;AACA,kBAAc,QAAQ;AAAA,EACxB;AACA,QAAM,UAAgB,aAAO,KAAK;AAClC,QAAM,iBAAiB,CAAC,OAAO,QAAQ,aAAa,gBAAgB,SAAS,cAAc;AACzF,QAAI,SAAS;AACb,QAAI,WAAW;AACf,QAAI,UAAU;AACZ,iBAAW,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,IAAI,CAAC;AACnD,UAAI,MAAuC;AACzC,cAAM,UAAU,SAAS,OAAO,SAAO,qBAAqB,QAAQ,GAAG,CAAC;AACxE,YAAI,QAAQ,SAAS,GAAG;AACtB,kBAAQ,MAAM,CAAC,+CAA+C,aAAa,6CAA6C,0EAA0E,QAAQ,MAAM,WAAW,EAAE,KAAK,IAAI,CAAC;AAAA,QACzO;AAAA,MACF;AACA,YAAM,YAAY,SAAS,UAAU,eAAa,qBAAqB,QAAQ,SAAS,CAAC;AACzF,UAAI,cAAc,IAAI;AACpB,iBAAS,KAAK,MAAM;AAAA,MACtB,WAAW,WAAW,YAAY;AAChC,iBAAS,OAAO,WAAW,CAAC;AAC5B,iBAAS;AAAA,MACX;AAAA,IACF;AACA,oBAAgB,OAAO,UAAU,MAAM;AACvC,gBAAY,OAAO,UAAU,QAAQ;AAAA,MACnC;AAAA,IACF,CAAC;AACD,QAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,MAAM,WAAW,CAAC,MAAM,UAAU;AACzE,kBAAY,OAAO,MAAM;AAAA,IAC3B;AACA,QAAI,iBAAiB,QAAQ,iBAAiB,WAAW,QAAQ,WAAW,iBAAiB,WAAW,CAAC,QAAQ,SAAS;AACxH,eAAS,QAAQ,KAAK;AAAA,IACxB;AAAA,EACF;AACA,WAAS,cAAc,OAAO,WAAW;AACvC,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AACA,QAAI,YAAY;AAChB,WAAO,MAAM;AAEX,UAAI,cAAc,UAAU,cAAc,MAAM,UAAU,cAAc,cAAc,cAAc,IAAI;AACtG,eAAO;AAAA,MACT;AACA,YAAM,SAAS,SAAS,cAAc,oBAAoB,SAAS,IAAI;AAGvE,UAAI,CAAC,UAAU,CAAC,OAAO,aAAa,UAAU,KAAK,OAAO,YAAY,OAAO,aAAa,eAAe,MAAM,QAAQ;AACrH,qBAAa,cAAc,SAAS,IAAI;AAAA,MAC1C,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,CAAC,OAAO,cAAc;AAC3C,QAAI,CAAC,UAAU;AACb;AAAA,IACF;AACA,QAAI,eAAe,IAAI;AACrB,kBAAY,OAAO,aAAa;AAAA,IAClC;AACA,QAAI,UAAU;AACd,QAAI,eAAe,IAAI;AACrB,UAAI,eAAe,MAAM,cAAc,YAAY;AACjD,kBAAU,MAAM,SAAS;AAAA,MAC3B;AAAA,IACF,OAAO;AACL,iBAAW,cAAc,SAAS,IAAI;AACtC,UAAI,UAAU,GAAG;AACf,kBAAU;AAAA,MACZ;AACA,UAAI,YAAY,MAAM,QAAQ;AAC5B,kBAAU;AAAA,MACZ;AAAA,IACF;AACA,cAAU,cAAc,SAAS,SAAS;AAC1C,kBAAc,OAAO;AACrB,aAAS,OAAO;AAAA,EAClB;AACA,QAAM,cAAc,WAAS;AAC3B,gBAAY,UAAU;AACtB,uBAAmB,EAAE;AACrB,QAAI,eAAe;AACjB,oBAAc,OAAO,IAAI,OAAO;AAAA,IAClC;AACA,gBAAY,OAAO,WAAW,CAAC,IAAI,MAAM,OAAO;AAAA,EAClD;AACA,QAAM,gBAAgB,WAAS,WAAS;AACtC,QAAI,MAAM,WAAW;AACnB,YAAM,UAAU,KAAK;AAAA,IACvB;AACA,QAAI,MAAM,qBAAqB;AAC7B;AAAA,IACF;AACA,QAAI,eAAe,MAAM,CAAC,CAAC,aAAa,YAAY,EAAE,SAAS,MAAM,GAAG,GAAG;AACzE,oBAAc,EAAE;AAChB,eAAS,EAAE;AAAA,IACb;AAGA,QAAI,MAAM,UAAU,KAAK;AACvB,cAAQ,MAAM,KAAK;AAAA,QACjB,KAAK;AACH,cAAI,aAAa,mBAAmB;AAElC,kBAAM,eAAe;AACrB,mCAAuB;AAAA,cACrB,MAAM;AAAA,cACN,WAAW;AAAA,cACX,QAAQ;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AACH,cAAI,aAAa,mBAAmB;AAElC,kBAAM,eAAe;AACrB,mCAAuB;AAAA,cACrB,MAAM;AAAA,cACN,WAAW;AAAA,cACX,QAAQ;AAAA,cACR;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM,CAAC;AAAA,YACP,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AAEH,gBAAM,eAAe;AACrB,iCAAuB;AAAA,YACrB,MAAM;AAAA,YACN,WAAW;AAAA,YACX,QAAQ;AAAA,YACR;AAAA,UACF,CAAC;AACD,qBAAW,KAAK;AAChB;AAAA,QACF,KAAK;AACH,yBAAe,OAAO,UAAU;AAChC;AAAA,QACF,KAAK;AACH,yBAAe,OAAO,MAAM;AAC5B;AAAA,QACF,KAAK;AACH,cAAI,oBAAoB,YAAY,MAAM,WAAW;AACnD,kBAAM,SAAS,gBAAgB,oBAAoB,OAAO;AAC1D,kBAAM,WAAW,oBAAoB,kBAAkB,MAAM,IAAI;AAGjE,kBAAM,eAAe;AACrB,gBAAI,UAAU;AACZ;AAAA,YACF;AACA,2BAAe,OAAO,QAAQ,cAAc;AAG5C,gBAAI,cAAc;AAChB,uBAAS,QAAQ,kBAAkB,SAAS,QAAQ,MAAM,QAAQ,SAAS,QAAQ,MAAM,MAAM;AAAA,YACjG;AAAA,UACF,WAAW,YAAY,eAAe,MAAM,8BAA8B,OAAO;AAC/E,gBAAI,UAAU;AAEZ,oBAAM,eAAe;AAAA,YACvB;AACA,2BAAe,OAAO,YAAY,gBAAgB,UAAU;AAAA,UAC9D;AACA;AAAA,QACF,KAAK;AACH,cAAI,WAAW;AAEb,kBAAM,eAAe;AAErB,kBAAM,gBAAgB;AACtB,wBAAY,OAAO,QAAQ;AAAA,UAC7B,WAAW,kBAAkB,eAAe,MAAM,YAAY,MAAM,SAAS,IAAI;AAE/E,kBAAM,eAAe;AAErB,kBAAM,gBAAgB;AACtB,wBAAY,KAAK;AAAA,UACnB;AACA;AAAA,QACF,KAAK;AAEH,cAAI,YAAY,CAAC,YAAY,eAAe,MAAM,MAAM,SAAS,GAAG;AAClE,kBAAM,QAAQ,eAAe,KAAK,MAAM,SAAS,IAAI;AACrD,kBAAM,WAAW,MAAM,MAAM;AAC7B,qBAAS,OAAO,OAAO,CAAC;AACxB,wBAAY,OAAO,UAAU,gBAAgB;AAAA,cAC3C,QAAQ,MAAM,KAAK;AAAA,YACrB,CAAC;AAAA,UACH;AACA;AAAA,QACF,KAAK;AAEH,cAAI,YAAY,CAAC,YAAY,eAAe,MAAM,MAAM,SAAS,KAAK,eAAe,IAAI;AACvF,kBAAM,QAAQ;AACd,kBAAM,WAAW,MAAM,MAAM;AAC7B,qBAAS,OAAO,OAAO,CAAC;AACxB,wBAAY,OAAO,UAAU,gBAAgB;AAAA,cAC3C,QAAQ,MAAM,KAAK;AAAA,YACrB,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,cAAc,WAAS;AAC3B,eAAW,IAAI;AACf,QAAI,eAAe,CAAC,YAAY,SAAS;AACvC,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,aAAa,WAAS;AAE1B,QAAI,kCAAkC,UAAU,GAAG;AACjD,eAAS,QAAQ,MAAM;AACvB;AAAA,IACF;AACA,eAAW,KAAK;AAChB,eAAW,UAAU;AACrB,gBAAY,UAAU;AACtB,QAAI,cAAc,oBAAoB,YAAY,MAAM,WAAW;AACjE,qBAAe,OAAO,gBAAgB,oBAAoB,OAAO,GAAG,MAAM;AAAA,IAC5E,WAAW,cAAc,YAAY,eAAe,IAAI;AACtD,qBAAe,OAAO,YAAY,QAAQ,UAAU;AAAA,IACtD,WAAW,aAAa;AACtB,sBAAgB,OAAO,OAAO,MAAM;AAAA,IACtC;AACA,gBAAY,OAAO,MAAM;AAAA,EAC3B;AACA,QAAM,oBAAoB,WAAS;AACjC,UAAM,WAAW,MAAM,OAAO;AAC9B,QAAI,eAAe,UAAU;AAC3B,yBAAmB,QAAQ;AAC3B,uBAAiB,KAAK;AACtB,UAAI,eAAe;AACjB,sBAAc,OAAO,UAAU,OAAO;AAAA,MACxC;AAAA,IACF;AACA,QAAI,aAAa,IAAI;AACnB,UAAI,CAAC,oBAAoB,CAAC,UAAU;AAClC,oBAAY,OAAO,MAAM,OAAO;AAAA,MAClC;AAAA,IACF,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AACA,QAAM,wBAAwB,WAAS;AACrC,UAAM,QAAQ,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAC1E,QAAI,oBAAoB,YAAY,OAAO;AACzC,0BAAoB;AAAA,QAClB;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,yBAAyB,WAAS;AACtC,wBAAoB;AAAA,MAClB;AAAA,MACA,OAAO,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAAA,MACnE,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,UAAU;AAAA,EACpB;AACA,QAAM,oBAAoB,WAAS;AACjC,UAAM,QAAQ,OAAO,MAAM,cAAc,aAAa,mBAAmB,CAAC;AAC1E,mBAAe,OAAO,gBAAgB,KAAK,GAAG,cAAc;AAC5D,YAAQ,UAAU;AAAA,EACpB;AACA,QAAM,kBAAkB,WAAS,WAAS;AACxC,UAAM,WAAW,MAAM,MAAM;AAC7B,aAAS,OAAO,OAAO,CAAC;AACxB,gBAAY,OAAO,UAAU,gBAAgB;AAAA,MAC3C,QAAQ,MAAM,KAAK;AAAA,IACrB,CAAC;AAAA,EACH;AACA,QAAM,uBAAuB,WAAS;AACpC,QAAI,MAAM;AACR,kBAAY,OAAO,aAAa;AAAA,IAClC,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAGA,QAAM,kBAAkB,WAAS;AAE/B,QAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C;AAAA,IACF;AACA,QAAI,MAAM,OAAO,aAAa,IAAI,MAAM,IAAI;AAC1C,YAAM,eAAe;AAAA,IACvB;AAAA,EACF;AAGA,QAAM,cAAc,WAAS;AAE3B,QAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAM,GAAG;AAC/C;AAAA,IACF;AACA,aAAS,QAAQ,MAAM;AACvB,QAAI,iBAAiB,WAAW,WAAW,SAAS,QAAQ,eAAe,SAAS,QAAQ,mBAAmB,GAAG;AAChH,eAAS,QAAQ,OAAO;AAAA,IAC1B;AACA,eAAW,UAAU;AAAA,EACvB;AACA,QAAM,uBAAuB,WAAS;AACpC,QAAI,CAAC,iBAAiB,eAAe,MAAM,CAAC,OAAO;AACjD,2BAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,QAAQ,YAAY,WAAW,SAAS;AAC5C,UAAQ,UAAU,WAAW,MAAM,SAAS,IAAI,UAAU;AAC1D,MAAI,iBAAiB;AACrB,MAAI,SAAS;AAEX,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,OAAO;AACX,qBAAiB,gBAAgB,OAAO,CAAC,KAAK,QAAQ,UAAU;AAC9D,YAAM,QAAQ,QAAQ,MAAM;AAC5B,UAAI,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,EAAE,UAAU,OAAO;AACzD,YAAI,IAAI,SAAS,CAAC,EAAE,QAAQ,KAAK,MAAM;AAAA,MACzC,OAAO;AACL,YAAI,MAAuC;AACzC,cAAI,QAAQ,IAAI,KAAK,KAAK,CAAC,MAAM;AAC/B,oBAAQ,KAAK,qEAAqE,aAAa,gCAAgC,8EAA8E;AAC7M,mBAAO;AAAA,UACT;AACA,kBAAQ,IAAI,OAAO,IAAI;AAAA,QACzB;AACA,YAAI,KAAK;AAAA,UACP,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA,SAAS,CAAC,MAAM;AAAA,QAClB,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,GAAG,CAAC,CAAC;AAAA,EACP;AACA,MAAI,gBAAgB,SAAS;AAC3B,eAAW;AAAA,EACb;AACA,SAAO;AAAA,IACL,cAAc,CAAC,QAAQ,CAAC,OAAO;AAAA,MAC7B,GAAG;AAAA,MACH,WAAW,cAAc,KAAK;AAAA,MAC9B,aAAa;AAAA,MACb,SAAS;AAAA,IACX;AAAA,IACA,oBAAoB,OAAO;AAAA,MACzB,IAAI,GAAG,EAAE;AAAA,MACT,SAAS;AAAA,IACX;AAAA,IACA,eAAe,OAAO;AAAA,MACpB;AAAA,MACA,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,MACV,aAAa;AAAA;AAAA;AAAA,MAGb,yBAAyB,YAAY,KAAK;AAAA,MAC1C,qBAAqB,eAAe,SAAS;AAAA,MAC7C,iBAAiB,mBAAmB,GAAG,EAAE,aAAa;AAAA,MACtD,iBAAiB;AAAA;AAAA;AAAA,MAGjB,cAAc;AAAA,MACd,KAAK;AAAA,MACL,gBAAgB;AAAA,MAChB,YAAY;AAAA,MACZ,MAAM;AAAA,MACN,UAAU;AAAA,IACZ;AAAA,IACA,eAAe,OAAO;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,wBAAwB,OAAO;AAAA,MAC7B,UAAU;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa,CAAC;AAAA,MACZ;AAAA,IACF,OAAO;AAAA,MACL,KAAK;AAAA,MACL,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,GAAI,CAAC,YAAY;AAAA,QACf,UAAU,gBAAgB,KAAK;AAAA,MACjC;AAAA,IACF;AAAA,IACA,iBAAiB,OAAO;AAAA,MACtB,MAAM;AAAA,MACN,IAAI,GAAG,EAAE;AAAA,MACT,mBAAmB,GAAG,EAAE;AAAA,MACxB,KAAK;AAAA,MACL,aAAa,WAAS;AAEpB,cAAM,eAAe;AAAA,MACvB;AAAA,IACF;AAAA,IACA,gBAAgB,CAAC;AAAA,MACf;AAAA,MACA;AAAA,IACF,MAAM;AACJ,YAAM,YAAY,WAAW,QAAQ,CAAC,KAAK,GAAG,KAAK,YAAU,UAAU,QAAQ,qBAAqB,QAAQ,MAAM,CAAC;AACnH,YAAM,WAAW,oBAAoB,kBAAkB,MAAM,IAAI;AACjE,aAAO;AAAA,QACL,MAAK,6CAAe,YAAW,eAAe,MAAM;AAAA,QACpD,UAAU;AAAA,QACV,MAAM;AAAA,QACN,IAAI,GAAG,EAAE,WAAW,KAAK;AAAA,QACzB,aAAa;AAAA,QACb,SAAS;AAAA,QACT,cAAc;AAAA,QACd,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,iBAAiB;AAAA,MACnB;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,aAAa;AAAA,IACvB;AAAA,IACA,SAAS,WAAW,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,0BAAQ;", "names": ["value"]}