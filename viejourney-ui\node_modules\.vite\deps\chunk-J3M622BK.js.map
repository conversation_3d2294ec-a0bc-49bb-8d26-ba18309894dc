{"version": 3, "sources": ["../../@mui/x-telemetry/esm/runtime/config.import-meta.js", "../../@mui/x-license/esm/encoding/md5.js", "../../@mui/x-license/esm/encoding/base64.js", "../../@mui/x-license/esm/utils/plan.js", "../../@mui/x-license/esm/utils/licenseModel.js", "../../@mui/x-license/esm/generateLicense/generateLicense.js", "../../@mui/x-license/esm/utils/licenseErrorMessageUtils.js", "../../@mui/x-license/esm/utils/licenseInfo.js", "../../@mui/x-license/esm/utils/licenseStatus.js", "../../@mui/x-license/esm/verifyLicense/verifyLicense.js", "../../@mui/x-license/esm/useLicenseVerifier/useLicenseVerifier.js", "../../@mui/x-telemetry/esm/runtime/events.js", "../../@mui/x-telemetry/esm/runtime/config.js", "../../@mui/x-telemetry/esm/runtime/fetcher.js", "../../@mui/x-telemetry/esm/runtime/sender.js", "../../@mui/x-telemetry/esm/runtime/settings.js", "../../@mui/x-telemetry/esm/index.js", "../../@mui/x-license/esm/Unstable_LicenseInfoProvider/MuiLicenseInfoContext.js", "../../@mui/x-license/esm/Watermark/Watermark.js", "../../@mui/x-internals/esm/fastMemo/fastMemo.js", "../../@mui/x-license/esm/Unstable_LicenseInfoProvider/LicenseInfoProvider.js"], "sourcesContent": ["const importMetaEnv = import.meta.env;\nexport { importMetaEnv };", "/* eslint-disable */\n// See \"precomputation\" in notes\nconst k = [];\nlet i = 0;\nfor (; i < 64;) {\n  k[i] = 0 | Math.sin(++i % Math.PI) * **********;\n  // k[i] = 0 | (Math.abs(Math.sin(++i)) * **********);\n}\nexport function md5(s) {\n  const words = [];\n  let b,\n    c,\n    d,\n    j = unescape(encodeURI(s)) + '\\x80',\n    a = j.length;\n  const h = [b = 0x67452301, c = 0xefcdab89, ~b, ~c];\n  s = --a / 4 + 2 | 15;\n\n  // See \"Length bits\" in notes\n  words[--s] = a * 8;\n  for (; ~a;) {\n    // a !== -1\n    words[a >> 2] |= j.charCodeAt(a) << 8 * a--;\n  }\n  for (i = j = 0; i < s; i += 16) {\n    a = h;\n    for (; j < 64; a = [d = a[3], b + ((d = a[0] + [b & c | ~b & d, d & b | ~d & c, b ^ c ^ d, c ^ (b | ~d)][a = j >> 4] + k[j] + ~~words[i | [j, 5 * j + 1, 3 * j + 5, 7 * j][a] & 15]) << (a = [7, 12, 17, 22, 5, 9, 14, 20, 4, 11, 16, 23, 6, 10, 15, 21][4 * a + j++ % 4]) | d >>> -a), b, c]) {\n      b = a[1] | 0;\n      c = a[2];\n    }\n\n    // See \"Integer safety\" in notes\n    for (j = 4; j;) h[--j] += a[j];\n\n    // j === 0\n  }\n  for (s = ''; j < 32;) {\n    s += (h[j >> 3] >> (1 ^ j++) * 4 & 15).toString(16);\n    // s += ((h[j >> 3] >> (4 ^ 4 * j++)) & 15).toString(16);\n  }\n  return s;\n}", "/* eslint-disable */\nconst _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\nfunction utf8Encode(str) {\n  for (let n = 0; n < str.length; n++) {\n    const c = str.charCodeAt(n);\n    if (c >= 128) {\n      throw new Error('ASCII only support');\n    }\n  }\n  return str;\n}\nexport const base64Decode = input => {\n  let output = '';\n  let chr1, chr2, chr3;\n  let enc1, enc2, enc3, enc4;\n  let i = 0;\n  input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, '');\n  while (i < input.length) {\n    enc1 = _keyStr.indexOf(input.charAt(i++));\n    enc2 = _keyStr.indexOf(input.charAt(i++));\n    enc3 = _keyStr.indexOf(input.charAt(i++));\n    enc4 = _keyStr.indexOf(input.charAt(i++));\n    chr1 = enc1 << 2 | enc2 >> 4;\n    chr2 = (enc2 & 15) << 4 | enc3 >> 2;\n    chr3 = (enc3 & 3) << 6 | enc4;\n    output = output + String.fromCharCode(chr1);\n    if (enc3 != 64) {\n      output = output + String.fromCharCode(chr2);\n    }\n    if (enc4 != 64) {\n      output = output + String.fromCharCode(chr3);\n    }\n  }\n  return output;\n};\nexport const base64Encode = input => {\n  let output = '';\n  let chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n  let i = 0;\n  input = utf8Encode(input);\n  while (i < input.length) {\n    chr1 = input.charCodeAt(i++);\n    chr2 = input.charCodeAt(i++);\n    chr3 = input.charCodeAt(i++);\n    enc1 = chr1 >> 2;\n    enc2 = (chr1 & 3) << 4 | chr2 >> 4;\n    enc3 = (chr2 & 15) << 2 | chr3 >> 6;\n    enc4 = chr3 & 63;\n    if (isNaN(chr2)) {\n      enc3 = enc4 = 64;\n    } else if (isNaN(chr3)) {\n      enc4 = 64;\n    }\n    output = output + _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4);\n  }\n  return output;\n};", "export const PLAN_SCOPES = ['pro', 'premium'];\nexport const PLAN_VERSIONS = ['initial', 'Q3-2024'];", "export const LICENSE_MODELS = [\n/**\n * A license is outdated if the current version of the software was released after the expiry date of the license.\n * But the license can be used indefinitely with an older version of the software.\n */\n'perpetual',\n/**\n * On development, a license is outdated if the expiry date has been reached\n * On production, a license is outdated if the current version of the software was released after the expiry date of the license (see \"perpetual\")\n */\n'annual',\n/**\n * Legacy. The previous name for 'annual'.\n * Can be removed once old license keys generated with 'subscription' are no longer supported.\n * To support for a while. We need more years of backward support and we sell multi year licenses.\n */\n'subscription'];", "import { md5 } from \"../encoding/md5.js\";\nimport { base64Encode } from \"../encoding/base64.js\";\nimport { PLAN_SCOPES } from \"../utils/plan.js\";\nimport { LICENSE_MODELS } from \"../utils/licenseModel.js\";\nconst licenseVersion = '2';\nfunction getClearLicenseString(details) {\n  if (details.planScope && !PLAN_SCOPES.includes(details.planScope)) {\n    throw new Error('MUI X: Invalid scope');\n  }\n  if (details.licenseModel && !LICENSE_MODELS.includes(details.licenseModel)) {\n    throw new Error('MUI X: Invalid licensing model');\n  }\n  const keyParts = [`O=${details.orderNumber}`, `E=${details.expiryDate.getTime()}`, `S=${details.planScope}`, `LM=${details.licenseModel}`, `PV=${details.planVersion}`, `KV=${licenseVersion}`];\n  return keyParts.join(',');\n}\nexport function generateLicense(details) {\n  const licenseStr = getClearLicenseString(details);\n  return `${md5(base64Encode(licenseStr))}${base64Encode(licenseStr)}`;\n}", "/**\n * Workaround for the codesadbox preview error.\n *\n * Once these issues are resolved\n * https://github.com/mui/mui-x/issues/15765\n * https://github.com/codesandbox/codesandbox-client/issues/8673\n *\n * `showError` can simply use `console.error` again.\n */\nconst isCodeSandbox = typeof window !== 'undefined' && window.location.hostname.endsWith('.csb.app');\nfunction showError(message) {\n  // eslint-disable-next-line no-console\n  const logger = isCodeSandbox ? console.log : console.error;\n  logger(['*************************************************************', '', ...message, '', '*************************************************************'].join('\\n'));\n}\nexport function showInvalidLicenseKeyError() {\n  showError(['MUI X: Invalid license key.', '', \"Your MUI X license key format isn't valid. It could be because the license key is missing a character or has a typo.\", '', 'To solve the issue, you need to double check that `setLicenseKey()` is called with the right argument', 'Please check the license key installation https://mui.com/r/x-license-key-installation.']);\n}\nexport function showLicenseKeyPlanMismatchError() {\n  showError(['MUI X: License key plan mismatch.', '', 'Your use of MUI X is not compatible with the plan of your license key. The feature you are trying to use is not included in the plan of your license key. This happens if you try to use Data Grid Premium with a license key for the Pro plan.', '', 'To solve the issue, you can upgrade your plan from Pro to Premium at https://mui.com/r/x-get-license?scope=premium.', \"Of if you didn't intend to use Premium features, you can replace the import of `@mui/x-data-grid-premium` with `@mui/x-data-grid-pro`.\"]);\n}\nexport function showNotAvailableInInitialProPlanError() {\n  showError(['MUI X: Component not included in your license.', '', 'The component you are trying to use is not included in the Pro Plan you purchased.', '', 'Your license is from an old version of the Pro Plan that is only compatible with the `@mui/x-data-grid-pro` and `@mui/x-date-pickers-pro` commercial packages.', '', 'To start using another Pro package, please consider reaching to our sales team to upgrade your license or visit https://mui.com/r/x-get-license to get a new license key.']);\n}\nexport function showMissingLicenseKeyError({\n  plan,\n  packageName\n}) {\n  showError(['MUI X: Missing license key.', '', `The license key is missing. You might not be allowed to use \\`${packageName}\\` which is part of MUI X ${plan}.`, '', 'To solve the issue, you can check the free trial conditions: https://mui.com/r/x-license-trial.', 'If you are eligible no actions are required. If you are not eligible to the free trial, you need to purchase a license https://mui.com/r/x-get-license or stop using the software immediately.']);\n}\nexport function showExpiredPackageVersionError({\n  packageName\n}) {\n  showError(['MUI X: Expired package version.', '', `You have installed a version of \\`${packageName}\\` that is outside of the maintenance plan of your license key. By default, commercial licenses provide access to new versions released during the first year after the purchase.`, '', 'To solve the issue, you can renew your license https://mui.com/r/x-get-license or install an older version of the npm package that is compatible with your license key.']);\n}\nexport function showExpiredAnnualGraceLicenseKeyError({\n  plan,\n  licenseKey,\n  expiryTimestamp\n}) {\n  showError(['MUI X: Expired license key.', '', `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, '', 'To solve the problem you can either:', '', '- Renew your license https://mui.com/r/x-get-license and use the new key', `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, '', 'Note that your license is perpetual in production environments with any version released before your license term ends.', '', `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, '']);\n}\nexport function showExpiredAnnualLicenseKeyError({\n  plan,\n  licenseKey,\n  expiryTimestamp\n}) {\n  throw new Error(['MUI X: Expired license key.', '', `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, '', 'To solve the problem you can either:', '', '- Renew your license https://mui.com/r/x-get-license and use the new key', `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, '', 'Note that your license is perpetual in production environments with any version released before your license term ends.', '', `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, ''].join('\\n'));\n}", "/**\n * @ignore - do not document.\n */\n\n// Store the license information in a global, so it can be shared\n// when module duplication occurs. The duplication of the modules can happen\n// if using multiple version of MUI X at the same time of the bundler\n// decide to duplicate to improve the size of the chunks.\n// eslint-disable-next-line no-underscore-dangle\nglobalThis.__MUI_LICENSE_INFO__ = globalThis.__MUI_LICENSE_INFO__ || {\n  key: undefined\n};\nexport class LicenseInfo {\n  static getLicenseInfo() {\n    // eslint-disable-next-line no-underscore-dangle\n    return globalThis.__MUI_LICENSE_INFO__;\n  }\n  static getLicenseKey() {\n    return LicenseInfo.getLicenseInfo().key;\n  }\n  static setLicenseKey(key) {\n    const licenseInfo = LicenseInfo.getLicenseInfo();\n    licenseInfo.key = key;\n  }\n}", "// eslint-disable-next-line @typescript-eslint/naming-convention\nexport let LICENSE_STATUS = /*#__PURE__*/function (LICENSE_STATUS) {\n  LICENSE_STATUS[\"NotFound\"] = \"NotFound\";\n  LICENSE_STATUS[\"Invalid\"] = \"Invalid\";\n  LICENSE_STATUS[\"ExpiredAnnual\"] = \"ExpiredAnnual\";\n  LICENSE_STATUS[\"ExpiredAnnualGrace\"] = \"ExpiredAnnualGrace\";\n  LICENSE_STATUS[\"ExpiredVersion\"] = \"ExpiredVersion\";\n  LICENSE_STATUS[\"Valid\"] = \"Valid\";\n  LICENSE_STATUS[\"OutOfScope\"] = \"OutOfScope\";\n  LICENSE_STATUS[\"NotAvailableInInitialProPlan\"] = \"NotAvailableInInitialProPlan\";\n  return LICENSE_STATUS;\n}({});", "import { base64Decode, base64Encode } from \"../encoding/base64.js\";\nimport { md5 } from \"../encoding/md5.js\";\nimport { LICENSE_STATUS } from \"../utils/licenseStatus.js\";\nimport { PLAN_SCOPES } from \"../utils/plan.js\";\nimport { LICENSE_MODELS } from \"../utils/licenseModel.js\";\nconst getDefaultReleaseDate = () => {\n  const today = new Date();\n  today.setHours(0, 0, 0, 0);\n  return today;\n};\nexport function generateReleaseInfo(releaseDate = getDefaultReleaseDate()) {\n  return base64Encode(releaseDate.getTime().toString());\n}\nfunction isPlanScopeSufficient(packageName, planScope) {\n  let acceptedScopes;\n  if (packageName.includes('-pro')) {\n    acceptedScopes = ['pro', 'premium'];\n  } else if (packageName.includes('-premium')) {\n    acceptedScopes = ['premium'];\n  } else {\n    acceptedScopes = [];\n  }\n  return acceptedScopes.includes(planScope);\n}\nconst expiryReg = /^.*EXPIRY=([0-9]+),.*$/;\nconst PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN = ['x-data-grid-pro', 'x-date-pickers-pro'];\n\n/**\n * Format: ORDER:${orderNumber},EXPIRY=${expiryTimestamp},KEYVERSION=1\n */\nconst decodeLicenseVersion1 = license => {\n  let expiryTimestamp;\n  try {\n    expiryTimestamp = parseInt(license.match(expiryReg)[1], 10);\n    if (!expiryTimestamp || Number.isNaN(expiryTimestamp)) {\n      expiryTimestamp = null;\n    }\n  } catch (err) {\n    expiryTimestamp = null;\n  }\n  return {\n    planScope: 'pro',\n    licenseModel: 'perpetual',\n    expiryTimestamp,\n    planVersion: 'initial'\n  };\n};\n\n/**\n * Format: O=${orderNumber},E=${expiryTimestamp},S=${planScope},LM=${licenseModel},PV=${planVersion},KV=2`;\n */\nconst decodeLicenseVersion2 = license => {\n  const licenseInfo = {\n    planScope: null,\n    licenseModel: null,\n    expiryTimestamp: null,\n    planVersion: 'initial'\n  };\n  license.split(',').map(token => token.split('=')).filter(el => el.length === 2).forEach(([key, value]) => {\n    if (key === 'S') {\n      licenseInfo.planScope = value;\n    }\n    if (key === 'LM') {\n      licenseInfo.licenseModel = value;\n    }\n    if (key === 'E') {\n      const expiryTimestamp = parseInt(value, 10);\n      if (expiryTimestamp && !Number.isNaN(expiryTimestamp)) {\n        licenseInfo.expiryTimestamp = expiryTimestamp;\n      }\n    }\n    if (key === 'PV') {\n      licenseInfo.planVersion = value;\n    }\n  });\n  return licenseInfo;\n};\n\n/**\n * Decode the license based on its key version and return a version-agnostic `MuiLicense` object.\n */\nconst decodeLicense = encodedLicense => {\n  const license = base64Decode(encodedLicense);\n  if (license.includes('KEYVERSION=1')) {\n    return decodeLicenseVersion1(license);\n  }\n  if (license.includes('KV=2')) {\n    return decodeLicenseVersion2(license);\n  }\n  return null;\n};\nexport function verifyLicense({\n  releaseInfo,\n  licenseKey,\n  packageName\n}) {\n  // Gets replaced at build time\n  // @ts-ignore\n  if (false) {\n    return {\n      status: LICENSE_STATUS.Valid\n    };\n  }\n  if (!releaseInfo) {\n    throw new Error('MUI X: The release information is missing. Not able to validate license.');\n  }\n  if (!licenseKey) {\n    return {\n      status: LICENSE_STATUS.NotFound\n    };\n  }\n  const hash = licenseKey.substr(0, 32);\n  const encoded = licenseKey.substr(32);\n  if (hash !== md5(encoded)) {\n    return {\n      status: LICENSE_STATUS.Invalid\n    };\n  }\n  const license = decodeLicense(encoded);\n  if (license == null) {\n    console.error('MUI X: Error checking license. Key version not found!');\n    return {\n      status: LICENSE_STATUS.Invalid\n    };\n  }\n  if (license.licenseModel == null || !LICENSE_MODELS.includes(license.licenseModel)) {\n    console.error('MUI X: Error checking license. Licensing model not found or invalid!');\n    return {\n      status: LICENSE_STATUS.Invalid\n    };\n  }\n  if (license.expiryTimestamp == null) {\n    console.error('MUI X: Error checking license. Expiry timestamp not found or invalid!');\n    return {\n      status: LICENSE_STATUS.Invalid\n    };\n  }\n  if (license.licenseModel === 'perpetual' || process.env.NODE_ENV === 'production') {\n    const pkgTimestamp = parseInt(base64Decode(releaseInfo), 10);\n    if (Number.isNaN(pkgTimestamp)) {\n      throw new Error('MUI X: The release information is invalid. Not able to validate license.');\n    }\n    if (license.expiryTimestamp < pkgTimestamp) {\n      return {\n        status: LICENSE_STATUS.ExpiredVersion\n      };\n    }\n  } else if (license.licenseModel === 'subscription' || license.licenseModel === 'annual') {\n    if (new Date().getTime() > license.expiryTimestamp) {\n      if (\n      // 30 days grace\n      new Date().getTime() < license.expiryTimestamp + 1000 * 3600 * 24 * 30 || process.env.NODE_ENV !== 'development') {\n        return {\n          status: LICENSE_STATUS.ExpiredAnnualGrace,\n          meta: {\n            expiryTimestamp: license.expiryTimestamp,\n            licenseKey\n          }\n        };\n      }\n      return {\n        status: LICENSE_STATUS.ExpiredAnnual,\n        meta: {\n          expiryTimestamp: license.expiryTimestamp,\n          licenseKey\n        }\n      };\n    }\n  }\n  if (license.planScope == null || !PLAN_SCOPES.includes(license.planScope)) {\n    console.error('MUI X: Error checking license. planScope not found or invalid!');\n    return {\n      status: LICENSE_STATUS.Invalid\n    };\n  }\n  if (!isPlanScopeSufficient(packageName, license.planScope)) {\n    return {\n      status: LICENSE_STATUS.OutOfScope\n    };\n  }\n\n  // 'charts-pro' or 'tree-view-pro' can only be used with a newer Pro license\n  if (license.planVersion === 'initial' && license.planScope === 'pro' && !PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN.includes(packageName)) {\n    return {\n      status: LICENSE_STATUS.NotAvailableInInitialProPlan\n    };\n  }\n  return {\n    status: LICENSE_STATUS.Valid\n  };\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { sendMuiXTelemetryEvent, muiXTelemetryEvents } from '@mui/x-telemetry';\nimport { verifyLicense } from \"../verifyLicense/verifyLicense.js\";\nimport { LicenseInfo } from \"../utils/licenseInfo.js\";\nimport { showExpiredAnnualGraceLicenseKeyError, showExpiredAnnualLicenseKeyError, showInvalidLicenseKeyError, showMissingLicenseKeyError, showLicenseKeyPlanMismatchError, showExpiredPackageVersionError, showNotAvailableInInitialProPlanError } from \"../utils/licenseErrorMessageUtils.js\";\nimport { LICENSE_STATUS } from \"../utils/licenseStatus.js\";\nimport MuiLicenseInfoContext from \"../Unstable_LicenseInfoProvider/MuiLicenseInfoContext.js\";\nexport const sharedLicenseStatuses = {};\n\n/**\n * Clears the license status cache for all packages.\n * This should not be used in production code, but can be useful for testing purposes.\n */\nexport function clearLicenseStatusCache() {\n  for (const packageName in sharedLicenseStatuses) {\n    if (Object.prototype.hasOwnProperty.call(sharedLicenseStatuses, packageName)) {\n      delete sharedLicenseStatuses[packageName];\n    }\n  }\n}\nexport function useLicenseVerifier(packageName, releaseInfo) {\n  const {\n    key: contextKey\n  } = React.useContext(MuiLicenseInfoContext);\n  return React.useMemo(() => {\n    const licenseKey = contextKey ?? LicenseInfo.getLicenseKey();\n\n    // Cache the response to not trigger the error twice.\n    if (sharedLicenseStatuses[packageName] && sharedLicenseStatuses[packageName].key === licenseKey) {\n      return sharedLicenseStatuses[packageName].licenseVerifier;\n    }\n    const plan = packageName.includes('premium') ? 'Premium' : 'Pro';\n    const licenseStatus = verifyLicense({\n      releaseInfo,\n      licenseKey,\n      packageName\n    });\n    const fullPackageName = `@mui/${packageName}`;\n    sendMuiXTelemetryEvent(muiXTelemetryEvents.licenseVerification({\n      licenseKey\n    }, {\n      packageName,\n      packageReleaseInfo: releaseInfo,\n      licenseStatus: licenseStatus?.status\n    }));\n    if (licenseStatus.status === LICENSE_STATUS.Valid) {\n      // Skip\n    } else if (licenseStatus.status === LICENSE_STATUS.Invalid) {\n      showInvalidLicenseKeyError();\n    } else if (licenseStatus.status === LICENSE_STATUS.NotAvailableInInitialProPlan) {\n      showNotAvailableInInitialProPlanError();\n    } else if (licenseStatus.status === LICENSE_STATUS.OutOfScope) {\n      showLicenseKeyPlanMismatchError();\n    } else if (licenseStatus.status === LICENSE_STATUS.NotFound) {\n      showMissingLicenseKeyError({\n        plan,\n        packageName: fullPackageName\n      });\n    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnualGrace) {\n      showExpiredAnnualGraceLicenseKeyError(_extends({\n        plan\n      }, licenseStatus.meta));\n    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnual) {\n      showExpiredAnnualLicenseKeyError(_extends({\n        plan\n      }, licenseStatus.meta));\n    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredVersion) {\n      showExpiredPackageVersionError({\n        packageName: fullPackageName\n      });\n    } else if (process.env.NODE_ENV !== 'production') {\n      throw new Error('missing status handler');\n    }\n    sharedLicenseStatuses[packageName] = {\n      key: licenseKey,\n      licenseVerifier: licenseStatus\n    };\n    return licenseStatus;\n  }, [packageName, releaseInfo, contextKey]);\n}", "const noop = () => null;\nconst muiXTelemetryEvents = {\n  licenseVerification: process.env.NODE_ENV === 'production' ? noop : (context, payload) => ({\n    eventName: 'licenseVerification',\n    payload,\n    context\n  })\n};\nexport default muiXTelemetryEvents;", "const envEnabledValues = ['1', 'true', 'yes', 'y'];\nconst envDisabledValues = ['0', 'false', 'no', 'n'];\nfunction getBooleanEnv(value) {\n  if (!value) {\n    return undefined;\n  }\n  if (envEnabledValues.includes(value)) {\n    return true;\n  }\n  if (envDisabledValues.includes(value)) {\n    return false;\n  }\n  return undefined;\n}\nfunction getBooleanEnvFromEnvObject(envKey, envObj) {\n  const keys = Object.keys(envObj);\n  for (let i = 0; i < keys.length; i += 1) {\n    const key = keys[i];\n    if (!key.endsWith(envKey)) {\n      continue;\n    }\n    const value = getBooleanEnv(envObj[key]?.toLowerCase());\n    if (typeof value === 'boolean') {\n      return value;\n    }\n  }\n  return undefined;\n}\nfunction getIsTelemetryCollecting() {\n  // Check global variable\n  // eslint-disable-next-line no-underscore-dangle\n  const globalValue = globalThis.__MUI_X_TELEMETRY_DISABLED__;\n  if (typeof globalValue === 'boolean') {\n    // If disabled=true, telemetry is disabled\n    // If disabled=false, telemetry is enabled\n    return !globalValue;\n  }\n  try {\n    if (typeof process !== 'undefined' && process.env && typeof process.env === 'object') {\n      const result = getBooleanEnvFromEnvObject('MUI_X_TELEMETRY_DISABLED', process.env);\n      if (typeof result === 'boolean') {\n        // If disabled=true, telemetry is disabled\n        // If disabled=false, telemetry is enabled\n        return !result;\n      }\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  try {\n    // e.g. Vite.js\n    // eslint-disable-next-line global-require\n    const {\n      importMetaEnv\n    } = require('./config.import-meta');\n    if (importMetaEnv) {\n      const result = getBooleanEnvFromEnvObject('MUI_X_TELEMETRY_DISABLED', importMetaEnv);\n      if (typeof result === 'boolean') {\n        // If disabled=true, telemetry is disabled\n        // If disabled=false, telemetry is enabled\n        return !result;\n      }\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  try {\n    // Some build tools replace env variables on compilation\n    // e.g. Next.js, webpack EnvironmentPlugin\n    const envValue = process.env.MUI_X_TELEMETRY_DISABLED || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DISABLED || process.env.GATSBY_MUI_X_TELEMETRY_DISABLED || process.env.REACT_APP_MUI_X_TELEMETRY_DISABLED || process.env.PUBLIC_MUI_X_TELEMETRY_DISABLED;\n    const result = getBooleanEnv(envValue);\n    if (typeof result === 'boolean') {\n      // If disabled=true, telemetry is disabled\n      // If disabled=false, telemetry is enabled\n      return !result;\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  return undefined;\n}\nfunction getIsDebugModeEnabled() {\n  try {\n    // Check global variable\n    // eslint-disable-next-line no-underscore-dangle\n    const globalValue = globalThis.__MUI_X_TELEMETRY_DEBUG__;\n    if (typeof globalValue === 'boolean') {\n      return globalValue;\n    }\n    if (typeof process !== 'undefined' && process.env && typeof process.env === 'object') {\n      const result = getBooleanEnvFromEnvObject('MUI_X_TELEMETRY_DEBUG', process.env);\n      if (typeof result === 'boolean') {\n        return result;\n      }\n    }\n\n    // e.g. Webpack EnvironmentPlugin\n    if (process.env.MUI_X_TELEMETRY_DEBUG) {\n      const result = getBooleanEnv(process.env.MUI_X_TELEMETRY_DEBUG);\n      if (typeof result === 'boolean') {\n        return result;\n      }\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  try {\n    // e.g. Vite.js\n    // eslint-disable-next-line global-require\n    const {\n      importMetaEnv\n    } = require('./config.import-meta');\n    if (importMetaEnv) {\n      const result = getBooleanEnvFromEnvObject('MUI_X_TELEMETRY_DEBUG', importMetaEnv);\n      if (typeof result === 'boolean') {\n        return result;\n      }\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  try {\n    // e.g. Next.js, webpack EnvironmentPlugin\n    const envValue = process.env.MUI_X_TELEMETRY_DEBUG || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DEBUG || process.env.GATSBY_MUI_X_TELEMETRY_DEBUG || process.env.REACT_APP_MUI_X_TELEMETRY_DEBUG || process.env.PUBLIC_MUI_X_TELEMETRY_DEBUG;\n    const result = getBooleanEnv(envValue);\n    if (typeof result === 'boolean') {\n      return result;\n    }\n  } catch (_) {\n    // If there is an error, return the default value\n  }\n  return false;\n}\nfunction getNodeEnv() {\n  try {\n    return process.env.NODE_ENV ?? '<unknown>';\n  } catch (_) {\n    return '<unknown>';\n  }\n}\nlet cachedEnv = null;\nexport function getTelemetryEnvConfig(skipCache = false) {\n  if (skipCache || !cachedEnv) {\n    cachedEnv = {\n      NODE_ENV: getNodeEnv(),\n      IS_COLLECTING: getIsTelemetryCollecting(),\n      DEBUG: getIsDebugModeEnabled()\n    };\n  }\n  return cachedEnv;\n}\nexport function getTelemetryEnvConfigValue(key) {\n  return getTelemetryEnvConfig()[key];\n}\nexport function setTelemetryEnvConfigValue(key, value) {\n  getTelemetryEnvConfig()[key] = value;\n}", "async function fetchWithRetry(url, options, retries = 3) {\n  try {\n    const response = await fetch(url, options);\n    if (response.ok) {\n      return response;\n    }\n    throw new Error(`Request failed with status ${response.status}`);\n  } catch (error) {\n    if (retries === 0) {\n      throw error;\n    }\n    return new Promise(resolve => {\n      setTimeout(() => {\n        resolve(fetchWithRetry(url, options, retries - 1));\n      }, Math.random() * 3_000);\n    });\n  }\n}\nexport { fetchWithRetry };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getTelemetryEnvConfigValue } from \"./config.js\";\nimport { fetchWithRetry } from \"./fetcher.js\";\nconst sendMuiXTelemetryRetries = 3;\nfunction shouldSendTelemetry(telemetryContext) {\n  // Disable reporting in SSR / Node.js\n  if (typeof window === 'undefined') {\n    return false;\n  }\n\n  // Priority to the config (e.g. in code, env)\n  const envIsCollecting = getTelemetryEnvConfigValue('IS_COLLECTING');\n  if (typeof envIsCollecting === 'boolean') {\n    return envIsCollecting;\n  }\n\n  // Disable collection of the telemetry in CI builds,\n  // as it not related to development process\n  if (telemetryContext.traits.isCI) {\n    return false;\n  }\n\n  // Disabled by default\n  return false;\n}\nasync function sendMuiXTelemetryEvent(event) {\n  try {\n    // Disable collection of the telemetry\n    // in production environment\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n    const {\n      default: getTelemetryContext\n    } = await import(\"./get-context.js\");\n    const telemetryContext = await getTelemetryContext();\n    if (!event || !shouldSendTelemetry(telemetryContext)) {\n      return;\n    }\n    const eventPayload = _extends({}, event, {\n      context: _extends({}, telemetryContext.traits, event.context)\n    });\n    if (getTelemetryEnvConfigValue('DEBUG')) {\n      console.log('[mui-x-telemetry] event', JSON.stringify(eventPayload, null, 2));\n      return;\n    }\n\n    // TODO: batch events and send them in a single request when there will be more\n    await fetchWithRetry('https://x-telemetry.mui.com/v2/telemetry/record', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-Telemetry-Client-Version': \"8.5.3\" ?? '<dev>',\n        'X-Telemetry-Node-Env': process.env.NODE_ENV ?? '<unknown>'\n      },\n      body: JSON.stringify([eventPayload])\n    }, sendMuiXTelemetryRetries);\n  } catch (_) {\n    console.log('[mui-x-telemetry] error', _);\n  }\n}\nexport default sendMuiXTelemetryEvent;", "import { setTelemetryEnvConfigValue } from \"./config.js\";\nconst muiXTelemetrySettings = {\n  enableDebug: () => {\n    setTelemetryEnvConfigValue('DEBUG', true);\n  },\n  enableTelemetry: () => {\n    setTelemetryEnvConfigValue('IS_COLLECTING', true);\n  },\n  disableTelemetry: () => {\n    setTelemetryEnvConfigValue('IS_COLLECTING', false);\n  }\n};\nexport default muiXTelemetrySettings;", "/**\n * @mui/x-telemetry v8.5.3\n *\n * @license MUI X Commercial\n * This source code is licensed under the commercial license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport muiXTelemetryEvents from \"./runtime/events.js\";\nimport sendMuiXTelemetryEventOriginal from \"./runtime/sender.js\";\nimport muiXTelemetrySettingsOriginal from \"./runtime/settings.js\";\nconst noop = () => {};\n\n// To cut unused imports in production as early as possible\nconst sendMuiXTelemetryEvent = process.env.NODE_ENV === 'production' ? noop : sendMuiXTelemetryEventOriginal;\n\n// To cut unused imports in production as early as possible\nconst muiXTelemetrySettings = process.env.NODE_ENV === 'production' ? {\n  enableDebug: noop,\n  enableTelemetry: noop,\n  disableTelemetry: noop\n} : muiXTelemetrySettingsOriginal;\nexport { muiXTelemetryEvents, sendMuiXTelemetryEvent, muiXTelemetrySettings };", "'use client';\n\nimport * as React from 'react';\nconst MuiLicenseInfoContext = /*#__PURE__*/React.createContext({\n  key: undefined\n});\nif (process.env.NODE_ENV !== \"production\") MuiLicenseInfoContext.displayName = \"MuiLicenseInfoContext\";\nexport default MuiLicenseInfoContext;", "import * as React from 'react';\nimport { fastMemo } from '@mui/x-internals/fastMemo';\nimport { useLicenseVerifier } from \"../useLicenseVerifier/index.js\";\nimport { LICENSE_STATUS } from \"../utils/licenseStatus.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getLicenseErrorMessage(licenseStatus) {\n  switch (licenseStatus) {\n    case LICENSE_STATUS.ExpiredAnnualGrace:\n    case LICENSE_STATUS.ExpiredAnnual:\n      return 'MUI X Expired license key';\n    case LICENSE_STATUS.ExpiredVersion:\n      return 'MUI X Expired package version';\n    case LICENSE_STATUS.Invalid:\n      return 'MUI X Invalid license key';\n    case LICENSE_STATUS.OutOfScope:\n      return 'MUI X License key plan mismatch';\n    case LICENSE_STATUS.NotAvailableInInitialProPlan:\n      return 'MUI X Product not covered by plan';\n    case LICENSE_STATUS.NotFound:\n      return 'MUI X Missing license key';\n    default:\n      throw new Error('Unhandled MUI X license status.');\n  }\n}\nfunction Watermark(props) {\n  const {\n    packageName,\n    releaseInfo\n  } = props;\n  const licenseStatus = useLicenseVerifier(packageName, releaseInfo);\n  if (licenseStatus.status === LICENSE_STATUS.Valid) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      position: 'absolute',\n      pointerEvents: 'none',\n      color: '#8282829e',\n      zIndex: 100000,\n      width: '100%',\n      textAlign: 'center',\n      bottom: '50%',\n      right: 0,\n      letterSpacing: 5,\n      fontSize: 24\n    },\n    children: getLicenseErrorMessage(licenseStatus.status)\n  });\n}\nconst MemoizedWatermark = fastMemo(Watermark);\nexport { MemoizedWatermark as Watermark };", "import * as React from 'react';\nimport { fastObjectShallowCompare } from \"../fastObjectShallowCompare/index.js\";\nexport function fastMemo(component) {\n  return /*#__PURE__*/React.memo(component, fastObjectShallowCompare);\n}", "import * as React from 'react';\nimport MuiLicenseInfoContext from \"./MuiLicenseInfoContext.js\";\n\n/**\n * @ignore - do not document.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - do not document.\n */\nexport function LicenseInfoProvider({\n  info,\n  children\n}) {\n  return /*#__PURE__*/_jsx(MuiLicenseInfoContext.Provider, {\n    value: info,\n    children: children\n  });\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA,IAAM;AAAN;AAAA;AAAA,IAAM,gBAAgB,YAAY;AAAA;AAAA;;;ACElC,IAAM,IAAI,CAAC;AACX,IAAI,IAAI;AACR,OAAO,IAAI,MAAK;AACd,IAAE,CAAC,IAAI,IAAI,KAAK,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAEvC;AACO,SAAS,IAAI,GAAG;AACrB,QAAM,QAAQ,CAAC;AACf,MAAI,GACF,GACA,GACA,IAAI,SAAS,UAAU,CAAC,CAAC,IAAI,KAC7B,IAAI,EAAE;AACR,QAAM,IAAI,CAAC,IAAI,YAAY,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACjD,MAAI,EAAE,IAAI,IAAI,IAAI;AAGlB,QAAM,EAAE,CAAC,IAAI,IAAI;AACjB,SAAO,CAAC,KAAI;AAEV,UAAM,KAAK,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI;AAAA,EAC1C;AACA,OAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI;AAC9B,QAAI;AACJ,WAAO,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,MAAM,CAAC,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG;AAC7R,UAAI,EAAE,CAAC,IAAI;AACX,UAAI,EAAE,CAAC;AAAA,IACT;AAGA,SAAK,IAAI,GAAG,IAAI,GAAE,EAAE,CAAC,KAAK,EAAE,CAAC;AAAA,EAG/B;AACA,OAAK,IAAI,IAAI,IAAI,MAAK;AACpB,UAAM,EAAE,KAAK,CAAC,MAAM,IAAI,OAAO,IAAI,IAAI,SAAS,EAAE;AAAA,EAEpD;AACA,SAAO;AACT;;;ACxCA,IAAM,UAAU;AAChB,SAAS,WAAW,KAAK;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,UAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,QAAI,KAAK,KAAK;AACZ,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,eAAe,WAAS;AACnC,MAAI,SAAS;AACb,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,MAAM,MAAM;AACtB,MAAIA,KAAI;AACR,UAAQ,MAAM,QAAQ,uBAAuB,EAAE;AAC/C,SAAOA,KAAI,MAAM,QAAQ;AACvB,WAAO,QAAQ,QAAQ,MAAM,OAAOA,IAAG,CAAC;AACxC,WAAO,QAAQ,QAAQ,MAAM,OAAOA,IAAG,CAAC;AACxC,WAAO,QAAQ,QAAQ,MAAM,OAAOA,IAAG,CAAC;AACxC,WAAO,QAAQ,QAAQ,MAAM,OAAOA,IAAG,CAAC;AACxC,WAAO,QAAQ,IAAI,QAAQ;AAC3B,YAAQ,OAAO,OAAO,IAAI,QAAQ;AAClC,YAAQ,OAAO,MAAM,IAAI;AACzB,aAAS,SAAS,OAAO,aAAa,IAAI;AAC1C,QAAI,QAAQ,IAAI;AACd,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC5C;AACA,QAAI,QAAQ,IAAI;AACd,eAAS,SAAS,OAAO,aAAa,IAAI;AAAA,IAC5C;AAAA,EACF;AACA,SAAO;AACT;AACO,IAAM,eAAe,WAAS;AACnC,MAAI,SAAS;AACb,MAAI,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM;AACxC,MAAIA,KAAI;AACR,UAAQ,WAAW,KAAK;AACxB,SAAOA,KAAI,MAAM,QAAQ;AACvB,WAAO,MAAM,WAAWA,IAAG;AAC3B,WAAO,MAAM,WAAWA,IAAG;AAC3B,WAAO,MAAM,WAAWA,IAAG;AAC3B,WAAO,QAAQ;AACf,YAAQ,OAAO,MAAM,IAAI,QAAQ;AACjC,YAAQ,OAAO,OAAO,IAAI,QAAQ;AAClC,WAAO,OAAO;AACd,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,OAAO;AAAA,IAChB,WAAW,MAAM,IAAI,GAAG;AACtB,aAAO;AAAA,IACT;AACA,aAAS,SAAS,QAAQ,OAAO,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI,QAAQ,OAAO,IAAI;AAAA,EAC5G;AACA,SAAO;AACT;;;ACxDO,IAAM,cAAc,CAAC,OAAO,SAAS;;;ACArC,IAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA;AAAc;;;ACZd,IAAM,iBAAiB;AACvB,SAAS,sBAAsB,SAAS;AACtC,MAAI,QAAQ,aAAa,CAAC,YAAY,SAAS,QAAQ,SAAS,GAAG;AACjE,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACxC;AACA,MAAI,QAAQ,gBAAgB,CAAC,eAAe,SAAS,QAAQ,YAAY,GAAG;AAC1E,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACA,QAAM,WAAW,CAAC,KAAK,QAAQ,WAAW,IAAI,KAAK,QAAQ,WAAW,QAAQ,CAAC,IAAI,KAAK,QAAQ,SAAS,IAAI,MAAM,QAAQ,YAAY,IAAI,MAAM,QAAQ,WAAW,IAAI,MAAM,cAAc,EAAE;AAC9L,SAAO,SAAS,KAAK,GAAG;AAC1B;AACO,SAAS,gBAAgB,SAAS;AACvC,QAAM,aAAa,sBAAsB,OAAO;AAChD,SAAO,GAAG,IAAI,aAAa,UAAU,CAAC,CAAC,GAAG,aAAa,UAAU,CAAC;AACpE;;;ACTA,IAAM,gBAAgB,OAAO,WAAW,eAAe,OAAO,SAAS,SAAS,SAAS,UAAU;AACnG,SAAS,UAAU,SAAS;AAE1B,QAAM,SAAS,gBAAgB,QAAQ,MAAM,QAAQ;AACrD,SAAO,CAAC,iEAAiE,IAAI,GAAG,SAAS,IAAI,+DAA+D,EAAE,KAAK,IAAI,CAAC;AAC1K;AACO,SAAS,6BAA6B;AAC3C,YAAU,CAAC,+BAA+B,IAAI,wHAAwH,IAAI,yGAAyG,yFAAyF,CAAC;AAC/W;AACO,SAAS,kCAAkC;AAChD,YAAU,CAAC,qCAAqC,IAAI,mPAAmP,IAAI,uHAAuH,wIAAwI,CAAC;AAC7iB;AACO,SAAS,wCAAwC;AACtD,YAAU,CAAC,kDAAkD,IAAI,sFAAsF,IAAI,kKAAkK,IAAI,2KAA2K,CAAC;AAC/e;AACO,SAAS,2BAA2B;AAAA,EACzC;AAAA,EACA;AACF,GAAG;AACD,YAAU,CAAC,+BAA+B,IAAI,iEAAiE,WAAW,6BAA6B,IAAI,KAAK,IAAI,mGAAmG,gMAAgM,CAAC;AAC1c;AACO,SAAS,+BAA+B;AAAA,EAC7C;AACF,GAAG;AACD,YAAU,CAAC,mCAAmC,IAAI,qCAAqC,WAAW,qLAAqL,IAAI,yKAAyK,CAAC;AACvc;AACO,SAAS,sCAAsC;AAAA,EACpD;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,YAAU,CAAC,+BAA+B,IAAI,wCAAwC,IAAI,sOAAsO,IAAI,oEAAoE,IAAI,wCAAwC,IAAI,4EAA4E,2EAA2E,IAAI,WAAW,IAAI,2HAA2H,IAAI,mCAAmC,IAAI,KAAK,eAAe,CAAC,IAAI,4BAA4B,UAAU,IAAI,EAAE,CAAC;AACh1B;AACO,SAAS,iCAAiC;AAAA,EAC/C;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,IAAI,MAAM,CAAC,+BAA+B,IAAI,wCAAwC,IAAI,sOAAsO,IAAI,oEAAoE,IAAI,wCAAwC,IAAI,4EAA4E,2EAA2E,IAAI,WAAW,IAAI,2HAA2H,IAAI,mCAAmC,IAAI,KAAK,eAAe,CAAC,IAAI,4BAA4B,UAAU,IAAI,EAAE,EAAE,KAAK,IAAI,CAAC;AACj2B;;;ACvCA,WAAW,uBAAuB,WAAW,wBAAwB;AAAA,EACnE,KAAK;AACP;AACO,IAAM,cAAN,MAAM,aAAY;AAAA,EACvB,OAAO,iBAAiB;AAEtB,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,gBAAgB;AACrB,WAAO,aAAY,eAAe,EAAE;AAAA,EACtC;AAAA,EACA,OAAO,cAAc,KAAK;AACxB,UAAM,cAAc,aAAY,eAAe;AAC/C,gBAAY,MAAM;AAAA,EACpB;AACF;;;ACvBO,IAAI,iBAA8B,SAAUC,iBAAgB;AACjE,EAAAA,gBAAe,UAAU,IAAI;AAC7B,EAAAA,gBAAe,SAAS,IAAI;AAC5B,EAAAA,gBAAe,eAAe,IAAI;AAClC,EAAAA,gBAAe,oBAAoB,IAAI;AACvC,EAAAA,gBAAe,gBAAgB,IAAI;AACnC,EAAAA,gBAAe,OAAO,IAAI;AAC1B,EAAAA,gBAAe,YAAY,IAAI;AAC/B,EAAAA,gBAAe,8BAA8B,IAAI;AACjD,SAAOA;AACT,EAAE,CAAC,CAAC;;;ACNJ,IAAM,wBAAwB,MAAM;AAClC,QAAM,QAAQ,oBAAI,KAAK;AACvB,QAAM,SAAS,GAAG,GAAG,GAAG,CAAC;AACzB,SAAO;AACT;AACO,SAAS,oBAAoB,cAAc,sBAAsB,GAAG;AACzE,SAAO,aAAa,YAAY,QAAQ,EAAE,SAAS,CAAC;AACtD;AACA,SAAS,sBAAsB,aAAa,WAAW;AACrD,MAAI;AACJ,MAAI,YAAY,SAAS,MAAM,GAAG;AAChC,qBAAiB,CAAC,OAAO,SAAS;AAAA,EACpC,WAAW,YAAY,SAAS,UAAU,GAAG;AAC3C,qBAAiB,CAAC,SAAS;AAAA,EAC7B,OAAO;AACL,qBAAiB,CAAC;AAAA,EACpB;AACA,SAAO,eAAe,SAAS,SAAS;AAC1C;AACA,IAAM,YAAY;AAClB,IAAM,6CAA6C,CAAC,mBAAmB,oBAAoB;AAK3F,IAAM,wBAAwB,aAAW;AACvC,MAAI;AACJ,MAAI;AACF,sBAAkB,SAAS,QAAQ,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE;AAC1D,QAAI,CAAC,mBAAmB,OAAO,MAAM,eAAe,GAAG;AACrD,wBAAkB;AAAA,IACpB;AAAA,EACF,SAAS,KAAK;AACZ,sBAAkB;AAAA,EACpB;AACA,SAAO;AAAA,IACL,WAAW;AAAA,IACX,cAAc;AAAA,IACd;AAAA,IACA,aAAa;AAAA,EACf;AACF;AAKA,IAAM,wBAAwB,aAAW;AACvC,QAAM,cAAc;AAAA,IAClB,WAAW;AAAA,IACX,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,aAAa;AAAA,EACf;AACA,UAAQ,MAAM,GAAG,EAAE,IAAI,WAAS,MAAM,MAAM,GAAG,CAAC,EAAE,OAAO,QAAM,GAAG,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACxG,QAAI,QAAQ,KAAK;AACf,kBAAY,YAAY;AAAA,IAC1B;AACA,QAAI,QAAQ,MAAM;AAChB,kBAAY,eAAe;AAAA,IAC7B;AACA,QAAI,QAAQ,KAAK;AACf,YAAM,kBAAkB,SAAS,OAAO,EAAE;AAC1C,UAAI,mBAAmB,CAAC,OAAO,MAAM,eAAe,GAAG;AACrD,oBAAY,kBAAkB;AAAA,MAChC;AAAA,IACF;AACA,QAAI,QAAQ,MAAM;AAChB,kBAAY,cAAc;AAAA,IAC5B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAKA,IAAM,gBAAgB,oBAAkB;AACtC,QAAM,UAAU,aAAa,cAAc;AAC3C,MAAI,QAAQ,SAAS,cAAc,GAAG;AACpC,WAAO,sBAAsB,OAAO;AAAA,EACtC;AACA,MAAI,QAAQ,SAAS,MAAM,GAAG;AAC5B,WAAO,sBAAsB,OAAO;AAAA,EACtC;AACA,SAAO;AACT;AACO,SAAS,cAAc;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AAGD,MAAI,OAAO;AACT,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,MAAI,CAAC,aAAa;AAChB,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC5F;AACA,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,QAAM,OAAO,WAAW,OAAO,GAAG,EAAE;AACpC,QAAM,UAAU,WAAW,OAAO,EAAE;AACpC,MAAI,SAAS,IAAI,OAAO,GAAG;AACzB,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,QAAM,UAAU,cAAc,OAAO;AACrC,MAAI,WAAW,MAAM;AACnB,YAAQ,MAAM,uDAAuD;AACrE,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,MAAI,QAAQ,gBAAgB,QAAQ,CAAC,eAAe,SAAS,QAAQ,YAAY,GAAG;AAClF,YAAQ,MAAM,sEAAsE;AACpF,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,MAAI,QAAQ,mBAAmB,MAAM;AACnC,YAAQ,MAAM,uEAAuE;AACrF,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,MAAI,QAAQ,iBAAiB,eAAe,OAAuC;AACjF,UAAM,eAAe,SAAS,aAAa,WAAW,GAAG,EAAE;AAC3D,QAAI,OAAO,MAAM,YAAY,GAAG;AAC9B,YAAM,IAAI,MAAM,0EAA0E;AAAA,IAC5F;AACA,QAAI,QAAQ,kBAAkB,cAAc;AAC1C,aAAO;AAAA,QACL,QAAQ,eAAe;AAAA,MACzB;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,iBAAiB,kBAAkB,QAAQ,iBAAiB,UAAU;AACvF,SAAI,oBAAI,KAAK,GAAE,QAAQ,IAAI,QAAQ,iBAAiB;AAClD;AAAA;AAAA,SAEA,oBAAI,KAAK,GAAE,QAAQ,IAAI,QAAQ,kBAAkB,MAAO,OAAO,KAAK,MAAM;AAAA,QAAwC;AAChH,eAAO;AAAA,UACL,QAAQ,eAAe;AAAA,UACvB,MAAM;AAAA,YACJ,iBAAiB,QAAQ;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,QAAQ,eAAe;AAAA,QACvB,MAAM;AAAA,UACJ,iBAAiB,QAAQ;AAAA,UACzB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,aAAa,QAAQ,CAAC,YAAY,SAAS,QAAQ,SAAS,GAAG;AACzE,YAAQ,MAAM,gEAAgE;AAC9E,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,MAAI,CAAC,sBAAsB,aAAa,QAAQ,SAAS,GAAG;AAC1D,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AAGA,MAAI,QAAQ,gBAAgB,aAAa,QAAQ,cAAc,SAAS,CAAC,2CAA2C,SAAS,WAAW,GAAG;AACzI,WAAO;AAAA,MACL,QAAQ,eAAe;AAAA,IACzB;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ,eAAe;AAAA,EACzB;AACF;;;AC7LA,IAAAC,SAAuB;;;ACAvB,IAAM,sBAAsB;AAAA,EAC1B,qBAAqB,QAAwC,OAAO,CAAC,SAAS,aAAa;AAAA,IACzF,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,iBAAQ;;;ACRf,IAAM,mBAAmB,CAAC,KAAK,QAAQ,OAAO,GAAG;AACjD,IAAM,oBAAoB,CAAC,KAAK,SAAS,MAAM,GAAG;AAClD,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,OAAO;AACV,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,SAAS,KAAK,GAAG;AACpC,WAAO;AAAA,EACT;AACA,MAAI,kBAAkB,SAAS,KAAK,GAAG;AACrC,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,2BAA2B,QAAQ,QAAQ;AAdpD;AAeE,QAAM,OAAO,OAAO,KAAK,MAAM;AAC/B,WAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK,GAAG;AACvC,UAAM,MAAM,KAAKA,EAAC;AAClB,QAAI,CAAC,IAAI,SAAS,MAAM,GAAG;AACzB;AAAA,IACF;AACA,UAAM,QAAQ,eAAc,YAAO,GAAG,MAAV,mBAAa,aAAa;AACtD,QAAI,OAAO,UAAU,WAAW;AAC9B,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,2BAA2B;AAGlC,QAAM,cAAc,WAAW;AAC/B,MAAI,OAAO,gBAAgB,WAAW;AAGpC,WAAO,CAAC;AAAA,EACV;AACA,MAAI;AACF,QAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,OAAO,QAAQ,QAAQ,UAAU;AACpF,YAAM,SAAS,2BAA2B,4BAA4B,QAAQ,GAAG;AACjF,UAAI,OAAO,WAAW,WAAW;AAG/B,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,MAAI;AAGF,UAAM;AAAA,MACJ,eAAAC;AAAA,IACF,IAAI;AACJ,QAAIA,gBAAe;AACjB,YAAM,SAAS,2BAA2B,4BAA4BA,cAAa;AACnF,UAAI,OAAO,WAAW,WAAW;AAG/B,eAAO,CAAC;AAAA,MACV;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,MAAI;AAGF,UAAM,WAAW,QAAQ,IAAI,4BAA4B,QAAQ,IAAI,wCAAwC,QAAQ,IAAI,mCAAmC,QAAQ,IAAI,sCAAsC,QAAQ,IAAI;AAC1N,UAAM,SAAS,cAAc,QAAQ;AACrC,QAAI,OAAO,WAAW,WAAW;AAG/B,aAAO,CAAC;AAAA,IACV;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;AACA,SAAS,wBAAwB;AAC/B,MAAI;AAGF,UAAM,cAAc,WAAW;AAC/B,QAAI,OAAO,gBAAgB,WAAW;AACpC,aAAO;AAAA,IACT;AACA,QAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,OAAO,QAAQ,QAAQ,UAAU;AACpF,YAAM,SAAS,2BAA2B,yBAAyB,QAAQ,GAAG;AAC9E,UAAI,OAAO,WAAW,WAAW;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAGA,QAAI,QAAQ,IAAI,uBAAuB;AACrC,YAAM,SAAS,cAAc,QAAQ,IAAI,qBAAqB;AAC9D,UAAI,OAAO,WAAW,WAAW;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,MAAI;AAGF,UAAM;AAAA,MACJ,eAAAA;AAAA,IACF,IAAI;AACJ,QAAIA,gBAAe;AACjB,YAAM,SAAS,2BAA2B,yBAAyBA,cAAa;AAChF,UAAI,OAAO,WAAW,WAAW;AAC/B,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,MAAI;AAEF,UAAM,WAAW,QAAQ,IAAI,yBAAyB,QAAQ,IAAI,qCAAqC,QAAQ,IAAI,gCAAgC,QAAQ,IAAI,mCAAmC,QAAQ,IAAI;AAC9M,UAAM,SAAS,cAAc,QAAQ;AACrC,QAAI,OAAO,WAAW,WAAW;AAC/B,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;AACA,SAAS,aAAa;AACpB,MAAI;AACF,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,IAAI,YAAY;AACT,SAAS,sBAAsB,YAAY,OAAO;AACvD,MAAI,aAAa,CAAC,WAAW;AAC3B,gBAAY;AAAA,MACV,UAAU,WAAW;AAAA,MACrB,eAAe,yBAAyB;AAAA,MACxC,OAAO,sBAAsB;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,2BAA2B,KAAK;AAC9C,SAAO,sBAAsB,EAAE,GAAG;AACpC;AACO,SAAS,2BAA2B,KAAK,OAAO;AACrD,wBAAsB,EAAE,GAAG,IAAI;AACjC;;;AC5JA,eAAe,eAAe,KAAK,SAAS,UAAU,GAAG;AACvD,MAAI;AACF,UAAM,WAAW,MAAM,MAAM,KAAK,OAAO;AACzC,QAAI,SAAS,IAAI;AACf,aAAO;AAAA,IACT;AACA,UAAM,IAAI,MAAM,8BAA8B,SAAS,MAAM,EAAE;AAAA,EACjE,SAAS,OAAO;AACd,QAAI,YAAY,GAAG;AACjB,YAAM;AAAA,IACR;AACA,WAAO,IAAI,QAAQ,aAAW;AAC5B,iBAAW,MAAM;AACf,gBAAQ,eAAe,KAAK,SAAS,UAAU,CAAC,CAAC;AAAA,MACnD,GAAG,KAAK,OAAO,IAAI,GAAK;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;;;ACdA,IAAM,2BAA2B;AACjC,SAAS,oBAAoB,kBAAkB;AAE7C,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AAGA,QAAM,kBAAkB,2BAA2B,eAAe;AAClE,MAAI,OAAO,oBAAoB,WAAW;AACxC,WAAO;AAAA,EACT;AAIA,MAAI,iBAAiB,OAAO,MAAM;AAChC,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AACA,eAAe,uBAAuB,OAAO;AAC3C,MAAI;AAGF,QAAI,OAAuC;AACzC;AAAA,IACF;AACA,UAAM;AAAA,MACJ,SAAS;AAAA,IACX,IAAI,MAAM,OAAO,2BAAkB;AACnC,UAAM,mBAAmB,MAAM,oBAAoB;AACnD,QAAI,CAAC,SAAS,CAAC,oBAAoB,gBAAgB,GAAG;AACpD;AAAA,IACF;AACA,UAAM,eAAe,SAAS,CAAC,GAAG,OAAO;AAAA,MACvC,SAAS,SAAS,CAAC,GAAG,iBAAiB,QAAQ,MAAM,OAAO;AAAA,IAC9D,CAAC;AACD,QAAI,2BAA2B,OAAO,GAAG;AACvC,cAAQ,IAAI,2BAA2B,KAAK,UAAU,cAAc,MAAM,CAAC,CAAC;AAC5E;AAAA,IACF;AAGA,UAAM,eAAe,mDAAmD;AAAA,MACtE,QAAQ;AAAA,MACR,SAAS;AAAA,QACP,gBAAgB;AAAA,QAChB,8BAA8B;AAAA,QAC9B,wBAAwB;AAAA,MAC1B;AAAA,MACA,MAAM,KAAK,UAAU,CAAC,YAAY,CAAC;AAAA,IACrC,GAAG,wBAAwB;AAAA,EAC7B,SAAS,GAAG;AACV,YAAQ,IAAI,2BAA2B,CAAC;AAAA,EAC1C;AACF;AACA,IAAO,iBAAQ;;;AC5Df,IAAM,wBAAwB;AAAA,EAC5B,aAAa,MAAM;AACjB,+BAA2B,SAAS,IAAI;AAAA,EAC1C;AAAA,EACA,iBAAiB,MAAM;AACrB,+BAA2B,iBAAiB,IAAI;AAAA,EAClD;AAAA,EACA,kBAAkB,MAAM;AACtB,+BAA2B,iBAAiB,KAAK;AAAA,EACnD;AACF;AACA,IAAO,mBAAQ;;;ACCf,IAAMC,0BAAyB,QAAwC,OAAO;AAG9E,IAAMC,yBAAwB,QAAwC;AAAA,EACpE,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AACpB,IAAI;;;AClBJ,YAAuB;AACvB,IAAM,wBAA2C,oBAAc;AAAA,EAC7D,KAAK;AACP,CAAC;AACD,IAAI,KAAuC,uBAAsB,cAAc;AAC/E,IAAO,gCAAQ;;;APCR,IAAM,wBAAwB,CAAC;AAM/B,SAAS,0BAA0B;AACxC,aAAW,eAAe,uBAAuB;AAC/C,QAAI,OAAO,UAAU,eAAe,KAAK,uBAAuB,WAAW,GAAG;AAC5E,aAAO,sBAAsB,WAAW;AAAA,IAC1C;AAAA,EACF;AACF;AACO,SAAS,mBAAmB,aAAa,aAAa;AAC3D,QAAM;AAAA,IACJ,KAAK;AAAA,EACP,IAAU,kBAAW,6BAAqB;AAC1C,SAAa,eAAQ,MAAM;AACzB,UAAM,aAAa,cAAc,YAAY,cAAc;AAG3D,QAAI,sBAAsB,WAAW,KAAK,sBAAsB,WAAW,EAAE,QAAQ,YAAY;AAC/F,aAAO,sBAAsB,WAAW,EAAE;AAAA,IAC5C;AACA,UAAM,OAAO,YAAY,SAAS,SAAS,IAAI,YAAY;AAC3D,UAAM,gBAAgB,cAAc;AAAA,MAClC;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AACD,UAAM,kBAAkB,QAAQ,WAAW;AAC3C,IAAAC,wBAAuB,eAAoB,oBAAoB;AAAA,MAC7D;AAAA,IACF,GAAG;AAAA,MACD;AAAA,MACA,oBAAoB;AAAA,MACpB,eAAe,+CAAe;AAAA,IAChC,CAAC,CAAC;AACF,QAAI,cAAc,WAAW,eAAe,OAAO;AAAA,IAEnD,WAAW,cAAc,WAAW,eAAe,SAAS;AAC1D,iCAA2B;AAAA,IAC7B,WAAW,cAAc,WAAW,eAAe,8BAA8B;AAC/E,4CAAsC;AAAA,IACxC,WAAW,cAAc,WAAW,eAAe,YAAY;AAC7D,sCAAgC;AAAA,IAClC,WAAW,cAAc,WAAW,eAAe,UAAU;AAC3D,iCAA2B;AAAA,QACzB;AAAA,QACA,aAAa;AAAA,MACf,CAAC;AAAA,IACH,WAAW,cAAc,WAAW,eAAe,oBAAoB;AACrE,4CAAsC,SAAS;AAAA,QAC7C;AAAA,MACF,GAAG,cAAc,IAAI,CAAC;AAAA,IACxB,WAAW,cAAc,WAAW,eAAe,eAAe;AAChE,uCAAiC,SAAS;AAAA,QACxC;AAAA,MACF,GAAG,cAAc,IAAI,CAAC;AAAA,IACxB,WAAW,cAAc,WAAW,eAAe,gBAAgB;AACjE,qCAA+B;AAAA,QAC7B,aAAa;AAAA,MACf,CAAC;AAAA,IACH,WAAW,MAAuC;AAChD,YAAM,IAAI,MAAM,wBAAwB;AAAA,IAC1C;AACA,0BAAsB,WAAW,IAAI;AAAA,MACnC,KAAK;AAAA,MACL,iBAAiB;AAAA,IACnB;AACA,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,aAAa,UAAU,CAAC;AAC3C;;;AQhFA,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;AAEhB,SAAS,SAAS,WAAW;AAClC,SAA0B,YAAK,WAAW,wBAAwB;AACpE;;;ADAA,yBAA4B;AAC5B,SAAS,uBAAuB,eAAe;AAC7C,UAAQ,eAAe;AAAA,IACrB,KAAK,eAAe;AAAA,IACpB,KAAK,eAAe;AAClB,aAAO;AAAA,IACT,KAAK,eAAe;AAClB,aAAO;AAAA,IACT,KAAK,eAAe;AAClB,aAAO;AAAA,IACT,KAAK,eAAe;AAClB,aAAO;AAAA,IACT,KAAK,eAAe;AAClB,aAAO;AAAA,IACT,KAAK,eAAe;AAClB,aAAO;AAAA,IACT;AACE,YAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AACF;AACA,SAAS,UAAU,OAAO;AACxB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,mBAAmB,aAAa,WAAW;AACjE,MAAI,cAAc,WAAW,eAAe,OAAO;AACjD,WAAO;AAAA,EACT;AACA,aAAoB,mBAAAC,KAAK,OAAO;AAAA,IAC9B,OAAO;AAAA,MACL,UAAU;AAAA,MACV,eAAe;AAAA,MACf,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,eAAe;AAAA,MACf,UAAU;AAAA,IACZ;AAAA,IACA,UAAU,uBAAuB,cAAc,MAAM;AAAA,EACvD,CAAC;AACH;AACA,IAAM,oBAAoB,SAAS,SAAS;;;AEjD5C,IAAAC,SAAuB;AAMvB,IAAAC,sBAA4B;AAIrB,SAAS,oBAAoB;AAAA,EAClC;AAAA,EACA;AACF,GAAG;AACD,aAAoB,oBAAAC,KAAK,8BAAsB,UAAU;AAAA,IACvD,OAAO;AAAA,IACP;AAAA,EACF,CAAC;AACH;", "names": ["i", "LICENSE_STATUS", "React", "i", "importMetaEnv", "sendMuiXTelemetryEvent", "muiXTelemetrySettings", "sendMuiXTelemetryEvent", "React", "React", "_jsx", "React", "import_jsx_runtime", "_jsx"]}