import { CreateAccountDto } from 'src/common/dtos/create-account.dto';
import { UpdateUserInfoDto } from 'src/common/dtos/update-userinfo.dto';
import { BulkUpdateRoleDto } from 'src/common/dtos/bulk-update-role.dto';
import { UserService } from '../userinfo/user.service';
import { AdminService } from './admin.service';
import { DashboardQueryDto } from 'src/common/dtos/dashboard-analytics.dto';
export declare class AdminController {
    private readonly adminService;
    private readonly userService;
    constructor(adminService: AdminService, userService: UserService);
    getDashboardAnalytics(query: DashboardQueryDto): Promise<import("src/common/dtos/dashboard-analytics.dto").DashboardAnalyticsDto>;
    getRoleBasedCounts(): Promise<{
        userCount: number;
        adminCount: number;
        managerCount: number;
    }>;
    getAllAccounts(): Promise<import("../../common/entities/account.entity").Account[]>;
    createAccount(createAccountDto: CreateAccountDto): Promise<import("../../common/entities/account.entity").Account>;
    getAccountById(id: string): Promise<import("../../common/entities/account.entity").Account | null>;
    deleteAccount(id: string): Promise<import("../../common/entities/account.entity").Account | null>;
    updateActiveStatus(id: string, active: boolean): Promise<import("../../common/entities/account.entity").Account | undefined>;
    getBlogsReport(views?: number): Promise<{
        totalBlogs: number;
        totalViews: any;
    }>;
    getCommentsReport(): Promise<{
        totalComments: number;
        commentsPerBlog: any[];
    }>;
    getAllUsers(query: any): Promise<{
        status: string;
        message: string;
        data: {
            users: Array<{
                userId: string;
                accountId: string;
                email: string;
                userName: string;
                role: string;
                status: string;
                phone: string;
                address: string;
                createdAt: Date;
            }>;
            totalPages?: number;
            currentPage?: number;
            pageSize?: number;
            totalItems: number;
        };
    }>;
    getFilterUsers(query: any): Promise<{
        status: string;
        message: string;
        data: {
            users: Array<{
                userId: string;
                accountId: string;
                email: string;
                userName: string;
                role: string;
                status: string;
                phone: string;
                address: string;
                createdAt: Date;
            }>;
            totalPages?: number;
            currentPage?: number;
            pageSize?: number;
            totalItems: number;
        };
    }>;
    getPaginatedUsers(body: any): Promise<{
        status: string;
        message: string;
        data: {
            users: Array<{
                userId: string;
                accountId: string;
                email: string;
                userName: string;
                role: string;
                status: string;
                phone: string;
                address: string;
                createdAt: Date;
            }>;
            totalPages?: number;
            currentPage?: number;
            pageSize?: number;
            totalItems: number;
        };
    }>;
    getUser(id: string): Promise<{
        avatar: string | null;
        _id: import("mongoose").Types.ObjectId;
        userId: import("../../common/entities/account.entity").Account;
        fullName: string;
        dob: Date;
        phone: string;
        address: string;
        lastLoginAt: Date;
        flaggedCount: number;
        banReason: string | null;
        bannedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        location: string;
        format: string;
        file_size: string;
        dimensions: string;
        __v: number;
    }>;
    updateUserInfo(id: string, updateUserInfoDto: UpdateUserInfoDto): Promise<import("../../common/entities/userInfos.entity").UserInfos>;
    deleteUserInfo(id: string): Promise<import("@nestjs/common").HttpStatus>;
    updateUserRole(id: string, role: string): Promise<{
        status: string;
        message: string;
        data: {
            userId: string;
            accountId: string;
            email: string;
            userName: string;
            role: string;
            status: string;
        };
    }>;
    banUser(id: string, reason: string): Promise<{
        userId: string;
        accountId: string;
        role: string;
        email: string;
        userName: string;
        status: string;
        banReason: string;
        flaggedCount: number;
        bannedAt: Date;
    }>;
    unbanUser(id: string): Promise<{
        userId: string;
        accountId: string;
        role: string;
        email: string;
        userName: string;
        status: string;
        banReason: string | null;
        flaggedCount: number;
        bannedAt: Date | null;
    }>;
    bulkUpdateUserRoles(bulkUpdateRoleDto: BulkUpdateRoleDto): Promise<{
        success: Array<{
            userId: string;
            accountId: string;
            email: string;
            userName: string;
            oldRole: string;
            newRole: string;
            status: string;
        }>;
        failed: Array<{
            userId: string;
            reason: string;
        }>;
        summary: {
            totalRequested: number;
            successCount: number;
            failedCount: number;
        };
    }>;
}
