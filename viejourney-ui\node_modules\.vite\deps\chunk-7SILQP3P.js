import {
  createSvgIcon
} from "./chunk-4YKN7KBD.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/icons-material/esm/FilterList.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var FilterList_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"
}), "FilterList");

export {
  FilterList_default
};
//# sourceMappingURL=chunk-7SILQP3P.js.map
