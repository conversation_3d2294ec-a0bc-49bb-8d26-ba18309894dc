import { Document } from 'mongoose';
export declare class Trip extends Document {
    _id: string;
    title: string;
    coverImage?: string;
    destination: {
        id: string;
        name: string;
        location: {
            lat: number;
            lng: number;
        };
    };
    startDate: Date;
    endDate: Date;
    createdBy: string;
    budgetRange: string;
    tripmateRange: string;
    description: string;
    visibility: boolean;
    tripmates: string[];
}
