import mongoose, { Document } from 'mongoose';
declare class Coordinate {
    latitude: number;
    longitude: number;
}
export declare class Hotel extends Document {
    name: string;
    description: string;
    rating: number;
    address: string;
    coordinate: Coordinate;
    image: string[];
}
export declare const HotelSchema: mongoose.Schema<Hotel, mongoose.Model<Hotel, any, any, any, mongoose.Document<unknown, any, Hotel, any> & Hotel & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Hotel, mongoose.Document<unknown, {}, mongoose.FlatRecord<Hotel>, {}> & mongoose.FlatRecord<Hotel> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
export {};
