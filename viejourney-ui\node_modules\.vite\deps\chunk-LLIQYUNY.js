import {
  createSvgIcon
} from "./chunk-4YKN7KBD.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/icons-material/esm/FiberManualRecord.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var FiberManualRecord_default = createSvgIcon((0, import_jsx_runtime.jsx)("circle", {
  cx: "12",
  cy: "12",
  r: "8"
}), "FiberManualRecord");

export {
  FiberManualRecord_default
};
//# sourceMappingURL=chunk-LLIQYUNY.js.map
