"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_decorators_1 = require("@nestjs/mongoose/dist/common/mongoose.decorators");
const mongoose_1 = require("mongoose");
let CommentService = class CommentService {
    commentModel;
    blogModel;
    userInfosModel;
    constructor(commentModel, blogModel, userInfosModel) {
        this.commentModel = commentModel;
        this.blogModel = blogModel;
        this.userInfosModel = userInfosModel;
    }
    async createComment(blogId, content, userId) {
        const userInfos = await this.userInfosModel.findOne({
            userId: new mongoose_1.Types.ObjectId(userId),
        });
        const blog = await this.blogModel.findById(blogId);
        if (!blog)
            throw new common_1.NotFoundException('Blog not found');
        const commentData = {
            blogId: new mongoose_1.Types.ObjectId(blogId),
            content,
            commentBy: userInfos?._id,
            likes: [],
            edited: false,
            editedAt: null,
        };
        const comment = (await this.commentModel.create(commentData)).populate({
            path: 'commentBy',
            select: 'fullName avatar',
            populate: {
                path: 'avatar',
                model: 'Asset',
                select: 'url',
            },
        });
        console.log(comment);
        return comment;
    }
    async getComments(blogId, limit, skip) {
        const limitValue = limit !== undefined ? limit : 10;
        const skipValue = skip !== undefined ? skip : 0;
        const totalToFetch = skipValue + limitValue;
        const comments = await this.commentModel
            .find({
            blogId: new mongoose_1.Types.ObjectId(blogId),
        })
            .populate({
            path: 'commentBy',
            select: 'fullName avatar',
            populate: {
                path: 'avatar',
                model: 'Asset',
                select: 'url',
            },
        })
            .sort({ createdAt: 1 })
            .limit(totalToFetch)
            .lean();
        return comments;
    }
    async editComment(commentId, userId, content) {
        const comment = await this.commentModel.findById(commentId).populate({
            path: 'commentBy',
            select: 'fullName avatar',
            populate: {
                path: 'avatar',
                model: 'Asset',
                select: 'url',
            },
        });
        if (!comment)
            throw new common_1.NotFoundException('Comment not found');
        comment.content = content;
        comment.edited = true;
        comment.editedAt = new Date();
        await comment.save();
        return comment;
    }
    async deleteComment(commentId, userId) {
        const comment = await this.commentModel.findById(commentId);
        if (!comment)
            throw new common_1.NotFoundException('Comment not found');
        const userInfos = await this.userInfosModel.findOne({
            userId: new mongoose_1.Types.ObjectId(userId),
        });
        if (comment.commentBy.toString() !== userInfos?._id.toString())
            throw new common_1.ForbiddenException('No permission');
        await this.deleteCommentAndChildren(commentId);
        const blog = await this.blogModel.findById(comment.blogId);
        if (blog && blog.metrics) {
            const totalDeleted = await this.countCommentAndChildren(commentId);
            blog.metrics.commentCount -= totalDeleted;
            if (blog.metrics.commentCount < 0)
                blog.metrics.commentCount = 0;
            console.log(`Total comments deleted: ${totalDeleted}`);
            console.log(`New comment count: ${blog.metrics.commentCount}`);
            await blog.save();
        }
        return { success: true };
    }
    async likeComment(commentId, userId) {
        const userInfos = await this.userInfosModel.findOne({
            userId: new mongoose_1.Types.ObjectId(userId),
        });
        const comment = await this.commentModel.findById(commentId);
        if (!comment)
            throw new common_1.NotFoundException('Comment not found');
        if (comment.likes.some((id) => id.toString() === userInfos?._id.toString())) {
            return await this.getLikeFullNames(commentId);
        }
        comment.likes.push(new mongoose_1.Types.ObjectId(userInfos?._id));
        await comment.save();
        return await this.getLikeFullNames(commentId);
    }
    async unlikeComment(commentId, userId) {
        const userInfos = await this.userInfosModel.findOne({
            userId: new mongoose_1.Types.ObjectId(userId),
        });
        const comment = await this.commentModel.findById(commentId);
        if (!comment)
            throw new common_1.NotFoundException('Comment not found');
        const index = comment.likes.findIndex((id) => id.toString() === userInfos?._id.toString());
        if (index === -1) {
            return await this.getLikeFullNames(commentId);
        }
        comment.likes.splice(index, 1);
        await comment.save();
        return await this.getLikeFullNames(commentId);
    }
    async getLikeFullNames(commentId) {
        const comment = await this.commentModel
            .findById(commentId)
            .populate({ path: 'likes', select: 'fullName' })
            .lean();
        if (!comment || !comment.likes)
            return { likes: [], likesCount: 0 };
        const fullNames = comment.likes.map((user) => user.fullName);
        return {
            likes: fullNames,
            likesCount: fullNames.length,
        };
    }
    async deleteCommentAndChildren(commentId) {
        const _commentId = new mongoose_1.Types.ObjectId(commentId);
        const replies = (await this.commentModel.find({
            parentId: _commentId,
        }));
        for (const reply of replies) {
            await this.deleteCommentAndChildren(reply._id.toString());
        }
        await this.commentModel.findByIdAndDelete(_commentId);
    }
    async countCommentAndChildren(commentId) {
        const _commentId = new mongoose_1.Types.ObjectId(commentId);
        let count = 1;
        const replies = (await this.commentModel.find({
            parentId: _commentId,
        }));
        for (const reply of replies) {
            count += await this.countCommentAndChildren(reply._id.toString());
        }
        return count;
    }
};
exports.CommentService = CommentService;
exports.CommentService = CommentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_decorators_1.InjectModel)('Comment')),
    __param(1, (0, mongoose_decorators_1.InjectModel)('Blog')),
    __param(2, (0, mongoose_decorators_1.InjectModel)('UserInfos')),
    __metadata("design:paramtypes", [mongoose_1.Model,
        mongoose_1.Model,
        mongoose_1.Model])
], CommentService);
//# sourceMappingURL=comment.service.js.map