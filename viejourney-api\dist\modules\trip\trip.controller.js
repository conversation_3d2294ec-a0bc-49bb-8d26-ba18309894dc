"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripController = void 0;
const common_1 = require("@nestjs/common");
const trip_service_1 = require("./trip.service");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const create_trip_dto_1 = require("../../common/dtos/create-trip.dto");
const update_trip_dto_1 = require("../../common/dtos/update-trip.dto");
let TripController = class TripController {
    tripService;
    constructor(tripService) {
        this.tripService = tripService;
    }
    async removeTripmate(req, request) {
        return await this.tripService.removeTripmate(req.tripId, req.email, request);
    }
    async inviteToTrip(req, request) {
        const userId = request.user?.['userId'];
        const trip = await this.tripService.findOne(req.tripId);
        if (!trip) {
            throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
        }
        const userEmail = request.user?.['email'];
        if (trip.createdBy.toString() !== userId &&
            !trip.tripmates.includes(userEmail)) {
            throw new common_1.HttpException('You do not have permission to invite others to this trip', common_1.HttpStatus.FORBIDDEN);
        }
        return await this.tripService.inviteToTrip(req.tripId, req.email);
    }
    async validateInvite(req) {
        return await this.tripService.validateInvite(req.tripId, req.token);
    }
    async create(req, createTripDto) {
        return await this.tripService.create(createTripDto, req);
    }
    async joinTrip(id, req) {
        return await this.tripService.addToTrip(id, req.token);
    }
    findByUser(req) {
        return this.tripService.findByUser(req.user?.['email']);
    }
    findOne(id) {
        return this.tripService.findOne(id);
    }
    update(id, updateTripDto) {
        return this.tripService.update(+id, updateTripDto);
    }
    remove(id) {
        return this.tripService.remove(+id);
    }
    findPlanByTripId(id) {
        return this.tripService.findPlanByTripId(id);
    }
    async patchPlanDates(req) {
        return this.tripService.updatePlanDates(req.tripId, req.startDate, req.endDate);
    }
    async updateTripCoverImage(req, request) {
        const userId = request.user?.['userId'];
        return this.tripService.updateTripCoverImage(req.tripId, req.assetId, userId);
    }
};
exports.TripController = TripController;
__decorate([
    (0, common_1.Post)('/remove-tripmate'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "removeTripmate", null);
__decorate([
    (0, common_1.Post)('invite'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "inviteToTrip", null);
__decorate([
    (0, common_1.Post)('validate-invite'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "validateInvite", null);
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_trip_dto_1.CreateTripDto]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "create", null);
__decorate([
    (0, common_1.Post)(':id/join'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "joinTrip", null);
__decorate([
    (0, common_1.Get)('/by-user'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], TripController.prototype, "findByUser", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TripController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_trip_dto_1.UpdateTripDto]),
    __metadata("design:returntype", void 0)
], TripController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TripController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('plan/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TripController.prototype, "findPlanByTripId", null);
__decorate([
    (0, common_1.Post)('update-dates'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "patchPlanDates", null);
__decorate([
    (0, common_1.Post)('update-cover-image'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], TripController.prototype, "updateTripCoverImage", null);
exports.TripController = TripController = __decorate([
    (0, common_1.Controller)('trip'),
    __metadata("design:paramtypes", [trip_service_1.TripService])
], TripController);
//# sourceMappingURL=trip.controller.js.map