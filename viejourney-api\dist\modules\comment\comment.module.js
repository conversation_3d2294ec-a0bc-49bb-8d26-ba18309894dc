"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommentModule = void 0;
const common_1 = require("@nestjs/common");
const comment_service_1 = require("./comment.service");
const comment_controller_1 = require("./comment.controller");
const mongoose_1 = require("@nestjs/mongoose");
const comment_schema_1 = require("../../infrastructure/database/comment.schema");
const comment_entity_1 = require("../../common/entities/comment.entity");
const auth_module_1 = require("../auth/auth.module");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const blog_entity_1 = require("../../common/entities/blog.entity");
const blog_schema_1 = require("../../infrastructure/database/blog.schema");
let CommentModule = class CommentModule {
};
exports.CommentModule = CommentModule;
exports.CommentModule = CommentModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: comment_entity_1.Comment.name, schema: comment_schema_1.CommentSchema },
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: blog_entity_1.Blog.name, schema: blog_schema_1.BlogSchema },
            ]),
            (0, common_1.forwardRef)(() => auth_module_1.AuthModule),
        ],
        providers: [comment_service_1.CommentService],
        controllers: [comment_controller_1.CommentController],
        exports: [comment_service_1.CommentService],
    })
], CommentModule);
//# sourceMappingURL=comment.module.js.map