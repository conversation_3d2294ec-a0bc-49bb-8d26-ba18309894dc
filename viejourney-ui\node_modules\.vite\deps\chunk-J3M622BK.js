import {
  fastObjectShallowCompare
} from "./chunk-EMO2UJSG.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __esm,
  __export,
  __toCommonJS,
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-telemetry/esm/runtime/config.import-meta.js
var config_import_meta_exports = {};
__export(config_import_meta_exports, {
  importMetaEnv: () => importMetaEnv
});
var importMetaEnv;
var init_config_import_meta = __esm({
  "node_modules/@mui/x-telemetry/esm/runtime/config.import-meta.js"() {
    importMetaEnv = import.meta.env;
  }
});

// node_modules/@mui/x-license/esm/encoding/md5.js
var k = [];
var i = 0;
for (; i < 64; ) {
  k[i] = 0 | Math.sin(++i % Math.PI) * **********;
}
function md5(s) {
  const words = [];
  let b, c, d, j = unescape(encodeURI(s)) + "", a = j.length;
  const h = [b = **********, c = **********, ~b, ~c];
  s = --a / 4 + 2 | 15;
  words[--s] = a * 8;
  for (; ~a; ) {
    words[a >> 2] |= j.charCodeAt(a) << 8 * a--;
  }
  for (i = j = 0; i < s; i += 16) {
    a = h;
    for (; j < 64; a = [d = a[3], b + ((d = a[0] + [b & c | ~b & d, d & b | ~d & c, b ^ c ^ d, c ^ (b | ~d)][a = j >> 4] + k[j] + ~~words[i | [j, 5 * j + 1, 3 * j + 5, 7 * j][a] & 15]) << (a = [7, 12, 17, 22, 5, 9, 14, 20, 4, 11, 16, 23, 6, 10, 15, 21][4 * a + j++ % 4]) | d >>> -a), b, c]) {
      b = a[1] | 0;
      c = a[2];
    }
    for (j = 4; j; ) h[--j] += a[j];
  }
  for (s = ""; j < 32; ) {
    s += (h[j >> 3] >> (1 ^ j++) * 4 & 15).toString(16);
  }
  return s;
}

// node_modules/@mui/x-license/esm/encoding/base64.js
var _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
function utf8Encode(str) {
  for (let n = 0; n < str.length; n++) {
    const c = str.charCodeAt(n);
    if (c >= 128) {
      throw new Error("ASCII only support");
    }
  }
  return str;
}
var base64Decode = (input) => {
  let output = "";
  let chr1, chr2, chr3;
  let enc1, enc2, enc3, enc4;
  let i2 = 0;
  input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
  while (i2 < input.length) {
    enc1 = _keyStr.indexOf(input.charAt(i2++));
    enc2 = _keyStr.indexOf(input.charAt(i2++));
    enc3 = _keyStr.indexOf(input.charAt(i2++));
    enc4 = _keyStr.indexOf(input.charAt(i2++));
    chr1 = enc1 << 2 | enc2 >> 4;
    chr2 = (enc2 & 15) << 4 | enc3 >> 2;
    chr3 = (enc3 & 3) << 6 | enc4;
    output = output + String.fromCharCode(chr1);
    if (enc3 != 64) {
      output = output + String.fromCharCode(chr2);
    }
    if (enc4 != 64) {
      output = output + String.fromCharCode(chr3);
    }
  }
  return output;
};
var base64Encode = (input) => {
  let output = "";
  let chr1, chr2, chr3, enc1, enc2, enc3, enc4;
  let i2 = 0;
  input = utf8Encode(input);
  while (i2 < input.length) {
    chr1 = input.charCodeAt(i2++);
    chr2 = input.charCodeAt(i2++);
    chr3 = input.charCodeAt(i2++);
    enc1 = chr1 >> 2;
    enc2 = (chr1 & 3) << 4 | chr2 >> 4;
    enc3 = (chr2 & 15) << 2 | chr3 >> 6;
    enc4 = chr3 & 63;
    if (isNaN(chr2)) {
      enc3 = enc4 = 64;
    } else if (isNaN(chr3)) {
      enc4 = 64;
    }
    output = output + _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
  }
  return output;
};

// node_modules/@mui/x-license/esm/utils/plan.js
var PLAN_SCOPES = ["pro", "premium"];

// node_modules/@mui/x-license/esm/utils/licenseModel.js
var LICENSE_MODELS = [
  /**
   * A license is outdated if the current version of the software was released after the expiry date of the license.
   * But the license can be used indefinitely with an older version of the software.
   */
  "perpetual",
  /**
   * On development, a license is outdated if the expiry date has been reached
   * On production, a license is outdated if the current version of the software was released after the expiry date of the license (see "perpetual")
   */
  "annual",
  /**
   * Legacy. The previous name for 'annual'.
   * Can be removed once old license keys generated with 'subscription' are no longer supported.
   * To support for a while. We need more years of backward support and we sell multi year licenses.
   */
  "subscription"
];

// node_modules/@mui/x-license/esm/generateLicense/generateLicense.js
var licenseVersion = "2";
function getClearLicenseString(details) {
  if (details.planScope && !PLAN_SCOPES.includes(details.planScope)) {
    throw new Error("MUI X: Invalid scope");
  }
  if (details.licenseModel && !LICENSE_MODELS.includes(details.licenseModel)) {
    throw new Error("MUI X: Invalid licensing model");
  }
  const keyParts = [`O=${details.orderNumber}`, `E=${details.expiryDate.getTime()}`, `S=${details.planScope}`, `LM=${details.licenseModel}`, `PV=${details.planVersion}`, `KV=${licenseVersion}`];
  return keyParts.join(",");
}
function generateLicense(details) {
  const licenseStr = getClearLicenseString(details);
  return `${md5(base64Encode(licenseStr))}${base64Encode(licenseStr)}`;
}

// node_modules/@mui/x-license/esm/utils/licenseErrorMessageUtils.js
var isCodeSandbox = typeof window !== "undefined" && window.location.hostname.endsWith(".csb.app");
function showError(message) {
  const logger = isCodeSandbox ? console.log : console.error;
  logger(["*************************************************************", "", ...message, "", "*************************************************************"].join("\n"));
}
function showInvalidLicenseKeyError() {
  showError(["MUI X: Invalid license key.", "", "Your MUI X license key format isn't valid. It could be because the license key is missing a character or has a typo.", "", "To solve the issue, you need to double check that `setLicenseKey()` is called with the right argument", "Please check the license key installation https://mui.com/r/x-license-key-installation."]);
}
function showLicenseKeyPlanMismatchError() {
  showError(["MUI X: License key plan mismatch.", "", "Your use of MUI X is not compatible with the plan of your license key. The feature you are trying to use is not included in the plan of your license key. This happens if you try to use Data Grid Premium with a license key for the Pro plan.", "", "To solve the issue, you can upgrade your plan from Pro to Premium at https://mui.com/r/x-get-license?scope=premium.", "Of if you didn't intend to use Premium features, you can replace the import of `@mui/x-data-grid-premium` with `@mui/x-data-grid-pro`."]);
}
function showNotAvailableInInitialProPlanError() {
  showError(["MUI X: Component not included in your license.", "", "The component you are trying to use is not included in the Pro Plan you purchased.", "", "Your license is from an old version of the Pro Plan that is only compatible with the `@mui/x-data-grid-pro` and `@mui/x-date-pickers-pro` commercial packages.", "", "To start using another Pro package, please consider reaching to our sales team to upgrade your license or visit https://mui.com/r/x-get-license to get a new license key."]);
}
function showMissingLicenseKeyError({
  plan,
  packageName
}) {
  showError(["MUI X: Missing license key.", "", `The license key is missing. You might not be allowed to use \`${packageName}\` which is part of MUI X ${plan}.`, "", "To solve the issue, you can check the free trial conditions: https://mui.com/r/x-license-trial.", "If you are eligible no actions are required. If you are not eligible to the free trial, you need to purchase a license https://mui.com/r/x-get-license or stop using the software immediately."]);
}
function showExpiredPackageVersionError({
  packageName
}) {
  showError(["MUI X: Expired package version.", "", `You have installed a version of \`${packageName}\` that is outside of the maintenance plan of your license key. By default, commercial licenses provide access to new versions released during the first year after the purchase.`, "", "To solve the issue, you can renew your license https://mui.com/r/x-get-license or install an older version of the npm package that is compatible with your license key."]);
}
function showExpiredAnnualGraceLicenseKeyError({
  plan,
  licenseKey,
  expiryTimestamp
}) {
  showError(["MUI X: Expired license key.", "", `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, "", "To solve the problem you can either:", "", "- Renew your license https://mui.com/r/x-get-license and use the new key", `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, "", "Note that your license is perpetual in production environments with any version released before your license term ends.", "", `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, ""]);
}
function showExpiredAnnualLicenseKeyError({
  plan,
  licenseKey,
  expiryTimestamp
}) {
  throw new Error(["MUI X: Expired license key.", "", `Your annual license key to use MUI X ${plan} in non-production environments has expired. If you are seeing this development console message, you might be close to breach the license terms by making direct or indirect changes to the frontend of an app that render a MUI X ${plan} component (more details in https://mui.com/r/x-license-annual).`, "", "To solve the problem you can either:", "", "- Renew your license https://mui.com/r/x-get-license and use the new key", `- Stop making changes to code depending directly or indirectly on MUI X ${plan}'s APIs`, "", "Note that your license is perpetual in production environments with any version released before your license term ends.", "", `- License key expiry timestamp: ${new Date(expiryTimestamp)}`, `- Installed license key: ${licenseKey}`, ""].join("\n"));
}

// node_modules/@mui/x-license/esm/utils/licenseInfo.js
globalThis.__MUI_LICENSE_INFO__ = globalThis.__MUI_LICENSE_INFO__ || {
  key: void 0
};
var LicenseInfo = class _LicenseInfo {
  static getLicenseInfo() {
    return globalThis.__MUI_LICENSE_INFO__;
  }
  static getLicenseKey() {
    return _LicenseInfo.getLicenseInfo().key;
  }
  static setLicenseKey(key) {
    const licenseInfo = _LicenseInfo.getLicenseInfo();
    licenseInfo.key = key;
  }
};

// node_modules/@mui/x-license/esm/utils/licenseStatus.js
var LICENSE_STATUS = function(LICENSE_STATUS2) {
  LICENSE_STATUS2["NotFound"] = "NotFound";
  LICENSE_STATUS2["Invalid"] = "Invalid";
  LICENSE_STATUS2["ExpiredAnnual"] = "ExpiredAnnual";
  LICENSE_STATUS2["ExpiredAnnualGrace"] = "ExpiredAnnualGrace";
  LICENSE_STATUS2["ExpiredVersion"] = "ExpiredVersion";
  LICENSE_STATUS2["Valid"] = "Valid";
  LICENSE_STATUS2["OutOfScope"] = "OutOfScope";
  LICENSE_STATUS2["NotAvailableInInitialProPlan"] = "NotAvailableInInitialProPlan";
  return LICENSE_STATUS2;
}({});

// node_modules/@mui/x-license/esm/verifyLicense/verifyLicense.js
var getDefaultReleaseDate = () => {
  const today = /* @__PURE__ */ new Date();
  today.setHours(0, 0, 0, 0);
  return today;
};
function generateReleaseInfo(releaseDate = getDefaultReleaseDate()) {
  return base64Encode(releaseDate.getTime().toString());
}
function isPlanScopeSufficient(packageName, planScope) {
  let acceptedScopes;
  if (packageName.includes("-pro")) {
    acceptedScopes = ["pro", "premium"];
  } else if (packageName.includes("-premium")) {
    acceptedScopes = ["premium"];
  } else {
    acceptedScopes = [];
  }
  return acceptedScopes.includes(planScope);
}
var expiryReg = /^.*EXPIRY=([0-9]+),.*$/;
var PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN = ["x-data-grid-pro", "x-date-pickers-pro"];
var decodeLicenseVersion1 = (license) => {
  let expiryTimestamp;
  try {
    expiryTimestamp = parseInt(license.match(expiryReg)[1], 10);
    if (!expiryTimestamp || Number.isNaN(expiryTimestamp)) {
      expiryTimestamp = null;
    }
  } catch (err) {
    expiryTimestamp = null;
  }
  return {
    planScope: "pro",
    licenseModel: "perpetual",
    expiryTimestamp,
    planVersion: "initial"
  };
};
var decodeLicenseVersion2 = (license) => {
  const licenseInfo = {
    planScope: null,
    licenseModel: null,
    expiryTimestamp: null,
    planVersion: "initial"
  };
  license.split(",").map((token) => token.split("=")).filter((el) => el.length === 2).forEach(([key, value]) => {
    if (key === "S") {
      licenseInfo.planScope = value;
    }
    if (key === "LM") {
      licenseInfo.licenseModel = value;
    }
    if (key === "E") {
      const expiryTimestamp = parseInt(value, 10);
      if (expiryTimestamp && !Number.isNaN(expiryTimestamp)) {
        licenseInfo.expiryTimestamp = expiryTimestamp;
      }
    }
    if (key === "PV") {
      licenseInfo.planVersion = value;
    }
  });
  return licenseInfo;
};
var decodeLicense = (encodedLicense) => {
  const license = base64Decode(encodedLicense);
  if (license.includes("KEYVERSION=1")) {
    return decodeLicenseVersion1(license);
  }
  if (license.includes("KV=2")) {
    return decodeLicenseVersion2(license);
  }
  return null;
};
function verifyLicense({
  releaseInfo,
  licenseKey,
  packageName
}) {
  if (false) {
    return {
      status: LICENSE_STATUS.Valid
    };
  }
  if (!releaseInfo) {
    throw new Error("MUI X: The release information is missing. Not able to validate license.");
  }
  if (!licenseKey) {
    return {
      status: LICENSE_STATUS.NotFound
    };
  }
  const hash = licenseKey.substr(0, 32);
  const encoded = licenseKey.substr(32);
  if (hash !== md5(encoded)) {
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  const license = decodeLicense(encoded);
  if (license == null) {
    console.error("MUI X: Error checking license. Key version not found!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.licenseModel == null || !LICENSE_MODELS.includes(license.licenseModel)) {
    console.error("MUI X: Error checking license. Licensing model not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.expiryTimestamp == null) {
    console.error("MUI X: Error checking license. Expiry timestamp not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (license.licenseModel === "perpetual" || false) {
    const pkgTimestamp = parseInt(base64Decode(releaseInfo), 10);
    if (Number.isNaN(pkgTimestamp)) {
      throw new Error("MUI X: The release information is invalid. Not able to validate license.");
    }
    if (license.expiryTimestamp < pkgTimestamp) {
      return {
        status: LICENSE_STATUS.ExpiredVersion
      };
    }
  } else if (license.licenseModel === "subscription" || license.licenseModel === "annual") {
    if ((/* @__PURE__ */ new Date()).getTime() > license.expiryTimestamp) {
      if (
        // 30 days grace
        (/* @__PURE__ */ new Date()).getTime() < license.expiryTimestamp + 1e3 * 3600 * 24 * 30 || false
      ) {
        return {
          status: LICENSE_STATUS.ExpiredAnnualGrace,
          meta: {
            expiryTimestamp: license.expiryTimestamp,
            licenseKey
          }
        };
      }
      return {
        status: LICENSE_STATUS.ExpiredAnnual,
        meta: {
          expiryTimestamp: license.expiryTimestamp,
          licenseKey
        }
      };
    }
  }
  if (license.planScope == null || !PLAN_SCOPES.includes(license.planScope)) {
    console.error("MUI X: Error checking license. planScope not found or invalid!");
    return {
      status: LICENSE_STATUS.Invalid
    };
  }
  if (!isPlanScopeSufficient(packageName, license.planScope)) {
    return {
      status: LICENSE_STATUS.OutOfScope
    };
  }
  if (license.planVersion === "initial" && license.planScope === "pro" && !PRO_PACKAGES_AVAILABLE_IN_INITIAL_PRO_PLAN.includes(packageName)) {
    return {
      status: LICENSE_STATUS.NotAvailableInInitialProPlan
    };
  }
  return {
    status: LICENSE_STATUS.Valid
  };
}

// node_modules/@mui/x-license/esm/useLicenseVerifier/useLicenseVerifier.js
var React2 = __toESM(require_react(), 1);

// node_modules/@mui/x-telemetry/esm/runtime/events.js
var muiXTelemetryEvents = {
  licenseVerification: false ? noop : (context, payload) => ({
    eventName: "licenseVerification",
    payload,
    context
  })
};
var events_default = muiXTelemetryEvents;

// node_modules/@mui/x-telemetry/esm/runtime/config.js
var envEnabledValues = ["1", "true", "yes", "y"];
var envDisabledValues = ["0", "false", "no", "n"];
function getBooleanEnv(value) {
  if (!value) {
    return void 0;
  }
  if (envEnabledValues.includes(value)) {
    return true;
  }
  if (envDisabledValues.includes(value)) {
    return false;
  }
  return void 0;
}
function getBooleanEnvFromEnvObject(envKey, envObj) {
  var _a;
  const keys = Object.keys(envObj);
  for (let i2 = 0; i2 < keys.length; i2 += 1) {
    const key = keys[i2];
    if (!key.endsWith(envKey)) {
      continue;
    }
    const value = getBooleanEnv((_a = envObj[key]) == null ? void 0 : _a.toLowerCase());
    if (typeof value === "boolean") {
      return value;
    }
  }
  return void 0;
}
function getIsTelemetryCollecting() {
  const globalValue = globalThis.__MUI_X_TELEMETRY_DISABLED__;
  if (typeof globalValue === "boolean") {
    return !globalValue;
  }
  try {
    if (typeof process !== "undefined" && process.env && typeof process.env === "object") {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DISABLED", process.env);
      if (typeof result === "boolean") {
        return !result;
      }
    }
  } catch (_) {
  }
  try {
    const {
      importMetaEnv: importMetaEnv2
    } = (init_config_import_meta(), __toCommonJS(config_import_meta_exports));
    if (importMetaEnv2) {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DISABLED", importMetaEnv2);
      if (typeof result === "boolean") {
        return !result;
      }
    }
  } catch (_) {
  }
  try {
    const envValue = process.env.MUI_X_TELEMETRY_DISABLED || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DISABLED || process.env.GATSBY_MUI_X_TELEMETRY_DISABLED || process.env.REACT_APP_MUI_X_TELEMETRY_DISABLED || process.env.PUBLIC_MUI_X_TELEMETRY_DISABLED;
    const result = getBooleanEnv(envValue);
    if (typeof result === "boolean") {
      return !result;
    }
  } catch (_) {
  }
  return void 0;
}
function getIsDebugModeEnabled() {
  try {
    const globalValue = globalThis.__MUI_X_TELEMETRY_DEBUG__;
    if (typeof globalValue === "boolean") {
      return globalValue;
    }
    if (typeof process !== "undefined" && process.env && typeof process.env === "object") {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DEBUG", process.env);
      if (typeof result === "boolean") {
        return result;
      }
    }
    if (process.env.MUI_X_TELEMETRY_DEBUG) {
      const result = getBooleanEnv(process.env.MUI_X_TELEMETRY_DEBUG);
      if (typeof result === "boolean") {
        return result;
      }
    }
  } catch (_) {
  }
  try {
    const {
      importMetaEnv: importMetaEnv2
    } = (init_config_import_meta(), __toCommonJS(config_import_meta_exports));
    if (importMetaEnv2) {
      const result = getBooleanEnvFromEnvObject("MUI_X_TELEMETRY_DEBUG", importMetaEnv2);
      if (typeof result === "boolean") {
        return result;
      }
    }
  } catch (_) {
  }
  try {
    const envValue = process.env.MUI_X_TELEMETRY_DEBUG || process.env.NEXT_PUBLIC_MUI_X_TELEMETRY_DEBUG || process.env.GATSBY_MUI_X_TELEMETRY_DEBUG || process.env.REACT_APP_MUI_X_TELEMETRY_DEBUG || process.env.PUBLIC_MUI_X_TELEMETRY_DEBUG;
    const result = getBooleanEnv(envValue);
    if (typeof result === "boolean") {
      return result;
    }
  } catch (_) {
  }
  return false;
}
function getNodeEnv() {
  try {
    return "development";
  } catch (_) {
    return "<unknown>";
  }
}
var cachedEnv = null;
function getTelemetryEnvConfig(skipCache = false) {
  if (skipCache || !cachedEnv) {
    cachedEnv = {
      NODE_ENV: getNodeEnv(),
      IS_COLLECTING: getIsTelemetryCollecting(),
      DEBUG: getIsDebugModeEnabled()
    };
  }
  return cachedEnv;
}
function getTelemetryEnvConfigValue(key) {
  return getTelemetryEnvConfig()[key];
}
function setTelemetryEnvConfigValue(key, value) {
  getTelemetryEnvConfig()[key] = value;
}

// node_modules/@mui/x-telemetry/esm/runtime/fetcher.js
async function fetchWithRetry(url, options, retries = 3) {
  try {
    const response = await fetch(url, options);
    if (response.ok) {
      return response;
    }
    throw new Error(`Request failed with status ${response.status}`);
  } catch (error) {
    if (retries === 0) {
      throw error;
    }
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(fetchWithRetry(url, options, retries - 1));
      }, Math.random() * 3e3);
    });
  }
}

// node_modules/@mui/x-telemetry/esm/runtime/sender.js
var sendMuiXTelemetryRetries = 3;
function shouldSendTelemetry(telemetryContext) {
  if (typeof window === "undefined") {
    return false;
  }
  const envIsCollecting = getTelemetryEnvConfigValue("IS_COLLECTING");
  if (typeof envIsCollecting === "boolean") {
    return envIsCollecting;
  }
  if (telemetryContext.traits.isCI) {
    return false;
  }
  return false;
}
async function sendMuiXTelemetryEvent(event) {
  try {
    if (false) {
      return;
    }
    const {
      default: getTelemetryContext
    } = await import("./get-context-RLTEH6TD.js");
    const telemetryContext = await getTelemetryContext();
    if (!event || !shouldSendTelemetry(telemetryContext)) {
      return;
    }
    const eventPayload = _extends({}, event, {
      context: _extends({}, telemetryContext.traits, event.context)
    });
    if (getTelemetryEnvConfigValue("DEBUG")) {
      console.log("[mui-x-telemetry] event", JSON.stringify(eventPayload, null, 2));
      return;
    }
    await fetchWithRetry("https://x-telemetry.mui.com/v2/telemetry/record", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Telemetry-Client-Version": "8.5.3",
        "X-Telemetry-Node-Env": "development"
      },
      body: JSON.stringify([eventPayload])
    }, sendMuiXTelemetryRetries);
  } catch (_) {
    console.log("[mui-x-telemetry] error", _);
  }
}
var sender_default = sendMuiXTelemetryEvent;

// node_modules/@mui/x-telemetry/esm/runtime/settings.js
var muiXTelemetrySettings = {
  enableDebug: () => {
    setTelemetryEnvConfigValue("DEBUG", true);
  },
  enableTelemetry: () => {
    setTelemetryEnvConfigValue("IS_COLLECTING", true);
  },
  disableTelemetry: () => {
    setTelemetryEnvConfigValue("IS_COLLECTING", false);
  }
};
var settings_default = muiXTelemetrySettings;

// node_modules/@mui/x-telemetry/esm/index.js
var sendMuiXTelemetryEvent2 = false ? noop : sender_default;
var muiXTelemetrySettings2 = false ? {
  enableDebug: noop,
  enableTelemetry: noop,
  disableTelemetry: noop
} : settings_default;

// node_modules/@mui/x-license/esm/Unstable_LicenseInfoProvider/MuiLicenseInfoContext.js
var React = __toESM(require_react(), 1);
var MuiLicenseInfoContext = React.createContext({
  key: void 0
});
if (true) MuiLicenseInfoContext.displayName = "MuiLicenseInfoContext";
var MuiLicenseInfoContext_default = MuiLicenseInfoContext;

// node_modules/@mui/x-license/esm/useLicenseVerifier/useLicenseVerifier.js
var sharedLicenseStatuses = {};
function clearLicenseStatusCache() {
  for (const packageName in sharedLicenseStatuses) {
    if (Object.prototype.hasOwnProperty.call(sharedLicenseStatuses, packageName)) {
      delete sharedLicenseStatuses[packageName];
    }
  }
}
function useLicenseVerifier(packageName, releaseInfo) {
  const {
    key: contextKey
  } = React2.useContext(MuiLicenseInfoContext_default);
  return React2.useMemo(() => {
    const licenseKey = contextKey ?? LicenseInfo.getLicenseKey();
    if (sharedLicenseStatuses[packageName] && sharedLicenseStatuses[packageName].key === licenseKey) {
      return sharedLicenseStatuses[packageName].licenseVerifier;
    }
    const plan = packageName.includes("premium") ? "Premium" : "Pro";
    const licenseStatus = verifyLicense({
      releaseInfo,
      licenseKey,
      packageName
    });
    const fullPackageName = `@mui/${packageName}`;
    sendMuiXTelemetryEvent2(events_default.licenseVerification({
      licenseKey
    }, {
      packageName,
      packageReleaseInfo: releaseInfo,
      licenseStatus: licenseStatus == null ? void 0 : licenseStatus.status
    }));
    if (licenseStatus.status === LICENSE_STATUS.Valid) {
    } else if (licenseStatus.status === LICENSE_STATUS.Invalid) {
      showInvalidLicenseKeyError();
    } else if (licenseStatus.status === LICENSE_STATUS.NotAvailableInInitialProPlan) {
      showNotAvailableInInitialProPlanError();
    } else if (licenseStatus.status === LICENSE_STATUS.OutOfScope) {
      showLicenseKeyPlanMismatchError();
    } else if (licenseStatus.status === LICENSE_STATUS.NotFound) {
      showMissingLicenseKeyError({
        plan,
        packageName: fullPackageName
      });
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnualGrace) {
      showExpiredAnnualGraceLicenseKeyError(_extends({
        plan
      }, licenseStatus.meta));
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredAnnual) {
      showExpiredAnnualLicenseKeyError(_extends({
        plan
      }, licenseStatus.meta));
    } else if (licenseStatus.status === LICENSE_STATUS.ExpiredVersion) {
      showExpiredPackageVersionError({
        packageName: fullPackageName
      });
    } else if (true) {
      throw new Error("missing status handler");
    }
    sharedLicenseStatuses[packageName] = {
      key: licenseKey,
      licenseVerifier: licenseStatus
    };
    return licenseStatus;
  }, [packageName, releaseInfo, contextKey]);
}

// node_modules/@mui/x-license/esm/Watermark/Watermark.js
var React4 = __toESM(require_react(), 1);

// node_modules/@mui/x-internals/esm/fastMemo/fastMemo.js
var React3 = __toESM(require_react(), 1);
function fastMemo(component) {
  return React3.memo(component, fastObjectShallowCompare);
}

// node_modules/@mui/x-license/esm/Watermark/Watermark.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function getLicenseErrorMessage(licenseStatus) {
  switch (licenseStatus) {
    case LICENSE_STATUS.ExpiredAnnualGrace:
    case LICENSE_STATUS.ExpiredAnnual:
      return "MUI X Expired license key";
    case LICENSE_STATUS.ExpiredVersion:
      return "MUI X Expired package version";
    case LICENSE_STATUS.Invalid:
      return "MUI X Invalid license key";
    case LICENSE_STATUS.OutOfScope:
      return "MUI X License key plan mismatch";
    case LICENSE_STATUS.NotAvailableInInitialProPlan:
      return "MUI X Product not covered by plan";
    case LICENSE_STATUS.NotFound:
      return "MUI X Missing license key";
    default:
      throw new Error("Unhandled MUI X license status.");
  }
}
function Watermark(props) {
  const {
    packageName,
    releaseInfo
  } = props;
  const licenseStatus = useLicenseVerifier(packageName, releaseInfo);
  if (licenseStatus.status === LICENSE_STATUS.Valid) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)("div", {
    style: {
      position: "absolute",
      pointerEvents: "none",
      color: "#8282829e",
      zIndex: 1e5,
      width: "100%",
      textAlign: "center",
      bottom: "50%",
      right: 0,
      letterSpacing: 5,
      fontSize: 24
    },
    children: getLicenseErrorMessage(licenseStatus.status)
  });
}
var MemoizedWatermark = fastMemo(Watermark);

// node_modules/@mui/x-license/esm/Unstable_LicenseInfoProvider/LicenseInfoProvider.js
var React5 = __toESM(require_react(), 1);
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
function LicenseInfoProvider({
  info,
  children
}) {
  return (0, import_jsx_runtime2.jsx)(MuiLicenseInfoContext_default.Provider, {
    value: info,
    children
  });
}

export {
  fastMemo,
  generateLicense,
  showInvalidLicenseKeyError,
  showLicenseKeyPlanMismatchError,
  showNotAvailableInInitialProPlanError,
  showMissingLicenseKeyError,
  showExpiredPackageVersionError,
  showExpiredAnnualGraceLicenseKeyError,
  showExpiredAnnualLicenseKeyError,
  LicenseInfo,
  LICENSE_STATUS,
  generateReleaseInfo,
  verifyLicense,
  muiXTelemetrySettings2 as muiXTelemetrySettings,
  clearLicenseStatusCache,
  useLicenseVerifier,
  MemoizedWatermark,
  LicenseInfoProvider
};
/*! Bundled license information:

@mui/x-telemetry/esm/index.js:
  (**
   * @mui/x-telemetry v8.5.3
   *
   * @license MUI X Commercial
   * This source code is licensed under the commercial license found in the
   * LICENSE file in the root directory of this source tree.
   *)

@mui/x-license/esm/index.js:
  (**
   * @mui/x-license v8.9.0
   *
   * @license SEE LICENSE IN LICENSE
   * This source code is licensed under the SEE LICENSE IN LICENSE license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-J3M622BK.js.map
