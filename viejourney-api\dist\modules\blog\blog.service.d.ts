import { Model, Types } from 'mongoose';
import { Blog } from 'src/common/entities/blog.entity';
import { Status } from 'src/common/enums/status.enum';
import { Account } from 'src/common/entities/account.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { CreateBlogDto } from 'src/common/dtos/create-blog.dto';
import { AssetsService } from '../assets/assets.service';
import { PaginationDto } from 'src/common/dtos/pagination-userlist.dto';
import { Request } from 'express';
import { Like } from 'src/common/entities/like.entity';
export declare class BlogService {
    private readonly blogModel;
    private readonly accountModel;
    private readonly userInfosModel;
    private readonly likeModel;
    private readonly assetsService;
    constructor(blogModel: Model<Blog>, accountModel: Model<Account>, userInfosModel: Model<UserInfos>, likeModel: Model<Like>, assetsService: AssetsService);
    hasUserLikedBlog(userId: string, blogId: string): Promise<boolean>;
    postLikeBlog(req: Request, blogId: string): Promise<{
        like: import("mongoose").Document<unknown, {}, Like, {}> & Like & Required<{
            _id: unknown;
        }> & {
            __v: number;
        };
        message: string;
    }>;
    getRelatedBlogs(blogId?: string, tags?: string[], destination?: string): Promise<(import("mongoose").Document<unknown, {}, Blog, {}> & Blog & Required<{
        _id: string;
    }> & {
        __v: number;
    })[]>;
    unlikeBlog(req: Request, blogId: string): Promise<{
        message: string;
    }>;
    findAll(paginationDto: PaginationDto): Promise<{
        data: never[];
        totalPages: number;
        currentPage: number;
        pageSize: number;
        totalItems: number;
        message: string;
        Total_Blogs?: undefined;
    } | {
        data: {
            _id: string;
            title: string;
            createdBy: {
                _id: Types.ObjectId;
                fullName: string;
                email: string;
            };
            avatarUser: string | null;
            summary: string | undefined;
            destination: string | null;
            viewCount: number;
            likeCount: number;
            commentCount: number;
            status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
            flags: number;
            createdAt: Date | undefined;
            updatedAt: Date | undefined;
        }[];
        Total_Blogs: number;
        currentPage: number;
        pageSize: number;
        totalPages: number;
        totalItems?: undefined;
        message?: undefined;
    }>;
    getAllApprovedBlogs(page?: number, limit?: number, search?: string): Promise<{
        status: string;
        data: {
            blogs: {
                author: {
                    _id: string;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                metrics: {
                    viewCount: number;
                    likeCount: number;
                    commentCount: number;
                };
                createdAt: Date | undefined;
                updatedAt: Date | undefined;
                createdBy: {
                    _id: Types.ObjectId | undefined;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                updatedBy: {
                    _id: Types.ObjectId | undefined;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                _id: string;
                title: string;
                slug?: string;
                content: string;
                summary?: string;
                tags?: string[];
                coverImage?: string;
                destination?: string | null;
                places?: import("src/common/entities/blog.entity").PlaceData[];
                likes?: Types.ObjectId[];
                status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
                flags?: {
                    userId: UserInfos | Types.ObjectId;
                    reason: string;
                    date: Date;
                }[];
                comments?: import("../../common/entities/comment.entity").Comment[] | Types.ObjectId[];
                $locals: Record<string, unknown>;
                $op: "save" | "validate" | "remove" | null;
                $where: Record<string, unknown>;
                baseModelName?: string;
                collection: import("mongoose").Collection;
                db: import("mongoose").Connection;
                errors?: import("mongoose").Error.ValidationError;
                id?: any;
                isNew: boolean;
                schema: import("mongoose").Schema;
                __v: number;
            }[];
            pagination: {
                currentPage: number;
                totalPages: number;
                totalItems: number;
                itemsPerPage: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    }>;
    findOneBlogById(blogId: string): Promise<{
        createdBy: {
            email: string;
            totalBlogs: number;
            likesCount: number;
            _id?: Types.ObjectId | undefined;
            userId?: Account | undefined;
            fullName?: string | undefined;
            dob?: Date | undefined;
            phone?: string | undefined;
            address?: string | undefined;
            avatar?: import("../../common/entities/asset.entity").Asset | undefined;
            lastLoginAt?: Date | undefined;
            flaggedCount?: number | undefined;
            banReason?: string | null | undefined;
            bannedAt?: Date | null | undefined;
            createdAt?: Date | undefined;
            updatedAt?: Date | undefined;
            location?: string | undefined;
            format?: string | undefined;
            file_size?: string | undefined;
            dimensions?: string | undefined;
        };
        _id?: string | undefined;
        title?: string | undefined;
        slug?: string;
        content?: string | undefined;
        summary?: string;
        tags?: string[];
        coverImage?: string;
        destination?: string | null;
        places?: import("src/common/entities/blog.entity").PlaceData[];
        updatedBy?: UserInfos | Types.ObjectId | undefined;
        likes?: Types.ObjectId[];
        status?: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED" | undefined;
        flags?: {
            userId: UserInfos | Types.ObjectId;
            reason: string;
            date: Date;
        }[];
        metrics?: {
            viewCount: number;
            likeCount: number;
            commentCount: number;
        };
        comments?: import("../../common/entities/comment.entity").Comment[] | Types.ObjectId[];
        createdAt?: Date;
        updatedAt?: Date;
        $locals?: Record<string, unknown> | undefined;
        $op?: "remove" | "save" | "validate" | null | undefined;
        $where?: Record<string, unknown> | undefined;
        baseModelName?: string;
        collection?: import("mongoose").Collection<import("bson").Document> | undefined;
        db?: import("mongoose").Connection | undefined;
        errors?: import("mongoose").Error.ValidationError;
        id?: any;
        isNew?: boolean | undefined;
        schema?: import("mongoose").Schema<any, Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
            [x: string]: unknown;
        }, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }>, {}> & import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        }> | undefined;
        __v?: number | undefined;
    }>;
    updateStatus(blogId: string, status: 'APPROVED' | 'REJECTED'): Promise<{
        _id: string;
        title: string;
        status: "APPROVED" | "REJECTED";
    }>;
    cleanFlags(blogId: string): Promise<{
        _id: string;
        title: string;
        flags: {
            userId: UserInfos | Types.ObjectId;
            reason: string;
            date: Date;
        }[];
    }>;
    banAuthor(blogId: string, reason: string): Promise<{
        _id: string;
        reasonBan: string | null;
        bannedAt: Date | null;
        status: Status;
    }>;
    deleteBlogById(blogId: string): Promise<{
        message: string;
    }>;
    createBlog(createBlogDto: CreateBlogDto, file: Express.Multer.File, userId: string): Promise<import("mongoose").Document<unknown, {}, Blog, {}> & Blog & Required<{
        _id: string;
    }> & {
        __v: number;
    }>;
    createFlag(blogId: string, reason: string, req: Request): Promise<{
        message: string;
        flags: {
            userId: UserInfos | Types.ObjectId;
            reason: string;
            date: Date;
        }[];
    }>;
    startBlog(location: string, userId: string, file?: Express.Multer.File): Promise<{
        blogId: string;
        title: string;
        destination: string | null | undefined;
        coverImage: string | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        message: string;
    }>;
    getDraftBlog(blogId: string, userId: string): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("src/common/entities/blog.entity").PlaceData[];
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    }>;
    getPublishedBlog(blogId: string, userId: string): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("src/common/entities/blog.entity").PlaceData[];
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        metrics: {
            viewCount: number;
            likeCount: number;
            commentCount: number;
        };
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
        message: string;
    }>;
    updateBlogDraft(blogId: string, updateData: any, userId: string, file?: Express.Multer.File): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("src/common/entities/blog.entity").PlaceData[] | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        updatedAt: Date | undefined;
        message: string;
    }>;
    publishBlog(blogId: string, userId: string, role: string): Promise<{
        blogId: string;
        title: string;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        publishedAt: Date | undefined;
        message: string;
    }>;
    editPublishedBlog(blogId: string, updateData: any, userId: string, file?: Express.Multer.File): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("src/common/entities/blog.entity").PlaceData[] | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        updatedAt: Date | undefined;
        message: string;
    }>;
    getUserBlogs(userId: string, status?: string): Promise<{
        blogs: (import("mongoose").Document<unknown, {}, Blog, {}> & Blog & Required<{
            _id: string;
        }> & {
            __v: number;
        })[];
        total: number;
    }>;
}
