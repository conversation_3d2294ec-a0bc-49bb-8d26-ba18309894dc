"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripModule = void 0;
const common_1 = require("@nestjs/common");
const trip_service_1 = require("./trip.service");
const trip_controller_1 = require("./trip.controller");
const mongoose_1 = require("@nestjs/mongoose");
const auth_module_1 = require("../auth/auth.module");
const trip_gateway_1 = require("./trip.gateway");
const account_module_1 = require("../account/account.module");
const jwt_1 = require("@nestjs/jwt");
const trip_schema_1 = require("../../infrastructure/database/trip.schema");
const trip_entity_1 = require("../../common/entities/trip.entity");
const plan_state_service_1 = require("./plan-state/plan-state.service");
const plan_schema_1 = require("../../infrastructure/database/plan.schema");
const user_module_1 = require("../userinfo/user.module");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
let TripModule = class TripModule {
};
exports.TripModule = TripModule;
exports.TripModule = TripModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: trip_entity_1.Trip.name, schema: trip_schema_1.TripSchema },
                {
                    name: 'Plan',
                    schema: plan_schema_1.TripPlanSchema,
                },
                { name: 'User', schema: userinfo_schema_1.UserInfosSchema },
                { name: 'Account', schema: 'AccountSchema' },
                { name: 'Asset', schema: 'AssetSchema' },
            ]),
            jwt_1.JwtModule,
            account_module_1.AccountModule,
            (0, common_1.forwardRef)(() => auth_module_1.AuthModule),
            user_module_1.UserModule,
        ],
        controllers: [trip_controller_1.TripController],
        providers: [trip_service_1.TripService, trip_gateway_1.TripGateway, plan_state_service_1.PlanStateService],
        exports: [trip_service_1.TripService],
    })
], TripModule);
//# sourceMappingURL=trip.module.js.map