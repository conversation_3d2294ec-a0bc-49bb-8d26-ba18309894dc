{"version": 3, "sources": ["../../tslib/tslib.es6.mjs", "../../@mui/x-telemetry/node_modules/@fingerprintjs/fingerprintjs/dist/fp.esm.js"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n", "/**\n * FingerprintJS v3.4.2 - Copyright (c) FingerprintJS, Inc, 2023 (https://fingerprint.com)\n * Licensed under the MIT (http://www.opensource.org/licenses/mit-license.php) license.\n *\n * This software contains code from open-source projects:\n * MurmurHash3 by <PERSON><PERSON> (https://github.com/karanlyons/murmurHash3.js)\n */\n\nimport { __awaiter, __generator, __assign, __spreadArray } from 'tslib';\n\nvar version = \"3.4.2\";\n\nfunction wait(durationMs, resolveWith) {\n    return new Promise(function (resolve) { return setTimeout(resolve, durationMs, resolveWith); });\n}\nfunction requestIdleCallbackIfAvailable(fallbackTimeout, deadlineTimeout) {\n    if (deadlineTimeout === void 0) { deadlineTimeout = Infinity; }\n    var requestIdleCallback = window.requestIdleCallback;\n    if (requestIdleCallback) {\n        // The function `requestIdleCallback` loses the binding to `window` here.\n        // `globalThis` isn't always equal `window` (see https://github.com/fingerprintjs/fingerprintjs/issues/683).\n        // Therefore, an error can occur. `call(window,` prevents the error.\n        return new Promise(function (resolve) { return requestIdleCallback.call(window, function () { return resolve(); }, { timeout: deadlineTimeout }); });\n    }\n    else {\n        return wait(Math.min(fallbackTimeout, deadlineTimeout));\n    }\n}\nfunction isPromise(value) {\n    return !!value && typeof value.then === 'function';\n}\n/**\n * Calls a maybe asynchronous function without creating microtasks when the function is synchronous.\n * Catches errors in both cases.\n *\n * If just you run a code like this:\n * ```\n * console.time('Action duration')\n * await action()\n * console.timeEnd('Action duration')\n * ```\n * The synchronous function time can be measured incorrectly because another microtask may run before the `await`\n * returns the control back to the code.\n */\nfunction awaitIfAsync(action, callback) {\n    try {\n        var returnedValue = action();\n        if (isPromise(returnedValue)) {\n            returnedValue.then(function (result) { return callback(true, result); }, function (error) { return callback(false, error); });\n        }\n        else {\n            callback(true, returnedValue);\n        }\n    }\n    catch (error) {\n        callback(false, error);\n    }\n}\n/**\n * If you run many synchronous tasks without using this function, the JS main loop will be busy and asynchronous tasks\n * (e.g. completing a network request, rendering the page) won't be able to happen.\n * This function allows running many synchronous tasks such way that asynchronous tasks can run too in background.\n */\nfunction mapWithBreaks(items, callback, loopReleaseInterval) {\n    if (loopReleaseInterval === void 0) { loopReleaseInterval = 16; }\n    return __awaiter(this, void 0, void 0, function () {\n        var results, lastLoopReleaseTime, i, now;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    results = Array(items.length);\n                    lastLoopReleaseTime = Date.now();\n                    i = 0;\n                    _a.label = 1;\n                case 1:\n                    if (!(i < items.length)) return [3 /*break*/, 4];\n                    results[i] = callback(items[i], i);\n                    now = Date.now();\n                    if (!(now >= lastLoopReleaseTime + loopReleaseInterval)) return [3 /*break*/, 3];\n                    lastLoopReleaseTime = now;\n                    // Allows asynchronous actions and microtasks to happen\n                    return [4 /*yield*/, wait(0)];\n                case 2:\n                    // Allows asynchronous actions and microtasks to happen\n                    _a.sent();\n                    _a.label = 3;\n                case 3:\n                    ++i;\n                    return [3 /*break*/, 1];\n                case 4: return [2 /*return*/, results];\n            }\n        });\n    });\n}\n/**\n * Makes the given promise never emit an unhandled promise rejection console warning.\n * The promise will still pass errors to the next promises.\n *\n * Otherwise, promise emits a console warning unless it has a `catch` listener.\n */\nfunction suppressUnhandledRejectionWarning(promise) {\n    promise.then(undefined, function () { return undefined; });\n}\n\n/*\n * Taken from https://github.com/karanlyons/murmurHash3.js/blob/a33d0723127e2e5415056c455f8aed2451ace208/murmurHash3.js\n */\n//\n// Given two 64bit ints (as an array of two 32bit ints) returns the two\n// added together as a 64bit int (as an array of two 32bit ints).\n//\nfunction x64Add(m, n) {\n    m = [m[0] >>> 16, m[0] & 0xffff, m[1] >>> 16, m[1] & 0xffff];\n    n = [n[0] >>> 16, n[0] & 0xffff, n[1] >>> 16, n[1] & 0xffff];\n    var o = [0, 0, 0, 0];\n    o[3] += m[3] + n[3];\n    o[2] += o[3] >>> 16;\n    o[3] &= 0xffff;\n    o[2] += m[2] + n[2];\n    o[1] += o[2] >>> 16;\n    o[2] &= 0xffff;\n    o[1] += m[1] + n[1];\n    o[0] += o[1] >>> 16;\n    o[1] &= 0xffff;\n    o[0] += m[0] + n[0];\n    o[0] &= 0xffff;\n    return [(o[0] << 16) | o[1], (o[2] << 16) | o[3]];\n}\n//\n// Given two 64bit ints (as an array of two 32bit ints) returns the two\n// multiplied together as a 64bit int (as an array of two 32bit ints).\n//\nfunction x64Multiply(m, n) {\n    m = [m[0] >>> 16, m[0] & 0xffff, m[1] >>> 16, m[1] & 0xffff];\n    n = [n[0] >>> 16, n[0] & 0xffff, n[1] >>> 16, n[1] & 0xffff];\n    var o = [0, 0, 0, 0];\n    o[3] += m[3] * n[3];\n    o[2] += o[3] >>> 16;\n    o[3] &= 0xffff;\n    o[2] += m[2] * n[3];\n    o[1] += o[2] >>> 16;\n    o[2] &= 0xffff;\n    o[2] += m[3] * n[2];\n    o[1] += o[2] >>> 16;\n    o[2] &= 0xffff;\n    o[1] += m[1] * n[3];\n    o[0] += o[1] >>> 16;\n    o[1] &= 0xffff;\n    o[1] += m[2] * n[2];\n    o[0] += o[1] >>> 16;\n    o[1] &= 0xffff;\n    o[1] += m[3] * n[1];\n    o[0] += o[1] >>> 16;\n    o[1] &= 0xffff;\n    o[0] += m[0] * n[3] + m[1] * n[2] + m[2] * n[1] + m[3] * n[0];\n    o[0] &= 0xffff;\n    return [(o[0] << 16) | o[1], (o[2] << 16) | o[3]];\n}\n//\n// Given a 64bit int (as an array of two 32bit ints) and an int\n// representing a number of bit positions, returns the 64bit int (as an\n// array of two 32bit ints) rotated left by that number of positions.\n//\nfunction x64Rotl(m, n) {\n    n %= 64;\n    if (n === 32) {\n        return [m[1], m[0]];\n    }\n    else if (n < 32) {\n        return [(m[0] << n) | (m[1] >>> (32 - n)), (m[1] << n) | (m[0] >>> (32 - n))];\n    }\n    else {\n        n -= 32;\n        return [(m[1] << n) | (m[0] >>> (32 - n)), (m[0] << n) | (m[1] >>> (32 - n))];\n    }\n}\n//\n// Given a 64bit int (as an array of two 32bit ints) and an int\n// representing a number of bit positions, returns the 64bit int (as an\n// array of two 32bit ints) shifted left by that number of positions.\n//\nfunction x64LeftShift(m, n) {\n    n %= 64;\n    if (n === 0) {\n        return m;\n    }\n    else if (n < 32) {\n        return [(m[0] << n) | (m[1] >>> (32 - n)), m[1] << n];\n    }\n    else {\n        return [m[1] << (n - 32), 0];\n    }\n}\n//\n// Given two 64bit ints (as an array of two 32bit ints) returns the two\n// xored together as a 64bit int (as an array of two 32bit ints).\n//\nfunction x64Xor(m, n) {\n    return [m[0] ^ n[0], m[1] ^ n[1]];\n}\n//\n// Given a block, returns murmurHash3's final x64 mix of that block.\n// (`[0, h[0] >>> 1]` is a 33 bit unsigned right shift. This is the\n// only place where we need to right shift 64bit ints.)\n//\nfunction x64Fmix(h) {\n    h = x64Xor(h, [0, h[0] >>> 1]);\n    h = x64Multiply(h, [0xff51afd7, 0xed558ccd]);\n    h = x64Xor(h, [0, h[0] >>> 1]);\n    h = x64Multiply(h, [0xc4ceb9fe, 0x1a85ec53]);\n    h = x64Xor(h, [0, h[0] >>> 1]);\n    return h;\n}\n//\n// Given a string and an optional seed as an int, returns a 128 bit\n// hash using the x64 flavor of MurmurHash3, as an unsigned hex.\n//\nfunction x64hash128(key, seed) {\n    key = key || '';\n    seed = seed || 0;\n    var remainder = key.length % 16;\n    var bytes = key.length - remainder;\n    var h1 = [0, seed];\n    var h2 = [0, seed];\n    var k1 = [0, 0];\n    var k2 = [0, 0];\n    var c1 = [0x87c37b91, 0x114253d5];\n    var c2 = [0x4cf5ad43, 0x2745937f];\n    var i;\n    for (i = 0; i < bytes; i = i + 16) {\n        k1 = [\n            (key.charCodeAt(i + 4) & 0xff) |\n                ((key.charCodeAt(i + 5) & 0xff) << 8) |\n                ((key.charCodeAt(i + 6) & 0xff) << 16) |\n                ((key.charCodeAt(i + 7) & 0xff) << 24),\n            (key.charCodeAt(i) & 0xff) |\n                ((key.charCodeAt(i + 1) & 0xff) << 8) |\n                ((key.charCodeAt(i + 2) & 0xff) << 16) |\n                ((key.charCodeAt(i + 3) & 0xff) << 24),\n        ];\n        k2 = [\n            (key.charCodeAt(i + 12) & 0xff) |\n                ((key.charCodeAt(i + 13) & 0xff) << 8) |\n                ((key.charCodeAt(i + 14) & 0xff) << 16) |\n                ((key.charCodeAt(i + 15) & 0xff) << 24),\n            (key.charCodeAt(i + 8) & 0xff) |\n                ((key.charCodeAt(i + 9) & 0xff) << 8) |\n                ((key.charCodeAt(i + 10) & 0xff) << 16) |\n                ((key.charCodeAt(i + 11) & 0xff) << 24),\n        ];\n        k1 = x64Multiply(k1, c1);\n        k1 = x64Rotl(k1, 31);\n        k1 = x64Multiply(k1, c2);\n        h1 = x64Xor(h1, k1);\n        h1 = x64Rotl(h1, 27);\n        h1 = x64Add(h1, h2);\n        h1 = x64Add(x64Multiply(h1, [0, 5]), [0, 0x52dce729]);\n        k2 = x64Multiply(k2, c2);\n        k2 = x64Rotl(k2, 33);\n        k2 = x64Multiply(k2, c1);\n        h2 = x64Xor(h2, k2);\n        h2 = x64Rotl(h2, 31);\n        h2 = x64Add(h2, h1);\n        h2 = x64Add(x64Multiply(h2, [0, 5]), [0, 0x38495ab5]);\n    }\n    k1 = [0, 0];\n    k2 = [0, 0];\n    switch (remainder) {\n        case 15:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 14)], 48));\n        // fallthrough\n        case 14:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 13)], 40));\n        // fallthrough\n        case 13:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 12)], 32));\n        // fallthrough\n        case 12:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 11)], 24));\n        // fallthrough\n        case 11:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 10)], 16));\n        // fallthrough\n        case 10:\n            k2 = x64Xor(k2, x64LeftShift([0, key.charCodeAt(i + 9)], 8));\n        // fallthrough\n        case 9:\n            k2 = x64Xor(k2, [0, key.charCodeAt(i + 8)]);\n            k2 = x64Multiply(k2, c2);\n            k2 = x64Rotl(k2, 33);\n            k2 = x64Multiply(k2, c1);\n            h2 = x64Xor(h2, k2);\n        // fallthrough\n        case 8:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 7)], 56));\n        // fallthrough\n        case 7:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 6)], 48));\n        // fallthrough\n        case 6:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 5)], 40));\n        // fallthrough\n        case 5:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 4)], 32));\n        // fallthrough\n        case 4:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 3)], 24));\n        // fallthrough\n        case 3:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 2)], 16));\n        // fallthrough\n        case 2:\n            k1 = x64Xor(k1, x64LeftShift([0, key.charCodeAt(i + 1)], 8));\n        // fallthrough\n        case 1:\n            k1 = x64Xor(k1, [0, key.charCodeAt(i)]);\n            k1 = x64Multiply(k1, c1);\n            k1 = x64Rotl(k1, 31);\n            k1 = x64Multiply(k1, c2);\n            h1 = x64Xor(h1, k1);\n        // fallthrough\n    }\n    h1 = x64Xor(h1, [0, key.length]);\n    h2 = x64Xor(h2, [0, key.length]);\n    h1 = x64Add(h1, h2);\n    h2 = x64Add(h2, h1);\n    h1 = x64Fmix(h1);\n    h2 = x64Fmix(h2);\n    h1 = x64Add(h1, h2);\n    h2 = x64Add(h2, h1);\n    return (('00000000' + (h1[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h1[1] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[0] >>> 0).toString(16)).slice(-8) +\n        ('00000000' + (h2[1] >>> 0).toString(16)).slice(-8));\n}\n\n/**\n * Converts an error object to a plain object that can be used with `JSON.stringify`.\n * If you just run `JSON.stringify(error)`, you'll get `'{}'`.\n */\nfunction errorToObject(error) {\n    var _a;\n    return __assign({ name: error.name, message: error.message, stack: (_a = error.stack) === null || _a === void 0 ? void 0 : _a.split('\\n') }, error);\n}\n\n/*\n * This file contains functions to work with pure data only (no browser features, DOM, side effects, etc).\n */\n/**\n * Does the same as Array.prototype.includes but has better typing\n */\nfunction includes(haystack, needle) {\n    for (var i = 0, l = haystack.length; i < l; ++i) {\n        if (haystack[i] === needle) {\n            return true;\n        }\n    }\n    return false;\n}\n/**\n * Like `!includes()` but with proper typing\n */\nfunction excludes(haystack, needle) {\n    return !includes(haystack, needle);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toInt(value) {\n    return parseInt(value);\n}\n/**\n * Be careful, NaN can return\n */\nfunction toFloat(value) {\n    return parseFloat(value);\n}\nfunction replaceNaN(value, replacement) {\n    return typeof value === 'number' && isNaN(value) ? replacement : value;\n}\nfunction countTruthy(values) {\n    return values.reduce(function (sum, value) { return sum + (value ? 1 : 0); }, 0);\n}\nfunction round(value, base) {\n    if (base === void 0) { base = 1; }\n    if (Math.abs(base) >= 1) {\n        return Math.round(value / base) * base;\n    }\n    else {\n        // Sometimes when a number is multiplied by a small number, precision is lost,\n        // for example 1234 * 0.0001 === 0.12340000000000001, and it's more precise divide: 1234 / (1 / 0.0001) === 0.1234.\n        var counterBase = 1 / base;\n        return Math.round(value * counterBase) / counterBase;\n    }\n}\n/**\n * Parses a CSS selector into tag name with HTML attributes.\n * Only single element selector are supported (without operators like space, +, >, etc).\n *\n * Multiple values can be returned for each attribute. You decide how to handle them.\n */\nfunction parseSimpleCssSelector(selector) {\n    var _a, _b;\n    var errorMessage = \"Unexpected syntax '\".concat(selector, \"'\");\n    var tagMatch = /^\\s*([a-z-]*)(.*)$/i.exec(selector);\n    var tag = tagMatch[1] || undefined;\n    var attributes = {};\n    var partsRegex = /([.:#][\\w-]+|\\[.+?\\])/gi;\n    var addAttribute = function (name, value) {\n        attributes[name] = attributes[name] || [];\n        attributes[name].push(value);\n    };\n    for (;;) {\n        var match = partsRegex.exec(tagMatch[2]);\n        if (!match) {\n            break;\n        }\n        var part = match[0];\n        switch (part[0]) {\n            case '.':\n                addAttribute('class', part.slice(1));\n                break;\n            case '#':\n                addAttribute('id', part.slice(1));\n                break;\n            case '[': {\n                var attributeMatch = /^\\[([\\w-]+)([~|^$*]?=(\"(.*?)\"|([\\w-]+)))?(\\s+[is])?\\]$/.exec(part);\n                if (attributeMatch) {\n                    addAttribute(attributeMatch[1], (_b = (_a = attributeMatch[4]) !== null && _a !== void 0 ? _a : attributeMatch[5]) !== null && _b !== void 0 ? _b : '');\n                }\n                else {\n                    throw new Error(errorMessage);\n                }\n                break;\n            }\n            default:\n                throw new Error(errorMessage);\n        }\n    }\n    return [tag, attributes];\n}\n\nfunction ensureErrorWithMessage(error) {\n    return error && typeof error === 'object' && 'message' in error ? error : { message: error };\n}\nfunction isFinalResultLoaded(loadResult) {\n    return typeof loadResult !== 'function';\n}\n/**\n * Loads the given entropy source. Returns a function that gets an entropy component from the source.\n *\n * The result is returned synchronously to prevent `loadSources` from\n * waiting for one source to load before getting the components from the other sources.\n */\nfunction loadSource(source, sourceOptions) {\n    var sourceLoadPromise = new Promise(function (resolveLoad) {\n        var loadStartTime = Date.now();\n        // `awaitIfAsync` is used instead of just `await` in order to measure the duration of synchronous sources\n        // correctly (other microtasks won't affect the duration).\n        awaitIfAsync(source.bind(null, sourceOptions), function () {\n            var loadArgs = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                loadArgs[_i] = arguments[_i];\n            }\n            var loadDuration = Date.now() - loadStartTime;\n            // Source loading failed\n            if (!loadArgs[0]) {\n                return resolveLoad(function () { return ({ error: ensureErrorWithMessage(loadArgs[1]), duration: loadDuration }); });\n            }\n            var loadResult = loadArgs[1];\n            // Source loaded with the final result\n            if (isFinalResultLoaded(loadResult)) {\n                return resolveLoad(function () { return ({ value: loadResult, duration: loadDuration }); });\n            }\n            // Source loaded with \"get\" stage\n            resolveLoad(function () {\n                return new Promise(function (resolveGet) {\n                    var getStartTime = Date.now();\n                    awaitIfAsync(loadResult, function () {\n                        var getArgs = [];\n                        for (var _i = 0; _i < arguments.length; _i++) {\n                            getArgs[_i] = arguments[_i];\n                        }\n                        var duration = loadDuration + Date.now() - getStartTime;\n                        // Source getting failed\n                        if (!getArgs[0]) {\n                            return resolveGet({ error: ensureErrorWithMessage(getArgs[1]), duration: duration });\n                        }\n                        // Source getting succeeded\n                        resolveGet({ value: getArgs[1], duration: duration });\n                    });\n                });\n            });\n        });\n    });\n    suppressUnhandledRejectionWarning(sourceLoadPromise);\n    return function getComponent() {\n        return sourceLoadPromise.then(function (finalizeSource) { return finalizeSource(); });\n    };\n}\n/**\n * Loads the given entropy sources. Returns a function that collects the entropy components.\n *\n * The result is returned synchronously in order to allow start getting the components\n * before the sources are loaded completely.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction loadSources(sources, sourceOptions, excludeSources) {\n    var includedSources = Object.keys(sources).filter(function (sourceKey) { return excludes(excludeSources, sourceKey); });\n    // Using `mapWithBreaks` allows asynchronous sources to complete between synchronous sources\n    // and measure the duration correctly\n    var sourceGettersPromise = mapWithBreaks(includedSources, function (sourceKey) {\n        return loadSource(sources[sourceKey], sourceOptions);\n    });\n    suppressUnhandledRejectionWarning(sourceGettersPromise);\n    return function getComponents() {\n        return __awaiter(this, void 0, void 0, function () {\n            var sourceGetters, componentPromises, componentArray, components, index;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, sourceGettersPromise];\n                    case 1:\n                        sourceGetters = _a.sent();\n                        return [4 /*yield*/, mapWithBreaks(sourceGetters, function (sourceGetter) {\n                                var componentPromise = sourceGetter();\n                                suppressUnhandledRejectionWarning(componentPromise);\n                                return componentPromise;\n                            })];\n                    case 2:\n                        componentPromises = _a.sent();\n                        return [4 /*yield*/, Promise.all(componentPromises)\n                            // Keeping the component keys order the same as the source keys order\n                        ];\n                    case 3:\n                        componentArray = _a.sent();\n                        components = {};\n                        for (index = 0; index < includedSources.length; ++index) {\n                            components[includedSources[index]] = componentArray[index];\n                        }\n                        return [2 /*return*/, components];\n                }\n            });\n        });\n    };\n}\n/**\n * Modifies an entropy source by transforming its returned value with the given function.\n * Keeps the source properties: sync/async, 1/2 stages.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction transformSource(source, transformValue) {\n    var transformLoadResult = function (loadResult) {\n        if (isFinalResultLoaded(loadResult)) {\n            return transformValue(loadResult);\n        }\n        return function () {\n            var getResult = loadResult();\n            if (isPromise(getResult)) {\n                return getResult.then(transformValue);\n            }\n            return transformValue(getResult);\n        };\n    };\n    return function (options) {\n        var loadResult = source(options);\n        if (isPromise(loadResult)) {\n            return loadResult.then(transformLoadResult);\n        }\n        return transformLoadResult(loadResult);\n    };\n}\n\n/*\n * Functions to help with features that vary through browsers\n */\n/**\n * Checks whether the browser is based on Trident (the Internet Explorer engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isTrident() {\n    var w = window;\n    var n = navigator;\n    // The properties are checked to be in IE 10, IE 11 and not to be in other browsers in October 2020\n    return (countTruthy([\n        'MSCSSMatrix' in w,\n        'msSetImmediate' in w,\n        'msIndexedDB' in w,\n        'msMaxTouchPoints' in n,\n        'msPointerEnabled' in n,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on EdgeHTML (the pre-Chromium Edge engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isEdgeHTML() {\n    // Based on research in October 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy(['msWriteProfilerMark' in w, 'MSStream' in w, 'msLaunchUri' in n, 'msSaveBlob' in n]) >= 3 &&\n        !isTrident());\n}\n/**\n * Checks whether the browser is based on Chromium without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isChromium() {\n    // Based on research in October 2020. Tested to detect Chromium 42-86.\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'webkitPersistentStorage' in n,\n        'webkitTemporaryStorage' in n,\n        n.vendor.indexOf('Google') === 0,\n        'webkitResolveLocalFileSystemURL' in w,\n        'BatteryManager' in w,\n        'webkitMediaStream' in w,\n        'webkitSpeechGrammar' in w,\n    ]) >= 5);\n}\n/**\n * Checks whether the browser is based on mobile or desktop Safari without using user-agent.\n * All iOS browsers use WebKit (the Safari engine).\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isWebKit() {\n    // Based on research in September 2020\n    var w = window;\n    var n = navigator;\n    return (countTruthy([\n        'ApplePayError' in w,\n        'CSSPrimitiveValue' in w,\n        'Counter' in w,\n        n.vendor.indexOf('Apple') === 0,\n        'getStorageUpdates' in n,\n        'WebKitMediaKeys' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the WebKit browser is a desktop Safari.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isDesktopSafari() {\n    var w = window;\n    return (countTruthy([\n        'safari' in w,\n        !('DeviceMotionEvent' in w),\n        !('ongestureend' in w),\n        !('standalone' in navigator),\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on Gecko (Firefox engine) without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isGecko() {\n    var _a, _b;\n    var w = window;\n    // Based on research in September 2020\n    return (countTruthy([\n        'buildID' in navigator,\n        'MozAppearance' in ((_b = (_a = document.documentElement) === null || _a === void 0 ? void 0 : _a.style) !== null && _b !== void 0 ? _b : {}),\n        'onmozfullscreenchange' in w,\n        'mozInnerScreenX' in w,\n        'CSSMozDocumentRule' in w,\n        'CanvasCaptureMediaStream' in w,\n    ]) >= 4);\n}\n/**\n * Checks whether the browser is based on Chromium version ≥86 without using user-agent.\n * It doesn't check that the browser is based on Chromium, there is a separate function for this.\n */\nfunction isChromium86OrNewer() {\n    // Checked in Chrome 85 vs Chrome 86 both on desktop and Android\n    var w = window;\n    return (countTruthy([\n        !('MediaSettingsRange' in w),\n        'RTCEncodedAudioFrame' in w,\n        '' + w.Intl === '[object Intl]',\n        '' + w.Reflect === '[object Reflect]',\n    ]) >= 3);\n}\n/**\n * Checks whether the browser is based on WebKit version ≥606 (Safari ≥12) without using user-agent.\n * It doesn't check that the browser is based on WebKit, there is a separate function for this.\n *\n * @link https://en.wikipedia.org/wiki/Safari_version_history#Release_history Safari-WebKit versions map\n */\nfunction isWebKit606OrNewer() {\n    // Checked in Safari 9–14\n    var w = window;\n    return (countTruthy([\n        'DOMRectList' in w,\n        'RTCPeerConnectionIceEvent' in w,\n        'SVGGeometryElement' in w,\n        'ontransitioncancel' in w,\n    ]) >= 3);\n}\n/**\n * Checks whether the device is an iPad.\n * It doesn't check that the engine is WebKit and that the WebKit isn't desktop.\n */\nfunction isIPad() {\n    // Checked on:\n    // Safari on iPadOS (both mobile and desktop modes): 8, 11, 12, 13, 14\n    // Chrome on iPadOS (both mobile and desktop modes): 11, 12, 13, 14\n    // Safari on iOS (both mobile and desktop modes): 9, 10, 11, 12, 13, 14\n    // Chrome on iOS (both mobile and desktop modes): 9, 10, 11, 12, 13, 14\n    // Before iOS 13. Safari tampers the value in \"request desktop site\" mode since iOS 13.\n    if (navigator.platform === 'iPad') {\n        return true;\n    }\n    var s = screen;\n    var screenRatio = s.width / s.height;\n    return (countTruthy([\n        'MediaSource' in window,\n        !!Element.prototype.webkitRequestFullscreen,\n        // iPhone 4S that runs iOS 9 matches this. But it won't match the criteria above, so it won't be detected as iPad.\n        screenRatio > 0.65 && screenRatio < 1.53,\n    ]) >= 2);\n}\n/**\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction getFullscreenElement() {\n    var d = document;\n    return d.fullscreenElement || d.msFullscreenElement || d.mozFullScreenElement || d.webkitFullscreenElement || null;\n}\nfunction exitFullscreen() {\n    var d = document;\n    // `call` is required because the function throws an error without a proper \"this\" context\n    return (d.exitFullscreen || d.msExitFullscreen || d.mozCancelFullScreen || d.webkitExitFullscreen).call(d);\n}\n/**\n * Checks whether the device runs on Android without using user-agent.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction isAndroid() {\n    var isItChromium = isChromium();\n    var isItGecko = isGecko();\n    // Only 2 browser engines are presented on Android.\n    // Actually, there is also Android 4.1 browser, but it's not worth detecting it at the moment.\n    if (!isItChromium && !isItGecko) {\n        return false;\n    }\n    var w = window;\n    // Chrome removes all words \"Android\" from `navigator` when desktop version is requested\n    // Firefox keeps \"Android\" in `navigator.appVersion` when desktop version is requested\n    return (countTruthy([\n        'onorientationchange' in w,\n        'orientation' in w,\n        isItChromium && !('SharedWorker' in w),\n        isItGecko && /android/i.test(navigator.appVersion),\n    ]) >= 2);\n}\n\n/**\n * A deep description: https://fingerprint.com/blog/audio-fingerprinting/\n * Inspired by and based on https://github.com/cozylife/audio-fingerprint\n */\nfunction getAudioFingerprint() {\n    var w = window;\n    var AudioContext = w.OfflineAudioContext || w.webkitOfflineAudioContext;\n    if (!AudioContext) {\n        return -2 /* SpecialFingerprint.NotSupported */;\n    }\n    // In some browsers, audio context always stays suspended unless the context is started in response to a user action\n    // (e.g. a click or a tap). It prevents audio fingerprint from being taken at an arbitrary moment of time.\n    // Such browsers are old and unpopular, so the audio fingerprinting is just skipped in them.\n    // See a similar case explanation at https://stackoverflow.com/questions/46363048/onaudioprocess-not-called-on-ios11#46534088\n    if (doesCurrentBrowserSuspendAudioContext()) {\n        return -1 /* SpecialFingerprint.KnownToSuspend */;\n    }\n    var hashFromIndex = 4500;\n    var hashToIndex = 5000;\n    var context = new AudioContext(1, hashToIndex, 44100);\n    var oscillator = context.createOscillator();\n    oscillator.type = 'triangle';\n    oscillator.frequency.value = 10000;\n    var compressor = context.createDynamicsCompressor();\n    compressor.threshold.value = -50;\n    compressor.knee.value = 40;\n    compressor.ratio.value = 12;\n    compressor.attack.value = 0;\n    compressor.release.value = 0.25;\n    oscillator.connect(compressor);\n    compressor.connect(context.destination);\n    oscillator.start(0);\n    var _a = startRenderingAudio(context), renderPromise = _a[0], finishRendering = _a[1];\n    var fingerprintPromise = renderPromise.then(function (buffer) { return getHash(buffer.getChannelData(0).subarray(hashFromIndex)); }, function (error) {\n        if (error.name === \"timeout\" /* InnerErrorName.Timeout */ || error.name === \"suspended\" /* InnerErrorName.Suspended */) {\n            return -3 /* SpecialFingerprint.Timeout */;\n        }\n        throw error;\n    });\n    // Suppresses the console error message in case when the fingerprint fails before requested\n    suppressUnhandledRejectionWarning(fingerprintPromise);\n    return function () {\n        finishRendering();\n        return fingerprintPromise;\n    };\n}\n/**\n * Checks if the current browser is known to always suspend audio context\n */\nfunction doesCurrentBrowserSuspendAudioContext() {\n    return isWebKit() && !isDesktopSafari() && !isWebKit606OrNewer();\n}\n/**\n * Starts rendering the audio context.\n * When the returned function is called, the render process starts finishing.\n */\nfunction startRenderingAudio(context) {\n    var renderTryMaxCount = 3;\n    var renderRetryDelay = 500;\n    var runningMaxAwaitTime = 500;\n    var runningSufficientTime = 5000;\n    var finalize = function () { return undefined; };\n    var resultPromise = new Promise(function (resolve, reject) {\n        var isFinalized = false;\n        var renderTryCount = 0;\n        var startedRunningAt = 0;\n        context.oncomplete = function (event) { return resolve(event.renderedBuffer); };\n        var startRunningTimeout = function () {\n            setTimeout(function () { return reject(makeInnerError(\"timeout\" /* InnerErrorName.Timeout */)); }, Math.min(runningMaxAwaitTime, startedRunningAt + runningSufficientTime - Date.now()));\n        };\n        var tryRender = function () {\n            try {\n                var renderingPromise = context.startRendering();\n                // `context.startRendering` has two APIs: Promise and callback, we check that it's really a promise just in case\n                if (isPromise(renderingPromise)) {\n                    // Suppresses all unhadled rejections in case of scheduled redundant retries after successful rendering\n                    suppressUnhandledRejectionWarning(renderingPromise);\n                }\n                switch (context.state) {\n                    case 'running':\n                        startedRunningAt = Date.now();\n                        if (isFinalized) {\n                            startRunningTimeout();\n                        }\n                        break;\n                    // Sometimes the audio context doesn't start after calling `startRendering` (in addition to the cases where\n                    // audio context doesn't start at all). A known case is starting an audio context when the browser tab is in\n                    // background on iPhone. Retries usually help in this case.\n                    case 'suspended':\n                        // The audio context can reject starting until the tab is in foreground. Long fingerprint duration\n                        // in background isn't a problem, therefore the retry attempts don't count in background. It can lead to\n                        // a situation when a fingerprint takes very long time and finishes successfully. FYI, the audio context\n                        // can be suspended when `document.hidden === false` and start running after a retry.\n                        if (!document.hidden) {\n                            renderTryCount++;\n                        }\n                        if (isFinalized && renderTryCount >= renderTryMaxCount) {\n                            reject(makeInnerError(\"suspended\" /* InnerErrorName.Suspended */));\n                        }\n                        else {\n                            setTimeout(tryRender, renderRetryDelay);\n                        }\n                        break;\n                }\n            }\n            catch (error) {\n                reject(error);\n            }\n        };\n        tryRender();\n        finalize = function () {\n            if (!isFinalized) {\n                isFinalized = true;\n                if (startedRunningAt > 0) {\n                    startRunningTimeout();\n                }\n            }\n        };\n    });\n    return [resultPromise, finalize];\n}\nfunction getHash(signal) {\n    var hash = 0;\n    for (var i = 0; i < signal.length; ++i) {\n        hash += Math.abs(signal[i]);\n    }\n    return hash;\n}\nfunction makeInnerError(name) {\n    var error = new Error(name);\n    error.name = name;\n    return error;\n}\n\n/**\n * Creates and keeps an invisible iframe while the given function runs.\n * The given function is called when the iframe is loaded and has a body.\n * The iframe allows to measure DOM sizes inside itself.\n *\n * Notice: passing an initial HTML code doesn't work in IE.\n *\n * Warning for package users:\n * This function is out of Semantic Versioning, i.e. can change unexpectedly. Usage is at your own risk.\n */\nfunction withIframe(action, initialHtml, domPollInterval) {\n    var _a, _b, _c;\n    if (domPollInterval === void 0) { domPollInterval = 50; }\n    return __awaiter(this, void 0, void 0, function () {\n        var d, iframe;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    d = document;\n                    _d.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 2:\n                    _d.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    iframe = d.createElement('iframe');\n                    _d.label = 4;\n                case 4:\n                    _d.trys.push([4, , 10, 11]);\n                    return [4 /*yield*/, new Promise(function (_resolve, _reject) {\n                            var isComplete = false;\n                            var resolve = function () {\n                                isComplete = true;\n                                _resolve();\n                            };\n                            var reject = function (error) {\n                                isComplete = true;\n                                _reject(error);\n                            };\n                            iframe.onload = resolve;\n                            iframe.onerror = reject;\n                            var style = iframe.style;\n                            style.setProperty('display', 'block', 'important'); // Required for browsers to calculate the layout\n                            style.position = 'absolute';\n                            style.top = '0';\n                            style.left = '0';\n                            style.visibility = 'hidden';\n                            if (initialHtml && 'srcdoc' in iframe) {\n                                iframe.srcdoc = initialHtml;\n                            }\n                            else {\n                                iframe.src = 'about:blank';\n                            }\n                            d.body.appendChild(iframe);\n                            // WebKit in WeChat doesn't fire the iframe's `onload` for some reason.\n                            // This code checks for the loading state manually.\n                            // See https://github.com/fingerprintjs/fingerprintjs/issues/645\n                            var checkReadyState = function () {\n                                var _a, _b;\n                                // The ready state may never become 'complete' in Firefox despite the 'load' event being fired.\n                                // So an infinite setTimeout loop can happen without this check.\n                                // See https://github.com/fingerprintjs/fingerprintjs/pull/716#issuecomment-986898796\n                                if (isComplete) {\n                                    return;\n                                }\n                                // Make sure iframe.contentWindow and iframe.contentWindow.document are both loaded\n                                // The contentWindow.document can miss in JSDOM (https://github.com/jsdom/jsdom).\n                                if (((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.readyState) === 'complete') {\n                                    resolve();\n                                }\n                                else {\n                                    setTimeout(checkReadyState, 10);\n                                }\n                            };\n                            checkReadyState();\n                        })];\n                case 5:\n                    _d.sent();\n                    _d.label = 6;\n                case 6:\n                    if (!!((_b = (_a = iframe.contentWindow) === null || _a === void 0 ? void 0 : _a.document) === null || _b === void 0 ? void 0 : _b.body)) return [3 /*break*/, 8];\n                    return [4 /*yield*/, wait(domPollInterval)];\n                case 7:\n                    _d.sent();\n                    return [3 /*break*/, 6];\n                case 8: return [4 /*yield*/, action(iframe, iframe.contentWindow)];\n                case 9: return [2 /*return*/, _d.sent()];\n                case 10:\n                    (_c = iframe.parentNode) === null || _c === void 0 ? void 0 : _c.removeChild(iframe);\n                    return [7 /*endfinally*/];\n                case 11: return [2 /*return*/];\n            }\n        });\n    });\n}\n/**\n * Creates a DOM element that matches the given selector.\n * Only single element selector are supported (without operators like space, +, >, etc).\n */\nfunction selectorToElement(selector) {\n    var _a = parseSimpleCssSelector(selector), tag = _a[0], attributes = _a[1];\n    var element = document.createElement(tag !== null && tag !== void 0 ? tag : 'div');\n    for (var _i = 0, _b = Object.keys(attributes); _i < _b.length; _i++) {\n        var name_1 = _b[_i];\n        var value = attributes[name_1].join(' ');\n        // Changing the `style` attribute can cause a CSP error, therefore we change the `style.cssText` property.\n        // https://github.com/fingerprintjs/fingerprintjs/issues/733\n        if (name_1 === 'style') {\n            addStyleString(element.style, value);\n        }\n        else {\n            element.setAttribute(name_1, value);\n        }\n    }\n    return element;\n}\n/**\n * Adds CSS styles from a string in such a way that doesn't trigger a CSP warning (unsafe-inline or unsafe-eval)\n */\nfunction addStyleString(style, source) {\n    // We don't use `style.cssText` because browsers must block it when no `unsafe-eval` CSP is presented: https://csplite.com/csp145/#w3c_note\n    // Even though the browsers ignore this standard, we don't use `cssText` just in case.\n    for (var _i = 0, _a = source.split(';'); _i < _a.length; _i++) {\n        var property = _a[_i];\n        var match = /^\\s*([\\w-]+)\\s*:\\s*(.+?)(\\s*!([\\w-]+))?\\s*$/.exec(property);\n        if (match) {\n            var name_2 = match[1], value = match[2], priority = match[4];\n            style.setProperty(name_2, value, priority || ''); // The last argument can't be undefined in IE11\n        }\n    }\n}\n\n// We use m or w because these two characters take up the maximum width.\n// And we use a LLi so that the same matching fonts can get separated.\nvar testString = 'mmMwWLliI0O&1';\n// We test using 48px font size, we may use any size. I guess larger the better.\nvar textSize = '48px';\n// A font will be compared against all the three default fonts.\n// And if for any default fonts it doesn't match, then that font is available.\nvar baseFonts = ['monospace', 'sans-serif', 'serif'];\nvar fontList = [\n    // This is android-specific font from \"Roboto\" family\n    'sans-serif-thin',\n    'ARNO PRO',\n    'Agency FB',\n    'Arabic Typesetting',\n    'Arial Unicode MS',\n    'AvantGarde Bk BT',\n    'BankGothic Md BT',\n    'Batang',\n    'Bitstream Vera Sans Mono',\n    'Calibri',\n    'Century',\n    'Century Gothic',\n    'Clarendon',\n    'EUROSTILE',\n    'Franklin Gothic',\n    'Futura Bk BT',\n    'Futura Md BT',\n    'GOTHAM',\n    'Gill Sans',\n    'HELV',\n    'Haettenschweiler',\n    'Helvetica Neue',\n    'Humanst521 BT',\n    'Leelawadee',\n    'Letter Gothic',\n    'Levenim MT',\n    'Lucida Bright',\n    'Lucida Sans',\n    'Menlo',\n    'MS Mincho',\n    'MS Outlook',\n    'MS Reference Specialty',\n    'MS UI Gothic',\n    'MT Extra',\n    'MYRIAD PRO',\n    'Marlett',\n    'Meiryo UI',\n    'Microsoft Uighur',\n    'Minion Pro',\n    'Monotype Corsiva',\n    'PMingLiU',\n    'Pristina',\n    'SCRIPTINA',\n    'Segoe UI Light',\n    'Serifa',\n    'SimHei',\n    'Small Fonts',\n    'Staccato222 BT',\n    'TRAJAN PRO',\n    'Univers CE 55 Medium',\n    'Vrinda',\n    'ZWAdobeF',\n];\n// kudos to http://www.lalit.org/lab/javascript-css-font-detect/\nfunction getFonts() {\n    // Running the script in an iframe makes it not affect the page look and not be affected by the page CSS. See:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/592\n    // https://github.com/fingerprintjs/fingerprintjs/issues/628\n    return withIframe(function (_, _a) {\n        var document = _a.document;\n        var holder = document.body;\n        holder.style.fontSize = textSize;\n        // div to load spans for the default fonts and the fonts to detect\n        var spansContainer = document.createElement('div');\n        var defaultWidth = {};\n        var defaultHeight = {};\n        // creates a span where the fonts will be loaded\n        var createSpan = function (fontFamily) {\n            var span = document.createElement('span');\n            var style = span.style;\n            style.position = 'absolute';\n            style.top = '0';\n            style.left = '0';\n            style.fontFamily = fontFamily;\n            span.textContent = testString;\n            spansContainer.appendChild(span);\n            return span;\n        };\n        // creates a span and load the font to detect and a base font for fallback\n        var createSpanWithFonts = function (fontToDetect, baseFont) {\n            return createSpan(\"'\".concat(fontToDetect, \"',\").concat(baseFont));\n        };\n        // creates spans for the base fonts and adds them to baseFontsDiv\n        var initializeBaseFontsSpans = function () {\n            return baseFonts.map(createSpan);\n        };\n        // creates spans for the fonts to detect and adds them to fontsDiv\n        var initializeFontsSpans = function () {\n            // Stores {fontName : [spans for that font]}\n            var spans = {};\n            var _loop_1 = function (font) {\n                spans[font] = baseFonts.map(function (baseFont) { return createSpanWithFonts(font, baseFont); });\n            };\n            for (var _i = 0, fontList_1 = fontList; _i < fontList_1.length; _i++) {\n                var font = fontList_1[_i];\n                _loop_1(font);\n            }\n            return spans;\n        };\n        // checks if a font is available\n        var isFontAvailable = function (fontSpans) {\n            return baseFonts.some(function (baseFont, baseFontIndex) {\n                return fontSpans[baseFontIndex].offsetWidth !== defaultWidth[baseFont] ||\n                    fontSpans[baseFontIndex].offsetHeight !== defaultHeight[baseFont];\n            });\n        };\n        // create spans for base fonts\n        var baseFontsSpans = initializeBaseFontsSpans();\n        // create spans for fonts to detect\n        var fontsSpans = initializeFontsSpans();\n        // add all the spans to the DOM\n        holder.appendChild(spansContainer);\n        // get the default width for the three base fonts\n        for (var index = 0; index < baseFonts.length; index++) {\n            defaultWidth[baseFonts[index]] = baseFontsSpans[index].offsetWidth; // width for the default font\n            defaultHeight[baseFonts[index]] = baseFontsSpans[index].offsetHeight; // height for the default font\n        }\n        // check available fonts\n        return fontList.filter(function (font) { return isFontAvailable(fontsSpans[font]); });\n    });\n}\n\nfunction getPlugins() {\n    var rawPlugins = navigator.plugins;\n    if (!rawPlugins) {\n        return undefined;\n    }\n    var plugins = [];\n    // Safari 10 doesn't support iterating navigator.plugins with for...of\n    for (var i = 0; i < rawPlugins.length; ++i) {\n        var plugin = rawPlugins[i];\n        if (!plugin) {\n            continue;\n        }\n        var mimeTypes = [];\n        for (var j = 0; j < plugin.length; ++j) {\n            var mimeType = plugin[j];\n            mimeTypes.push({\n                type: mimeType.type,\n                suffixes: mimeType.suffixes,\n            });\n        }\n        plugins.push({\n            name: plugin.name,\n            description: plugin.description,\n            mimeTypes: mimeTypes,\n        });\n    }\n    return plugins;\n}\n\n// https://www.browserleaks.com/canvas#how-does-it-work\nfunction getCanvasFingerprint() {\n    var winding = false;\n    var geometry;\n    var text;\n    var _a = makeCanvasContext(), canvas = _a[0], context = _a[1];\n    if (!isSupported(canvas, context)) {\n        geometry = text = ''; // The value will be 'unsupported' in v3.4\n    }\n    else {\n        winding = doesSupportWinding(context);\n        renderTextImage(canvas, context);\n        var textImage1 = canvasToString(canvas);\n        var textImage2 = canvasToString(canvas); // It's slightly faster to double-encode the text image\n        // Some browsers add a noise to the canvas: https://github.com/fingerprintjs/fingerprintjs/issues/791\n        // The canvas is excluded from the fingerprint in this case\n        if (textImage1 !== textImage2) {\n            geometry = text = 'unstable';\n        }\n        else {\n            text = textImage1;\n            // Text is unstable:\n            // https://github.com/fingerprintjs/fingerprintjs/issues/583\n            // https://github.com/fingerprintjs/fingerprintjs/issues/103\n            // Therefore it's extracted into a separate image.\n            renderGeometryImage(canvas, context);\n            geometry = canvasToString(canvas);\n        }\n    }\n    return { winding: winding, geometry: geometry, text: text };\n}\nfunction makeCanvasContext() {\n    var canvas = document.createElement('canvas');\n    canvas.width = 1;\n    canvas.height = 1;\n    return [canvas, canvas.getContext('2d')];\n}\nfunction isSupported(canvas, context) {\n    return !!(context && canvas.toDataURL);\n}\nfunction doesSupportWinding(context) {\n    // https://web.archive.org/web/20170825024655/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // https://github.com/Modernizr/Modernizr/blob/master/feature-detects/canvas/winding.js\n    context.rect(0, 0, 10, 10);\n    context.rect(2, 2, 6, 6);\n    return !context.isPointInPath(5, 5, 'evenodd');\n}\nfunction renderTextImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 240;\n    canvas.height = 60;\n    context.textBaseline = 'alphabetic';\n    context.fillStyle = '#f60';\n    context.fillRect(100, 1, 62, 20);\n    context.fillStyle = '#069';\n    // It's important to use explicit built-in fonts in order to exclude the affect of font preferences\n    // (there is a separate entropy source for them).\n    context.font = '11pt \"Times New Roman\"';\n    // The choice of emojis has a gigantic impact on rendering performance (especially in FF).\n    // Some newer emojis cause it to slow down 50-200 times.\n    // There must be no text to the right of the emoji, see https://github.com/fingerprintjs/fingerprintjs/issues/574\n    // A bare emoji shouldn't be used because the canvas will change depending on the script encoding:\n    // https://github.com/fingerprintjs/fingerprintjs/issues/66\n    // Escape sequence shouldn't be used too because Terser will turn it into a bare unicode.\n    var printedText = \"Cwm fjordbank gly \".concat(String.fromCharCode(55357, 56835) /* 😃 */);\n    context.fillText(printedText, 2, 15);\n    context.fillStyle = 'rgba(102, 204, 0, 0.2)';\n    context.font = '18pt Arial';\n    context.fillText(printedText, 4, 45);\n}\nfunction renderGeometryImage(canvas, context) {\n    // Resizing the canvas cleans it\n    canvas.width = 122;\n    canvas.height = 110;\n    // Canvas blending\n    // https://web.archive.org/web/**************/http://blogs.adobe.com/webplatform/2013/01/28/blending-features-in-canvas/\n    // http://jsfiddle.net/NDYV8/16/\n    context.globalCompositeOperation = 'multiply';\n    for (var _i = 0, _a = [\n        ['#f2f', 40, 40],\n        ['#2ff', 80, 40],\n        ['#ff2', 60, 80],\n    ]; _i < _a.length; _i++) {\n        var _b = _a[_i], color = _b[0], x = _b[1], y = _b[2];\n        context.fillStyle = color;\n        context.beginPath();\n        context.arc(x, y, 40, 0, Math.PI * 2, true);\n        context.closePath();\n        context.fill();\n    }\n    // Canvas winding\n    // https://web.archive.org/web/20130913061632/http://blogs.adobe.com/webplatform/2013/01/30/winding-rules-in-canvas/\n    // http://jsfiddle.net/NDYV8/19/\n    context.fillStyle = '#f9c';\n    context.arc(60, 60, 60, 0, Math.PI * 2, true);\n    context.arc(60, 60, 20, 0, Math.PI * 2, true);\n    context.fill('evenodd');\n}\nfunction canvasToString(canvas) {\n    return canvas.toDataURL();\n}\n\n/**\n * This is a crude and primitive touch screen detection. It's not possible to currently reliably detect the availability\n * of a touch screen with a JS, without actually subscribing to a touch event.\n *\n * @see http://www.stucox.com/blog/you-cant-detect-a-touchscreen/\n * @see https://github.com/Modernizr/Modernizr/issues/548\n */\nfunction getTouchSupport() {\n    var n = navigator;\n    var maxTouchPoints = 0;\n    var touchEvent;\n    if (n.maxTouchPoints !== undefined) {\n        maxTouchPoints = toInt(n.maxTouchPoints);\n    }\n    else if (n.msMaxTouchPoints !== undefined) {\n        maxTouchPoints = n.msMaxTouchPoints;\n    }\n    try {\n        document.createEvent('TouchEvent');\n        touchEvent = true;\n    }\n    catch (_a) {\n        touchEvent = false;\n    }\n    var touchStart = 'ontouchstart' in window;\n    return {\n        maxTouchPoints: maxTouchPoints,\n        touchEvent: touchEvent,\n        touchStart: touchStart,\n    };\n}\n\nfunction getOsCpu() {\n    return navigator.oscpu;\n}\n\nfunction getLanguages() {\n    var n = navigator;\n    var result = [];\n    var language = n.language || n.userLanguage || n.browserLanguage || n.systemLanguage;\n    if (language !== undefined) {\n        result.push([language]);\n    }\n    if (Array.isArray(n.languages)) {\n        // Starting from Chromium 86, there is only a single value in `navigator.language` in Incognito mode:\n        // the value of `navigator.language`. Therefore the value is ignored in this browser.\n        if (!(isChromium() && isChromium86OrNewer())) {\n            result.push(n.languages);\n        }\n    }\n    else if (typeof n.languages === 'string') {\n        var languages = n.languages;\n        if (languages) {\n            result.push(languages.split(','));\n        }\n    }\n    return result;\n}\n\nfunction getColorDepth() {\n    return window.screen.colorDepth;\n}\n\nfunction getDeviceMemory() {\n    // `navigator.deviceMemory` is a string containing a number in some unidentified cases\n    return replaceNaN(toFloat(navigator.deviceMemory), undefined);\n}\n\nfunction getScreenResolution() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    // Some browsers even return  screen resolution as not numbers.\n    var parseDimension = function (value) { return replaceNaN(toInt(value), null); };\n    var dimensions = [parseDimension(s.width), parseDimension(s.height)];\n    dimensions.sort().reverse();\n    return dimensions;\n}\n\nvar screenFrameCheckInterval = 2500;\nvar roundingPrecision = 10;\n// The type is readonly to protect from unwanted mutations\nvar screenFrameBackup;\nvar screenFrameSizeTimeoutId;\n/**\n * Starts watching the screen frame size. When a non-zero size appears, the size is saved and the watch is stopped.\n * Later, when `getScreenFrame` runs, it will return the saved non-zero size if the current size is null.\n *\n * This trick is required to mitigate the fact that the screen frame turns null in some cases.\n * See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n */\nfunction watchScreenFrame() {\n    if (screenFrameSizeTimeoutId !== undefined) {\n        return;\n    }\n    var checkScreenFrame = function () {\n        var frameSize = getCurrentScreenFrame();\n        if (isFrameSizeNull(frameSize)) {\n            screenFrameSizeTimeoutId = setTimeout(checkScreenFrame, screenFrameCheckInterval);\n        }\n        else {\n            screenFrameBackup = frameSize;\n            screenFrameSizeTimeoutId = undefined;\n        }\n    };\n    checkScreenFrame();\n}\nfunction getScreenFrame() {\n    var _this = this;\n    watchScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    frameSize = getCurrentScreenFrame();\n                    if (!isFrameSizeNull(frameSize)) return [3 /*break*/, 2];\n                    if (screenFrameBackup) {\n                        return [2 /*return*/, __spreadArray([], screenFrameBackup, true)];\n                    }\n                    if (!getFullscreenElement()) return [3 /*break*/, 2];\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    return [4 /*yield*/, exitFullscreen()];\n                case 1:\n                    // Some browsers set the screen frame to zero when programmatic fullscreen is on.\n                    // There is a chance of getting a non-zero frame after exiting the fullscreen.\n                    // See more on this at https://github.com/fingerprintjs/fingerprintjs/issues/568\n                    _a.sent();\n                    frameSize = getCurrentScreenFrame();\n                    _a.label = 2;\n                case 2:\n                    if (!isFrameSizeNull(frameSize)) {\n                        screenFrameBackup = frameSize;\n                    }\n                    return [2 /*return*/, frameSize];\n            }\n        });\n    }); };\n}\n/**\n * Sometimes the available screen resolution changes a bit, e.g. 1900x1440 → 1900x1439. A possible reason: macOS Dock\n * shrinks to fit more icons when there is too little space. The rounding is used to mitigate the difference.\n */\nfunction getRoundedScreenFrame() {\n    var _this = this;\n    var screenFrameGetter = getScreenFrame();\n    return function () { return __awaiter(_this, void 0, void 0, function () {\n        var frameSize, processSize;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0: return [4 /*yield*/, screenFrameGetter()];\n                case 1:\n                    frameSize = _a.sent();\n                    processSize = function (sideSize) { return (sideSize === null ? null : round(sideSize, roundingPrecision)); };\n                    // It might look like I don't know about `for` and `map`.\n                    // In fact, such code is used to avoid TypeScript issues without using `as`.\n                    return [2 /*return*/, [processSize(frameSize[0]), processSize(frameSize[1]), processSize(frameSize[2]), processSize(frameSize[3])]];\n            }\n        });\n    }); };\n}\nfunction getCurrentScreenFrame() {\n    var s = screen;\n    // Some browsers return screen resolution as strings, e.g. \"1200\", instead of a number, e.g. 1200.\n    // I suspect it's done by certain plugins that randomize browser properties to prevent fingerprinting.\n    //\n    // Some browsers (IE, Edge ≤18) don't provide `screen.availLeft` and `screen.availTop`. The property values are\n    // replaced with 0 in such cases to not lose the entropy from `screen.availWidth` and `screen.availHeight`.\n    return [\n        replaceNaN(toFloat(s.availTop), null),\n        replaceNaN(toFloat(s.width) - toFloat(s.availWidth) - replaceNaN(toFloat(s.availLeft), 0), null),\n        replaceNaN(toFloat(s.height) - toFloat(s.availHeight) - replaceNaN(toFloat(s.availTop), 0), null),\n        replaceNaN(toFloat(s.availLeft), null),\n    ];\n}\nfunction isFrameSizeNull(frameSize) {\n    for (var i = 0; i < 4; ++i) {\n        if (frameSize[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction getHardwareConcurrency() {\n    // sometimes hardware concurrency is a string\n    return replaceNaN(toInt(navigator.hardwareConcurrency), undefined);\n}\n\nfunction getTimezone() {\n    var _a;\n    var DateTimeFormat = (_a = window.Intl) === null || _a === void 0 ? void 0 : _a.DateTimeFormat;\n    if (DateTimeFormat) {\n        var timezone = new DateTimeFormat().resolvedOptions().timeZone;\n        if (timezone) {\n            return timezone;\n        }\n    }\n    // For browsers that don't support timezone names\n    // The minus is intentional because the JS offset is opposite to the real offset\n    var offset = -getTimezoneOffset();\n    return \"UTC\".concat(offset >= 0 ? '+' : '').concat(Math.abs(offset));\n}\nfunction getTimezoneOffset() {\n    var currentYear = new Date().getFullYear();\n    // The timezone offset may change over time due to daylight saving time (DST) shifts.\n    // The non-DST timezone offset is used as the result timezone offset.\n    // Since the DST season differs in the northern and the southern hemispheres,\n    // both January and July timezones offsets are considered.\n    return Math.max(\n    // `getTimezoneOffset` returns a number as a string in some unidentified cases\n    toFloat(new Date(currentYear, 0, 1).getTimezoneOffset()), toFloat(new Date(currentYear, 6, 1).getTimezoneOffset()));\n}\n\nfunction getSessionStorage() {\n    try {\n        return !!window.sessionStorage;\n    }\n    catch (error) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\n// https://bugzilla.mozilla.org/show_bug.cgi?id=781447\nfunction getLocalStorage() {\n    try {\n        return !!window.localStorage;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getIndexedDB() {\n    // IE and Edge don't allow accessing indexedDB in private mode, therefore IE and Edge will have different\n    // visitor identifier in normal and private modes.\n    if (isTrident() || isEdgeHTML()) {\n        return undefined;\n    }\n    try {\n        return !!window.indexedDB;\n    }\n    catch (e) {\n        /* SecurityError when referencing it means it exists */\n        return true;\n    }\n}\n\nfunction getOpenDatabase() {\n    return !!window.openDatabase;\n}\n\nfunction getCpuClass() {\n    return navigator.cpuClass;\n}\n\nfunction getPlatform() {\n    // Android Chrome 86 and 87 and Android Firefox 80 and 84 don't mock the platform value when desktop mode is requested\n    var platform = navigator.platform;\n    // iOS mocks the platform value when desktop version is requested: https://github.com/fingerprintjs/fingerprintjs/issues/514\n    // iPad uses desktop mode by default since iOS 13\n    // The value is 'MacIntel' on M1 Macs\n    // The value is 'iPhone' on iPod Touch\n    if (platform === 'MacIntel') {\n        if (isWebKit() && !isDesktopSafari()) {\n            return isIPad() ? 'iPad' : 'iPhone';\n        }\n    }\n    return platform;\n}\n\nfunction getVendor() {\n    return navigator.vendor || '';\n}\n\n/**\n * Checks for browser-specific (not engine specific) global variables to tell browsers with the same engine apart.\n * Only somewhat popular browsers are considered.\n */\nfunction getVendorFlavors() {\n    var flavors = [];\n    for (var _i = 0, _a = [\n        // Blink and some browsers on iOS\n        'chrome',\n        // Safari on macOS\n        'safari',\n        // Chrome on iOS (checked in 85 on 13 and 87 on 14)\n        '__crWeb',\n        '__gCrWeb',\n        // Yandex Browser on iOS, macOS and Android (checked in 21.2 on iOS 14, macOS and Android)\n        'yandex',\n        // Yandex Browser on iOS (checked in 21.2 on 14)\n        '__yb',\n        '__ybro',\n        // Firefox on iOS (checked in 32 on 14)\n        '__firefox__',\n        // Edge on iOS (checked in 46 on 14)\n        '__edgeTrackingPreventionStatistics',\n        'webkit',\n        // Opera Touch on iOS (checked in 2.6 on 14)\n        'oprt',\n        // Samsung Internet on Android (checked in 11.1)\n        'samsungAr',\n        // UC Browser on Android (checked in 12.10 and 13.0)\n        'ucweb',\n        'UCShellJava',\n        // Puffin on Android (checked in 9.0)\n        'puffinDevice',\n        // UC on iOS and Opera on Android have no specific global variables\n        // Edge for Android isn't checked\n    ]; _i < _a.length; _i++) {\n        var key = _a[_i];\n        var value = window[key];\n        if (value && typeof value === 'object') {\n            flavors.push(key);\n        }\n    }\n    return flavors.sort();\n}\n\n/**\n * navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n * cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past with\n * site-specific exceptions. Don't rely on it.\n *\n * @see https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js Taken from here\n */\nfunction areCookiesEnabled() {\n    var d = document;\n    // Taken from here: https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cookies.js\n    // navigator.cookieEnabled cannot detect custom or nuanced cookie blocking configurations. For example, when blocking\n    // cookies via the Advanced Privacy Settings in IE9, it always returns true. And there have been issues in the past\n    // with site-specific exceptions. Don't rely on it.\n    // try..catch because some in situations `document.cookie` is exposed but throws a\n    // SecurityError if you try to access it; e.g. documents created from data URIs\n    // or in sandboxed iframes (depending on flags/context)\n    try {\n        // Create cookie\n        d.cookie = 'cookietest=1; SameSite=Strict;';\n        var result = d.cookie.indexOf('cookietest=') !== -1;\n        // Delete cookie\n        d.cookie = 'cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT';\n        return result;\n    }\n    catch (e) {\n        return false;\n    }\n}\n\n/**\n * Only single element selector are supported (no operators like space, +, >, etc).\n * `embed` and `position: fixed;` will be considered as blocked anyway because it always has no offsetParent.\n * Avoid `iframe` and anything with `[src=]` because they produce excess HTTP requests.\n *\n * The \"inappropriate\" selectors are obfuscated. See https://github.com/fingerprintjs/fingerprintjs/issues/734.\n * A function is used instead of a plain object to help tree-shaking.\n *\n * The function code is generated automatically. See docs/content_blockers.md to learn how to make the list.\n */\nfunction getFilters() {\n    var fromB64 = atob; // Just for better minification\n    return {\n        abpIndo: [\n            '#Iklan-Melayang',\n            '#Kolom-Iklan-728',\n            '#SidebarIklan-wrapper',\n            '[title=\"ALIENBOLA\" i]',\n            fromB64('I0JveC1CYW5uZXItYWRz'),\n        ],\n        abpvn: ['.quangcao', '#mobileCatfish', fromB64('LmNsb3NlLWFkcw=='), '[id^=\"bn_bottom_fixed_\"]', '#pmadv'],\n        adBlockFinland: [\n            '.mainostila',\n            fromB64('LnNwb25zb3JpdA=='),\n            '.ylamainos',\n            fromB64('YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd'),\n        ],\n        adBlockPersian: [\n            '#navbar_notice_50',\n            '.kadr',\n            'TABLE[width=\"140px\"]',\n            '#divAgahi',\n            fromB64('YVtocmVmXj0iaHR0cDovL2cxLnYuZndtcm0ubmV0L2FkLyJd'),\n        ],\n        adBlockWarningRemoval: [\n            '#adblock-honeypot',\n            '.adblocker-root',\n            '.wp_adblock_detect',\n            fromB64('LmhlYWRlci1ibG9ja2VkLWFk'),\n            fromB64('I2FkX2Jsb2NrZXI='),\n        ],\n        adGuardAnnoyances: [\n            '.hs-sosyal',\n            '#cookieconsentdiv',\n            'div[class^=\"app_gdpr\"]',\n            '.as-oil',\n            '[data-cypress=\"soft-push-notification-modal\"]',\n        ],\n        adGuardBase: [\n            '.BetterJsPopOverlay',\n            fromB64('I2FkXzMwMFgyNTA='),\n            fromB64('I2Jhbm5lcmZsb2F0MjI='),\n            fromB64('I2NhbXBhaWduLWJhbm5lcg=='),\n            fromB64('I0FkLUNvbnRlbnQ='),\n        ],\n        adGuardChinese: [\n            fromB64('LlppX2FkX2FfSA=='),\n            fromB64('YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd'),\n            '#widget-quan',\n            fromB64('YVtocmVmKj0iLzg0OTkyMDIwLnh5eiJd'),\n            fromB64('YVtocmVmKj0iLjE5NTZobC5jb20vIl0='),\n        ],\n        adGuardFrench: [\n            '#pavePub',\n            fromB64('LmFkLWRlc2t0b3AtcmVjdGFuZ2xl'),\n            '.mobile_adhesion',\n            '.widgetadv',\n            fromB64('LmFkc19iYW4='),\n        ],\n        adGuardGerman: ['aside[data-portal-id=\"leaderboard\"]'],\n        adGuardJapanese: [\n            '#kauli_yad_1',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0='),\n            fromB64('Ll9wb3BJbl9pbmZpbml0ZV9hZA=='),\n            fromB64('LmFkZ29vZ2xl'),\n            fromB64('Ll9faXNib29zdFJldHVybkFk'),\n        ],\n        adGuardMobile: [\n            fromB64('YW1wLWF1dG8tYWRz'),\n            fromB64('LmFtcF9hZA=='),\n            'amp-embed[type=\"24smi\"]',\n            '#mgid_iframe1',\n            fromB64('I2FkX2ludmlld19hcmVh'),\n        ],\n        adGuardRussian: [\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0='),\n            fromB64('LnJlY2xhbWE='),\n            'div[id^=\"smi2adblock\"]',\n            fromB64('ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd'),\n            '#psyduckpockeball',\n        ],\n        adGuardSocial: [\n            fromB64('YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0='),\n            fromB64('YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0='),\n            '.etsy-tweet',\n            '#inlineShare',\n            '.popup-social',\n        ],\n        adGuardSpanishPortuguese: ['#barraPublicidade', '#Publicidade', '#publiEspecial', '#queTooltip', '.cnt-publi'],\n        adGuardTrackingProtection: [\n            '#qoo-counter',\n            fromB64('YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=='),\n            '#top100counter',\n        ],\n        adGuardTurkish: [\n            '#backkapat',\n            fromB64('I3Jla2xhbWk='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0='),\n            fromB64('YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ=='),\n        ],\n        bulgarian: [fromB64('dGQjZnJlZW5ldF90YWJsZV9hZHM='), '#ea_intext_div', '.lapni-pop-over', '#xenium_hot_offers'],\n        easyList: [\n            '.yb-floorad',\n            fromB64('LndpZGdldF9wb19hZHNfd2lkZ2V0'),\n            fromB64('LnRyYWZmaWNqdW5reS1hZA=='),\n            '.textad_headline',\n            fromB64('LnNwb25zb3JlZC10ZXh0LWxpbmtz'),\n        ],\n        easyListChina: [\n            fromB64('LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=='),\n            fromB64('LmZyb250cGFnZUFkdk0='),\n            '#taotaole',\n            '#aafoot.top_box',\n            '.cfa_popup',\n        ],\n        easyListCookie: [\n            '.ezmob-footer',\n            '.cc-CookieWarning',\n            '[data-cookie-number]',\n            fromB64('LmF3LWNvb2tpZS1iYW5uZXI='),\n            '.sygnal24-gdpr-modal-wrap',\n        ],\n        easyListCzechSlovak: [\n            '#onlajny-stickers',\n            fromB64('I3Jla2xhbW5pLWJveA=='),\n            fromB64('LnJla2xhbWEtbWVnYWJvYXJk'),\n            '.sklik',\n            fromB64('W2lkXj0ic2tsaWtSZWtsYW1hIl0='),\n        ],\n        easyListDutch: [\n            fromB64('I2FkdmVydGVudGll'),\n            fromB64('I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=='),\n            '.adstekst',\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0='),\n            '#semilo-lrectangle',\n        ],\n        easyListGermany: [\n            '#SSpotIMPopSlider',\n            fromB64('LnNwb25zb3JsaW5rZ3J1ZW4='),\n            fromB64('I3dlcmJ1bmdza3k='),\n            fromB64('I3Jla2xhbWUtcmVjaHRzLW1pdHRl'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9iZDc0Mi5jb20vIl0='),\n        ],\n        easyListItaly: [\n            fromB64('LmJveF9hZHZfYW5udW5jaQ=='),\n            '.sb-box-pubbliredazionale',\n            fromB64('YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ=='),\n        ],\n        easyListLithuania: [\n            fromB64('LnJla2xhbW9zX3RhcnBhcw=='),\n            fromB64('LnJla2xhbW9zX251b3JvZG9z'),\n            fromB64('aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd'),\n            fromB64('aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd'),\n            fromB64('aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd'),\n        ],\n        estonian: [fromB64('QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==')],\n        fanboyAnnoyances: ['#ac-lre-player', '.navigate-to-top', '#subscribe_popup', '.newsletter_holder', '#back-top'],\n        fanboyAntiFacebook: ['.util-bar-module-firefly-visible'],\n        fanboyEnhancedTrackers: [\n            '.open.pushModal',\n            '#issuem-leaky-paywall-articles-zero-remaining-nag',\n            '#sovrn_container',\n            'div[class$=\"-hide\"][zoompage-fontsize][style=\"display: block;\"]',\n            '.BlockNag__Card',\n        ],\n        fanboySocial: ['#FollowUs', '#meteored_share', '#social_follow', '.article-sharer', '.community__social-desc'],\n        frellwitSwedish: [\n            fromB64('YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=='),\n            fromB64('YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=='),\n            'article.category-samarbete',\n            fromB64('ZGl2LmhvbGlkQWRz'),\n            'ul.adsmodern',\n        ],\n        greekAdBlock: [\n            fromB64('QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd'),\n            fromB64('QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=='),\n            fromB64('QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd'),\n            'DIV.agores300',\n            'TABLE.advright',\n        ],\n        hungarian: [\n            '#cemp_doboz',\n            '.optimonk-iframe-container',\n            fromB64('LmFkX19tYWlu'),\n            fromB64('W2NsYXNzKj0iR29vZ2xlQWRzIl0='),\n            '#hirdetesek_box',\n        ],\n        iDontCareAboutCookies: [\n            '.alert-info[data-block-track*=\"CookieNotice\"]',\n            '.ModuleTemplateCookieIndicator',\n            '.o--cookies--container',\n            '#cookies-policy-sticky',\n            '#stickyCookieBar',\n        ],\n        icelandicAbp: [fromB64('QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==')],\n        latvian: [\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0O' +\n                'iA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0='),\n            fromB64('YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6I' +\n                'DMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ=='),\n        ],\n        listKr: [\n            fromB64('YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0='),\n            fromB64('I2xpdmVyZUFkV3JhcHBlcg=='),\n            fromB64('YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=='),\n            fromB64('aW5zLmZhc3R2aWV3LWFk'),\n            '.revenue_unit_item.dable',\n        ],\n        listeAr: [\n            fromB64('LmdlbWluaUxCMUFk'),\n            '.right-and-left-sponsers',\n            fromB64('YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=='),\n            fromB64('YVtocmVmKj0iYm9vcmFxLm9yZyJd'),\n            fromB64('YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd'),\n        ],\n        listeFr: [\n            fromB64('YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=='),\n            fromB64('I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=='),\n            fromB64('YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0='),\n            '.site-pub-interstitiel',\n            'div[id^=\"crt-\"][data-criteo-id]',\n        ],\n        officialPolish: [\n            '#ceneo-placeholder-ceneo-12',\n            fromB64('W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=='),\n            fromB64('YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=='),\n            fromB64('ZGl2I3NrYXBpZWNfYWQ='),\n        ],\n        ro: [\n            fromB64('YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd'),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0='),\n            fromB64('YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd'),\n            'a[href^=\"/url/\"]',\n        ],\n        ruAd: [\n            fromB64('YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd'),\n            fromB64('YVtocmVmKj0iLy91dGltZy5ydS8iXQ=='),\n            fromB64('YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0='),\n            '#pgeldiz',\n            '.yandex-rtb-block',\n        ],\n        thaiAds: [\n            'a[href*=macau-uta-popup]',\n            fromB64('I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=='),\n            fromB64('LmFkczMwMHM='),\n            '.bumq',\n            '.img-kosana',\n        ],\n        webAnnoyancesUltralist: [\n            '#mod-social-share-2',\n            '#social-tools',\n            fromB64('LmN0cGwtZnVsbGJhbm5lcg=='),\n            '.zergnet-recommend',\n            '.yt.btn-link.btn-md.btn',\n        ],\n    };\n}\n/**\n * The order of the returned array means nothing (it's always sorted alphabetically).\n *\n * Notice that the source is slightly unstable.\n * Safari provides a 2-taps way to disable all content blockers on a page temporarily.\n * Also content blockers can be disabled permanently for a domain, but it requires 4 taps.\n * So empty array shouldn't be treated as \"no blockers\", it should be treated as \"no signal\".\n * If you are a website owner, don't make your visitors want to disable content blockers.\n */\nfunction getDomBlockers(_a) {\n    var _b = _a === void 0 ? {} : _a, debug = _b.debug;\n    return __awaiter(this, void 0, void 0, function () {\n        var filters, filterNames, allSelectors, blockedSelectors, activeBlockers;\n        var _c;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (!isApplicable()) {\n                        return [2 /*return*/, undefined];\n                    }\n                    filters = getFilters();\n                    filterNames = Object.keys(filters);\n                    allSelectors = (_c = []).concat.apply(_c, filterNames.map(function (filterName) { return filters[filterName]; }));\n                    return [4 /*yield*/, getBlockedSelectors(allSelectors)];\n                case 1:\n                    blockedSelectors = _d.sent();\n                    if (debug) {\n                        printDebug(filters, blockedSelectors);\n                    }\n                    activeBlockers = filterNames.filter(function (filterName) {\n                        var selectors = filters[filterName];\n                        var blockedCount = countTruthy(selectors.map(function (selector) { return blockedSelectors[selector]; }));\n                        return blockedCount > selectors.length * 0.6;\n                    });\n                    activeBlockers.sort();\n                    return [2 /*return*/, activeBlockers];\n            }\n        });\n    });\n}\nfunction isApplicable() {\n    // Safari (desktop and mobile) and all Android browsers keep content blockers in both regular and private mode\n    return isWebKit() || isAndroid();\n}\nfunction getBlockedSelectors(selectors) {\n    var _a;\n    return __awaiter(this, void 0, void 0, function () {\n        var d, root, elements, blockedSelectors, i, element, holder, i;\n        return __generator(this, function (_b) {\n            switch (_b.label) {\n                case 0:\n                    d = document;\n                    root = d.createElement('div');\n                    elements = new Array(selectors.length);\n                    blockedSelectors = {} // Set() isn't used just in case somebody need older browser support\n                    ;\n                    forceShow(root);\n                    // First create all elements that can be blocked. If the DOM steps below are done in a single cycle,\n                    // browser will alternate tree modification and layout reading, that is very slow.\n                    for (i = 0; i < selectors.length; ++i) {\n                        element = selectorToElement(selectors[i]);\n                        if (element.tagName === 'DIALOG') {\n                            element.show();\n                        }\n                        holder = d.createElement('div') // Protects from unwanted effects of `+` and `~` selectors of filters\n                        ;\n                        forceShow(holder);\n                        holder.appendChild(element);\n                        root.appendChild(holder);\n                        elements[i] = element;\n                    }\n                    _b.label = 1;\n                case 1:\n                    if (!!d.body) return [3 /*break*/, 3];\n                    return [4 /*yield*/, wait(50)];\n                case 2:\n                    _b.sent();\n                    return [3 /*break*/, 1];\n                case 3:\n                    d.body.appendChild(root);\n                    try {\n                        // Then check which of the elements are blocked\n                        for (i = 0; i < selectors.length; ++i) {\n                            if (!elements[i].offsetParent) {\n                                blockedSelectors[selectors[i]] = true;\n                            }\n                        }\n                    }\n                    finally {\n                        // Then remove the elements\n                        (_a = root.parentNode) === null || _a === void 0 ? void 0 : _a.removeChild(root);\n                    }\n                    return [2 /*return*/, blockedSelectors];\n            }\n        });\n    });\n}\nfunction forceShow(element) {\n    element.style.setProperty('display', 'block', 'important');\n}\nfunction printDebug(filters, blockedSelectors) {\n    var message = 'DOM blockers debug:\\n```';\n    for (var _i = 0, _a = Object.keys(filters); _i < _a.length; _i++) {\n        var filterName = _a[_i];\n        message += \"\\n\".concat(filterName, \":\");\n        for (var _b = 0, _c = filters[filterName]; _b < _c.length; _b++) {\n            var selector = _c[_b];\n            message += \"\\n  \".concat(blockedSelectors[selector] ? '🚫' : '➡️', \" \").concat(selector);\n        }\n    }\n    // console.log is ok here because it's under a debug clause\n    // eslint-disable-next-line no-console\n    console.log(\"\".concat(message, \"\\n```\"));\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/color-gamut\n */\nfunction getColorGamut() {\n    // rec2020 includes p3 and p3 includes srgb\n    for (var _i = 0, _a = ['rec2020', 'p3', 'srgb']; _i < _a.length; _i++) {\n        var gamut = _a[_i];\n        if (matchMedia(\"(color-gamut: \".concat(gamut, \")\")).matches) {\n            return gamut;\n        }\n    }\n    return undefined;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/inverted-colors\n */\nfunction areColorsInverted() {\n    if (doesMatch$4('inverted')) {\n        return true;\n    }\n    if (doesMatch$4('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$4(value) {\n    return matchMedia(\"(inverted-colors: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/forced-colors\n */\nfunction areColorsForced() {\n    if (doesMatch$3('active')) {\n        return true;\n    }\n    if (doesMatch$3('none')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$3(value) {\n    return matchMedia(\"(forced-colors: \".concat(value, \")\")).matches;\n}\n\nvar maxValueToCheck = 100;\n/**\n * If the display is monochrome (e.g. black&white), the value will be ≥0 and will mean the number of bits per pixel.\n * If the display is not monochrome, the returned value will be 0.\n * If the browser doesn't support this feature, the returned value will be undefined.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/monochrome\n */\nfunction getMonochromeDepth() {\n    if (!matchMedia('(min-monochrome: 0)').matches) {\n        // The media feature isn't supported by the browser\n        return undefined;\n    }\n    // A variation of binary search algorithm can be used here.\n    // But since expected values are very small (≤10), there is no sense in adding the complexity.\n    for (var i = 0; i <= maxValueToCheck; ++i) {\n        if (matchMedia(\"(max-monochrome: \".concat(i, \")\")).matches) {\n            return i;\n        }\n    }\n    throw new Error('Too high value');\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#prefers-contrast\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-contrast\n */\nfunction getContrastPreference() {\n    if (doesMatch$2('no-preference')) {\n        return 0 /* ContrastPreference.None */;\n    }\n    // The sources contradict on the keywords. Probably 'high' and 'low' will never be implemented.\n    // Need to check it when all browsers implement the feature.\n    if (doesMatch$2('high') || doesMatch$2('more')) {\n        return 1 /* ContrastPreference.More */;\n    }\n    if (doesMatch$2('low') || doesMatch$2('less')) {\n        return -1 /* ContrastPreference.Less */;\n    }\n    if (doesMatch$2('forced')) {\n        return 10 /* ContrastPreference.ForcedColors */;\n    }\n    return undefined;\n}\nfunction doesMatch$2(value) {\n    return matchMedia(\"(prefers-contrast: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion\n */\nfunction isMotionReduced() {\n    if (doesMatch$1('reduce')) {\n        return true;\n    }\n    if (doesMatch$1('no-preference')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch$1(value) {\n    return matchMedia(\"(prefers-reduced-motion: \".concat(value, \")\")).matches;\n}\n\n/**\n * @see https://www.w3.org/TR/mediaqueries-5/#dynamic-range\n */\nfunction isHDR() {\n    if (doesMatch('high')) {\n        return true;\n    }\n    if (doesMatch('standard')) {\n        return false;\n    }\n    return undefined;\n}\nfunction doesMatch(value) {\n    return matchMedia(\"(dynamic-range: \".concat(value, \")\")).matches;\n}\n\nvar M = Math; // To reduce the minified code size\nvar fallbackFn = function () { return 0; };\n/**\n * @see https://gitlab.torproject.org/legacy/trac/-/issues/13018\n * @see https://bugzilla.mozilla.org/show_bug.cgi?id=531915\n */\nfunction getMathFingerprint() {\n    // Native operations\n    var acos = M.acos || fallbackFn;\n    var acosh = M.acosh || fallbackFn;\n    var asin = M.asin || fallbackFn;\n    var asinh = M.asinh || fallbackFn;\n    var atanh = M.atanh || fallbackFn;\n    var atan = M.atan || fallbackFn;\n    var sin = M.sin || fallbackFn;\n    var sinh = M.sinh || fallbackFn;\n    var cos = M.cos || fallbackFn;\n    var cosh = M.cosh || fallbackFn;\n    var tan = M.tan || fallbackFn;\n    var tanh = M.tanh || fallbackFn;\n    var exp = M.exp || fallbackFn;\n    var expm1 = M.expm1 || fallbackFn;\n    var log1p = M.log1p || fallbackFn;\n    // Operation polyfills\n    var powPI = function (value) { return M.pow(M.PI, value); };\n    var acoshPf = function (value) { return M.log(value + M.sqrt(value * value - 1)); };\n    var asinhPf = function (value) { return M.log(value + M.sqrt(value * value + 1)); };\n    var atanhPf = function (value) { return M.log((1 + value) / (1 - value)) / 2; };\n    var sinhPf = function (value) { return M.exp(value) - 1 / M.exp(value) / 2; };\n    var coshPf = function (value) { return (M.exp(value) + 1 / M.exp(value)) / 2; };\n    var expm1Pf = function (value) { return M.exp(value) - 1; };\n    var tanhPf = function (value) { return (M.exp(2 * value) - 1) / (M.exp(2 * value) + 1); };\n    var log1pPf = function (value) { return M.log(1 + value); };\n    // Note: constant values are empirical\n    return {\n        acos: acos(0.123124234234234242),\n        acosh: acosh(1e308),\n        acoshPf: acoshPf(1e154),\n        asin: asin(0.123124234234234242),\n        asinh: asinh(1),\n        asinhPf: asinhPf(1),\n        atanh: atanh(0.5),\n        atanhPf: atanhPf(0.5),\n        atan: atan(0.5),\n        sin: sin(-1e300),\n        sinh: sinh(1),\n        sinhPf: sinhPf(1),\n        cos: cos(10.000000000123),\n        cosh: cosh(1),\n        coshPf: coshPf(1),\n        tan: tan(-1e300),\n        tanh: tanh(1),\n        tanhPf: tanhPf(1),\n        exp: exp(1),\n        expm1: expm1(1),\n        expm1Pf: expm1Pf(1),\n        log1p: log1p(10),\n        log1pPf: log1pPf(10),\n        powPI: powPI(-100),\n    };\n}\n\n/**\n * We use m or w because these two characters take up the maximum width.\n * Also there are a couple of ligatures.\n */\nvar defaultText = 'mmMwWLliI0fiflO&1';\n/**\n * Settings of text blocks to measure. The keys are random but persistent words.\n */\nvar presets = {\n    /**\n     * The default font. User can change it in desktop Chrome, desktop Firefox, IE 11,\n     * Android Chrome (but only when the size is ≥ than the default) and Android Firefox.\n     */\n    default: [],\n    /** OS font on macOS. User can change its size and weight. Applies after Safari restart. */\n    apple: [{ font: '-apple-system-body' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    serif: [{ fontFamily: 'serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    sans: [{ fontFamily: 'sans-serif' }],\n    /** User can change it in desktop Chrome and desktop Firefox. */\n    mono: [{ fontFamily: 'monospace' }],\n    /**\n     * Check the smallest allowed font size. User can change it in desktop Chrome, desktop Firefox and desktop Safari.\n     * The height can be 0 in Chrome on a retina display.\n     */\n    min: [{ fontSize: '1px' }],\n    /** Tells one OS from another in desktop Chrome. */\n    system: [{ fontFamily: 'system-ui' }],\n};\n/**\n * The result is a dictionary of the width of the text samples.\n * Heights aren't included because they give no extra entropy and are unstable.\n *\n * The result is very stable in IE 11, Edge 18 and Safari 14.\n * The result changes when the OS pixel density changes in Chromium 87. The real pixel density is required to solve,\n * but seems like it's impossible: https://stackoverflow.com/q/1713771/1118709.\n * The \"min\" and the \"mono\" (only on Windows) value may change when the page is zoomed in Firefox 87.\n */\nfunction getFontPreferences() {\n    return withNaturalFonts(function (document, container) {\n        var elements = {};\n        var sizes = {};\n        // First create all elements to measure. If the DOM steps below are done in a single cycle,\n        // browser will alternate tree modification and layout reading, that is very slow.\n        for (var _i = 0, _a = Object.keys(presets); _i < _a.length; _i++) {\n            var key = _a[_i];\n            var _b = presets[key], _c = _b[0], style = _c === void 0 ? {} : _c, _d = _b[1], text = _d === void 0 ? defaultText : _d;\n            var element = document.createElement('span');\n            element.textContent = text;\n            element.style.whiteSpace = 'nowrap';\n            for (var _e = 0, _f = Object.keys(style); _e < _f.length; _e++) {\n                var name_1 = _f[_e];\n                var value = style[name_1];\n                if (value !== undefined) {\n                    element.style[name_1] = value;\n                }\n            }\n            elements[key] = element;\n            container.appendChild(document.createElement('br'));\n            container.appendChild(element);\n        }\n        // Then measure the created elements\n        for (var _g = 0, _h = Object.keys(presets); _g < _h.length; _g++) {\n            var key = _h[_g];\n            sizes[key] = elements[key].getBoundingClientRect().width;\n        }\n        return sizes;\n    });\n}\n/**\n * Creates a DOM environment that provides the most natural font available, including Android OS font.\n * Measurements of the elements are zoom-independent.\n * Don't put a content to measure inside an absolutely positioned element.\n */\nfunction withNaturalFonts(action, containerWidthPx) {\n    if (containerWidthPx === void 0) { containerWidthPx = 4000; }\n    /*\n     * Requirements for Android Chrome to apply the system font size to a text inside an iframe:\n     * - The iframe mustn't have a `display: none;` style;\n     * - The text mustn't be positioned absolutely;\n     * - The text block must be wide enough.\n     *   2560px on some devices in portrait orientation for the biggest font size option (32px);\n     * - There must be much enough text to form a few lines (I don't know the exact numbers);\n     * - The text must have the `text-size-adjust: none` style. Otherwise the text will scale in \"Desktop site\" mode;\n     *\n     * Requirements for Android Firefox to apply the system font size to a text inside an iframe:\n     * - The iframe document must have a header: `<meta name=\"viewport\" content=\"width=device-width, initial-scale=1\" />`.\n     *   The only way to set it is to use the `srcdoc` attribute of the iframe;\n     * - The iframe content must get loaded before adding extra content with JavaScript;\n     *\n     * https://example.com as the iframe target always inherits Android font settings so it can be used as a reference.\n     *\n     * Observations on how page zoom affects the measurements:\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - macOS Safari 11.1, 12.1, 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - macOS Safari 14.0: offsetWidth = 5% fluctuation;\n     * - macOS Safari 14.0: getBoundingClientRect = 5% fluctuation;\n     * - iOS Safari 9, 10, 11.0, 12.0: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - iOS Safari 13.1, 14.0: zoom reset + offsetWidth = 100% reliable;\n     * - iOS Safari 13.1, 14.0: zoom reset + getBoundingClientRect = 100% reliable;\n     * - iOS Safari 14.0: offsetWidth = 100% reliable;\n     * - iOS Safari 14.0: getBoundingClientRect = 100% reliable;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + offsetWidth = 1px fluctuation;\n     * - Chrome 42, 65, 80, 87: zoom 1/devicePixelRatio + getBoundingClientRect = 100% reliable;\n     * - Chrome 87: offsetWidth = 1px fluctuation;\n     * - Chrome 87: getBoundingClientRect = 0.7px fluctuation;\n     * - Firefox 48, 51: offsetWidth = 10% fluctuation;\n     * - Firefox 48, 51: getBoundingClientRect = 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: offsetWidth = width 100% reliable, height 10% fluctuation;\n     * - Firefox 52, 53, 57, 62, 66, 67, 68, 71, 75, 80, 84: getBoundingClientRect = width 100% reliable, height 10%\n     *   fluctuation;\n     * - Android Chrome 86: haven't found a way to zoom a page (pinch doesn't change layout);\n     * - Android Firefox 84: font size in accessibility settings changes all the CSS sizes, but offsetWidth and\n     *   getBoundingClientRect keep measuring with regular units, so the size reflects the font size setting and doesn't\n     *   fluctuate;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: zoom 1/devicePixelRatio + getBoundingClientRect = reflects the zoom level;\n     * - IE 11, Edge 18: offsetWidth = 100% reliable;\n     * - IE 11, Edge 18: getBoundingClientRect = 100% reliable;\n     */\n    return withIframe(function (_, iframeWindow) {\n        var iframeDocument = iframeWindow.document;\n        var iframeBody = iframeDocument.body;\n        var bodyStyle = iframeBody.style;\n        bodyStyle.width = \"\".concat(containerWidthPx, \"px\");\n        bodyStyle.webkitTextSizeAdjust = bodyStyle.textSizeAdjust = 'none';\n        // See the big comment above\n        if (isChromium()) {\n            iframeBody.style.zoom = \"\".concat(1 / iframeWindow.devicePixelRatio);\n        }\n        else if (isWebKit()) {\n            iframeBody.style.zoom = 'reset';\n        }\n        // See the big comment above\n        var linesOfText = iframeDocument.createElement('div');\n        linesOfText.textContent = __spreadArray([], Array((containerWidthPx / 20) << 0), true).map(function () { return 'word'; }).join(' ');\n        iframeBody.appendChild(linesOfText);\n        return action(iframeDocument, iframeBody);\n    }, '<!doctype html><html><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">');\n}\n\n/**\n * @see Credits: https://stackoverflow.com/a/49267844\n */\nfunction getVideoCard() {\n    var _a;\n    var canvas = document.createElement('canvas');\n    var gl = (_a = canvas.getContext('webgl')) !== null && _a !== void 0 ? _a : canvas.getContext('experimental-webgl');\n    if (gl && 'getExtension' in gl) {\n        var debugInfo = gl.getExtension('WEBGL_debug_renderer_info');\n        if (debugInfo) {\n            return {\n                vendor: (gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL) || '').toString(),\n                renderer: (gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) || '').toString(),\n            };\n        }\n    }\n    return undefined;\n}\n\nfunction isPdfViewerEnabled() {\n    return navigator.pdfViewerEnabled;\n}\n\n/**\n * Unlike most other architectures, on x86/x86-64 when floating-point instructions\n * have no NaN arguments, but produce NaN output, the output NaN has sign bit set.\n * We use it to distinguish x86/x86-64 from other architectures, by doing subtraction\n * of two infinities (must produce NaN per IEEE 754 standard).\n *\n * See https://codebrowser.bddppq.com/pytorch/pytorch/third_party/XNNPACK/src/init.c.html#79\n */\nfunction getArchitecture() {\n    var f = new Float32Array(1);\n    var u8 = new Uint8Array(f.buffer);\n    f[0] = Infinity;\n    f[0] = f[0] - f[0];\n    return u8[3];\n}\n\n/**\n * The list of entropy sources used to make visitor identifiers.\n *\n * This value isn't restricted by Semantic Versioning, i.e. it may be changed without bumping minor or major version of\n * this package.\n *\n * Note: Rollup and Webpack are smart enough to remove unused properties of this object during tree-shaking, so there is\n * no need to export the sources individually.\n */\nvar sources = {\n    // READ FIRST:\n    // See https://github.com/fingerprintjs/fingerprintjs/blob/master/contributing.md#how-to-make-an-entropy-source\n    // to learn how entropy source works and how to make your own.\n    // The sources run in this exact order.\n    // The asynchronous sources are at the start to run in parallel with other sources.\n    fonts: getFonts,\n    domBlockers: getDomBlockers,\n    fontPreferences: getFontPreferences,\n    audio: getAudioFingerprint,\n    screenFrame: getRoundedScreenFrame,\n    osCpu: getOsCpu,\n    languages: getLanguages,\n    colorDepth: getColorDepth,\n    deviceMemory: getDeviceMemory,\n    screenResolution: getScreenResolution,\n    hardwareConcurrency: getHardwareConcurrency,\n    timezone: getTimezone,\n    sessionStorage: getSessionStorage,\n    localStorage: getLocalStorage,\n    indexedDB: getIndexedDB,\n    openDatabase: getOpenDatabase,\n    cpuClass: getCpuClass,\n    platform: getPlatform,\n    plugins: getPlugins,\n    canvas: getCanvasFingerprint,\n    touchSupport: getTouchSupport,\n    vendor: getVendor,\n    vendorFlavors: getVendorFlavors,\n    cookiesEnabled: areCookiesEnabled,\n    colorGamut: getColorGamut,\n    invertedColors: areColorsInverted,\n    forcedColors: areColorsForced,\n    monochrome: getMonochromeDepth,\n    contrast: getContrastPreference,\n    reducedMotion: isMotionReduced,\n    hdr: isHDR,\n    math: getMathFingerprint,\n    videoCard: getVideoCard,\n    pdfViewerEnabled: isPdfViewerEnabled,\n    architecture: getArchitecture,\n};\n/**\n * Loads the built-in entropy sources.\n * Returns a function that collects the entropy components to make the visitor identifier.\n */\nfunction loadBuiltinSources(options) {\n    return loadSources(sources, options, []);\n}\n\nvar commentTemplate = '$ if upgrade to Pro: https://fpjs.dev/pro';\nfunction getConfidence(components) {\n    var openConfidenceScore = getOpenConfidenceScore(components);\n    var proConfidenceScore = deriveProConfidenceScore(openConfidenceScore);\n    return { score: openConfidenceScore, comment: commentTemplate.replace(/\\$/g, \"\".concat(proConfidenceScore)) };\n}\nfunction getOpenConfidenceScore(components) {\n    // In order to calculate the true probability of the visitor identifier being correct, we need to know the number of\n    // website visitors (the higher the number, the less the probability because the fingerprint entropy is limited).\n    // JS agent doesn't know the number of visitors, so we can only do an approximate assessment.\n    if (isAndroid()) {\n        return 0.4;\n    }\n    // Safari (mobile and desktop)\n    if (isWebKit()) {\n        return isDesktopSafari() ? 0.5 : 0.3;\n    }\n    var platform = components.platform.value || '';\n    // Windows\n    if (/^Win/.test(platform)) {\n        // The score is greater than on macOS because of the higher variety of devices running Windows.\n        // Chrome provides more entropy than Firefox according too\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Windows%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.6;\n    }\n    // macOS\n    if (/^Mac/.test(platform)) {\n        // Chrome provides more entropy than Safari and Safari provides more entropy than Firefox.\n        // Chrome is more popular than Safari and Safari is more popular than Firefox according to\n        // https://netmarketshare.com/browser-market-share.aspx?options=%7B%22filter%22%3A%7B%22%24and%22%3A%5B%7B%22platform%22%3A%7B%22%24in%22%3A%5B%22Mac%20OS%22%5D%7D%7D%5D%7D%2C%22dateLabel%22%3A%22Trend%22%2C%22attributes%22%3A%22share%22%2C%22group%22%3A%22browser%22%2C%22sort%22%3A%7B%22share%22%3A-1%7D%2C%22id%22%3A%22browsersDesktop%22%2C%22dateInterval%22%3A%22Monthly%22%2C%22dateStart%22%3A%222019-11%22%2C%22dateEnd%22%3A%222020-10%22%2C%22segments%22%3A%22-1000%22%7D\n        // So we assign the same score to them.\n        return 0.5;\n    }\n    // Another platform, e.g. a desktop Linux. It's rare, so it should be pretty unique.\n    return 0.7;\n}\nfunction deriveProConfidenceScore(openConfidenceScore) {\n    return round(0.99 + 0.01 * openConfidenceScore, 0.0001);\n}\n\nfunction componentsToCanonicalString(components) {\n    var result = '';\n    for (var _i = 0, _a = Object.keys(components).sort(); _i < _a.length; _i++) {\n        var componentKey = _a[_i];\n        var component = components[componentKey];\n        var value = component.error ? 'error' : JSON.stringify(component.value);\n        result += \"\".concat(result ? '|' : '').concat(componentKey.replace(/([:|\\\\])/g, '\\\\$1'), \":\").concat(value);\n    }\n    return result;\n}\nfunction componentsToDebugString(components) {\n    return JSON.stringify(components, function (_key, value) {\n        if (value instanceof Error) {\n            return errorToObject(value);\n        }\n        return value;\n    }, 2);\n}\nfunction hashComponents(components) {\n    return x64hash128(componentsToCanonicalString(components));\n}\n/**\n * Makes a GetResult implementation that calculates the visitor id hash on demand.\n * Designed for optimisation.\n */\nfunction makeLazyGetResult(components) {\n    var visitorIdCache;\n    // This function runs very fast, so there is no need to make it lazy\n    var confidence = getConfidence(components);\n    // A plain class isn't used because its getters and setters aren't enumerable.\n    return {\n        get visitorId() {\n            if (visitorIdCache === undefined) {\n                visitorIdCache = hashComponents(this.components);\n            }\n            return visitorIdCache;\n        },\n        set visitorId(visitorId) {\n            visitorIdCache = visitorId;\n        },\n        confidence: confidence,\n        components: components,\n        version: version,\n    };\n}\n/**\n * A delay is required to ensure consistent entropy components.\n * See https://github.com/fingerprintjs/fingerprintjs/issues/254\n * and https://github.com/fingerprintjs/fingerprintjs/issues/307\n * and https://github.com/fingerprintjs/fingerprintjs/commit/945633e7c5f67ae38eb0fea37349712f0e669b18\n */\nfunction prepareForSources(delayFallback) {\n    if (delayFallback === void 0) { delayFallback = 50; }\n    // A proper deadline is unknown. Let it be twice the fallback timeout so that both cases have the same average time.\n    return requestIdleCallbackIfAvailable(delayFallback, delayFallback * 2);\n}\n/**\n * The function isn't exported from the index file to not allow to call it without `load()`.\n * The hiding gives more freedom for future non-breaking updates.\n *\n * A factory function is used instead of a class to shorten the attribute names in the minified code.\n * Native private class fields could've been used, but TypeScript doesn't allow them with `\"target\": \"es5\"`.\n */\nfunction makeAgent(getComponents, debug) {\n    var creationTime = Date.now();\n    return {\n        get: function (options) {\n            return __awaiter(this, void 0, void 0, function () {\n                var startTime, components, result;\n                return __generator(this, function (_a) {\n                    switch (_a.label) {\n                        case 0:\n                            startTime = Date.now();\n                            return [4 /*yield*/, getComponents()];\n                        case 1:\n                            components = _a.sent();\n                            result = makeLazyGetResult(components);\n                            if (debug || (options === null || options === void 0 ? void 0 : options.debug)) {\n                                // console.log is ok here because it's under a debug clause\n                                // eslint-disable-next-line no-console\n                                console.log(\"Copy the text below to get the debug data:\\n\\n```\\nversion: \".concat(result.version, \"\\nuserAgent: \").concat(navigator.userAgent, \"\\ntimeBetweenLoadAndGet: \").concat(startTime - creationTime, \"\\nvisitorId: \").concat(result.visitorId, \"\\ncomponents: \").concat(componentsToDebugString(components), \"\\n```\"));\n                            }\n                            return [2 /*return*/, result];\n                    }\n                });\n            });\n        },\n    };\n}\n/**\n * Sends an unpersonalized AJAX request to collect installation statistics\n */\nfunction monitor() {\n    // The FingerprintJS CDN (https://github.com/fingerprintjs/cdn) replaces `window.__fpjs_d_m` with `true`\n    if (window.__fpjs_d_m || Math.random() >= 0.001) {\n        return;\n    }\n    try {\n        var request = new XMLHttpRequest();\n        request.open('get', \"https://m1.openfpcdn.io/fingerprintjs/v\".concat(version, \"/npm-monitoring\"), true);\n        request.send();\n    }\n    catch (error) {\n        // console.error is ok here because it's an unexpected error handler\n        // eslint-disable-next-line no-console\n        console.error(error);\n    }\n}\n/**\n * Builds an instance of Agent and waits a delay required for a proper operation.\n */\nfunction load(_a) {\n    var _b = _a === void 0 ? {} : _a, delayFallback = _b.delayFallback, debug = _b.debug, _c = _b.monitoring, monitoring = _c === void 0 ? true : _c;\n    return __awaiter(this, void 0, void 0, function () {\n        var getComponents;\n        return __generator(this, function (_d) {\n            switch (_d.label) {\n                case 0:\n                    if (monitoring) {\n                        monitor();\n                    }\n                    return [4 /*yield*/, prepareForSources(delayFallback)];\n                case 1:\n                    _d.sent();\n                    getComponents = loadBuiltinSources({ debug: debug });\n                    return [2 /*return*/, makeAgent(getComponents, debug)];\n            }\n        });\n    });\n}\n\n// The default export is a syntax sugar (`import * as FP from '...' → import FP from '...'`).\n// It should contain all the public exported values.\nvar index = { load: load, hashComponents: hashComponents, componentsToDebugString: componentsToDebugString };\n// The exports below are for private usage. They may change unexpectedly. Use them at your own risk.\n/** Not documented, out of Semantic Versioning, usage is at your own risk */\nvar murmurX64Hash128 = x64hash128;\n\nexport { componentsToDebugString, index as default, getFullscreenElement, getScreenFrame, hashComponents, isAndroid, isChromium, isDesktopSafari, isEdgeHTML, isGecko, isTrident, isWebKit, load, loadSources, murmurX64Hash128, prepareForSources, sources, transformSource, withIframe };\n"], "mappings": ";;;AA+BO,IAAI,WAAW,WAAW;AAC/B,aAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AA0EO,SAAS,UAAU,SAAS,YAAY,GAAG,WAAW;AAC3D,WAAS,MAAM,OAAO;AAAE,WAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,SAAS;AAAE,cAAQ,KAAK;AAAA,IAAG,CAAC;AAAA,EAAG;AAC3G,SAAO,KAAK,MAAM,IAAI,UAAU,SAAU,SAAS,QAAQ;AACvD,aAAS,UAAU,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,KAAK,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC1F,aAAS,SAAS,OAAO;AAAE,UAAI;AAAE,aAAK,UAAU,OAAO,EAAE,KAAK,CAAC;AAAA,MAAG,SAAS,GAAG;AAAE,eAAO,CAAC;AAAA,MAAG;AAAA,IAAE;AAC7F,aAAS,KAAK,QAAQ;AAAE,aAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,KAAK,WAAW,QAAQ;AAAA,IAAG;AAC7G,UAAM,YAAY,UAAU,MAAM,SAAS,cAAc,CAAC,CAAC,GAAG,KAAK,CAAC;AAAA,EACxE,CAAC;AACH;AAEO,SAAS,YAAY,SAAS,MAAM;AACzC,MAAI,IAAI,EAAE,OAAO,GAAG,MAAM,WAAW;AAAE,QAAI,EAAE,CAAC,IAAI,EAAG,OAAM,EAAE,CAAC;AAAG,WAAO,EAAE,CAAC;AAAA,EAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,QAAQ,OAAO,aAAa,aAAa,WAAW,QAAQ,SAAS;AAC/L,SAAO,EAAE,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,IAAI,KAAK,CAAC,GAAG,EAAE,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,WAAW,eAAe,EAAE,OAAO,QAAQ,IAAI,WAAW;AAAE,WAAO;AAAA,EAAM,IAAI;AAC1J,WAAS,KAAK,GAAG;AAAE,WAAO,SAAU,GAAG;AAAE,aAAO,KAAK,CAAC,GAAG,CAAC,CAAC;AAAA,IAAG;AAAA,EAAG;AACjE,WAAS,KAAK,IAAI;AACd,QAAI,EAAG,OAAM,IAAI,UAAU,iCAAiC;AAC5D,WAAO,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,EAAG,KAAI;AAC1C,UAAI,IAAI,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,IAAI,EAAE,QAAQ,IAAI,GAAG,CAAC,IAAI,EAAE,OAAO,OAAO,IAAI,EAAE,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,KAAM,QAAO;AAC3J,UAAI,IAAI,GAAG,EAAG,MAAK,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,KAAK;AACtC,cAAQ,GAAG,CAAC,GAAG;AAAA,QACX,KAAK;AAAA,QAAG,KAAK;AAAG,cAAI;AAAI;AAAA,QACxB,KAAK;AAAG,YAAE;AAAS,iBAAO,EAAE,OAAO,GAAG,CAAC,GAAG,MAAM,MAAM;AAAA,QACtD,KAAK;AAAG,YAAE;AAAS,cAAI,GAAG,CAAC;AAAG,eAAK,CAAC,CAAC;AAAG;AAAA,QACxC,KAAK;AAAG,eAAK,EAAE,IAAI,IAAI;AAAG,YAAE,KAAK,IAAI;AAAG;AAAA,QACxC;AACI,cAAI,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,SAAS,KAAK,EAAE,EAAE,SAAS,CAAC,OAAO,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,IAAI;AAAE,gBAAI;AAAG;AAAA,UAAU;AAC3G,cAAI,GAAG,CAAC,MAAM,MAAM,CAAC,KAAM,GAAG,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,EAAE,CAAC,IAAK;AAAE,cAAE,QAAQ,GAAG,CAAC;AAAG;AAAA,UAAO;AACrF,cAAI,GAAG,CAAC,MAAM,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,gBAAI;AAAI;AAAA,UAAO;AACpE,cAAI,KAAK,EAAE,QAAQ,EAAE,CAAC,GAAG;AAAE,cAAE,QAAQ,EAAE,CAAC;AAAG,cAAE,IAAI,KAAK,EAAE;AAAG;AAAA,UAAO;AAClE,cAAI,EAAE,CAAC,EAAG,GAAE,IAAI,IAAI;AACpB,YAAE,KAAK,IAAI;AAAG;AAAA,MACtB;AACA,WAAK,KAAK,KAAK,SAAS,CAAC;AAAA,IAC7B,SAAS,GAAG;AAAE,WAAK,CAAC,GAAG,CAAC;AAAG,UAAI;AAAA,IAAG,UAAE;AAAU,UAAI,IAAI;AAAA,IAAG;AACzD,QAAI,GAAG,CAAC,IAAI,EAAG,OAAM,GAAG,CAAC;AAAG,WAAO,EAAE,OAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,QAAQ,MAAM,KAAK;AAAA,EACnF;AACF;AA+DO,SAAS,cAAc,IAAI,MAAM,MAAM;AAC5C,MAAI,QAAQ,UAAU,WAAW,EAAG,UAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,IAAI,GAAG,KAAK;AACjF,QAAI,MAAM,EAAE,KAAK,OAAO;AACpB,UAAI,CAAC,GAAI,MAAK,MAAM,UAAU,MAAM,KAAK,MAAM,GAAG,CAAC;AACnD,SAAG,CAAC,IAAI,KAAK,CAAC;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,GAAG,OAAO,MAAM,MAAM,UAAU,MAAM,KAAK,IAAI,CAAC;AACzD;;;ACnNA,IAAI,UAAU;AAEd,SAAS,KAAK,YAAY,aAAa;AACnC,SAAO,IAAI,QAAQ,SAAU,SAAS;AAAE,WAAO,WAAW,SAAS,YAAY,WAAW;AAAA,EAAG,CAAC;AAClG;AACA,SAAS,+BAA+B,iBAAiB,iBAAiB;AACtE,MAAI,oBAAoB,QAAQ;AAAE,sBAAkB;AAAA,EAAU;AAC9D,MAAI,sBAAsB,OAAO;AACjC,MAAI,qBAAqB;AAIrB,WAAO,IAAI,QAAQ,SAAU,SAAS;AAAE,aAAO,oBAAoB,KAAK,QAAQ,WAAY;AAAE,eAAO,QAAQ;AAAA,MAAG,GAAG,EAAE,SAAS,gBAAgB,CAAC;AAAA,IAAG,CAAC;AAAA,EACvJ,OACK;AACD,WAAO,KAAK,KAAK,IAAI,iBAAiB,eAAe,CAAC;AAAA,EAC1D;AACJ;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,CAAC,CAAC,SAAS,OAAO,MAAM,SAAS;AAC5C;AAcA,SAAS,aAAa,QAAQ,UAAU;AACpC,MAAI;AACA,QAAI,gBAAgB,OAAO;AAC3B,QAAI,UAAU,aAAa,GAAG;AAC1B,oBAAc,KAAK,SAAU,QAAQ;AAAE,eAAO,SAAS,MAAM,MAAM;AAAA,MAAG,GAAG,SAAU,OAAO;AAAE,eAAO,SAAS,OAAO,KAAK;AAAA,MAAG,CAAC;AAAA,IAChI,OACK;AACD,eAAS,MAAM,aAAa;AAAA,IAChC;AAAA,EACJ,SACO,OAAO;AACV,aAAS,OAAO,KAAK;AAAA,EACzB;AACJ;AAMA,SAAS,cAAc,OAAO,UAAU,qBAAqB;AACzD,MAAI,wBAAwB,QAAQ;AAAE,0BAAsB;AAAA,EAAI;AAChE,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,SAAS,qBAAqB,GAAG;AACrC,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,oBAAU,MAAM,MAAM,MAAM;AAC5B,gCAAsB,KAAK,IAAI;AAC/B,cAAI;AACJ,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,EAAE,IAAI,MAAM,QAAS,QAAO,CAAC,GAAa,CAAC;AAC/C,kBAAQ,CAAC,IAAI,SAAS,MAAM,CAAC,GAAG,CAAC;AACjC,gBAAM,KAAK,IAAI;AACf,cAAI,EAAE,OAAO,sBAAsB,qBAAsB,QAAO,CAAC,GAAa,CAAC;AAC/E,gCAAsB;AAEtB,iBAAO,CAAC,GAAa,KAAK,CAAC,CAAC;AAAA,QAChC,KAAK;AAED,aAAG,KAAK;AACR,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,YAAE;AACF,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAG,iBAAO,CAAC,GAAc,OAAO;AAAA,MACzC;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAOA,SAAS,kCAAkC,SAAS;AAChD,UAAQ,KAAK,QAAW,WAAY;AAAE,WAAO;AAAA,EAAW,CAAC;AAC7D;AASA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,OAAQ,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,KAAM;AAC3D,MAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,OAAQ,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,KAAM;AAC3D,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK;AACR,SAAO,CAAE,EAAE,CAAC,KAAK,KAAM,EAAE,CAAC,GAAI,EAAE,CAAC,KAAK,KAAM,EAAE,CAAC,CAAC;AACpD;AAKA,SAAS,YAAY,GAAG,GAAG;AACvB,MAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,OAAQ,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,KAAM;AAC3D,MAAI,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,OAAQ,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,IAAI,KAAM;AAC3D,MAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;AAClB,IAAE,CAAC,KAAK,EAAE,CAAC,MAAM;AACjB,IAAE,CAAC,KAAK;AACR,IAAE,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AAC5D,IAAE,CAAC,KAAK;AACR,SAAO,CAAE,EAAE,CAAC,KAAK,KAAM,EAAE,CAAC,GAAI,EAAE,CAAC,KAAK,KAAM,EAAE,CAAC,CAAC;AACpD;AAMA,SAAS,QAAQ,GAAG,GAAG;AACnB,OAAK;AACL,MAAI,MAAM,IAAI;AACV,WAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AAAA,EACtB,WACS,IAAI,IAAI;AACb,WAAO,CAAE,EAAE,CAAC,KAAK,IAAM,EAAE,CAAC,MAAO,KAAK,GAAM,EAAE,CAAC,KAAK,IAAM,EAAE,CAAC,MAAO,KAAK,CAAG;AAAA,EAChF,OACK;AACD,SAAK;AACL,WAAO,CAAE,EAAE,CAAC,KAAK,IAAM,EAAE,CAAC,MAAO,KAAK,GAAM,EAAE,CAAC,KAAK,IAAM,EAAE,CAAC,MAAO,KAAK,CAAG;AAAA,EAChF;AACJ;AAMA,SAAS,aAAa,GAAG,GAAG;AACxB,OAAK;AACL,MAAI,MAAM,GAAG;AACT,WAAO;AAAA,EACX,WACS,IAAI,IAAI;AACb,WAAO,CAAE,EAAE,CAAC,KAAK,IAAM,EAAE,CAAC,MAAO,KAAK,GAAK,EAAE,CAAC,KAAK,CAAC;AAAA,EACxD,OACK;AACD,WAAO,CAAC,EAAE,CAAC,KAAM,IAAI,IAAK,CAAC;AAAA,EAC/B;AACJ;AAKA,SAAS,OAAO,GAAG,GAAG;AAClB,SAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;AACpC;AAMA,SAAS,QAAQ,GAAG;AAChB,MAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAI,YAAY,GAAG,CAAC,YAAY,UAAU,CAAC;AAC3C,MAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7B,MAAI,YAAY,GAAG,CAAC,YAAY,SAAU,CAAC;AAC3C,MAAI,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7B,SAAO;AACX;AAKA,SAAS,WAAW,KAAK,MAAM;AAC3B,QAAM,OAAO;AACb,SAAO,QAAQ;AACf,MAAI,YAAY,IAAI,SAAS;AAC7B,MAAI,QAAQ,IAAI,SAAS;AACzB,MAAI,KAAK,CAAC,GAAG,IAAI;AACjB,MAAI,KAAK,CAAC,GAAG,IAAI;AACjB,MAAI,KAAK,CAAC,GAAG,CAAC;AACd,MAAI,KAAK,CAAC,GAAG,CAAC;AACd,MAAI,KAAK,CAAC,YAAY,SAAU;AAChC,MAAI,KAAK,CAAC,YAAY,SAAU;AAChC,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,OAAO,IAAI,IAAI,IAAI;AAC/B,SAAK;AAAA,MACA,IAAI,WAAW,IAAI,CAAC,IAAI,OACnB,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS,KACjC,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS,MACjC,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS;AAAA,MACtC,IAAI,WAAW,CAAC,IAAI,OACf,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS,KACjC,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS,MACjC,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS;AAAA,IAC3C;AACA,SAAK;AAAA,MACA,IAAI,WAAW,IAAI,EAAE,IAAI,OACpB,IAAI,WAAW,IAAI,EAAE,IAAI,QAAS,KAClC,IAAI,WAAW,IAAI,EAAE,IAAI,QAAS,MAClC,IAAI,WAAW,IAAI,EAAE,IAAI,QAAS;AAAA,MACvC,IAAI,WAAW,IAAI,CAAC,IAAI,OACnB,IAAI,WAAW,IAAI,CAAC,IAAI,QAAS,KACjC,IAAI,WAAW,IAAI,EAAE,IAAI,QAAS,MAClC,IAAI,WAAW,IAAI,EAAE,IAAI,QAAS;AAAA,IAC5C;AACA,SAAK,YAAY,IAAI,EAAE;AACvB,SAAK,QAAQ,IAAI,EAAE;AACnB,SAAK,YAAY,IAAI,EAAE;AACvB,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,QAAQ,IAAI,EAAE;AACnB,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,OAAO,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AACpD,SAAK,YAAY,IAAI,EAAE;AACvB,SAAK,QAAQ,IAAI,EAAE;AACnB,SAAK,YAAY,IAAI,EAAE;AACvB,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,QAAQ,IAAI,EAAE;AACnB,SAAK,OAAO,IAAI,EAAE;AAClB,SAAK,OAAO,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAU,CAAC;AAAA,EACxD;AACA,OAAK,CAAC,GAAG,CAAC;AACV,OAAK,CAAC,GAAG,CAAC;AACV,UAAQ,WAAW;AAAA,IACf,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEjE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEjE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEjE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEjE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEjE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA;AAAA,IAE/D,KAAK;AACD,WAAK,OAAO,IAAI,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,CAAC;AAC1C,WAAK,YAAY,IAAI,EAAE;AACvB,WAAK,QAAQ,IAAI,EAAE;AACnB,WAAK,YAAY,IAAI,EAAE;AACvB,WAAK,OAAO,IAAI,EAAE;AAAA;AAAA,IAEtB,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA;AAAA,IAEhE,KAAK;AACD,WAAK,OAAO,IAAI,aAAa,CAAC,GAAG,IAAI,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AAAA;AAAA,IAE/D,KAAK;AACD,WAAK,OAAO,IAAI,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC;AACtC,WAAK,YAAY,IAAI,EAAE;AACvB,WAAK,QAAQ,IAAI,EAAE;AACnB,WAAK,YAAY,IAAI,EAAE;AACvB,WAAK,OAAO,IAAI,EAAE;AAAA,EAE1B;AACA,OAAK,OAAO,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC;AAC/B,OAAK,OAAO,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC;AAC/B,OAAK,OAAO,IAAI,EAAE;AAClB,OAAK,OAAO,IAAI,EAAE;AAClB,OAAK,QAAQ,EAAE;AACf,OAAK,QAAQ,EAAE;AACf,OAAK,OAAO,IAAI,EAAE;AAClB,OAAK,OAAO,IAAI,EAAE;AAClB,UAAS,cAAc,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,KACrD,cAAc,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,KACjD,cAAc,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE,KACjD,cAAc,GAAG,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,MAAM,EAAE;AAC1D;AAMA,SAAS,cAAc,OAAO;AAC1B,MAAI;AACJ,SAAO,SAAS,EAAE,MAAM,MAAM,MAAM,SAAS,MAAM,SAAS,QAAQ,KAAK,MAAM,WAAW,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM,IAAI,EAAE,GAAG,KAAK;AACtJ;AAQA,SAAS,SAAS,UAAU,QAAQ;AAChC,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC7C,QAAI,SAAS,CAAC,MAAM,QAAQ;AACxB,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,SAAS,UAAU,QAAQ;AAChC,SAAO,CAAC,SAAS,UAAU,MAAM;AACrC;AAIA,SAAS,MAAM,OAAO;AAClB,SAAO,SAAS,KAAK;AACzB;AAIA,SAAS,QAAQ,OAAO;AACpB,SAAO,WAAW,KAAK;AAC3B;AACA,SAAS,WAAW,OAAO,aAAa;AACpC,SAAO,OAAO,UAAU,YAAY,MAAM,KAAK,IAAI,cAAc;AACrE;AACA,SAAS,YAAY,QAAQ;AACzB,SAAO,OAAO,OAAO,SAAU,KAAK,OAAO;AAAE,WAAO,OAAO,QAAQ,IAAI;AAAA,EAAI,GAAG,CAAC;AACnF;AACA,SAAS,MAAM,OAAO,MAAM;AACxB,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAG;AACjC,MAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AACrB,WAAO,KAAK,MAAM,QAAQ,IAAI,IAAI;AAAA,EACtC,OACK;AAGD,QAAI,cAAc,IAAI;AACtB,WAAO,KAAK,MAAM,QAAQ,WAAW,IAAI;AAAA,EAC7C;AACJ;AAOA,SAAS,uBAAuB,UAAU;AACtC,MAAI,IAAI;AACR,MAAI,eAAe,sBAAsB,OAAO,UAAU,GAAG;AAC7D,MAAI,WAAW,sBAAsB,KAAK,QAAQ;AAClD,MAAI,MAAM,SAAS,CAAC,KAAK;AACzB,MAAI,aAAa,CAAC;AAClB,MAAI,aAAa;AACjB,MAAI,eAAe,SAAU,MAAM,OAAO;AACtC,eAAW,IAAI,IAAI,WAAW,IAAI,KAAK,CAAC;AACxC,eAAW,IAAI,EAAE,KAAK,KAAK;AAAA,EAC/B;AACA,aAAS;AACL,QAAI,QAAQ,WAAW,KAAK,SAAS,CAAC,CAAC;AACvC,QAAI,CAAC,OAAO;AACR;AAAA,IACJ;AACA,QAAI,OAAO,MAAM,CAAC;AAClB,YAAQ,KAAK,CAAC,GAAG;AAAA,MACb,KAAK;AACD,qBAAa,SAAS,KAAK,MAAM,CAAC,CAAC;AACnC;AAAA,MACJ,KAAK;AACD,qBAAa,MAAM,KAAK,MAAM,CAAC,CAAC;AAChC;AAAA,MACJ,KAAK,KAAK;AACN,YAAI,iBAAiB,yDAAyD,KAAK,IAAI;AACvF,YAAI,gBAAgB;AAChB,uBAAa,eAAe,CAAC,IAAI,MAAM,KAAK,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,eAAe,CAAC,OAAO,QAAQ,OAAO,SAAS,KAAK,EAAE;AAAA,QAC1J,OACK;AACD,gBAAM,IAAI,MAAM,YAAY;AAAA,QAChC;AACA;AAAA,MACJ;AAAA,MACA;AACI,cAAM,IAAI,MAAM,YAAY;AAAA,IACpC;AAAA,EACJ;AACA,SAAO,CAAC,KAAK,UAAU;AAC3B;AAEA,SAAS,uBAAuB,OAAO;AACnC,SAAO,SAAS,OAAO,UAAU,YAAY,aAAa,QAAQ,QAAQ,EAAE,SAAS,MAAM;AAC/F;AACA,SAAS,oBAAoB,YAAY;AACrC,SAAO,OAAO,eAAe;AACjC;AAOA,SAAS,WAAW,QAAQ,eAAe;AACvC,MAAI,oBAAoB,IAAI,QAAQ,SAAU,aAAa;AACvD,QAAI,gBAAgB,KAAK,IAAI;AAG7B,iBAAa,OAAO,KAAK,MAAM,aAAa,GAAG,WAAY;AACvD,UAAI,WAAW,CAAC;AAChB,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAS,EAAE,IAAI,UAAU,EAAE;AAAA,MAC/B;AACA,UAAI,eAAe,KAAK,IAAI,IAAI;AAEhC,UAAI,CAAC,SAAS,CAAC,GAAG;AACd,eAAO,YAAY,WAAY;AAAE,iBAAQ,EAAE,OAAO,uBAAuB,SAAS,CAAC,CAAC,GAAG,UAAU,aAAa;AAAA,QAAI,CAAC;AAAA,MACvH;AACA,UAAI,aAAa,SAAS,CAAC;AAE3B,UAAI,oBAAoB,UAAU,GAAG;AACjC,eAAO,YAAY,WAAY;AAAE,iBAAQ,EAAE,OAAO,YAAY,UAAU,aAAa;AAAA,QAAI,CAAC;AAAA,MAC9F;AAEA,kBAAY,WAAY;AACpB,eAAO,IAAI,QAAQ,SAAU,YAAY;AACrC,cAAI,eAAe,KAAK,IAAI;AAC5B,uBAAa,YAAY,WAAY;AACjC,gBAAI,UAAU,CAAC;AACf,qBAASC,MAAK,GAAGA,MAAK,UAAU,QAAQA,OAAM;AAC1C,sBAAQA,GAAE,IAAI,UAAUA,GAAE;AAAA,YAC9B;AACA,gBAAI,WAAW,eAAe,KAAK,IAAI,IAAI;AAE3C,gBAAI,CAAC,QAAQ,CAAC,GAAG;AACb,qBAAO,WAAW,EAAE,OAAO,uBAAuB,QAAQ,CAAC,CAAC,GAAG,SAAmB,CAAC;AAAA,YACvF;AAEA,uBAAW,EAAE,OAAO,QAAQ,CAAC,GAAG,SAAmB,CAAC;AAAA,UACxD,CAAC;AAAA,QACL,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AAAA,EACL,CAAC;AACD,oCAAkC,iBAAiB;AACnD,SAAO,SAAS,eAAe;AAC3B,WAAO,kBAAkB,KAAK,SAAU,gBAAgB;AAAE,aAAO,eAAe;AAAA,IAAG,CAAC;AAAA,EACxF;AACJ;AAUA,SAAS,YAAYC,UAAS,eAAe,gBAAgB;AACzD,MAAI,kBAAkB,OAAO,KAAKA,QAAO,EAAE,OAAO,SAAU,WAAW;AAAE,WAAO,SAAS,gBAAgB,SAAS;AAAA,EAAG,CAAC;AAGtH,MAAI,uBAAuB,cAAc,iBAAiB,SAAU,WAAW;AAC3E,WAAO,WAAWA,SAAQ,SAAS,GAAG,aAAa;AAAA,EACvD,CAAC;AACD,oCAAkC,oBAAoB;AACtD,SAAO,SAAS,gBAAgB;AAC5B,WAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,UAAI,eAAe,mBAAmB,gBAAgB,YAAYC;AAClE,aAAO,YAAY,MAAM,SAAU,IAAI;AACnC,gBAAQ,GAAG,OAAO;AAAA,UACd,KAAK;AAAG,mBAAO,CAAC,GAAa,oBAAoB;AAAA,UACjD,KAAK;AACD,4BAAgB,GAAG,KAAK;AACxB,mBAAO,CAAC,GAAa,cAAc,eAAe,SAAU,cAAc;AAClE,kBAAI,mBAAmB,aAAa;AACpC,gDAAkC,gBAAgB;AAClD,qBAAO;AAAA,YACX,CAAC,CAAC;AAAA,UACV,KAAK;AACD,gCAAoB,GAAG,KAAK;AAC5B,mBAAO;AAAA,cAAC;AAAA,cAAa,QAAQ,IAAI,iBAAiB;AAAA;AAAA,YAElD;AAAA,UACJ,KAAK;AACD,6BAAiB,GAAG,KAAK;AACzB,yBAAa,CAAC;AACd,iBAAKA,SAAQ,GAAGA,SAAQ,gBAAgB,QAAQ,EAAEA,QAAO;AACrD,yBAAW,gBAAgBA,MAAK,CAAC,IAAI,eAAeA,MAAK;AAAA,YAC7D;AACA,mBAAO,CAAC,GAAc,UAAU;AAAA,QACxC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AACJ;AAQA,SAAS,gBAAgB,QAAQ,gBAAgB;AAC7C,MAAI,sBAAsB,SAAU,YAAY;AAC5C,QAAI,oBAAoB,UAAU,GAAG;AACjC,aAAO,eAAe,UAAU;AAAA,IACpC;AACA,WAAO,WAAY;AACf,UAAI,YAAY,WAAW;AAC3B,UAAI,UAAU,SAAS,GAAG;AACtB,eAAO,UAAU,KAAK,cAAc;AAAA,MACxC;AACA,aAAO,eAAe,SAAS;AAAA,IACnC;AAAA,EACJ;AACA,SAAO,SAAU,SAAS;AACtB,QAAI,aAAa,OAAO,OAAO;AAC/B,QAAI,UAAU,UAAU,GAAG;AACvB,aAAO,WAAW,KAAK,mBAAmB;AAAA,IAC9C;AACA,WAAO,oBAAoB,UAAU;AAAA,EACzC;AACJ;AAWA,SAAS,YAAY;AACjB,MAAI,IAAI;AACR,MAAI,IAAI;AAER,SAAQ,YAAY;AAAA,IAChB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,EAC1B,CAAC,KAAK;AACV;AAOA,SAAS,aAAa;AAElB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,SAAQ,YAAY,CAAC,yBAAyB,GAAG,cAAc,GAAG,iBAAiB,GAAG,gBAAgB,CAAC,CAAC,KAAK,KACzG,CAAC,UAAU;AACnB;AAOA,SAAS,aAAa;AAElB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,SAAQ,YAAY;AAAA,IAChB,6BAA6B;AAAA,IAC7B,4BAA4B;AAAA,IAC5B,EAAE,OAAO,QAAQ,QAAQ,MAAM;AAAA,IAC/B,qCAAqC;AAAA,IACrC,oBAAoB;AAAA,IACpB,uBAAuB;AAAA,IACvB,yBAAyB;AAAA,EAC7B,CAAC,KAAK;AACV;AAQA,SAAS,WAAW;AAEhB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,SAAQ,YAAY;AAAA,IAChB,mBAAmB;AAAA,IACnB,uBAAuB;AAAA,IACvB,aAAa;AAAA,IACb,EAAE,OAAO,QAAQ,OAAO,MAAM;AAAA,IAC9B,uBAAuB;AAAA,IACvB,qBAAqB;AAAA,EACzB,CAAC,KAAK;AACV;AAOA,SAAS,kBAAkB;AACvB,MAAI,IAAI;AACR,SAAQ,YAAY;AAAA,IAChB,YAAY;AAAA,IACZ,EAAE,uBAAuB;AAAA,IACzB,EAAE,kBAAkB;AAAA,IACpB,EAAE,gBAAgB;AAAA,EACtB,CAAC,KAAK;AACV;AAOA,SAAS,UAAU;AACf,MAAI,IAAI;AACR,MAAI,IAAI;AAER,SAAQ,YAAY;AAAA,IAChB,aAAa;AAAA,IACb,qBAAqB,MAAM,KAAK,SAAS,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA,IAC3I,2BAA2B;AAAA,IAC3B,qBAAqB;AAAA,IACrB,wBAAwB;AAAA,IACxB,8BAA8B;AAAA,EAClC,CAAC,KAAK;AACV;AAKA,SAAS,sBAAsB;AAE3B,MAAI,IAAI;AACR,SAAQ,YAAY;AAAA,IAChB,EAAE,wBAAwB;AAAA,IAC1B,0BAA0B;AAAA,IAC1B,KAAK,EAAE,SAAS;AAAA,IAChB,KAAK,EAAE,YAAY;AAAA,EACvB,CAAC,KAAK;AACV;AAOA,SAAS,qBAAqB;AAE1B,MAAI,IAAI;AACR,SAAQ,YAAY;AAAA,IAChB,iBAAiB;AAAA,IACjB,+BAA+B;AAAA,IAC/B,wBAAwB;AAAA,IACxB,wBAAwB;AAAA,EAC5B,CAAC,KAAK;AACV;AAKA,SAAS,SAAS;AAOd,MAAI,UAAU,aAAa,QAAQ;AAC/B,WAAO;AAAA,EACX;AACA,MAAI,IAAI;AACR,MAAI,cAAc,EAAE,QAAQ,EAAE;AAC9B,SAAQ,YAAY;AAAA,IAChB,iBAAiB;AAAA,IACjB,CAAC,CAAC,QAAQ,UAAU;AAAA;AAAA,IAEpB,cAAc,QAAQ,cAAc;AAAA,EACxC,CAAC,KAAK;AACV;AAKA,SAAS,uBAAuB;AAC5B,MAAI,IAAI;AACR,SAAO,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,wBAAwB,EAAE,2BAA2B;AAClH;AACA,SAAS,iBAAiB;AACtB,MAAI,IAAI;AAER,UAAQ,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,sBAAsB,KAAK,CAAC;AAC7G;AAOA,SAAS,YAAY;AACjB,MAAI,eAAe,WAAW;AAC9B,MAAI,YAAY,QAAQ;AAGxB,MAAI,CAAC,gBAAgB,CAAC,WAAW;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,IAAI;AAGR,SAAQ,YAAY;AAAA,IAChB,yBAAyB;AAAA,IACzB,iBAAiB;AAAA,IACjB,gBAAgB,EAAE,kBAAkB;AAAA,IACpC,aAAa,WAAW,KAAK,UAAU,UAAU;AAAA,EACrD,CAAC,KAAK;AACV;AAMA,SAAS,sBAAsB;AAC3B,MAAI,IAAI;AACR,MAAI,eAAe,EAAE,uBAAuB,EAAE;AAC9C,MAAI,CAAC,cAAc;AACf,WAAO;AAAA,EACX;AAKA,MAAI,sCAAsC,GAAG;AACzC,WAAO;AAAA,EACX;AACA,MAAI,gBAAgB;AACpB,MAAI,cAAc;AAClB,MAAI,UAAU,IAAI,aAAa,GAAG,aAAa,KAAK;AACpD,MAAI,aAAa,QAAQ,iBAAiB;AAC1C,aAAW,OAAO;AAClB,aAAW,UAAU,QAAQ;AAC7B,MAAI,aAAa,QAAQ,yBAAyB;AAClD,aAAW,UAAU,QAAQ;AAC7B,aAAW,KAAK,QAAQ;AACxB,aAAW,MAAM,QAAQ;AACzB,aAAW,OAAO,QAAQ;AAC1B,aAAW,QAAQ,QAAQ;AAC3B,aAAW,QAAQ,UAAU;AAC7B,aAAW,QAAQ,QAAQ,WAAW;AACtC,aAAW,MAAM,CAAC;AAClB,MAAI,KAAK,oBAAoB,OAAO,GAAG,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC;AACpF,MAAI,qBAAqB,cAAc,KAAK,SAAU,QAAQ;AAAE,WAAO,QAAQ,OAAO,eAAe,CAAC,EAAE,SAAS,aAAa,CAAC;AAAA,EAAG,GAAG,SAAU,OAAO;AAClJ,QAAI,MAAM,SAAS,aAA0C,MAAM,SAAS,aAA4C;AACpH,aAAO;AAAA,IACX;AACA,UAAM;AAAA,EACV,CAAC;AAED,oCAAkC,kBAAkB;AACpD,SAAO,WAAY;AACf,oBAAgB;AAChB,WAAO;AAAA,EACX;AACJ;AAIA,SAAS,wCAAwC;AAC7C,SAAO,SAAS,KAAK,CAAC,gBAAgB,KAAK,CAAC,mBAAmB;AACnE;AAKA,SAAS,oBAAoB,SAAS;AAClC,MAAI,oBAAoB;AACxB,MAAI,mBAAmB;AACvB,MAAI,sBAAsB;AAC1B,MAAI,wBAAwB;AAC5B,MAAI,WAAW,WAAY;AAAE,WAAO;AAAA,EAAW;AAC/C,MAAI,gBAAgB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACvD,QAAI,cAAc;AAClB,QAAI,iBAAiB;AACrB,QAAI,mBAAmB;AACvB,YAAQ,aAAa,SAAU,OAAO;AAAE,aAAO,QAAQ,MAAM,cAAc;AAAA,IAAG;AAC9E,QAAI,sBAAsB,WAAY;AAClC,iBAAW,WAAY;AAAE,eAAO,OAAO;AAAA,UAAe;AAAA;AAAA,QAAsC,CAAC;AAAA,MAAG,GAAG,KAAK,IAAI,qBAAqB,mBAAmB,wBAAwB,KAAK,IAAI,CAAC,CAAC;AAAA,IAC3L;AACA,QAAI,YAAY,WAAY;AACxB,UAAI;AACA,YAAI,mBAAmB,QAAQ,eAAe;AAE9C,YAAI,UAAU,gBAAgB,GAAG;AAE7B,4CAAkC,gBAAgB;AAAA,QACtD;AACA,gBAAQ,QAAQ,OAAO;AAAA,UACnB,KAAK;AACD,+BAAmB,KAAK,IAAI;AAC5B,gBAAI,aAAa;AACb,kCAAoB;AAAA,YACxB;AACA;AAAA;AAAA;AAAA;AAAA,UAIJ,KAAK;AAKD,gBAAI,CAAC,SAAS,QAAQ;AAClB;AAAA,YACJ;AACA,gBAAI,eAAe,kBAAkB,mBAAmB;AACpD,qBAAO;AAAA,gBAAe;AAAA;AAAA,cAA0C,CAAC;AAAA,YACrE,OACK;AACD,yBAAW,WAAW,gBAAgB;AAAA,YAC1C;AACA;AAAA,QACR;AAAA,MACJ,SACO,OAAO;AACV,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ;AACA,cAAU;AACV,eAAW,WAAY;AACnB,UAAI,CAAC,aAAa;AACd,sBAAc;AACd,YAAI,mBAAmB,GAAG;AACtB,8BAAoB;AAAA,QACxB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO,CAAC,eAAe,QAAQ;AACnC;AACA,SAAS,QAAQ,QAAQ;AACrB,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,YAAQ,KAAK,IAAI,OAAO,CAAC,CAAC;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,QAAQ,IAAI,MAAM,IAAI;AAC1B,QAAM,OAAO;AACb,SAAO;AACX;AAYA,SAAS,WAAW,QAAQ,aAAa,iBAAiB;AACtD,MAAI,IAAI,IAAI;AACZ,MAAI,oBAAoB,QAAQ;AAAE,sBAAkB;AAAA,EAAI;AACxD,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,GAAG;AACP,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,cAAI;AACJ,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,CAAC,EAAE,KAAM,QAAO,CAAC,GAAa,CAAC;AACpC,iBAAO,CAAC,GAAa,KAAK,eAAe,CAAC;AAAA,QAC9C,KAAK;AACD,aAAG,KAAK;AACR,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AACD,mBAAS,EAAE,cAAc,QAAQ;AACjC,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,aAAG,KAAK,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC;AAC1B,iBAAO,CAAC,GAAa,IAAI,QAAQ,SAAU,UAAU,SAAS;AACtD,gBAAI,aAAa;AACjB,gBAAI,UAAU,WAAY;AACtB,2BAAa;AACb,uBAAS;AAAA,YACb;AACA,gBAAI,SAAS,SAAU,OAAO;AAC1B,2BAAa;AACb,sBAAQ,KAAK;AAAA,YACjB;AACA,mBAAO,SAAS;AAChB,mBAAO,UAAU;AACjB,gBAAI,QAAQ,OAAO;AACnB,kBAAM,YAAY,WAAW,SAAS,WAAW;AACjD,kBAAM,WAAW;AACjB,kBAAM,MAAM;AACZ,kBAAM,OAAO;AACb,kBAAM,aAAa;AACnB,gBAAI,eAAe,YAAY,QAAQ;AACnC,qBAAO,SAAS;AAAA,YACpB,OACK;AACD,qBAAO,MAAM;AAAA,YACjB;AACA,cAAE,KAAK,YAAY,MAAM;AAIzB,gBAAI,kBAAkB,WAAY;AAC9B,kBAAIC,KAAIC;AAIR,kBAAI,YAAY;AACZ;AAAA,cACJ;AAGA,oBAAMA,OAAMD,MAAK,OAAO,mBAAmB,QAAQA,QAAO,SAAS,SAASA,IAAG,cAAc,QAAQC,QAAO,SAAS,SAASA,IAAG,gBAAgB,YAAY;AACzJ,wBAAQ;AAAA,cACZ,OACK;AACD,2BAAW,iBAAiB,EAAE;AAAA,cAClC;AAAA,YACJ;AACA,4BAAgB;AAAA,UACpB,CAAC,CAAC;AAAA,QACV,KAAK;AACD,aAAG,KAAK;AACR,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,GAAG,MAAM,KAAK,OAAO,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAO,QAAO,CAAC,GAAa,CAAC;AAChK,iBAAO,CAAC,GAAa,KAAK,eAAe,CAAC;AAAA,QAC9C,KAAK;AACD,aAAG,KAAK;AACR,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AAAG,iBAAO,CAAC,GAAa,OAAO,QAAQ,OAAO,aAAa,CAAC;AAAA,QACjE,KAAK;AAAG,iBAAO,CAAC,GAAc,GAAG,KAAK,CAAC;AAAA,QACvC,KAAK;AACD,WAAC,KAAK,OAAO,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,MAAM;AACnF,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAgB;AAAA,QAC5B,KAAK;AAAI,iBAAO;AAAA,YAAC;AAAA;AAAA,UAAY;AAAA,MACjC;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAKA,SAAS,kBAAkB,UAAU;AACjC,MAAI,KAAK,uBAAuB,QAAQ,GAAG,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,CAAC;AACzE,MAAI,UAAU,SAAS,cAAc,QAAQ,QAAQ,QAAQ,SAAS,MAAM,KAAK;AACjF,WAAS,KAAK,GAAG,KAAK,OAAO,KAAK,UAAU,GAAG,KAAK,GAAG,QAAQ,MAAM;AACjE,QAAI,SAAS,GAAG,EAAE;AAClB,QAAI,QAAQ,WAAW,MAAM,EAAE,KAAK,GAAG;AAGvC,QAAI,WAAW,SAAS;AACpB,qBAAe,QAAQ,OAAO,KAAK;AAAA,IACvC,OACK;AACD,cAAQ,aAAa,QAAQ,KAAK;AAAA,IACtC;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,eAAe,OAAO,QAAQ;AAGnC,WAAS,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC3D,QAAI,WAAW,GAAG,EAAE;AACpB,QAAI,QAAQ,8CAA8C,KAAK,QAAQ;AACvE,QAAI,OAAO;AACP,UAAI,SAAS,MAAM,CAAC,GAAG,QAAQ,MAAM,CAAC,GAAG,WAAW,MAAM,CAAC;AAC3D,YAAM,YAAY,QAAQ,OAAO,YAAY,EAAE;AAAA,IACnD;AAAA,EACJ;AACJ;AAIA,IAAI,aAAa;AAEjB,IAAI,WAAW;AAGf,IAAI,YAAY,CAAC,aAAa,cAAc,OAAO;AACnD,IAAI,WAAW;AAAA;AAAA,EAEX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,WAAW;AAIhB,SAAO,WAAW,SAAU,GAAG,IAAI;AAC/B,QAAIC,YAAW,GAAG;AAClB,QAAI,SAASA,UAAS;AACtB,WAAO,MAAM,WAAW;AAExB,QAAI,iBAAiBA,UAAS,cAAc,KAAK;AACjD,QAAI,eAAe,CAAC;AACpB,QAAI,gBAAgB,CAAC;AAErB,QAAI,aAAa,SAAU,YAAY;AACnC,UAAI,OAAOA,UAAS,cAAc,MAAM;AACxC,UAAI,QAAQ,KAAK;AACjB,YAAM,WAAW;AACjB,YAAM,MAAM;AACZ,YAAM,OAAO;AACb,YAAM,aAAa;AACnB,WAAK,cAAc;AACnB,qBAAe,YAAY,IAAI;AAC/B,aAAO;AAAA,IACX;AAEA,QAAI,sBAAsB,SAAU,cAAc,UAAU;AACxD,aAAO,WAAW,IAAI,OAAO,cAAc,IAAI,EAAE,OAAO,QAAQ,CAAC;AAAA,IACrE;AAEA,QAAI,2BAA2B,WAAY;AACvC,aAAO,UAAU,IAAI,UAAU;AAAA,IACnC;AAEA,QAAI,uBAAuB,WAAY;AAEnC,UAAI,QAAQ,CAAC;AACb,UAAI,UAAU,SAAUC,OAAM;AAC1B,cAAMA,KAAI,IAAI,UAAU,IAAI,SAAU,UAAU;AAAE,iBAAO,oBAAoBA,OAAM,QAAQ;AAAA,QAAG,CAAC;AAAA,MACnG;AACA,eAAS,KAAK,GAAG,aAAa,UAAU,KAAK,WAAW,QAAQ,MAAM;AAClE,YAAI,OAAO,WAAW,EAAE;AACxB,gBAAQ,IAAI;AAAA,MAChB;AACA,aAAO;AAAA,IACX;AAEA,QAAI,kBAAkB,SAAU,WAAW;AACvC,aAAO,UAAU,KAAK,SAAU,UAAU,eAAe;AACrD,eAAO,UAAU,aAAa,EAAE,gBAAgB,aAAa,QAAQ,KACjE,UAAU,aAAa,EAAE,iBAAiB,cAAc,QAAQ;AAAA,MACxE,CAAC;AAAA,IACL;AAEA,QAAI,iBAAiB,yBAAyB;AAE9C,QAAI,aAAa,qBAAqB;AAEtC,WAAO,YAAY,cAAc;AAEjC,aAASJ,SAAQ,GAAGA,SAAQ,UAAU,QAAQA,UAAS;AACnD,mBAAa,UAAUA,MAAK,CAAC,IAAI,eAAeA,MAAK,EAAE;AACvD,oBAAc,UAAUA,MAAK,CAAC,IAAI,eAAeA,MAAK,EAAE;AAAA,IAC5D;AAEA,WAAO,SAAS,OAAO,SAAU,MAAM;AAAE,aAAO,gBAAgB,WAAW,IAAI,CAAC;AAAA,IAAG,CAAC;AAAA,EACxF,CAAC;AACL;AAEA,SAAS,aAAa;AAClB,MAAI,aAAa,UAAU;AAC3B,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,MAAI,UAAU,CAAC;AAEf,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,EAAE,GAAG;AACxC,QAAI,SAAS,WAAW,CAAC;AACzB,QAAI,CAAC,QAAQ;AACT;AAAA,IACJ;AACA,QAAI,YAAY,CAAC;AACjB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACpC,UAAI,WAAW,OAAO,CAAC;AACvB,gBAAU,KAAK;AAAA,QACX,MAAM,SAAS;AAAA,QACf,UAAU,SAAS;AAAA,MACvB,CAAC;AAAA,IACL;AACA,YAAQ,KAAK;AAAA,MACT,MAAM,OAAO;AAAA,MACb,aAAa,OAAO;AAAA,MACpB;AAAA,IACJ,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAGA,SAAS,uBAAuB;AAC5B,MAAI,UAAU;AACd,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,kBAAkB,GAAG,SAAS,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AAC5D,MAAI,CAAC,YAAY,QAAQ,OAAO,GAAG;AAC/B,eAAW,OAAO;AAAA,EACtB,OACK;AACD,cAAU,mBAAmB,OAAO;AACpC,oBAAgB,QAAQ,OAAO;AAC/B,QAAI,aAAa,eAAe,MAAM;AACtC,QAAI,aAAa,eAAe,MAAM;AAGtC,QAAI,eAAe,YAAY;AAC3B,iBAAW,OAAO;AAAA,IACtB,OACK;AACD,aAAO;AAKP,0BAAoB,QAAQ,OAAO;AACnC,iBAAW,eAAe,MAAM;AAAA,IACpC;AAAA,EACJ;AACA,SAAO,EAAE,SAAkB,UAAoB,KAAW;AAC9D;AACA,SAAS,oBAAoB;AACzB,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,SAAO,CAAC,QAAQ,OAAO,WAAW,IAAI,CAAC;AAC3C;AACA,SAAS,YAAY,QAAQ,SAAS;AAClC,SAAO,CAAC,EAAE,WAAW,OAAO;AAChC;AACA,SAAS,mBAAmB,SAAS;AAGjC,UAAQ,KAAK,GAAG,GAAG,IAAI,EAAE;AACzB,UAAQ,KAAK,GAAG,GAAG,GAAG,CAAC;AACvB,SAAO,CAAC,QAAQ,cAAc,GAAG,GAAG,SAAS;AACjD;AACA,SAAS,gBAAgB,QAAQ,SAAS;AAEtC,SAAO,QAAQ;AACf,SAAO,SAAS;AAChB,UAAQ,eAAe;AACvB,UAAQ,YAAY;AACpB,UAAQ,SAAS,KAAK,GAAG,IAAI,EAAE;AAC/B,UAAQ,YAAY;AAGpB,UAAQ,OAAO;AAOf,MAAI,cAAc,qBAAqB;AAAA,IAAO,OAAO,aAAa,OAAO,KAAK;AAAA;AAAA,EAAU;AACxF,UAAQ,SAAS,aAAa,GAAG,EAAE;AACnC,UAAQ,YAAY;AACpB,UAAQ,OAAO;AACf,UAAQ,SAAS,aAAa,GAAG,EAAE;AACvC;AACA,SAAS,oBAAoB,QAAQ,SAAS;AAE1C,SAAO,QAAQ;AACf,SAAO,SAAS;AAIhB,UAAQ,2BAA2B;AACnC,WAAS,KAAK,GAAG,KAAK;AAAA,IAClB,CAAC,QAAQ,IAAI,EAAE;AAAA,IACf,CAAC,QAAQ,IAAI,EAAE;AAAA,IACf,CAAC,QAAQ,IAAI,EAAE;AAAA,EACnB,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrB,QAAI,KAAK,GAAG,EAAE,GAAG,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;AACnD,YAAQ,YAAY;AACpB,YAAQ,UAAU;AAClB,YAAQ,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,KAAK,GAAG,IAAI;AAC1C,YAAQ,UAAU;AAClB,YAAQ,KAAK;AAAA,EACjB;AAIA,UAAQ,YAAY;AACpB,UAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG,IAAI;AAC5C,UAAQ,IAAI,IAAI,IAAI,IAAI,GAAG,KAAK,KAAK,GAAG,IAAI;AAC5C,UAAQ,KAAK,SAAS;AAC1B;AACA,SAAS,eAAe,QAAQ;AAC5B,SAAO,OAAO,UAAU;AAC5B;AASA,SAAS,kBAAkB;AACvB,MAAI,IAAI;AACR,MAAI,iBAAiB;AACrB,MAAI;AACJ,MAAI,EAAE,mBAAmB,QAAW;AAChC,qBAAiB,MAAM,EAAE,cAAc;AAAA,EAC3C,WACS,EAAE,qBAAqB,QAAW;AACvC,qBAAiB,EAAE;AAAA,EACvB;AACA,MAAI;AACA,aAAS,YAAY,YAAY;AACjC,iBAAa;AAAA,EACjB,SACO,IAAI;AACP,iBAAa;AAAA,EACjB;AACA,MAAI,aAAa,kBAAkB;AACnC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,SAAS,WAAW;AAChB,SAAO,UAAU;AACrB;AAEA,SAAS,eAAe;AACpB,MAAI,IAAI;AACR,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,mBAAmB,EAAE;AACtE,MAAI,aAAa,QAAW;AACxB,WAAO,KAAK,CAAC,QAAQ,CAAC;AAAA,EAC1B;AACA,MAAI,MAAM,QAAQ,EAAE,SAAS,GAAG;AAG5B,QAAI,EAAE,WAAW,KAAK,oBAAoB,IAAI;AAC1C,aAAO,KAAK,EAAE,SAAS;AAAA,IAC3B;AAAA,EACJ,WACS,OAAO,EAAE,cAAc,UAAU;AACtC,QAAI,YAAY,EAAE;AAClB,QAAI,WAAW;AACX,aAAO,KAAK,UAAU,MAAM,GAAG,CAAC;AAAA,IACpC;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,gBAAgB;AACrB,SAAO,OAAO,OAAO;AACzB;AAEA,SAAS,kBAAkB;AAEvB,SAAO,WAAW,QAAQ,UAAU,YAAY,GAAG,MAAS;AAChE;AAEA,SAAS,sBAAsB;AAC3B,MAAI,IAAI;AAIR,MAAI,iBAAiB,SAAU,OAAO;AAAE,WAAO,WAAW,MAAM,KAAK,GAAG,IAAI;AAAA,EAAG;AAC/E,MAAI,aAAa,CAAC,eAAe,EAAE,KAAK,GAAG,eAAe,EAAE,MAAM,CAAC;AACnE,aAAW,KAAK,EAAE,QAAQ;AAC1B,SAAO;AACX;AAEA,IAAI,2BAA2B;AAC/B,IAAI,oBAAoB;AAExB,IAAI;AACJ,IAAI;AAQJ,SAAS,mBAAmB;AACxB,MAAI,6BAA6B,QAAW;AACxC;AAAA,EACJ;AACA,MAAI,mBAAmB,WAAY;AAC/B,QAAI,YAAY,sBAAsB;AACtC,QAAI,gBAAgB,SAAS,GAAG;AAC5B,iCAA2B,WAAW,kBAAkB,wBAAwB;AAAA,IACpF,OACK;AACD,0BAAoB;AACpB,iCAA2B;AAAA,IAC/B;AAAA,EACJ;AACA,mBAAiB;AACrB;AACA,SAAS,iBAAiB;AACtB,MAAI,QAAQ;AACZ,mBAAiB;AACjB,SAAO,WAAY;AAAE,WAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACrE,UAAI;AACJ,aAAO,YAAY,MAAM,SAAU,IAAI;AACnC,gBAAQ,GAAG,OAAO;AAAA,UACd,KAAK;AACD,wBAAY,sBAAsB;AAClC,gBAAI,CAAC,gBAAgB,SAAS,EAAG,QAAO,CAAC,GAAa,CAAC;AACvD,gBAAI,mBAAmB;AACnB,qBAAO,CAAC,GAAc,cAAc,CAAC,GAAG,mBAAmB,IAAI,CAAC;AAAA,YACpE;AACA,gBAAI,CAAC,qBAAqB,EAAG,QAAO,CAAC,GAAa,CAAC;AAInD,mBAAO,CAAC,GAAa,eAAe,CAAC;AAAA,UACzC,KAAK;AAID,eAAG,KAAK;AACR,wBAAY,sBAAsB;AAClC,eAAG,QAAQ;AAAA,UACf,KAAK;AACD,gBAAI,CAAC,gBAAgB,SAAS,GAAG;AAC7B,kCAAoB;AAAA,YACxB;AACA,mBAAO,CAAC,GAAc,SAAS;AAAA,QACvC;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG;AACR;AAKA,SAAS,wBAAwB;AAC7B,MAAI,QAAQ;AACZ,MAAI,oBAAoB,eAAe;AACvC,SAAO,WAAY;AAAE,WAAO,UAAU,OAAO,QAAQ,QAAQ,WAAY;AACrE,UAAI,WAAW;AACf,aAAO,YAAY,MAAM,SAAU,IAAI;AACnC,gBAAQ,GAAG,OAAO;AAAA,UACd,KAAK;AAAG,mBAAO,CAAC,GAAa,kBAAkB,CAAC;AAAA,UAChD,KAAK;AACD,wBAAY,GAAG,KAAK;AACpB,0BAAc,SAAU,UAAU;AAAE,qBAAQ,aAAa,OAAO,OAAO,MAAM,UAAU,iBAAiB;AAAA,YAAI;AAG5G,mBAAO,CAAC,GAAc,CAAC,YAAY,UAAU,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,CAAC,GAAG,YAAY,UAAU,CAAC,CAAC,CAAC,CAAC;AAAA,QAC1I;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EAAG;AACR;AACA,SAAS,wBAAwB;AAC7B,MAAI,IAAI;AAMR,SAAO;AAAA,IACH,WAAW,QAAQ,EAAE,QAAQ,GAAG,IAAI;AAAA,IACpC,WAAW,QAAQ,EAAE,KAAK,IAAI,QAAQ,EAAE,UAAU,IAAI,WAAW,QAAQ,EAAE,SAAS,GAAG,CAAC,GAAG,IAAI;AAAA,IAC/F,WAAW,QAAQ,EAAE,MAAM,IAAI,QAAQ,EAAE,WAAW,IAAI,WAAW,QAAQ,EAAE,QAAQ,GAAG,CAAC,GAAG,IAAI;AAAA,IAChG,WAAW,QAAQ,EAAE,SAAS,GAAG,IAAI;AAAA,EACzC;AACJ;AACA,SAAS,gBAAgB,WAAW;AAChC,WAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACxB,QAAI,UAAU,CAAC,GAAG;AACd,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,yBAAyB;AAE9B,SAAO,WAAW,MAAM,UAAU,mBAAmB,GAAG,MAAS;AACrE;AAEA,SAAS,cAAc;AACnB,MAAI;AACJ,MAAI,kBAAkB,KAAK,OAAO,UAAU,QAAQ,OAAO,SAAS,SAAS,GAAG;AAChF,MAAI,gBAAgB;AAChB,QAAI,WAAW,IAAI,eAAe,EAAE,gBAAgB,EAAE;AACtD,QAAI,UAAU;AACV,aAAO;AAAA,IACX;AAAA,EACJ;AAGA,MAAI,SAAS,CAAC,kBAAkB;AAChC,SAAO,MAAM,OAAO,UAAU,IAAI,MAAM,EAAE,EAAE,OAAO,KAAK,IAAI,MAAM,CAAC;AACvE;AACA,SAAS,oBAAoB;AACzB,MAAI,eAAc,oBAAI,KAAK,GAAE,YAAY;AAKzC,SAAO,KAAK;AAAA;AAAA,IAEZ,QAAQ,IAAI,KAAK,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC;AAAA,IAAG,QAAQ,IAAI,KAAK,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC;AAAA,EAAC;AACtH;AAEA,SAAS,oBAAoB;AACzB,MAAI;AACA,WAAO,CAAC,CAAC,OAAO;AAAA,EACpB,SACO,OAAO;AAEV,WAAO;AAAA,EACX;AACJ;AAGA,SAAS,kBAAkB;AACvB,MAAI;AACA,WAAO,CAAC,CAAC,OAAO;AAAA,EACpB,SACO,GAAG;AAEN,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,eAAe;AAGpB,MAAI,UAAU,KAAK,WAAW,GAAG;AAC7B,WAAO;AAAA,EACX;AACA,MAAI;AACA,WAAO,CAAC,CAAC,OAAO;AAAA,EACpB,SACO,GAAG;AAEN,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,kBAAkB;AACvB,SAAO,CAAC,CAAC,OAAO;AACpB;AAEA,SAAS,cAAc;AACnB,SAAO,UAAU;AACrB;AAEA,SAAS,cAAc;AAEnB,MAAI,WAAW,UAAU;AAKzB,MAAI,aAAa,YAAY;AACzB,QAAI,SAAS,KAAK,CAAC,gBAAgB,GAAG;AAClC,aAAO,OAAO,IAAI,SAAS;AAAA,IAC/B;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,YAAY;AACjB,SAAO,UAAU,UAAU;AAC/B;AAMA,SAAS,mBAAmB;AACxB,MAAI,UAAU,CAAC;AACf,WAAS,KAAK,GAAG,KAAK;AAAA;AAAA,IAElB;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA;AAAA,IAEA;AAAA,IACA;AAAA;AAAA,IAEA;AAAA;AAAA;AAAA,EAGJ,GAAG,KAAK,GAAG,QAAQ,MAAM;AACrB,QAAI,MAAM,GAAG,EAAE;AACf,QAAI,QAAQ,OAAO,GAAG;AACtB,QAAI,SAAS,OAAO,UAAU,UAAU;AACpC,cAAQ,KAAK,GAAG;AAAA,IACpB;AAAA,EACJ;AACA,SAAO,QAAQ,KAAK;AACxB;AASA,SAAS,oBAAoB;AACzB,MAAI,IAAI;AAQR,MAAI;AAEA,MAAE,SAAS;AACX,QAAI,SAAS,EAAE,OAAO,QAAQ,aAAa,MAAM;AAEjD,MAAE,SAAS;AACX,WAAO;AAAA,EACX,SACO,GAAG;AACN,WAAO;AAAA,EACX;AACJ;AAYA,SAAS,aAAa;AAClB,MAAI,UAAU;AACd,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,sBAAsB;AAAA,IAClC;AAAA,IACA,OAAO,CAAC,aAAa,kBAAkB,QAAQ,kBAAkB,GAAG,4BAA4B,QAAQ;AAAA,IACxG,gBAAgB;AAAA,MACZ;AAAA,MACA,QAAQ,kBAAkB;AAAA,MAC1B;AAAA,MACA,QAAQ,sCAAsC;AAAA,MAC9C,QAAQ,sDAAsD;AAAA,IAClE;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,kDAAkD;AAAA,IAC9D;AAAA,IACA,uBAAuB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,kBAAkB;AAAA,IAC9B;AAAA,IACA,mBAAmB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,MACT;AAAA,MACA,QAAQ,kBAAkB;AAAA,MAC1B,QAAQ,sBAAsB;AAAA,MAC9B,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,kBAAkB;AAAA,IAC9B;AAAA,IACA,gBAAgB;AAAA,MACZ,QAAQ,kBAAkB;AAAA,MAC1B,QAAQ,kCAAkC;AAAA,MAC1C;AAAA,MACA,QAAQ,kCAAkC;AAAA,MAC1C,QAAQ,kCAAkC;AAAA,IAC9C;AAAA,IACA,eAAe;AAAA,MACX;AAAA,MACA,QAAQ,8BAA8B;AAAA,MACtC;AAAA,MACA;AAAA,MACA,QAAQ,cAAc;AAAA,IAC1B;AAAA,IACA,eAAe,CAAC,qCAAqC;AAAA,IACrD,iBAAiB;AAAA,MACb;AAAA,MACA,QAAQ,sDAAsD;AAAA,MAC9D,QAAQ,8BAA8B;AAAA,MACtC,QAAQ,cAAc;AAAA,MACtB,QAAQ,0BAA0B;AAAA,IACtC;AAAA,IACA,eAAe;AAAA,MACX,QAAQ,kBAAkB;AAAA,MAC1B,QAAQ,cAAc;AAAA,MACtB;AAAA,MACA;AAAA,MACA,QAAQ,sBAAsB;AAAA,IAClC;AAAA,IACA,gBAAgB;AAAA,MACZ,QAAQ,kDAAkD;AAAA,MAC1D,QAAQ,cAAc;AAAA,MACtB;AAAA,MACA,QAAQ,kCAAkC;AAAA,MAC1C;AAAA,IACJ;AAAA,IACA,eAAe;AAAA,MACX,QAAQ,8DAA8D;AAAA,MACtE,QAAQ,kDAAkD;AAAA,MAC1D;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,0BAA0B,CAAC,qBAAqB,gBAAgB,kBAAkB,eAAe,YAAY;AAAA,IAC7G,2BAA2B;AAAA,MACvB;AAAA,MACA,QAAQ,kDAAkD;AAAA,MAC1D,QAAQ,8DAA8D;AAAA,MACtE,QAAQ,kDAAkD;AAAA,MAC1D;AAAA,IACJ;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,MACA,QAAQ,cAAc;AAAA,MACtB,QAAQ,sDAAsD;AAAA,MAC9D,QAAQ,sDAAsD;AAAA,MAC9D,QAAQ,sDAAsD;AAAA,IAClE;AAAA,IACA,WAAW,CAAC,QAAQ,8BAA8B,GAAG,kBAAkB,mBAAmB,oBAAoB;AAAA,IAC9G,UAAU;AAAA,MACN;AAAA,MACA,QAAQ,8BAA8B;AAAA,MACtC,QAAQ,0BAA0B;AAAA,MAClC;AAAA,MACA,QAAQ,8BAA8B;AAAA,IAC1C;AAAA,IACA,eAAe;AAAA,MACX,QAAQ,sDAAsD;AAAA,MAC9D,QAAQ,sBAAsB;AAAA,MAC9B;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,0BAA0B;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,qBAAqB;AAAA,MACjB;AAAA,MACA,QAAQ,sBAAsB;AAAA,MAC9B,QAAQ,0BAA0B;AAAA,MAClC;AAAA,MACA,QAAQ,8BAA8B;AAAA,IAC1C;AAAA,IACA,eAAe;AAAA,MACX,QAAQ,kBAAkB;AAAA,MAC1B,QAAQ,kCAAkC;AAAA,MAC1C;AAAA,MACA,QAAQ,kDAAkD;AAAA,MAC1D;AAAA,IACJ;AAAA,IACA,iBAAiB;AAAA,MACb;AAAA,MACA,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,kBAAkB;AAAA,MAC1B,QAAQ,8BAA8B;AAAA,MACtC,QAAQ,0CAA0C;AAAA,IACtD;AAAA,IACA,eAAe;AAAA,MACX,QAAQ,0BAA0B;AAAA,MAClC;AAAA,MACA,QAAQ,0DAA0D;AAAA,MAClE,QAAQ,kDAAkD;AAAA,MAC1D,QAAQ,8DAA8D;AAAA,IAC1E;AAAA,IACA,mBAAmB;AAAA,MACf,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,0CAA0C;AAAA,MAClD,QAAQ,8CAA8C;AAAA,MACtD,QAAQ,8CAA8C;AAAA,IAC1D;AAAA,IACA,UAAU,CAAC,QAAQ,kDAAkD,CAAC;AAAA,IACtE,kBAAkB,CAAC,kBAAkB,oBAAoB,oBAAoB,sBAAsB,WAAW;AAAA,IAC9G,oBAAoB,CAAC,kCAAkC;AAAA,IACvD,wBAAwB;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,cAAc,CAAC,aAAa,mBAAmB,kBAAkB,mBAAmB,yBAAyB;AAAA,IAC7G,iBAAiB;AAAA,MACb,QAAQ,0DAA0D;AAAA,MAClE,QAAQ,8CAA8C;AAAA,MACtD;AAAA,MACA,QAAQ,kBAAkB;AAAA,MAC1B;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,MACV,QAAQ,8CAA8C;AAAA,MACtD,QAAQ,0DAA0D;AAAA,MAClE,QAAQ,kEAAkE;AAAA,MAC1E;AAAA,MACA;AAAA,IACJ;AAAA,IACA,WAAW;AAAA,MACP;AAAA,MACA;AAAA,MACA,QAAQ,cAAc;AAAA,MACtB,QAAQ,8BAA8B;AAAA,MACtC;AAAA,IACJ;AAAA,IACA,uBAAuB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AAAA,IACA,cAAc,CAAC,QAAQ,kEAAkE,CAAC;AAAA,IAC1F,SAAS;AAAA,MACL,QAAQ,0KAC6D;AAAA,MACrE,QAAQ,0KAC6D;AAAA,IACzE;AAAA,IACA,QAAQ;AAAA,MACJ,QAAQ,8CAA8C;AAAA,MACtD,QAAQ,0BAA0B;AAAA,MAClC,QAAQ,8CAA8C;AAAA,MACtD,QAAQ,sBAAsB;AAAA,MAC9B;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL,QAAQ,kBAAkB;AAAA,MAC1B;AAAA,MACA,QAAQ,kCAAkC;AAAA,MAC1C,QAAQ,8BAA8B;AAAA,MACtC,QAAQ,sDAAsD;AAAA,IAClE;AAAA,IACA,SAAS;AAAA,MACL,QAAQ,kDAAkD;AAAA,MAC1D,QAAQ,kCAAkC;AAAA,MAC1C,QAAQ,8CAA8C;AAAA,MACtD;AAAA,MACA;AAAA,IACJ;AAAA,IACA,gBAAgB;AAAA,MACZ;AAAA,MACA,QAAQ,8CAA8C;AAAA,MACtD,QAAQ,sEAAsE;AAAA,MAC9E,QAAQ,8DAA8D;AAAA,MACtE,QAAQ,sBAAsB;AAAA,IAClC;AAAA,IACA,IAAI;AAAA,MACA,QAAQ,0DAA0D;AAAA,MAClE,QAAQ,kEAAkE;AAAA,MAC1E,QAAQ,0EAA0E;AAAA,MAClF,QAAQ,kDAAkD;AAAA,MAC1D;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,MACF,QAAQ,kCAAkC;AAAA,MAC1C,QAAQ,kCAAkC;AAAA,MAC1C,QAAQ,sCAAsC;AAAA,MAC9C;AAAA,MACA;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,MACL;AAAA,MACA,QAAQ,kDAAkD;AAAA,MAC1D,QAAQ,cAAc;AAAA,MACtB;AAAA,MACA;AAAA,IACJ;AAAA,IACA,wBAAwB;AAAA,MACpB;AAAA,MACA;AAAA,MACA,QAAQ,0BAA0B;AAAA,MAClC;AAAA,MACA;AAAA,IACJ;AAAA,EACJ;AACJ;AAUA,SAAS,eAAe,IAAI;AACxB,MAAI,KAAK,OAAO,SAAS,CAAC,IAAI,IAAI,QAAQ,GAAG;AAC7C,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,SAAS,aAAa,cAAc,kBAAkB;AAC1D,QAAI;AACJ,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,cAAI,CAAC,aAAa,GAAG;AACjB,mBAAO,CAAC,GAAc,MAAS;AAAA,UACnC;AACA,oBAAU,WAAW;AACrB,wBAAc,OAAO,KAAK,OAAO;AACjC,0BAAgB,KAAK,CAAC,GAAG,OAAO,MAAM,IAAI,YAAY,IAAI,SAAU,YAAY;AAAE,mBAAO,QAAQ,UAAU;AAAA,UAAG,CAAC,CAAC;AAChH,iBAAO,CAAC,GAAa,oBAAoB,YAAY,CAAC;AAAA,QAC1D,KAAK;AACD,6BAAmB,GAAG,KAAK;AAC3B,cAAI,OAAO;AACP,uBAAW,SAAS,gBAAgB;AAAA,UACxC;AACA,2BAAiB,YAAY,OAAO,SAAU,YAAY;AACtD,gBAAI,YAAY,QAAQ,UAAU;AAClC,gBAAI,eAAe,YAAY,UAAU,IAAI,SAAU,UAAU;AAAE,qBAAO,iBAAiB,QAAQ;AAAA,YAAG,CAAC,CAAC;AACxG,mBAAO,eAAe,UAAU,SAAS;AAAA,UAC7C,CAAC;AACD,yBAAe,KAAK;AACpB,iBAAO,CAAC,GAAc,cAAc;AAAA,MAC5C;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,eAAe;AAEpB,SAAO,SAAS,KAAK,UAAU;AACnC;AACA,SAAS,oBAAoB,WAAW;AACpC,MAAI;AACJ,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI,GAAG,MAAM,UAAU,kBAAkB,GAAG,SAAS,QAAQ;AAC7D,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,cAAI;AACJ,iBAAO,EAAE,cAAc,KAAK;AAC5B,qBAAW,IAAI,MAAM,UAAU,MAAM;AACrC,6BAAmB,CAAC;AAEpB,oBAAU,IAAI;AAGd,eAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACnC,sBAAU,kBAAkB,UAAU,CAAC,CAAC;AACxC,gBAAI,QAAQ,YAAY,UAAU;AAC9B,sBAAQ,KAAK;AAAA,YACjB;AACA,qBAAS,EAAE,cAAc,KAAK;AAE9B,sBAAU,MAAM;AAChB,mBAAO,YAAY,OAAO;AAC1B,iBAAK,YAAY,MAAM;AACvB,qBAAS,CAAC,IAAI;AAAA,UAClB;AACA,aAAG,QAAQ;AAAA,QACf,KAAK;AACD,cAAI,CAAC,CAAC,EAAE,KAAM,QAAO,CAAC,GAAa,CAAC;AACpC,iBAAO,CAAC,GAAa,KAAK,EAAE,CAAC;AAAA,QACjC,KAAK;AACD,aAAG,KAAK;AACR,iBAAO,CAAC,GAAa,CAAC;AAAA,QAC1B,KAAK;AACD,YAAE,KAAK,YAAY,IAAI;AACvB,cAAI;AAEA,iBAAK,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACnC,kBAAI,CAAC,SAAS,CAAC,EAAE,cAAc;AAC3B,iCAAiB,UAAU,CAAC,CAAC,IAAI;AAAA,cACrC;AAAA,YACJ;AAAA,UACJ,UACA;AAEI,aAAC,KAAK,KAAK,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,IAAI;AAAA,UACnF;AACA,iBAAO,CAAC,GAAc,gBAAgB;AAAA,MAC9C;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,SAAS,UAAU,SAAS;AACxB,UAAQ,MAAM,YAAY,WAAW,SAAS,WAAW;AAC7D;AACA,SAAS,WAAW,SAAS,kBAAkB;AAC3C,MAAI,UAAU;AACd,WAAS,KAAK,GAAG,KAAK,OAAO,KAAK,OAAO,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC9D,QAAI,aAAa,GAAG,EAAE;AACtB,eAAW,KAAK,OAAO,YAAY,GAAG;AACtC,aAAS,KAAK,GAAG,KAAK,QAAQ,UAAU,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC7D,UAAI,WAAW,GAAG,EAAE;AACpB,iBAAW,OAAO,OAAO,iBAAiB,QAAQ,IAAI,OAAO,MAAM,GAAG,EAAE,OAAO,QAAQ;AAAA,IAC3F;AAAA,EACJ;AAGA,UAAQ,IAAI,GAAG,OAAO,SAAS,OAAO,CAAC;AAC3C;AAKA,SAAS,gBAAgB;AAErB,WAAS,KAAK,GAAG,KAAK,CAAC,WAAW,MAAM,MAAM,GAAG,KAAK,GAAG,QAAQ,MAAM;AACnE,QAAI,QAAQ,GAAG,EAAE;AACjB,QAAI,WAAW,iBAAiB,OAAO,OAAO,GAAG,CAAC,EAAE,SAAS;AACzD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAKA,SAAS,oBAAoB;AACzB,MAAI,YAAY,UAAU,GAAG;AACzB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,MAAM,GAAG;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,WAAW,qBAAqB,OAAO,OAAO,GAAG,CAAC,EAAE;AAC/D;AAKA,SAAS,kBAAkB;AACvB,MAAI,YAAY,QAAQ,GAAG;AACvB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,MAAM,GAAG;AACrB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,WAAW,mBAAmB,OAAO,OAAO,GAAG,CAAC,EAAE;AAC7D;AAEA,IAAI,kBAAkB;AAQtB,SAAS,qBAAqB;AAC1B,MAAI,CAAC,WAAW,qBAAqB,EAAE,SAAS;AAE5C,WAAO;AAAA,EACX;AAGA,WAAS,IAAI,GAAG,KAAK,iBAAiB,EAAE,GAAG;AACvC,QAAI,WAAW,oBAAoB,OAAO,GAAG,GAAG,CAAC,EAAE,SAAS;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,gBAAgB;AACpC;AAMA,SAAS,wBAAwB;AAC7B,MAAI,YAAY,eAAe,GAAG;AAC9B,WAAO;AAAA,EACX;AAGA,MAAI,YAAY,MAAM,KAAK,YAAY,MAAM,GAAG;AAC5C,WAAO;AAAA,EACX;AACA,MAAI,YAAY,KAAK,KAAK,YAAY,MAAM,GAAG;AAC3C,WAAO;AAAA,EACX;AACA,MAAI,YAAY,QAAQ,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,WAAW,sBAAsB,OAAO,OAAO,GAAG,CAAC,EAAE;AAChE;AAKA,SAAS,kBAAkB;AACvB,MAAI,YAAY,QAAQ,GAAG;AACvB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,eAAe,GAAG;AAC9B,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,YAAY,OAAO;AACxB,SAAO,WAAW,4BAA4B,OAAO,OAAO,GAAG,CAAC,EAAE;AACtE;AAKA,SAAS,QAAQ;AACb,MAAI,UAAU,MAAM,GAAG;AACnB,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU,GAAG;AACvB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,UAAU,OAAO;AACtB,SAAO,WAAW,mBAAmB,OAAO,OAAO,GAAG,CAAC,EAAE;AAC7D;AAEA,IAAI,IAAI;AACR,IAAI,aAAa,WAAY;AAAE,SAAO;AAAG;AAKzC,SAAS,qBAAqB;AAE1B,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,QAAQ,EAAE,SAAS;AACvB,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,QAAQ,EAAE,SAAS;AACvB,MAAI,QAAQ,EAAE,SAAS;AACvB,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,MAAM,EAAE,OAAO;AACnB,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,MAAM,EAAE,OAAO;AACnB,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,MAAM,EAAE,OAAO;AACnB,MAAI,OAAO,EAAE,QAAQ;AACrB,MAAI,MAAM,EAAE,OAAO;AACnB,MAAI,QAAQ,EAAE,SAAS;AACvB,MAAI,QAAQ,EAAE,SAAS;AAEvB,MAAI,QAAQ,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,EAAE,IAAI,KAAK;AAAA,EAAG;AAC1D,MAAI,UAAU,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,QAAQ,EAAE,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAAG;AAClF,MAAI,UAAU,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,QAAQ,EAAE,KAAK,QAAQ,QAAQ,CAAC,CAAC;AAAA,EAAG;AAClF,MAAI,UAAU,SAAU,OAAO;AAAE,WAAO,EAAE,KAAK,IAAI,UAAU,IAAI,MAAM,IAAI;AAAA,EAAG;AAC9E,MAAI,SAAS,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI;AAAA,EAAG;AAC5E,MAAI,SAAS,SAAU,OAAO;AAAE,YAAQ,EAAE,IAAI,KAAK,IAAI,IAAI,EAAE,IAAI,KAAK,KAAK;AAAA,EAAG;AAC9E,MAAI,UAAU,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,KAAK,IAAI;AAAA,EAAG;AAC1D,MAAI,SAAS,SAAU,OAAO;AAAE,YAAQ,EAAE,IAAI,IAAI,KAAK,IAAI,MAAM,EAAE,IAAI,IAAI,KAAK,IAAI;AAAA,EAAI;AACxF,MAAI,UAAU,SAAU,OAAO;AAAE,WAAO,EAAE,IAAI,IAAI,KAAK;AAAA,EAAG;AAE1D,SAAO;AAAA,IACH,MAAM,KAAK,mBAAoB;AAAA,IAC/B,OAAO,MAAM,KAAK;AAAA,IAClB,SAAS,QAAQ,KAAK;AAAA,IACtB,MAAM,KAAK,mBAAoB;AAAA,IAC/B,OAAO,MAAM,CAAC;AAAA,IACd,SAAS,QAAQ,CAAC;AAAA,IAClB,OAAO,MAAM,GAAG;AAAA,IAChB,SAAS,QAAQ,GAAG;AAAA,IACpB,MAAM,KAAK,GAAG;AAAA,IACd,KAAK,IAAI,MAAM;AAAA,IACf,MAAM,KAAK,CAAC;AAAA,IACZ,QAAQ,OAAO,CAAC;AAAA,IAChB,KAAK,IAAI,eAAe;AAAA,IACxB,MAAM,KAAK,CAAC;AAAA,IACZ,QAAQ,OAAO,CAAC;AAAA,IAChB,KAAK,IAAI,MAAM;AAAA,IACf,MAAM,KAAK,CAAC;AAAA,IACZ,QAAQ,OAAO,CAAC;AAAA,IAChB,KAAK,IAAI,CAAC;AAAA,IACV,OAAO,MAAM,CAAC;AAAA,IACd,SAAS,QAAQ,CAAC;AAAA,IAClB,OAAO,MAAM,EAAE;AAAA,IACf,SAAS,QAAQ,EAAE;AAAA,IACnB,OAAO,MAAM,IAAI;AAAA,EACrB;AACJ;AAMA,IAAI,cAAc;AAIlB,IAAI,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,SAAS,CAAC;AAAA;AAAA,EAEV,OAAO,CAAC,EAAE,MAAM,qBAAqB,CAAC;AAAA;AAAA,EAEtC,OAAO,CAAC,EAAE,YAAY,QAAQ,CAAC;AAAA;AAAA,EAE/B,MAAM,CAAC,EAAE,YAAY,aAAa,CAAC;AAAA;AAAA,EAEnC,MAAM,CAAC,EAAE,YAAY,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,KAAK,CAAC,EAAE,UAAU,MAAM,CAAC;AAAA;AAAA,EAEzB,QAAQ,CAAC,EAAE,YAAY,YAAY,CAAC;AACxC;AAUA,SAAS,qBAAqB;AAC1B,SAAO,iBAAiB,SAAUG,WAAU,WAAW;AACnD,QAAI,WAAW,CAAC;AAChB,QAAI,QAAQ,CAAC;AAGb,aAAS,KAAK,GAAG,KAAK,OAAO,KAAK,OAAO,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC9D,UAAI,MAAM,GAAG,EAAE;AACf,UAAI,KAAK,QAAQ,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,QAAQ,OAAO,SAAS,CAAC,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,OAAO,SAAS,cAAc;AACrH,UAAI,UAAUA,UAAS,cAAc,MAAM;AAC3C,cAAQ,cAAc;AACtB,cAAQ,MAAM,aAAa;AAC3B,eAAS,KAAK,GAAG,KAAK,OAAO,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC5D,YAAI,SAAS,GAAG,EAAE;AAClB,YAAI,QAAQ,MAAM,MAAM;AACxB,YAAI,UAAU,QAAW;AACrB,kBAAQ,MAAM,MAAM,IAAI;AAAA,QAC5B;AAAA,MACJ;AACA,eAAS,GAAG,IAAI;AAChB,gBAAU,YAAYA,UAAS,cAAc,IAAI,CAAC;AAClD,gBAAU,YAAY,OAAO;AAAA,IACjC;AAEA,aAAS,KAAK,GAAG,KAAK,OAAO,KAAK,OAAO,GAAG,KAAK,GAAG,QAAQ,MAAM;AAC9D,UAAI,MAAM,GAAG,EAAE;AACf,YAAM,GAAG,IAAI,SAAS,GAAG,EAAE,sBAAsB,EAAE;AAAA,IACvD;AACA,WAAO;AAAA,EACX,CAAC;AACL;AAMA,SAAS,iBAAiB,QAAQ,kBAAkB;AAChD,MAAI,qBAAqB,QAAQ;AAAE,uBAAmB;AAAA,EAAM;AA6C5D,SAAO,WAAW,SAAU,GAAG,cAAc;AACzC,QAAI,iBAAiB,aAAa;AAClC,QAAI,aAAa,eAAe;AAChC,QAAI,YAAY,WAAW;AAC3B,cAAU,QAAQ,GAAG,OAAO,kBAAkB,IAAI;AAClD,cAAU,uBAAuB,UAAU,iBAAiB;AAE5D,QAAI,WAAW,GAAG;AACd,iBAAW,MAAM,OAAO,GAAG,OAAO,IAAI,aAAa,gBAAgB;AAAA,IACvE,WACS,SAAS,GAAG;AACjB,iBAAW,MAAM,OAAO;AAAA,IAC5B;AAEA,QAAI,cAAc,eAAe,cAAc,KAAK;AACpD,gBAAY,cAAc,cAAc,CAAC,GAAG,MAAO,mBAAmB,MAAO,CAAC,GAAG,IAAI,EAAE,IAAI,WAAY;AAAE,aAAO;AAAA,IAAQ,CAAC,EAAE,KAAK,GAAG;AACnI,eAAW,YAAY,WAAW;AAClC,WAAO,OAAO,gBAAgB,UAAU;AAAA,EAC5C,GAAG,iGAAiG;AACxG;AAKA,SAAS,eAAe;AACpB,MAAI;AACJ,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,MAAI,MAAM,KAAK,OAAO,WAAW,OAAO,OAAO,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,oBAAoB;AAClH,MAAI,MAAM,kBAAkB,IAAI;AAC5B,QAAI,YAAY,GAAG,aAAa,2BAA2B;AAC3D,QAAI,WAAW;AACX,aAAO;AAAA,QACH,SAAS,GAAG,aAAa,UAAU,qBAAqB,KAAK,IAAI,SAAS;AAAA,QAC1E,WAAW,GAAG,aAAa,UAAU,uBAAuB,KAAK,IAAI,SAAS;AAAA,MAClF;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,qBAAqB;AAC1B,SAAO,UAAU;AACrB;AAUA,SAAS,kBAAkB;AACvB,MAAI,IAAI,IAAI,aAAa,CAAC;AAC1B,MAAI,KAAK,IAAI,WAAW,EAAE,MAAM;AAChC,IAAE,CAAC,IAAI;AACP,IAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;AACjB,SAAO,GAAG,CAAC;AACf;AAWA,IAAI,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,OAAO;AAAA,EACP,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,OAAO;AAAA,EACP,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,cAAc;AAAA,EACd,UAAU;AAAA,EACV,UAAU;AAAA,EACV,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,eAAe;AAAA,EACf,KAAK;AAAA,EACL,MAAM;AAAA,EACN,WAAW;AAAA,EACX,kBAAkB;AAAA,EAClB,cAAc;AAClB;AAKA,SAAS,mBAAmB,SAAS;AACjC,SAAO,YAAY,SAAS,SAAS,CAAC,CAAC;AAC3C;AAEA,IAAI,kBAAkB;AACtB,SAAS,cAAc,YAAY;AAC/B,MAAI,sBAAsB,uBAAuB,UAAU;AAC3D,MAAI,qBAAqB,yBAAyB,mBAAmB;AACrE,SAAO,EAAE,OAAO,qBAAqB,SAAS,gBAAgB,QAAQ,OAAO,GAAG,OAAO,kBAAkB,CAAC,EAAE;AAChH;AACA,SAAS,uBAAuB,YAAY;AAIxC,MAAI,UAAU,GAAG;AACb,WAAO;AAAA,EACX;AAEA,MAAI,SAAS,GAAG;AACZ,WAAO,gBAAgB,IAAI,MAAM;AAAA,EACrC;AACA,MAAI,WAAW,WAAW,SAAS,SAAS;AAE5C,MAAI,OAAO,KAAK,QAAQ,GAAG;AAKvB,WAAO;AAAA,EACX;AAEA,MAAI,OAAO,KAAK,QAAQ,GAAG;AAKvB,WAAO;AAAA,EACX;AAEA,SAAO;AACX;AACA,SAAS,yBAAyB,qBAAqB;AACnD,SAAO,MAAM,OAAO,OAAO,qBAAqB,IAAM;AAC1D;AAEA,SAAS,4BAA4B,YAAY;AAC7C,MAAI,SAAS;AACb,WAAS,KAAK,GAAG,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,GAAG,KAAK,GAAG,QAAQ,MAAM;AACxE,QAAI,eAAe,GAAG,EAAE;AACxB,QAAI,YAAY,WAAW,YAAY;AACvC,QAAI,QAAQ,UAAU,QAAQ,UAAU,KAAK,UAAU,UAAU,KAAK;AACtE,cAAU,GAAG,OAAO,SAAS,MAAM,EAAE,EAAE,OAAO,aAAa,QAAQ,aAAa,MAAM,GAAG,GAAG,EAAE,OAAO,KAAK;AAAA,EAC9G;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,YAAY;AACzC,SAAO,KAAK,UAAU,YAAY,SAAU,MAAM,OAAO;AACrD,QAAI,iBAAiB,OAAO;AACxB,aAAO,cAAc,KAAK;AAAA,IAC9B;AACA,WAAO;AAAA,EACX,GAAG,CAAC;AACR;AACA,SAAS,eAAe,YAAY;AAChC,SAAO,WAAW,4BAA4B,UAAU,CAAC;AAC7D;AAKA,SAAS,kBAAkB,YAAY;AACnC,MAAI;AAEJ,MAAI,aAAa,cAAc,UAAU;AAEzC,SAAO;AAAA,IACH,IAAI,YAAY;AACZ,UAAI,mBAAmB,QAAW;AAC9B,yBAAiB,eAAe,KAAK,UAAU;AAAA,MACnD;AACA,aAAO;AAAA,IACX;AAAA,IACA,IAAI,UAAU,WAAW;AACrB,uBAAiB;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAOA,SAAS,kBAAkB,eAAe;AACtC,MAAI,kBAAkB,QAAQ;AAAE,oBAAgB;AAAA,EAAI;AAEpD,SAAO,+BAA+B,eAAe,gBAAgB,CAAC;AAC1E;AAQA,SAAS,UAAU,eAAe,OAAO;AACrC,MAAI,eAAe,KAAK,IAAI;AAC5B,SAAO;AAAA,IACH,KAAK,SAAU,SAAS;AACpB,aAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,YAAI,WAAW,YAAY;AAC3B,eAAO,YAAY,MAAM,SAAU,IAAI;AACnC,kBAAQ,GAAG,OAAO;AAAA,YACd,KAAK;AACD,0BAAY,KAAK,IAAI;AACrB,qBAAO,CAAC,GAAa,cAAc,CAAC;AAAA,YACxC,KAAK;AACD,2BAAa,GAAG,KAAK;AACrB,uBAAS,kBAAkB,UAAU;AACrC,kBAAI,UAAU,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,QAAQ;AAG5E,wBAAQ,IAAI,+DAA+D,OAAO,OAAO,SAAS,eAAe,EAAE,OAAO,UAAU,WAAW,2BAA2B,EAAE,OAAO,YAAY,cAAc,eAAe,EAAE,OAAO,OAAO,WAAW,gBAAgB,EAAE,OAAO,wBAAwB,UAAU,GAAG,OAAO,CAAC;AAAA,cACjU;AACA,qBAAO,CAAC,GAAc,MAAM;AAAA,UACpC;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AAAA,EACJ;AACJ;AAIA,SAAS,UAAU;AAEf,MAAI,OAAO,cAAc,KAAK,OAAO,KAAK,MAAO;AAC7C;AAAA,EACJ;AACA,MAAI;AACA,QAAI,UAAU,IAAI,eAAe;AACjC,YAAQ,KAAK,OAAO,0CAA0C,OAAO,SAAS,iBAAiB,GAAG,IAAI;AACtG,YAAQ,KAAK;AAAA,EACjB,SACO,OAAO;AAGV,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AAIA,SAAS,KAAK,IAAI;AACd,MAAI,KAAK,OAAO,SAAS,CAAC,IAAI,IAAI,gBAAgB,GAAG,eAAe,QAAQ,GAAG,OAAO,KAAK,GAAG,YAAY,aAAa,OAAO,SAAS,OAAO;AAC9I,SAAO,UAAU,MAAM,QAAQ,QAAQ,WAAY;AAC/C,QAAI;AACJ,WAAO,YAAY,MAAM,SAAU,IAAI;AACnC,cAAQ,GAAG,OAAO;AAAA,QACd,KAAK;AACD,cAAI,YAAY;AACZ,oBAAQ;AAAA,UACZ;AACA,iBAAO,CAAC,GAAa,kBAAkB,aAAa,CAAC;AAAA,QACzD,KAAK;AACD,aAAG,KAAK;AACR,0BAAgB,mBAAmB,EAAE,MAAa,CAAC;AACnD,iBAAO,CAAC,GAAc,UAAU,eAAe,KAAK,CAAC;AAAA,MAC7D;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AAIA,IAAI,QAAQ,EAAE,MAAY,gBAAgC,wBAAiD;AAG3G,IAAI,mBAAmB;", "names": ["__assign", "_i", "sources", "index", "_a", "_b", "document", "font"]}