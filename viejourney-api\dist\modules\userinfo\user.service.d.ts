import { HttpStatus } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { FilterUserDto } from 'src/common/dtos/filter-userinfo.dto';
import { PaginationDto } from 'src/common/dtos/pagination-userlist.dto';
import { Account } from 'src/common/entities/account.entity';
import { Asset } from 'src/common/entities/asset.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { Blog } from 'src/infrastructure/database/blog.schema';
import { Trip } from 'src/infrastructure/database/trip.schema';
import { AssetsService } from '../assets/assets.service';
export declare class UserService {
    private readonly userInfosModel;
    private readonly accountModel;
    private readonly assetModel;
    private readonly tripModel;
    private readonly blogModel;
    private readonly assetsService;
    constructor(userInfosModel: Model<UserInfos>, accountModel: Model<Account>, assetModel: Model<Asset>, tripModel: Model<Trip>, blogModel: Model<Blog>, assetsService: AssetsService);
    getAllUser(filter?: FilterUserDto, pagination?: PaginationDto): Promise<{
        status: string;
        message: string;
        data: {
            users: Array<{
                userId: string;
                accountId: string;
                email: string;
                userName: string;
                role: string;
                status: string;
                phone: string;
                address: string;
                createdAt: Date;
            }>;
            totalPages?: number;
            currentPage?: number;
            pageSize?: number;
            totalItems: number;
        };
    }>;
    getUserByID(id: string): Promise<{
        avatar: string | null;
        _id: Types.ObjectId;
        userId: Account;
        fullName: string;
        dob: Date;
        phone: string;
        address: string;
        lastLoginAt: Date;
        flaggedCount: number;
        banReason: string | null;
        bannedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        location: string;
        format: string;
        file_size: string;
        dimensions: string;
        __v: number;
    }>;
    updateUserAvatar(id: string, file: Express.Multer.File): Promise<(import("mongoose").Document<unknown, {}, UserInfos, {}> & UserInfos & Required<{
        _id: Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
    updateUserInfo(id: string, updateUserInfoDto: any): Promise<UserInfos>;
    deleteUserInfo(id: string): Promise<HttpStatus>;
    updateUserRole(userInfoId: string, role: string): Promise<{
        status: string;
        message: string;
        data: {
            userId: string;
            accountId: string;
            email: string;
            userName: string;
            role: string;
            status: string;
        };
    }>;
    getUserDetails(userId: string, email: string): Promise<{
        destinations: {
            name: string;
            location: import("mongoose").FlattenMaps<{
                lat: number;
                lng: number;
            }>;
        }[];
        blogCount: number;
        likeCount: number;
        tripCount: number;
    }>;
}
