import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/material/Dialog/DialogContext.js
var React = __toESM(require_react());
var DialogContext = React.createContext({});
if (true) {
  DialogContext.displayName = "DialogContext";
}
var DialogContext_default = DialogContext;

export {
  DialogContext_default
};
//# sourceMappingURL=chunk-UFJEMFUK.js.map
