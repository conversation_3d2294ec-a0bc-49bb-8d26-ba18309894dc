{"version": 3, "sources": ["../../@tiptap/extension-text-align/src/text-align.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\n\nexport interface TextAlignOptions {\n  /**\n   * The types where the text align attribute can be applied.\n   * @default []\n   * @example ['heading', 'paragraph']\n   */\n  types: string[],\n\n  /**\n   * The alignments which are allowed.\n   * @default ['left', 'center', 'right', 'justify']\n   * @example ['left', 'right']\n   */\n  alignments: string[],\n\n  /**\n   * The default alignment.\n   * @default null\n   * @example 'center'\n   */\n  defaultAlignment: string | null,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    textAlign: {\n      /**\n       * Set the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.setTextAlign('left')\n       */\n      setTextAlign: (alignment: string) => ReturnType,\n      /**\n       * Unset the text align attribute\n       * @example editor.commands.unsetTextAlign()\n       */\n      unsetTextAlign: () => ReturnType,\n      /**\n       * Toggle the text align attribute\n       * @param alignment The alignment\n       * @example editor.commands.toggleTextAlign('right')\n       */\n      toggleTextAlign: (alignment: string) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to align text.\n * @see https://www.tiptap.dev/api/extensions/text-align\n */\nexport const TextAlign = Extension.create<TextAlignOptions>({\n  name: 'textAlign',\n\n  addOptions() {\n    return {\n      types: [],\n      alignments: ['left', 'center', 'right', 'justify'],\n      defaultAlignment: null,\n    }\n  },\n\n  addGlobalAttributes() {\n    return [\n      {\n        types: this.options.types,\n        attributes: {\n          textAlign: {\n            default: this.options.defaultAlignment,\n            parseHTML: element => {\n              const alignment = element.style.textAlign\n\n              return this.options.alignments.includes(alignment) ? alignment : this.options.defaultAlignment\n            },\n            renderHTML: attributes => {\n              if (!attributes.textAlign) {\n                return {}\n              }\n\n              return { style: `text-align: ${attributes.textAlign}` }\n            },\n          },\n        },\n      },\n    ]\n  },\n\n  addCommands() {\n    return {\n      setTextAlign: (alignment: string) => ({ commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        return this.options.types\n          .map(type => commands.updateAttributes(type, { textAlign: alignment }))\n          .every(response => response)\n      },\n\n      unsetTextAlign: () => ({ commands }) => {\n        return this.options.types\n          .map(type => commands.resetAttributes(type, 'textAlign'))\n          .every(response => response)\n      },\n\n      toggleTextAlign: alignment => ({ editor, commands }) => {\n        if (!this.options.alignments.includes(alignment)) {\n          return false\n        }\n\n        if (editor.isActive({ textAlign: alignment })) {\n          return commands.unsetTextAlign()\n        }\n        return commands.setTextAlign(alignment)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-l': () => this.editor.commands.setTextAlign('left'),\n      'Mod-Shift-e': () => this.editor.commands.setTextAlign('center'),\n      'Mod-Shift-r': () => this.editor.commands.setTextAlign('right'),\n      'Mod-Shift-j': () => this.editor.commands.setTextAlign('justify'),\n    }\n  },\n})\n"], "mappings": ";;;;;;;;;AAqDa,IAAA,YAAY,UAAU,OAAyB;EAC1D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO,CAAA;MACP,YAAY,CAAC,QAAQ,UAAU,SAAS,SAAS;MACjD,kBAAkB;;;EAItB,sBAAmB;AACjB,WAAO;MACL;QACE,OAAO,KAAK,QAAQ;QACpB,YAAY;UACV,WAAW;YACT,SAAS,KAAK,QAAQ;YACtB,WAAW,aAAU;AACnB,oBAAM,YAAY,QAAQ,MAAM;AAEhC,qBAAO,KAAK,QAAQ,WAAW,SAAS,SAAS,IAAI,YAAY,KAAK,QAAQ;;YAEhF,YAAY,gBAAa;AACvB,kBAAI,CAAC,WAAW,WAAW;AACzB,uBAAO,CAAA;;AAGT,qBAAO,EAAE,OAAO,eAAe,WAAW,SAAS,GAAE;;UAExD;QACF;MACF;;;EAIL,cAAW;AACT,WAAO;MACL,cAAc,CAAC,cAAsB,CAAC,EAAE,SAAQ,MAAM;AACpD,YAAI,CAAC,KAAK,QAAQ,WAAW,SAAS,SAAS,GAAG;AAChD,iBAAO;;AAGT,eAAO,KAAK,QAAQ,MACjB,IAAI,UAAQ,SAAS,iBAAiB,MAAM,EAAE,WAAW,UAAS,CAAE,CAAC,EACrE,MAAM,cAAY,QAAQ;;MAG/B,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,KAAK,QAAQ,MACjB,IAAI,UAAQ,SAAS,gBAAgB,MAAM,WAAW,CAAC,EACvD,MAAM,cAAY,QAAQ;;MAG/B,iBAAiB,eAAa,CAAC,EAAE,QAAQ,SAAQ,MAAM;AACrD,YAAI,CAAC,KAAK,QAAQ,WAAW,SAAS,SAAS,GAAG;AAChD,iBAAO;;AAGT,YAAI,OAAO,SAAS,EAAE,WAAW,UAAS,CAAE,GAAG;AAC7C,iBAAO,SAAS,eAAc;;AAEhC,eAAO,SAAS,aAAa,SAAS;;;;EAK5C,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,MAAM;MAC7D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,QAAQ;MAC/D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,OAAO;MAC9D,eAAe,MAAM,KAAK,OAAO,SAAS,aAAa,SAAS;;;AAGrE,CAAA;", "names": []}