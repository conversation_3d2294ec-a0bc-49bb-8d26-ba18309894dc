export declare class DashboardQueryDto {
    timeRange?: string;
}
export declare class DashboardAnalyticsDto {
    totalUsers: number;
    totalTrips: number;
    totalBlogs: number;
    totalInteractions: number;
    usersToday: number;
    tripsToday: number;
    blogsToday: number;
    interactionsToday: number;
    userGrowthData: Array<{
        month: string;
        users: number;
        newUsers: number;
    }>;
    contentCreationData: Array<{
        month: string;
        blogs: number;
        trips: number;
    }>;
    engagementData: Array<{
        day: string;
        likes: number;
        comments: number;
        shares: number;
    }>;
    userActivityData: Array<{
        id: number;
        value: number;
        label: string;
        color: string;
    }>;
    contentStatusData: Array<{
        id: number;
        value: number;
        label: string;
        color: string;
    }>;
    topLocationsData: Array<{
        location: string;
        trips: number;
        blogs: number;
    }>;
}
