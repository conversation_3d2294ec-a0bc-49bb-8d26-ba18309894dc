import { HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { AuthService } from './auth.service';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(req: {
        email: string;
        password: string;
    }, res: Response): Promise<{
        userId: string;
        accessToken: string;
    }>;
    register(req: {
        email: string;
        password: string;
    }): Promise<HttpStatus>;
    refresh(req: Request, res: Response): Promise<{
        accessToken: string;
    }>;
    logout(req: Request, res: Response): Promise<HttpStatus>;
    verify(token: string): Promise<HttpStatus>;
    resendVerificationEmail(body: {
        email: string;
    }, res: Response): Promise<void>;
    sendForgotPasswordEmail(body: {
        email: string;
    }, res: Response): Promise<void>;
    forgotPassword(body: {
        token: string;
        password: string;
    }, res: Response): Promise<HttpStatus>;
    googleLogin(): {
        msg: string;
    };
    googleAuth(req: Request, res: Response): Promise<void>;
    validateToken(body: {
        token: string;
    }, res: Response): Promise<{
        userId: string;
        status: import("../../common/enums/status.enum").Status;
    } | null | undefined>;
}
