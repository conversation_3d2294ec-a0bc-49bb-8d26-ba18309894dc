import { Request } from 'express';
import { BlogService } from './blog.service';
import { PaginationDto } from 'src/common/dtos/pagination-userlist.dto';
import { CreateBlogDto } from 'src/common/dtos/create-blog.dto';
import { StartBlogDto } from 'src/common/dtos/start-blog.dto';
import { UpdateBlogDraftDto } from 'src/common/dtos/update-blog-draft.dto';
export declare class BlogController {
    private readonly blogService;
    constructor(blogService: BlogService);
    getAllBlogs(paginationDto: PaginationDto): Promise<{
        data: never[];
        totalPages: number;
        currentPage: number;
        pageSize: number;
        totalItems: number;
        message: string;
        Total_Blogs?: undefined;
    } | {
        data: {
            _id: string;
            title: string;
            createdBy: {
                _id: import("mongoose").Types.ObjectId;
                fullName: string;
                email: string;
            };
            avatarUser: string | null;
            summary: string | undefined;
            destination: string | null;
            viewCount: number;
            likeCount: number;
            commentCount: number;
            status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
            flags: number;
            createdAt: Date | undefined;
            updatedAt: Date | undefined;
        }[];
        Total_Blogs: number;
        currentPage: number;
        pageSize: number;
        totalPages: number;
        totalItems?: undefined;
        message?: undefined;
    }>;
    getAllApprovedBlogs(page?: string, limit?: string, search?: string): Promise<{
        status: string;
        data: {
            blogs: {
                author: {
                    _id: string;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                metrics: {
                    viewCount: number;
                    likeCount: number;
                    commentCount: number;
                };
                createdAt: Date | undefined;
                updatedAt: Date | undefined;
                createdBy: {
                    _id: import("mongoose").Types.ObjectId | undefined;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                updatedBy: {
                    _id: import("mongoose").Types.ObjectId | undefined;
                    fullName: string;
                    email: string;
                    avatar: string | null;
                };
                _id: string;
                title: string;
                slug?: string;
                content: string;
                summary?: string;
                tags?: string[];
                coverImage?: string;
                destination?: string | null;
                places?: import("../../common/entities/blog.entity").PlaceData[];
                likes?: import("mongoose").Types.ObjectId[];
                status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
                flags?: {
                    userId: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId;
                    reason: string;
                    date: Date;
                }[];
                comments?: import("../../common/entities/comment.entity").Comment[] | import("mongoose").Types.ObjectId[];
                $locals: Record<string, unknown>;
                $op: "save" | "validate" | "remove" | null;
                $where: Record<string, unknown>;
                baseModelName?: string;
                collection: import("mongoose").Collection;
                db: import("mongoose").Connection;
                errors?: import("mongoose").Error.ValidationError;
                id?: any;
                isNew: boolean;
                schema: import("mongoose").Schema;
                __v: number;
            }[];
            pagination: {
                currentPage: number;
                totalPages: number;
                totalItems: number;
                itemsPerPage: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
        };
    }>;
    getRelatedBlogs(tags: string[], blogId: string, destination: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/blog.entity").Blog, {}> & import("../../common/entities/blog.entity").Blog & Required<{
        _id: string;
    }> & {
        __v: number;
    })[]>;
    getUserBlogs(req: Request, status?: string): Promise<{
        blogs: (import("mongoose").Document<unknown, {}, import("../../common/entities/blog.entity").Blog, {}> & import("../../common/entities/blog.entity").Blog & Required<{
            _id: string;
        }> & {
            __v: number;
        })[];
        total: number;
    }>;
    getBlogById(id: string, req: Request): Promise<{
        createdBy: {
            email: string;
            totalBlogs: number;
            likesCount: number;
            _id?: import("mongoose").Types.ObjectId | undefined;
            userId?: import("../../common/entities/account.entity").Account | undefined;
            fullName?: string | undefined;
            dob?: Date | undefined;
            phone?: string | undefined;
            address?: string | undefined;
            avatar?: import("../../common/entities/asset.entity").Asset | undefined;
            lastLoginAt?: Date | undefined;
            flaggedCount?: number | undefined;
            banReason?: string | null | undefined;
            bannedAt?: Date | null | undefined;
            createdAt?: Date | undefined;
            updatedAt?: Date | undefined;
            location?: string | undefined;
            format?: string | undefined;
            file_size?: string | undefined;
            dimensions?: string | undefined;
        };
        _id?: string | undefined;
        title?: string | undefined;
        slug?: string;
        content?: string | undefined;
        summary?: string;
        tags?: string[];
        coverImage?: string;
        destination?: string | null;
        places?: import("../../common/entities/blog.entity").PlaceData[];
        updatedBy?: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId | undefined;
        likes?: import("mongoose").Types.ObjectId[];
        status?: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED" | undefined;
        flags?: {
            userId: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId;
            reason: string;
            date: Date;
        }[];
        metrics?: {
            viewCount: number;
            likeCount: number;
            commentCount: number;
        };
        comments?: import("../../common/entities/comment.entity").Comment[] | import("mongoose").Types.ObjectId[];
        createdAt?: Date;
        updatedAt?: Date;
        $locals?: Record<string, unknown> | undefined;
        $op?: "remove" | "save" | "validate" | null | undefined;
        $where?: Record<string, unknown> | undefined;
        baseModelName?: string;
        collection?: import("mongoose").Collection<import("bson").Document> | undefined;
        db?: import("mongoose").Connection | undefined;
        errors?: import("mongoose").Error.ValidationError;
        id?: any;
        isNew?: boolean | undefined;
        schema?: import("mongoose").Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
            [x: string]: unknown;
        }, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }>, {}> & import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        }> | undefined;
        __v?: number | undefined;
    }>;
    startBlog(dto: StartBlogDto, coverImage: Express.Multer.File, req: Request): Promise<{
        blogId: string;
        title: string;
        destination: string | null | undefined;
        coverImage: string | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        message: string;
    }>;
    getDraftBlog(id: string, req: Request): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("../../common/entities/blog.entity").PlaceData[];
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
    }>;
    publishBlog(id: string, req: Request): Promise<{
        blogId: string;
        title: string;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        publishedAt: Date | undefined;
        message: string;
    }>;
    getPublishedBlog(id: string, req: Request): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("../../common/entities/blog.entity").PlaceData[];
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        metrics: {
            viewCount: number;
            likeCount: number;
            commentCount: number;
        };
        createdAt: Date | undefined;
        updatedAt: Date | undefined;
        message: string;
    }>;
    updateBlogDraftById(id: string, dto: UpdateBlogDraftDto, coverImage: Express.Multer.File, req: Request): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("../../common/entities/blog.entity").PlaceData[] | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        updatedAt: Date | undefined;
        message: string;
    }>;
    editPublishedBlog(id: string, dto: UpdateBlogDraftDto, coverImage: Express.Multer.File, req: Request): Promise<{
        _id: string;
        title: string;
        content: string;
        summary: string | undefined;
        tags: string[] | undefined;
        coverImage: string | undefined;
        destination: string | null | undefined;
        places: import("../../common/entities/blog.entity").PlaceData[] | undefined;
        status: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED";
        updatedAt: Date | undefined;
        message: string;
    }>;
    getManagerBlogById(id: string): Promise<{
        createdBy: {
            email: string;
            totalBlogs: number;
            likesCount: number;
            _id?: import("mongoose").Types.ObjectId | undefined;
            userId?: import("../../common/entities/account.entity").Account | undefined;
            fullName?: string | undefined;
            dob?: Date | undefined;
            phone?: string | undefined;
            address?: string | undefined;
            avatar?: import("../../common/entities/asset.entity").Asset | undefined;
            lastLoginAt?: Date | undefined;
            flaggedCount?: number | undefined;
            banReason?: string | null | undefined;
            bannedAt?: Date | null | undefined;
            createdAt?: Date | undefined;
            updatedAt?: Date | undefined;
            location?: string | undefined;
            format?: string | undefined;
            file_size?: string | undefined;
            dimensions?: string | undefined;
        };
        _id?: string | undefined;
        title?: string | undefined;
        slug?: string;
        content?: string | undefined;
        summary?: string;
        tags?: string[];
        coverImage?: string;
        destination?: string | null;
        places?: import("../../common/entities/blog.entity").PlaceData[];
        updatedBy?: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId | undefined;
        likes?: import("mongoose").Types.ObjectId[];
        status?: "DRAFT" | "PENDING" | "APPROVED" | "REJECTED" | undefined;
        flags?: {
            userId: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId;
            reason: string;
            date: Date;
        }[];
        metrics?: {
            viewCount: number;
            likeCount: number;
            commentCount: number;
        };
        comments?: import("../../common/entities/comment.entity").Comment[] | import("mongoose").Types.ObjectId[];
        createdAt?: Date;
        updatedAt?: Date;
        $locals?: Record<string, unknown> | undefined;
        $op?: "remove" | "save" | "validate" | null | undefined;
        $where?: Record<string, unknown> | undefined;
        baseModelName?: string;
        collection?: import("mongoose").Collection<import("bson").Document> | undefined;
        db?: import("mongoose").Connection | undefined;
        errors?: import("mongoose").Error.ValidationError;
        id?: any;
        isNew?: boolean | undefined;
        schema?: import("mongoose").Schema<any, import("mongoose").Model<any, any, any, any, any, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, {
            [x: string]: unknown;
        }, import("mongoose").Document<unknown, {}, import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }>, {}> & import("mongoose").FlatRecord<{
            [x: string]: unknown;
        }> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        }> | undefined;
        __v?: number | undefined;
    }>;
    createBlog(file: Express.Multer.File, createBlogDto: CreateBlogDto, req: Request): Promise<import("mongoose").Document<unknown, {}, import("../../common/entities/blog.entity").Blog, {}> & import("../../common/entities/blog.entity").Blog & Required<{
        _id: string;
    }> & {
        __v: number;
    }>;
    updateStatus(id: string, status: 'APPROVED' | 'REJECTED'): Promise<{
        _id: string;
        title: string;
        status: "APPROVED" | "REJECTED";
    }>;
    cleanFlags(blogId: string): Promise<{
        _id: string;
        title: string;
        flags: {
            userId: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId;
            reason: string;
            date: Date;
        }[];
    }>;
    banAuthor(blogId: string, reason: string): Promise<{
        _id: string;
        reasonBan: string | null;
        bannedAt: Date | null;
        status: import("../../common/enums/status.enum").Status;
    }>;
    deleteBlog(id: string): Promise<{
        message: string;
    }>;
    createFlag(id: string, reason: string, req: Request): Promise<{
        message: string;
        flags: {
            userId: import("../../common/entities/userInfos.entity").UserInfos | import("mongoose").Types.ObjectId;
            reason: string;
            date: Date;
        }[];
    }>;
    likeBlog(blogId: string, req: any): Promise<{
        like: import("mongoose").Document<unknown, {}, import("../../common/entities/like.entity").Like, {}> & import("../../common/entities/like.entity").Like & Required<{
            _id: unknown;
        }> & {
            __v: number;
        };
        message: string;
    }>;
    unlikeBlog(blogId: string, req: any): Promise<{
        message: string;
    }>;
    checkUserLikedBlog(blogId: string, req: any): Promise<{
        liked: boolean;
    }>;
}
