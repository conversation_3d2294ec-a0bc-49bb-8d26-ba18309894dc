import { Model } from 'mongoose';
import { Account } from '../entities/account.entity';
declare const JwtStrategy_base: new (...args: any) => any;
export declare class JwtStrategy extends JwtStrategy_base {
    private accountModel;
    constructor(accountModel: Model<Account>);
    validate(payload: any): Promise<{
        userId: any;
        email: any;
        role: import("../enums/role.enum").Role;
    }>;
}
export {};
