import { MailerService } from '@nestjs-modules/mailer';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { Model } from 'mongoose';
import { CreateTripDto } from 'src/common/dtos/create-trip.dto';
import { UpdateTripDto } from 'src/common/dtos/update-trip.dto';
import { Plan, TripPlan } from 'src/common/entities/plan.entity';
import { Trip } from 'src/common/entities/trip.entity';
import { AccountService } from '../account/account.service';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { Account } from 'src/common/entities/account.entity';
import { Asset } from 'src/common/entities/asset.entity';
export declare class TripService {
    private readonly tripModel;
    private readonly planModel;
    private readonly userModel;
    private readonly accountModel;
    private readonly assetModel;
    private readonly accountService;
    private readonly jwtService;
    private readonly mailService;
    constructor(tripModel: Model<Trip>, planModel: Model<TripPlan>, userModel: Model<UserInfos>, accountModel: Model<Account>, assetModel: Model<Asset>, accountService: AccountService, jwtService: JwtService, mailService: MailerService);
    removeTripmate(tripId: string, email: string, req: Request): Promise<Trip>;
    create(createTripDto: CreateTripDto, req: Request): Promise<Trip>;
    validateInvite(tripId: string, token: string): Promise<{
        valid: boolean;
        trip: {
            id: string;
            title: string;
            destination: {
                id: string;
                name: string;
                location: {
                    lat: number;
                    lng: number;
                };
            };
            startDate: Date;
            endDate: Date;
        };
        userExists: boolean;
        email: any;
    }>;
    addToTrip(tripId: string, token: string): Promise<void>;
    findByUser(email: string): Promise<(import("mongoose").Document<unknown, {}, Trip, {}> & Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    })[] | undefined>;
    findAll(): string;
    findOne(id: string): Promise<(import("mongoose").Document<unknown, {}, Trip, {}> & Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    }) | null>;
    update(id: number, updateTripDto: UpdateTripDto): string;
    updatePlan(tripId: string, plan: Plan, userId?: string): Promise<TripPlan>;
    remove(id: number): string;
    inviteToTrip(tripId: string, email: string): Promise<{
        success: boolean;
        message: string;
        expiresAt: Date;
    }>;
    findPlanByTripId(tripId: string): Promise<TripPlan | null>;
    updatePlanDates(tripId: string, startDate: Date, endDate: Date): Promise<(import("mongoose").Document<unknown, {}, Trip, {}> & Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    }) | null>;
    updateTripCoverImage(tripId: string, assetId: string, userId: string): Promise<(import("mongoose").Document<unknown, {}, Trip, {}> & Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    }) | undefined>;
}
