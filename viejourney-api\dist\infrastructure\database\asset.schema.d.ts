import mongoose, { Document } from 'mongoose';
export declare class Asset extends Document {
    userId: mongoose.Types.ObjectId;
    type: string;
    subsection: string;
    assetOwner: string;
    url: string;
    publicId: string;
    location: string;
    format: string;
    file_size: string;
    dimensions: string;
}
export declare const AssetSchema: mongoose.Schema<Asset, mongoose.Model<Asset, any, any, any, mongoose.Document<unknown, any, Asset, any> & Asset & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Asset, mongoose.Document<unknown, {}, mongoose.FlatRecord<Asset>, {}> & mongoose.FlatRecord<Asset> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
