"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTripDto = void 0;
const class_transformer_1 = require("class-transformer");
const class_validator_1 = require("class-validator");
class CreateTripDto {
    destination;
    dates;
    travelers;
    budget;
    description;
    visibility;
    inviteEmails;
}
exports.CreateTripDto = CreateTripDto;
__decorate([
    (0, class_validator_1.IsString)({ message: 'Please enter valid destination' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Please enter destination' }),
    __metadata("design:type", Object)
], CreateTripDto.prototype, "destination", void 0);
__decorate([
    (0, class_validator_1.IsArray)({ message: 'Please enter valid date range' }),
    (0, class_validator_1.ArrayMinSize)(2, { message: 'Please enter valid date range' }),
    (0, class_validator_1.ArrayMaxSize)(2, { message: 'Please enter valid date range' }),
    (0, class_validator_1.IsDate)({ each: true, message: 'Please enter valid date range' }),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Array)
], CreateTripDto.prototype, "dates", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)([
        'Solo traveler',
        '2 travelers',
        '3 travelers',
        '4 travelers',
        '5+ travelers',
    ], { message: 'Please enter valid traveler range' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateTripDto.prototype, "travelers", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsIn)(['Budget ($0 - $500)', 'Mid-range ($500 - $1500)', 'Luxury ($1500+)'], {
        message: 'Please enter valid budget range',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateTripDto.prototype, "budget", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateTripDto.prototype, "description", void 0);
__decorate([
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Type)(() => Boolean),
    __metadata("design:type", Boolean)
], CreateTripDto.prototype, "visibility", void 0);
__decorate([
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEmail)({}, { each: true, message: 'Please enter valid emails' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], CreateTripDto.prototype, "inviteEmails", void 0);
//# sourceMappingURL=create-trip.dto.js.map