import { Test, TestingModule } from '@nestjs/testing';
import { <PERSON><PERSON>ontroller } from './trip.controller';
import { TripService } from './trip.service';

describe('TripController', () => {
  let controller: TripController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TripController],
      providers: [TripService],
    }).compile();

    controller = module.get<TripController>(TripController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
