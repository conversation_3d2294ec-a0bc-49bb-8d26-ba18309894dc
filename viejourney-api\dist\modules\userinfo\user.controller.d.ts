import { UpdateUserInfoDto } from 'src/common/dtos/update-userinfo.dto';
import { UserService } from './user.service';
import { Types } from 'mongoose';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    updateUserInfo(req: any, updateUserInfoDto: UpdateUserInfoDto): Promise<import("../../common/entities/userInfos.entity").UserInfos>;
    getUserInfo(req: any): Promise<{
        avatar: string | null;
        _id: Types.ObjectId;
        userId: import("../../common/entities/account.entity").Account;
        fullName: string;
        dob: Date;
        phone: string;
        address: string;
        lastLoginAt: Date;
        flaggedCount: number;
        banReason: string | null;
        bannedAt: Date | null;
        createdAt: Date;
        updatedAt: Date;
        location: string;
        format: string;
        file_size: string;
        dimensions: string;
        __v: number;
    }>;
    updateUserAvatar(file: Express.Multer.File, id: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/userInfos.entity").UserInfos, {}> & import("../../common/entities/userInfos.entity").UserInfos & Required<{
        _id: Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
    getUserDetails(req: any): Promise<{
        destinations: {
            name: string;
            location: import("mongoose").FlattenMaps<{
                lat: number;
                lng: number;
            }>;
        }[];
        blogCount: number;
        likeCount: number;
        tripCount: number;
    }>;
}
