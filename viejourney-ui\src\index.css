@import url("https://fonts.googleapis.com/css2?family=Mona+Sans:ital,wght@0,200..900;1,200..900&display=swap");
@import "tailwindcss" important;
@import "../@/styles/_keyframe-animations.scss";
@import "../@/styles/_variables.scss";
@plugin '@tailwindcss/typography';

@theme {
  /* Primary colors - main brand colors */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e8ff;
  --color-primary-200: #c7d4ff;
  --color-primary-300: #a5b8fd;
  --color-primary-400: #8294fb;
  --color-primary-500: #5e71f6;
  --color-primary-600: #3f61d3; /* Your brand blue */
  --color-primary-700: #3861b0; /* Your secondary blue */
  --color-primary-800: #2c4a9e;
  --color-primary-900: #25397f;
  --color-primary-950: #1a2554;

  /* Neutral colors - grays for text and backgrounds */
  --color-neutral-50: #f8fafc; /* Lightest background */
  --color-neutral-100: #f5f5f5; /* Light background */
  --color-neutral-200: #f3f4f5; /* Light background alternate */
  --color-neutral-300: #e7e7e7; /* Light background for errors/alerts */
  --color-neutral-400: #d9d9d9;
  --color-neutral-500: #b2b2b2;
  --color-neutral-600: #7c7c7c; /* Subtle text */
  --color-neutral-700: #6d6d6d; /* Secondary text */
  --color-neutral-800: #5b5b5b; /* Primary text gray */
  --color-neutral-900: #4b4b4b; /* Dark text */
  --color-neutral-950: #30373f; /* Darkest text */

  /* Dark mode colors */
  --color-dark-50: #f4f4f4; /* Light text on dark bg */
  --color-dark-100: #e5e5e5;
  --color-dark-200: #d9d9d9;
  --color-dark-300: #cacaca;
  --color-dark-400: #adb5bd;
  --color-dark-500: #797979;
  --color-dark-600: #616161;
  --color-dark-700: #495057;
  --color-dark-800: #2e2e2e; /* Dark background alternate */
  --color-dark-900: #181818; /* Main dark background */
  --color-dark-950: #141414; /* Darkest background */

  /* Accent colors - additional UI elements */
  --color-accent-border: #ccc;
  --color-accent-button: #dee2e6;
  --color-accent-button-hover: #adb5bd;
}

/* Theme configurations */
.theme-light {
  --bg-primary: var(--color-neutral-100);
  --bg-secondary: var(--color-neutral-200);
  --bg-tertiary: var(--color-neutral-300);
  --text-primary: var(--color-neutral-950);
  --text-secondary: var(--color-neutral-800);
  --text-muted: var(--color-neutral-700);
  --accent-primary: var(--color-primary-600);
  --accent-secondary: var(--color-primary-700);
  --border-color: var(--color-accent-border);
}

.theme-dark {
  --bg-primary: var(--color-dark-900);
  --bg-secondary: var(--color-dark-800);
  --bg-tertiary: var(--color-dark-700);
  --text-primary: var(--color-dark-50);
  --text-secondary: var(--color-dark-100);
  --text-muted: var(--color-dark-300);
  --accent-primary: var(--color-primary-400);
  --accent-secondary: var(--color-primary-300);
  --border-color: var(--color-dark-600);
}

/* Apply default theme */
:root {
  color-scheme: light;
}

/* You can add a class to the HTML element to switch themes */
html.dark {
  color-scheme: dark;
}

* {
  font-family: "Mona Sans", sans-serif;
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  font-optical-sizing: auto;
  -moz-osx-font-smoothing: antialiased;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

*::-webkit-scrollbar {
  border-radius: 0;
  background-color: transparent;
  border-color: black;

  width: 8px;
  height: 8px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
::-webkit-scrollbar-thumb {
  background: #888;
  width: 8px;
  height: 8px;
}
.swiper {
  width: 240px;
  height: 320px;
}

.swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 18px;
  font-size: 22px;
  font-weight: bold;
  color: #fff;
}

.card {
  /* color used to softly clip top and bottom of the .words container */
  background-color: var(--bg-color);
  padding: 1rem 2rem;
  border-radius: 1.25rem;
}
.loader {
  color: rgb(124, 124, 124);
  font-weight: 500;
  font-size: 25px;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 40px;
  padding: 10px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 8px;
}

.words {
  overflow: hidden;
  position: relative;
}
.words::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    var(--bg-color) 10%,
    transparent 30%,
    transparent 70%,
    var(--bg-color) 90%
  );
  z-index: 20;
}

.word {
  display: block;
  height: 100%;
  padding-left: 6px;
  color: #1565c0;
  animation: spin_4991 4s infinite;
}

@keyframes spin_4991 {
  10% {
    -webkit-transform: translateY(-102%);
    transform: translateY(-102%);
  }

  25% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  35% {
    -webkit-transform: translateY(-202%);
    transform: translateY(-202%);
  }

  50% {
    -webkit-transform: translateY(-200%);
    transform: translateY(-200%);
  }

  60% {
    -webkit-transform: translateY(-302%);
    transform: translateY(-302%);
  }

  75% {
    -webkit-transform: translateY(-300%);
    transform: translateY(-300%);
  }

  85% {
    -webkit-transform: translateY(-402%);
    transform: translateY(-402%);
  }

  100% {
    -webkit-transform: translateY(-400%);
    transform: translateY(-400%);
  }
}

button {
  text-transform: none !important;
}
/* Hide the default close "X" button */
.gm-ui-hover-effect {
  display: none !important;
}

/* Main content wrapper inside InfoWindow */
.gm-style-iw-d {
  padding: 16px !important; /* Use MUI spacing (4 * 4px) */
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-size: 14px;
  color: #333;
}

/* Outer container – remove padding and apply elevation */
.gm-style-iw-c {
  padding: 0 !important;
  border-radius: 8px !important;
  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.15) !important; /* MUI elevation 2 */
  background-color: #fff !important;
  overflow: hidden;
}

/* Remove unnecessary default padding */
.gm-style-iw-ch {
  padding: 0 !important;
}

/* Custom class for date or label */
.date-view {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.tiptap-preview {
  font-family: "Inter", sans-serif;
  color: #1f2937;
  line-height: 1.75;
  font-size: 16px;
  word-break: break-word;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }

  h1 {
    font-size: 2.25rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.3rem;
  }
  h2 {
    font-size: 1.75rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.3rem;
  }
  h3 {
    font-size: 1.5rem;
  }
  h4 {
    font-size: 1.25rem;
  }
  h5 {
    font-size: 1.125rem;
  }
  h6 {
    font-size: 1rem;
  }

  p {
    margin: 1em 0;
  }

  a {
    color: #3b82f6;
    text-decoration: underline;
    &:hover {
      color: #2563eb;
    }
  }

  strong {
    font-weight: 700;
  }

  em {
    font-style: italic;
  }

  blockquote {
    border-left: 4px solid #9ca3af;
    padding-left: 1rem;
    color: #6b7280;
    font-style: italic;
    margin: 1em 0;
    background-color: #f9fafb;
  }

  ul,
  ol {
    padding-left: 1.5rem;
    margin: 1em 0;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-bottom: 0.5em;
  }

  code {
    background-color: #f3f4f6;
    color: #dc2626;
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    font-family: "Fira Code", monospace;
    font-size: 0.9em;
  }

  pre {
    background-color: #0f172a;
    color: #f1f5f9;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1em 0;
    font-family: "Fira Code", monospace;
    font-size: 0.9rem;
    line-height: 1.5;

    code {
      background: none;
      color: inherit;
      padding: 0;
    }
  }

  hr {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 2em 0;
  }

  img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
    border-radius: 0.5rem;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;

    th,
    td {
      border: 1px solid #d1d5db;
      padding: 0.5rem 1rem;
      text-align: left;
    }

    th {
      background-color: #f3f4f6;
      font-weight: 600;
    }
  }
}
.tiptap-preview {
  font-family: "Inter", sans-serif;
  color: #1f2937;
  line-height: 1.75;
  font-size: 16px;
  word-break: break-word;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 700;
    line-height: 1.3;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
  }

  h1 {
    font-size: 2.25rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.3rem;
  }
  h2 {
    font-size: 1.75rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.3rem;
  }
  h3 {
    font-size: 1.5rem;
  }
  h4 {
    font-size: 1.25rem;
  }
  h5 {
    font-size: 1.125rem;
  }
  h6 {
    font-size: 1rem;
  }

  p {
    margin: 1em 0;
  }

  a {
    color: #3b82f6;
    text-decoration: underline;
    &:hover {
      color: #2563eb;
    }
  }

  strong {
    font-weight: 700;
  }

  em {
    font-style: italic;
  }

  blockquote {
    border-left: 4px solid #9ca3af;
    padding-left: 1rem;
    color: #6b7280;
    font-style: italic;
    margin: 1em 0;
    background-color: #f9fafb;
  }

  ul,
  ol {
    padding-left: 1.5rem;
    margin: 1em 0;
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-bottom: 0.5em;
  }

  code {
    background-color: #f3f4f6;
    color: #dc2626;
    padding: 0.2em 0.4em;
    border-radius: 0.25rem;
    font-family: "Fira Code", monospace;
    font-size: 0.9em;
  }

  pre {
    background-color: #0f172a;
    color: #f1f5f9;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1em 0;
    font-family: "Fira Code", monospace;
    font-size: 0.9rem;
    line-height: 1.5;

    code {
      background: none;
      color: inherit;
      padding: 0;
    }
  }

  hr {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 2em 0;
  }

  img {
    max-width: 100%;
    height: auto;
    margin: 1em 0;
    border-radius: 0.5rem;
    box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;

    th,
    td {
      border: 1px solid #d1d5db;
      padding: 0.5rem 1rem;
      text-align: left;
    }

    th {
      background-color: #f3f4f6;
      font-weight: 600;
    }
  }
}
