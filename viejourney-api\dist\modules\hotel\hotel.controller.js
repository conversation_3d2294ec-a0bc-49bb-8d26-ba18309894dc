"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotelController = void 0;
const common_1 = require("@nestjs/common");
const hotel_service_1 = require("./hotel.service");
const update_hotel_dto_1 = require("../../common/dtos/update-hotel.dto");
let HotelController = class HotelController {
    hotelService;
    constructor(hotelService) {
        this.hotelService = hotelService;
    }
    async getHotelList() {
        return this.hotelService.getHotelList();
    }
    async getHotelDetail(id) {
        const hotel = await this.hotelService.getHotelDetail(id);
        if (!hotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return hotel;
    }
    async deleteHotel(id) {
        return this.hotelService.deleteHotel(id);
    }
    async updateHotel(id, updateHotelDto) {
        const hotel = await this.hotelService.updateHotel(id, updateHotelDto);
        if (!hotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return hotel;
    }
};
exports.HotelController = HotelController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "getHotelList", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "getHotelDetail", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "deleteHotel", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_hotel_dto_1.UpdateHotelDto]),
    __metadata("design:returntype", Promise)
], HotelController.prototype, "updateHotel", null);
exports.HotelController = HotelController = __decorate([
    (0, common_1.Controller)('hotel'),
    __metadata("design:paramtypes", [hotel_service_1.HotelService])
], HotelController);
//# sourceMappingURL=hotel.controller.js.map