"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HotelService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("mongoose");
const mongoose_2 = require("@nestjs/mongoose");
let HotelService = class HotelService {
    hotelModel;
    constructor(hotelModel) {
        this.hotelModel = hotelModel;
    }
    async getHotelList() {
        return this.hotelModel.find().exec();
    }
    async getHotelDetail(id) {
        const hotel = await this.hotelModel.findById(id).exec();
        if (!hotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return hotel;
    }
    async addHotel(createHotelDto) {
        const createdHotel = new this.hotelModel(createHotelDto);
        return await createdHotel.save();
    }
    async deleteHotel(id) {
        const deletedHotel = await this.hotelModel.findByIdAndDelete(id).exec();
        if (!deletedHotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return deletedHotel;
    }
    async updateHotel(id, updateHotelDto) {
        const existingHotel = await this.hotelModel
            .findByIdAndUpdate(id, updateHotelDto, { new: true })
            .exec();
        if (!existingHotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return existingHotel;
    }
    async addListOfHotels(hotels) {
        await this.hotelModel.insertMany(hotels);
    }
};
exports.HotelService = HotelService;
exports.HotelService = HotelService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_2.InjectModel)('Hotel')),
    __metadata("design:paramtypes", [mongoose_1.Model])
], HotelService);
//# sourceMappingURL=hotel.service.js.map