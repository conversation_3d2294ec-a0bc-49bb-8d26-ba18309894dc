import { Model } from 'mongoose';
import { CreateAccountDto } from 'src/common/dtos/create-account.dto';
import { Account } from 'src/common/entities/account.entity';
import { Asset } from 'src/common/entities/asset.entity';
import { Blog } from 'src/common/entities/blog.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { Trip } from 'src/infrastructure/database/trip.schema';
import { AssetsService } from '../assets/assets.service';
import { DashboardQueryDto, DashboardAnalyticsDto } from 'src/common/dtos/dashboard-analytics.dto';
export declare class AdminService {
    private readonly blogModel;
    private readonly commentModel;
    private readonly accountModel;
    private readonly userInfosModel;
    private readonly assetModel;
    private readonly tripModel;
    private readonly assetsService;
    constructor(blogModel: Model<Blog>, commentModel: Model<Comment>, accountModel: Model<Account>, userInfosModel: Model<UserInfos>, assetModel: Model<Asset>, tripModel: Model<Trip>, assetsService: AssetsService);
    getBlogsReport(minViews?: number): Promise<{
        totalBlogs: number;
        totalViews: any;
    }>;
    getCommentsReport(): Promise<{
        totalComments: number;
        commentsPerBlog: any[];
    }>;
    getAllAccounts(): Promise<Account[]>;
    getAccountById(id: string): Promise<Account | null>;
    createAccount(createAccountDto: CreateAccountDto): Promise<Account>;
    deleteAccount(id: string): Promise<Account | null>;
    updateActiveStatus(id: string, active: boolean): Promise<Account | undefined>;
    banUser(userId: string, reason: string): Promise<{
        userId: string;
        accountId: string;
        role: string;
        email: string;
        userName: string;
        status: string;
        banReason: string;
        flaggedCount: number;
        bannedAt: Date;
    }>;
    unbanUser(userId: string): Promise<{
        userId: string;
        accountId: string;
        role: string;
        email: string;
        userName: string;
        status: string;
        banReason: string | null;
        flaggedCount: number;
        bannedAt: Date | null;
    }>;
    bulkUpdateUserRoles(userIds: string[], newRole: string): Promise<{
        success: Array<{
            userId: string;
            accountId: string;
            email: string;
            userName: string;
            oldRole: string;
            newRole: string;
            status: string;
        }>;
        failed: Array<{
            userId: string;
            reason: string;
        }>;
        summary: {
            totalRequested: number;
            successCount: number;
            failedCount: number;
        };
    }>;
    getDashboardAnalytics(query: DashboardQueryDto): Promise<DashboardAnalyticsDto>;
    private getStartDate;
    private getLikesAndCommentsCount;
    private getTodayInteractions;
    private getUserGrowthData;
    private getContentCreationData;
    private getEngagementData;
    private getUserActivityStats;
    private getContentStatusStats;
    private getTopLocations;
    getRoleBasedCounts(): Promise<{
        userCount: number;
        adminCount: number;
        managerCount: number;
    }>;
}
