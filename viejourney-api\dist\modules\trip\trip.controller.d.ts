import { TripService } from './trip.service';
import { Request } from 'express';
import { CreateTripDto } from 'src/common/dtos/create-trip.dto';
import { UpdateTripDto } from 'src/common/dtos/update-trip.dto';
export declare class TripController {
    private readonly tripService;
    constructor(tripService: TripService);
    removeTripmate(req: {
        tripId: string;
        email: string;
    }, request: Request): Promise<import("../../common/entities/trip.entity").Trip>;
    inviteToTrip(req: {
        tripId: string;
        email: string;
    }, request: Request): Promise<{
        success: boolean;
        message: string;
        expiresAt: Date;
    }>;
    validateInvite(req: {
        tripId: string;
        token: string;
    }): Promise<{
        valid: boolean;
        trip?: any;
        userExists?: boolean;
        email?: string;
    }>;
    create(req: Request, createTripDto: CreateTripDto): Promise<import("../../common/entities/trip.entity").Trip>;
    joinTrip(id: string, req: {
        token: string;
    }): Promise<void>;
    findByUser(req: Request): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/trip.entity").Trip, {}> & import("../../common/entities/trip.entity").Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    })[] | undefined>;
    findOne(id: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/trip.entity").Trip, {}> & import("../../common/entities/trip.entity").Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    }) | null>;
    update(id: string, updateTripDto: UpdateTripDto): string;
    remove(id: string): string;
    findPlanByTripId(id: string): Promise<import("../../common/entities/plan.entity").TripPlan | null>;
    patchPlanDates(req: {
        tripId: string;
        startDate: Date;
        endDate: Date;
    }): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/trip.entity").Trip, {}> & import("../../common/entities/trip.entity").Trip & Required<{
        _id: string;
    }> & {
        __v: number;
    }) | null>;
    updateTripCoverImage(req: {
        tripId: string;
        assetId: string;
    }, request: Request): Promise<any>;
}
