"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogController = void 0;
const common_1 = require("@nestjs/common");
const blog_service_1 = require("./blog.service");
const role_enum_1 = require("../../common/enums/role.enum");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const pagination_userlist_dto_1 = require("../../common/dtos/pagination-userlist.dto");
const create_blog_dto_1 = require("../../common/dtos/create-blog.dto");
const start_blog_dto_1 = require("../../common/dtos/start-blog.dto");
const update_blog_draft_dto_1 = require("../../common/dtos/update-blog-draft.dto");
const platform_express_1 = require("@nestjs/platform-express");
const roles_guard_1 = require("../../common/guards/roles.guard");
let BlogController = class BlogController {
    blogService;
    constructor(blogService) {
        this.blogService = blogService;
    }
    async getAllBlogs(paginationDto) {
        return this.blogService.findAll(paginationDto);
    }
    async getAllApprovedBlogs(page, limit, search) {
        const pageNum = page ? parseInt(page) : 1;
        const limitNum = limit ? parseInt(limit) : 10;
        if (pageNum < 1 || limitNum < 1 || limitNum > 50) {
            throw new common_1.BadRequestException('Invalid pagination parameters. Page must be >=1, limit must be 1-50');
        }
        return this.blogService.getAllApprovedBlogs(pageNum, limitNum, search);
    }
    async getRelatedBlogs(tags, blogId, destination) {
        return this.blogService.getRelatedBlogs(blogId, tags, destination);
    }
    async getUserBlogs(req, status) {
        const userId = req.user?.['userId'];
        if (!userId) {
            throw new common_1.BadRequestException('User ID not found in request');
        }
        return this.blogService.getUserBlogs(userId, status);
    }
    async getBlogById(id, req) {
        return this.blogService.findOneBlogById(id);
    }
    async startBlog(dto, coverImage, req) {
        const userId = req.user?.['userId'];
        if (!userId)
            throw new common_1.BadRequestException('User ID not found');
        return this.blogService.startBlog(dto.location, userId, coverImage);
    }
    async getDraftBlog(id, req) {
        const userId = req.user?.['userId'];
        return this.blogService.getDraftBlog(id, userId);
    }
    async publishBlog(id, req) {
        const userId = req.user?.['userId'];
        const role = req.user?.['role'];
        return this.blogService.publishBlog(id, userId, role);
    }
    async getPublishedBlog(id, req) {
        const userId = req.user?.['userId'];
        return this.blogService.getPublishedBlog(id, userId);
    }
    async updateBlogDraftById(id, dto, coverImage, req) {
        const userId = req.user?.['userId'];
        return this.blogService.updateBlogDraft(id, dto, userId, coverImage);
    }
    async editPublishedBlog(id, dto, coverImage, req) {
        const userId = req.user?.['userId'];
        return this.blogService.editPublishedBlog(id, dto, userId, coverImage);
    }
    async getManagerBlogById(id) {
        return this.blogService.findOneBlogById(id);
    }
    async createBlog(file, createBlogDto, req) {
        const userId = req.user?.['userId'];
        return this.blogService.createBlog(createBlogDto, file, userId);
    }
    async updateStatus(id, status) {
        return this.blogService.updateStatus(id, status);
    }
    async cleanFlags(blogId) {
        return this.blogService.cleanFlags(blogId);
    }
    async banAuthor(blogId, reason) {
        return this.blogService.banAuthor(blogId, reason);
    }
    async deleteBlog(id) {
        return this.blogService.deleteBlogById(id);
    }
    async createFlag(id, reason, req) {
        if (!reason?.trim()) {
            throw new common_1.BadRequestException('Reason is required');
        }
        return this.blogService.createFlag(id, reason, req);
    }
    async likeBlog(blogId, req) {
        return this.blogService.postLikeBlog(req, blogId);
    }
    async unlikeBlog(blogId, req) {
        return this.blogService.unlikeBlog(req, blogId);
    }
    async checkUserLikedBlog(blogId, req) {
        const userId = req.user?.['userId'];
        const liked = await this.blogService.hasUserLikedBlog(userId, blogId);
        return { liked };
    }
};
exports.BlogController = BlogController;
__decorate([
    (0, common_1.Get)('manager'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [pagination_userlist_dto_1.PaginationDto]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getAllBlogs", null);
__decorate([
    (0, common_1.Get)('home'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getAllApprovedBlogs", null);
__decorate([
    (0, common_1.Post)('related'),
    __param(0, (0, common_1.Body)('tags')),
    __param(1, (0, common_1.Body)('blogId')),
    __param(2, (0, common_1.Body)('destination')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, String, String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getRelatedBlogs", null);
__decorate([
    (0, common_1.Get)('my-blogs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getUserBlogs", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getBlogById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('start-blog'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('coverImage', {
        limits: { fileSize: 5 * 1024 * 1024 },
        fileFilter: (_, file, cb) => {
            if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|avif)$/)) {
                return cb(new common_1.BadRequestException('Only image files allowed!'), false);
            }
            cb(null, true);
        },
    })),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFile)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [start_blog_dto_1.StartBlogDto, Object, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "startBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('draft/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getDraftBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('publish/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "publishBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('published/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getPublishedBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('draft/:id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('coverImage', {
        limits: { fileSize: 5 * 1024 * 1024 },
        fileFilter: (_, file, cb) => {
            if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|avif)$/)) {
                return cb(new common_1.BadRequestException('Only image files allowed!'), false);
            }
            cb(null, true);
        },
    })),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_blog_draft_dto_1.UpdateBlogDraftDto, Object, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "updateBlogDraftById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Patch)('edit/:id'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('coverImage', {
        limits: { fileSize: 5 * 1024 * 1024 },
        fileFilter: (_, file, cb) => {
            if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|avif)$/)) {
                return cb(new common_1.BadRequestException('Only image files allowed!'), false);
            }
            cb(null, true);
        },
    })),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.UploadedFile)()),
    __param(3, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_blog_draft_dto_1.UpdateBlogDraftDto, Object, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "editPublishedBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Get)('manager/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "getManagerBlogById", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Post)('manager'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file', {
        limits: { fileSize: 5 * 1024 * 1024 },
        fileFilter: (_, file, cb) => {
            if (!file.mimetype.match(/\/(jpg|jpeg|png|gif|webp|avif)$/)) {
                return cb(new common_1.BadRequestException('Only image files allowed!'), false);
            }
            cb(null, true);
        },
    })),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_blog_dto_1.CreateBlogDto, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "createBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Post)(':id/status'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "updateStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Patch)(':id/flags'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "cleanFlags", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Post)('ban-author/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('reason')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "banAuthor", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "deleteBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':id/flags'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('reason')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "createFlag", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':id/like'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "likeBlog", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':id/unlike'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "unlikeBlog", null);
__decorate([
    (0, common_1.Get)(':id/like'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], BlogController.prototype, "checkUserLikedBlog", null);
exports.BlogController = BlogController = __decorate([
    (0, common_1.Controller)('blogs'),
    __metadata("design:paramtypes", [blog_service_1.BlogService])
], BlogController);
//# sourceMappingURL=blog.controller.js.map