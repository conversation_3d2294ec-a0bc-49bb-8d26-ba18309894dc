"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripGateway = void 0;
const websockets_1 = require("@nestjs/websockets");
const socket_io_1 = require("socket.io");
const trip_service_1 = require("./trip.service");
const plan_state_service_1 = require("./plan-state/plan-state.service");
let TripGateway = class TripGateway {
    tripService;
    planService;
    server;
    constructor(tripService, planService) {
        this.tripService = tripService;
        this.planService = planService;
        this.planService.notifySaveStatus = this.notifySaveStatus.bind(this);
    }
    notifySaveStatus(tripId, status, errorMessage) {
        const payload = {
            status,
            timestamp: new Date().toISOString(),
            ...(errorMessage && { error: errorMessage }),
        };
        this.server.to(tripId).emit('savePlanStatus', payload);
    }
    async handleConnection(client) {
        const tripId = client.handshake.auth.tripId;
        const user = client.handshake.auth.user;
        if (!tripId || !user) {
            client.disconnect();
            return;
        }
        const trip = await this.tripService.findOne(tripId);
        if (!trip || !trip.tripmates.includes(user?.email)) {
            client.emit('unauthorizedJoin', {
                reason: 'You are not a participant in this trip plan.',
            });
            client.disconnect();
            return;
        }
        await client.join(tripId);
        console.log(`Client connected: ${client.id}`);
        console.log(`Client: ${JSON.stringify(client.handshake.auth?.user, null, 2)}`);
    }
    handleDisconnect(client) {
        console.log(`Client disconnected: ${client.id}`);
    }
    handlePing(data, client) {
        console.log(`Received ping from client: ${client.id}`, data);
        console.log(`Auth: ${JSON.stringify(client.handshake.auth?.user, null, 2)}`);
        this.server.emit('pong', { message: 'pong', received: data });
    }
    handlePlanItemAdded(data, client) {
        const tripId = client.handshake.auth?.tripId;
        const user = client.handshake.auth?.user;
        console.log(`Added item in section ${data.section}:`, data.item);
        const itemId = this.planService.addItem(tripId, data.section, data.item, user);
        if (data.section === 'budget') {
            this.server.to(tripId).emit('onPlanItemAdded', {
                section: data.section,
                item: data.item,
                addedBy: user,
            });
        }
        else {
            this.server.to(tripId).emit('onPlanItemAdded', {
                section: data.section,
                item: { ...data.item, id: itemId },
                addedBy: user,
            });
        }
    }
    handlePlanItemUpdated(data, client) {
        const tripId = client.handshake.auth?.tripId;
        const user = client.handshake.auth?.user;
        console.log(`Updated item in section ${data.section}:`, data.item);
        this.planService.updateItem(tripId, data.section, data.item);
        console.log(`Updated by user:`, user);
        this.server.to(tripId).emit('onPlanItemUpdated', {
            section: data.section,
            item: data.item,
            updatedBy: user,
        });
    }
    handlePlanItemDeleted(data, client) {
        const tripId = client.handshake.auth?.tripId;
        const user = client.handshake.auth?.user;
        this.planService.deleteItem(tripId, data.section, data.itemId);
        console.log(`Deleted item in section ${data.section}:`, data.itemId);
        console.log(`Deleted by user:`, user);
        this.server.to(tripId).emit('onPlanItemDeleted', {
            section: data.section,
            itemId: data.itemId,
            deletedBy: user,
        });
    }
    async handleRequestSaveStatus(data = {}, client) {
        const tripId = client.handshake.auth?.tripId;
        const user = client.handshake.auth?.user;
        const isSaving = this.planService.isSavingPlan(tripId);
        if (data.forceSave) {
            console.log(`Force saving trip plan for ${tripId} requested by ${user?.email}`);
            client.emit('savePlanStatus', {
                status: isSaving ? 'saving' : 'saved',
                timestamp: new Date().toISOString(),
            });
            await this.planService.forceSave(tripId);
            return;
        }
        client.emit('savePlanStatus', {
            status: isSaving ? 'saving' : 'saved',
            timestamp: new Date().toISOString(),
        });
    }
};
exports.TripGateway = TripGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], TripGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TripGateway.prototype, "handlePing", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('planItemAdded'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TripGateway.prototype, "handlePlanItemAdded", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('planItemUpdated'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TripGateway.prototype, "handlePlanItemUpdated", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('planItemDeleted'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], TripGateway.prototype, "handlePlanItemDeleted", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('requestSaveStatus'),
    __param(0, (0, websockets_1.MessageBody)()),
    __param(1, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, socket_io_1.Socket]),
    __metadata("design:returntype", Promise)
], TripGateway.prototype, "handleRequestSaveStatus", null);
exports.TripGateway = TripGateway = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: '*',
        },
        namespace: 'trip',
        transports: ['websocket', 'polling'],
    }),
    __metadata("design:paramtypes", [trip_service_1.TripService,
        plan_state_service_1.PlanStateService])
], TripGateway);
//# sourceMappingURL=trip.gateway.js.map