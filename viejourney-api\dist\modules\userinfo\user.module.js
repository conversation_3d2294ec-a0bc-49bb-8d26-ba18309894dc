"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const account_entity_1 = require("../../common/entities/account.entity");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const account_schema_1 = require("../../infrastructure/database/account.schema");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const assets_module_1 = require("../assets/assets.module");
const user_controller_1 = require("./user.controller");
const user_service_1 = require("./user.service");
const asset_entity_1 = require("../../common/entities/asset.entity");
const asset_schema_1 = require("../../infrastructure/database/asset.schema");
const trip_schema_1 = require("../../infrastructure/database/trip.schema");
const blog_entity_1 = require("../../common/entities/blog.entity");
const blog_schema_1 = require("../../infrastructure/database/blog.schema");
let UserModule = class UserModule {
};
exports.UserModule = UserModule;
exports.UserModule = UserModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: account_entity_1.Account.name, schema: account_schema_1.AccountSchema },
                { name: asset_entity_1.Asset.name, schema: asset_schema_1.AssetSchema },
                { name: trip_schema_1.Trip.name, schema: trip_schema_1.TripSchema },
                { name: blog_entity_1.Blog.name, schema: blog_schema_1.BlogSchema },
            ]),
            assets_module_1.AssetsModule,
        ],
        controllers: [user_controller_1.UserController],
        providers: [user_service_1.UserService],
        exports: [user_service_1.UserService, UserModule],
    })
], UserModule);
//# sourceMappingURL=user.module.js.map