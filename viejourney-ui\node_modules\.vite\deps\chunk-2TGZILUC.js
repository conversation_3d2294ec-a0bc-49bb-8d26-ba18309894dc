import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-internals/esm/reactMajor/index.js
var React = __toESM(require_react(), 1);
var reactMajor_default = parseInt(React.version, 10);

// node_modules/@mui/x-internals/esm/useComponentRenderer/useComponentRenderer.js
var React2 = __toESM(require_react(), 1);
function useComponentRenderer(defaultElement, render, props, state = {}) {
  if (typeof render === "function") {
    return render(props, state);
  }
  if (render) {
    if (render.props.className) {
      props.className = mergeClassNames(render.props.className, props.className);
    }
    if (render.props.style || props.style) {
      props.style = _extends({}, props.style, render.props.style);
    }
    return React2.cloneElement(render, props);
  }
  return React2.createElement(defaultElement, props);
}
function mergeClassNames(className, otherClassName) {
  if (!className || !otherClassName) {
    return className || otherClassName;
  }
  return `${className} ${otherClassName}`;
}

export {
  reactMajor_default,
  useComponentRenderer
};
//# sourceMappingURL=chunk-2TGZILUC.js.map
