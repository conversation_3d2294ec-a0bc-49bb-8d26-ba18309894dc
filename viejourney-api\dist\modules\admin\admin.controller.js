"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminController = void 0;
const common_1 = require("@nestjs/common");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const create_account_dto_1 = require("../../common/dtos/create-account.dto");
const update_userinfo_dto_1 = require("../../common/dtos/update-userinfo.dto");
const bulk_update_role_dto_1 = require("../../common/dtos/bulk-update-role.dto");
const role_enum_1 = require("../../common/enums/role.enum");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const user_service_1 = require("../userinfo/user.service");
const admin_service_1 = require("./admin.service");
const roles_guard_1 = require("../../common/guards/roles.guard");
const dashboard_analytics_dto_1 = require("../../common/dtos/dashboard-analytics.dto");
let AdminController = class AdminController {
    adminService;
    userService;
    constructor(adminService, userService) {
        this.adminService = adminService;
        this.userService = userService;
    }
    async getDashboardAnalytics(query) {
        return this.adminService.getDashboardAnalytics(query);
    }
    async getRoleBasedCounts() {
        return this.adminService.getRoleBasedCounts();
    }
    async getAllAccounts() {
        return this.adminService.getAllAccounts();
    }
    async createAccount(createAccountDto) {
        return this.adminService.createAccount(createAccountDto);
    }
    async getAccountById(id) {
        return this.adminService.getAccountById(id);
    }
    async deleteAccount(id) {
        return this.adminService.deleteAccount(id);
    }
    async updateActiveStatus(id, active) {
        return this.adminService.updateActiveStatus(id, active);
    }
    async getBlogsReport(views) {
        return this.adminService.getBlogsReport(views);
    }
    async getCommentsReport() {
        return this.adminService.getCommentsReport();
    }
    async getAllUsers(query) {
        const filter = {
            role: query.role,
            status: query.status,
            username: query.username,
            userId: query.userId,
            email: query.email,
        };
        const pagination = query.page && query.pageSize
            ? {
                page: parseInt(query.page),
                pageSize: parseInt(query.pageSize),
            }
            : undefined;
        const resp = await this.userService.getAllUser(filter, pagination);
        return this.userService.getAllUser(filter, pagination);
    }
    async getFilterUsers(query) {
        const filter = {
            role: query.role,
            status: query.status,
            username: query.username,
            userId: query.userId,
            email: query.email,
        };
        return this.userService.getAllUser(filter);
    }
    async getPaginatedUsers(body) {
        const filter = body.filter || {};
        const pagination = {
            page: body.page,
            pageSize: body.pageSize,
        };
        return this.userService.getAllUser(filter, pagination);
    }
    async getUser(id) {
        return this.userService.getUserByID(id);
    }
    async updateUserInfo(id, updateUserInfoDto) {
        return this.userService.updateUserInfo(id, updateUserInfoDto);
    }
    async deleteUserInfo(id) {
        return this.userService.deleteUserInfo(id);
    }
    async updateUserRole(id, role) {
        const validRoles = ['USER', 'ADMIN', 'MANAGER'];
        if (!validRoles.includes(role)) {
            throw new common_1.BadRequestException(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
        }
        return this.userService.updateUserRole(id, role);
    }
    async banUser(id, reason) {
        if (!reason || reason.trim().length === 0) {
            throw new common_1.BadRequestException('Ban reason is required');
        }
        return this.adminService.banUser(id, reason);
    }
    async unbanUser(id) {
        return this.adminService.unbanUser(id);
    }
    async bulkUpdateUserRoles(bulkUpdateRoleDto) {
        const validRoles = ['USER', 'ADMIN', 'MANAGER'];
        if (!validRoles.includes(bulkUpdateRoleDto.role)) {
            throw new common_1.BadRequestException(`Invalid role. Must be one of: ${validRoles.join(', ')}`);
        }
        if (!bulkUpdateRoleDto.userIds || bulkUpdateRoleDto.userIds.length === 0) {
            throw new common_1.BadRequestException('At least one userId is required');
        }
        return this.adminService.bulkUpdateUserRoles(bulkUpdateRoleDto.userIds, bulkUpdateRoleDto.role);
    }
};
exports.AdminController = AdminController;
__decorate([
    (0, common_1.Get)('analytics'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dashboard_analytics_dto_1.DashboardQueryDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getDashboardAnalytics", null);
__decorate([
    (0, common_1.Get)('roles'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getRoleBasedCounts", null);
__decorate([
    (0, common_1.Get)('accounts'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllAccounts", null);
__decorate([
    (0, common_1.Post)('accounts'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_account_dto_1.CreateAccountDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "createAccount", null);
__decorate([
    (0, common_1.Get)('accounts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAccountById", null);
__decorate([
    (0, common_1.Delete)('accounts/delete/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteAccount", null);
__decorate([
    (0, common_1.Patch)('accounts/updateActive/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('active')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateActiveStatus", null);
__decorate([
    (0, common_1.Get)('reports/blogs'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin),
    __param(0, (0, common_1.Query)('views')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getBlogsReport", null);
__decorate([
    (0, common_1.Get)('reports/comments'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getCommentsReport", null);
__decorate([
    (0, common_1.Get)('users'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Get)('users/filter'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getFilterUsers", null);
__decorate([
    (0, common_1.Post)('users/paginate'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getPaginatedUsers", null);
__decorate([
    (0, common_1.Get)('users/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "getUser", null);
__decorate([
    (0, common_1.Patch)('userInfo/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_userinfo_dto_1.UpdateUserInfoDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUserInfo", null);
__decorate([
    (0, common_1.Delete)('userInfo/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "deleteUserInfo", null);
__decorate([
    (0, common_1.Patch)('users/:id/role'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "updateUserRole", null);
__decorate([
    (0, common_1.Patch)('users/:id/ban'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('reason')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "banUser", null);
__decorate([
    (0, common_1.Patch)('users/:id/unban'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "unbanUser", null);
__decorate([
    (0, common_1.Patch)('users/bulk-update-roles'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [bulk_update_role_dto_1.BulkUpdateRoleDto]),
    __metadata("design:returntype", Promise)
], AdminController.prototype, "bulkUpdateUserRoles", null);
exports.AdminController = AdminController = __decorate([
    (0, common_1.Controller)('admin'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Admin),
    __metadata("design:paramtypes", [admin_service_1.AdminService,
        user_service_1.UserService])
], AdminController);
//# sourceMappingURL=admin.controller.js.map