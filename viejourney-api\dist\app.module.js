"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mailer_1 = require("@nestjs-modules/mailer");
const admin_module_1 = require("./modules/admin/admin.module");
const manager_module_1 = require("./modules/manager/manager.module");
const hotel_module_1 = require("./modules/hotel/hotel.module");
const user_module_1 = require("./modules/userinfo/user.module");
const account_module_1 = require("./modules/account/account.module");
const assets_module_1 = require("./modules/assets/assets.module");
const trip_module_1 = require("./modules/trip/trip.module");
const auth_module_1 = require("./modules/auth/auth.module");
const blog_module_1 = require("./modules/blog/blog.module");
const comment_module_1 = require("./modules/comment/comment.module");
const handlebars_adapter_1 = require("@nestjs-modules/mailer/dist/adapters/handlebars.adapter");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.development.local', '.env.development', '.env'],
                ignoreEnvFile: process.env.NODE_ENV === 'production',
                expandVariables: true,
                load: [],
            }),
            mongoose_1.MongooseModule.forRoot(process.env.MONGODB_URI || ''),
            admin_module_1.AdminModule,
            blog_module_1.BlogModule,
            manager_module_1.ManagerModule,
            hotel_module_1.HotelModule,
            user_module_1.UserModule,
            account_module_1.AccountModule,
            trip_module_1.TripModule,
            comment_module_1.CommentModule,
            assets_module_1.AssetsModule,
            mailer_1.MailerModule.forRoot({
                transport: {
                    service: process.env.MAIL_SERVICE,
                    host: process.env.MAIL_HOST,
                    port: Number(process.env.MAIL_PORT),
                    secure: process.env.MAIL_SECURE === 'true',
                    auth: {
                        user: process.env.MAIL_USER,
                        pass: process.env.MAIL_PASS,
                    },
                },
                defaults: {
                    from: `"VieJourney" <${process.env.MAIL_FROM}>`,
                },
                template: {
                    dir: process.cwd() + '/src/templates',
                    adapter: new handlebars_adapter_1.HandlebarsAdapter(),
                    options: {
                        strict: false,
                    },
                },
            }),
            auth_module_1.AuthModule,
            account_module_1.AccountModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map