import mongoose, { Document } from 'mongoose';
import { UserInfos } from './userinfo.schema';
import { Blog } from './blog.schema';
export declare class Like extends Document {
    userId: UserInfos;
    blogId: Blog;
}
export declare const LikeSchema: mongoose.Schema<Like, mongoose.Model<Like, any, any, any, mongoose.Document<unknown, any, Like, any> & Like & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Like, mongoose.Document<unknown, {}, mongoose.FlatRecord<Like>, {}> & mongoose.FlatRecord<Like> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
