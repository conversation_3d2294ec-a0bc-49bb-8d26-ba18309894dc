"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const account_controller_1 = require("./account.controller");
const account_service_1 = require("./account.service");
const auth_module_1 = require("../auth/auth.module");
const assets_module_1 = require("../assets/assets.module");
const userInfos_entity_1 = require("../../common/entities/userInfos.entity");
const account_entity_1 = require("../../common/entities/account.entity");
const account_schema_1 = require("../../infrastructure/database/account.schema");
const userinfo_schema_1 = require("../../infrastructure/database/userinfo.schema");
const asset_schema_1 = require("../../infrastructure/database/asset.schema");
const asset_entity_1 = require("../../common/entities/asset.entity");
let AccountModule = class AccountModule {
};
exports.AccountModule = AccountModule;
exports.AccountModule = AccountModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: account_entity_1.Account.name, schema: account_schema_1.AccountSchema },
                { name: userInfos_entity_1.UserInfos.name, schema: userinfo_schema_1.UserInfosSchema },
                { name: asset_entity_1.Asset.name, schema: asset_schema_1.AssetSchema },
            ]),
            (0, common_1.forwardRef)(() => auth_module_1.AuthModule),
            assets_module_1.AssetsModule,
        ],
        controllers: [account_controller_1.AccountController],
        providers: [account_service_1.AccountService],
        exports: [account_service_1.AccountService],
    })
], AccountModule);
//# sourceMappingURL=account.module.js.map