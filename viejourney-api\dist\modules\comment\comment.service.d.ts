import { Model, Types } from 'mongoose';
import { Blog } from 'src/common/entities/blog.entity';
import { Comment } from 'src/common/entities/comment.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
export declare class CommentService {
    private commentModel;
    private blogModel;
    private userInfosModel;
    constructor(commentModel: Model<Comment>, blogModel: Model<Blog>, userInfosModel: Model<UserInfos>);
    createComment(blogId: string, content: string, userId: string): Promise<Omit<import("mongoose").Document<unknown, {}, Comment, {}> & Comment & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    }, never>>;
    getComments(blogId: string, limit?: number, skip?: number): Promise<Comment[]>;
    editComment(commentId: string, userId: string, content: string): Promise<import("mongoose").Document<unknown, {}, Comment, {}> & Comment & {
        _id: Types.ObjectId;
    } & {
        __v: number;
    }>;
    deleteComment(commentId: string, userId: string): Promise<{
        success: boolean;
    }>;
    likeComment(commentId: string, userId: string): Promise<{
        likes: string[];
        likesCount: number;
    }>;
    unlikeComment(commentId: string, userId: string): Promise<{
        likes: string[];
        likesCount: number;
    }>;
    private getLikeFullNames;
    private deleteCommentAndChildren;
    private countCommentAndChildren;
}
