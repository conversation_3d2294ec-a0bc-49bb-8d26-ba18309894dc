{"version": 3, "sources": ["../../@mui/x-internals/esm/fastObjectShallowCompare/fastObjectShallowCompare.js"], "sourcesContent": ["const is = Object.is;\n\n/**\n * Fast shallow compare for objects.\n * @returns true if objects are equal.\n */\nexport function fastObjectShallowCompare(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (!(a instanceof Object) || !(b instanceof Object)) {\n    return false;\n  }\n  let aLength = 0;\n  let bLength = 0;\n\n  /* eslint-disable guard-for-in */\n  for (const key in a) {\n    aLength += 1;\n    if (!is(a[key], b[key])) {\n      return false;\n    }\n    if (!(key in b)) {\n      return false;\n    }\n  }\n\n  /* eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars */\n  for (const _ in b) {\n    bLength += 1;\n  }\n  return aLength === bLength;\n}"], "mappings": ";AAAA,IAAM,KAAK,OAAO;AAMX,SAAS,yBAAyB,GAAG,GAAG;AAC7C,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,EAAE,aAAa,WAAW,EAAE,aAAa,SAAS;AACpD,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACd,MAAI,UAAU;AAGd,aAAW,OAAO,GAAG;AACnB,eAAW;AACX,QAAI,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,EAAE,OAAO,IAAI;AACf,aAAO;AAAA,IACT;AAAA,EACF;AAGA,aAAW,KAAK,GAAG;AACjB,eAAW;AAAA,EACb;AACA,SAAO,YAAY;AACrB;", "names": []}