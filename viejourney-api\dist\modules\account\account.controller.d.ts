import { AccountService } from './account.service';
import { UpdateAccountDto } from 'src/common/dtos/update-account.dto';
import { CreateAccountDto } from 'src/common/dtos/create-account.dto';
import { EditProfileDto } from 'src/common/dtos/editProfile.dto';
import { Role } from 'src/common/enums/role.enum';
export declare class AccountController {
    private readonly accountService;
    constructor(accountService: AccountService);
    editProfile(editProfileDto: EditProfileDto, file: Express.Multer.File, userId: string): Promise<(import("mongoose").Document<unknown, {}, import("../../common/entities/userInfos.entity").UserInfos, {}> & import("../../common/entities/userInfos.entity").UserInfos & Required<{
        _id: import("mongoose").Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
    create(createAccountDto: CreateAccountDto): Promise<import("../../common/entities/account.entity").Account>;
    findAll(): Promise<import("../../common/entities/account.entity").Account[]>;
    update(id: string, updateAccountDto: UpdateAccountDto): Promise<import("../../common/entities/account.entity").Account>;
    remove(id: string): Promise<import("../../common/entities/account.entity").Account>;
    getProfile(req: any): Promise<{
        _id: string;
        email: string;
        status: import("../../common/enums/status.enum").Status;
        role: Role;
    }>;
}
