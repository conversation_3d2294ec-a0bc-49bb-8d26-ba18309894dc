import mongoose, { Document } from 'mongoose';
import { Like } from './like.schema';
import { UserInfos } from './userinfo.schema';
export declare class PlaceData {
    placeId: string;
    displayName: string;
    latitude?: number;
    longitude?: number;
    editorialSummary?: string;
    types?: string[];
    photos?: string[];
    googleMapsURI?: string;
    showDetails: boolean;
}
export declare class Blog extends Document {
    title: string;
    slug: string;
    content: string;
    summary: string;
    tags: string[];
    coverImage: string;
    destination: string | null;
    places: PlaceData[];
    createdBy: UserInfos;
    updatedBy: UserInfos;
    likes: Like[];
    status: string;
    flags: {
        userId: UserInfos | mongoose.Types.ObjectId;
        reason: string;
        date: Date;
    }[];
    metrics: {
        viewCount: number;
        likeCount: number;
        commentCount: number;
    };
}
export declare const BlogSchema: mongoose.Schema<Blog, mongoose.Model<Blog, any, any, any, mongoose.Document<unknown, any, Blog, any> & Blog & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Blog, mongoose.Document<unknown, {}, mongoose.FlatRecord<Blog>, {}> & mongoose.FlatRecord<Blog> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
