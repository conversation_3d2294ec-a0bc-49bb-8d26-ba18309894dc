import {
  ANIMATION_DURATION_MS,
  ANIMATION_TIMING_FUNCTION,
  ChartDataProvider,
  ChartsLegend,
  ChartsOverlay,
  ChartsSurface,
  ChartsTooltip,
  ChartsWrapper,
  DEFAULT_PIE_CHART_MARGIN,
  composeClasses,
  defaultizeMargin,
  deg2rad,
  generateUtilityClass,
  generateUtilityClasses,
  getLabel,
  useAnimatePieArc,
  useAnimatePieArcLabel,
  useChartContainerProps,
  useChartHighlight,
  useChartInteraction,
  useDrawingArea,
  useInteractionItemProps,
  useItemHighlightedGetter,
  usePieSeriesContext,
  useSkipAnimation
} from "./chunk-ZSDK7NHJ.js";
import {
  useThemeProps
} from "./chunk-ZZOFVYJW.js";
import {
  _objectWithoutPropertiesLoose
} from "./chunk-FD6QZ34J.js";
import {
  styled_default
} from "./chunk-2364KQYK.js";
import {
  _extends
} from "./chunk-EQCCHGRT.js";
import {
  require_prop_types
} from "./chunk-CCWQ57J5.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  require_react
} from "./chunk-EVIISGDI.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/x-charts/esm/PieChart/PieChart.js
var React7 = __toESM(require_react(), 1);
var import_prop_types6 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/PieChart/PiePlot.js
var React6 = __toESM(require_react(), 1);
var import_prop_types5 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/PieChart/PieArcPlot.js
var React3 = __toESM(require_react(), 1);
var import_prop_types2 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/PieChart/PieArc.js
var React = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var _excluded = ["classes", "color", "dataIndex", "id", "isFaded", "isHighlighted", "onClick", "cornerRadius", "startAngle", "endAngle", "innerRadius", "outerRadius", "paddingAngle", "skipAnimation"];
function getPieArcUtilityClass(slot) {
  return generateUtilityClass("MuiPieArc", slot);
}
var pieArcClasses = generateUtilityClasses("MuiPieArc", ["root", "highlighted", "faded", "series"]);
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted,
    dataIndex
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, `data-index-${dataIndex}`, isHighlighted && "highlighted", isFaded && "faded"]
  };
  return composeClasses(slots, getPieArcUtilityClass, classes);
};
var PieArcRoot = styled_default("path", {
  name: "MuiPieArc",
  slot: "Root",
  overridesResolver: (_, styles) => styles.arc
  // FIXME: Inconsistent naming with slot
})(({
  theme
}) => ({
  // Got to move stroke to an element prop instead of style.
  stroke: (theme.vars || theme).palette.background.paper,
  transitionProperty: "opacity, fill, filter",
  transitionDuration: `${ANIMATION_DURATION_MS}ms`,
  transitionTimingFunction: ANIMATION_TIMING_FUNCTION
}));
var PieArc = React.forwardRef(function PieArc2(props, ref) {
  const {
    classes: innerClasses,
    color,
    dataIndex,
    id,
    isFaded,
    isHighlighted,
    onClick,
    cornerRadius,
    startAngle,
    endAngle,
    innerRadius,
    outerRadius,
    paddingAngle,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded);
  const ownerState = {
    id,
    dataIndex,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted
  };
  const classes = useUtilityClasses(ownerState);
  const interactionProps = useInteractionItemProps({
    type: "pie",
    seriesId: id,
    dataIndex
  });
  const animatedProps = useAnimatePieArc({
    cornerRadius,
    startAngle,
    endAngle,
    innerRadius,
    outerRadius,
    paddingAngle,
    skipAnimation,
    ref
  });
  return (0, import_jsx_runtime.jsx)(PieArcRoot, _extends({
    onClick,
    cursor: onClick ? "pointer" : "unset",
    ownerState,
    className: classes.root,
    fill: ownerState.color,
    opacity: ownerState.isFaded ? 0.3 : 1,
    filter: ownerState.isHighlighted ? "brightness(120%)" : "none",
    strokeWidth: 1,
    strokeLinejoin: "round",
    "data-highlighted": ownerState.isHighlighted || void 0,
    "data-faded": ownerState.isFaded || void 0
  }, other, interactionProps, animatedProps));
});
if (true) PieArc.displayName = "PieArc";
true ? PieArc.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  classes: import_prop_types.default.object,
  cornerRadius: import_prop_types.default.number.isRequired,
  dataIndex: import_prop_types.default.number.isRequired,
  endAngle: import_prop_types.default.number.isRequired,
  id: import_prop_types.default.oneOfType([import_prop_types.default.number, import_prop_types.default.string]).isRequired,
  innerRadius: import_prop_types.default.number.isRequired,
  isFaded: import_prop_types.default.bool.isRequired,
  isHighlighted: import_prop_types.default.bool.isRequired,
  outerRadius: import_prop_types.default.number.isRequired,
  paddingAngle: import_prop_types.default.number.isRequired,
  /**
   * @default false
   */
  skipAnimation: import_prop_types.default.bool.isRequired,
  startAngle: import_prop_types.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/dataTransform/useTransformData.js
var React2 = __toESM(require_react(), 1);
function useTransformData(series) {
  const {
    id: seriesId,
    data,
    faded,
    highlighted,
    paddingAngle: basePaddingAngle = 0,
    innerRadius: baseInnerRadius = 0,
    arcLabelRadius: baseArcLabelRadius,
    outerRadius: baseOuterRadius,
    cornerRadius: baseCornerRadius = 0
  } = series;
  const {
    isFaded: isItemFaded,
    isHighlighted: isItemHighlighted
  } = useItemHighlightedGetter();
  const dataWithHighlight = React2.useMemo(() => data.map((item, itemIndex) => {
    const currentItem = {
      seriesId,
      dataIndex: itemIndex
    };
    const isHighlighted = isItemHighlighted(currentItem);
    const isFaded = !isHighlighted && isItemFaded(currentItem);
    const attributesOverride = _extends({
      additionalRadius: 0
    }, isFaded && faded || isHighlighted && highlighted || {});
    const paddingAngle = Math.max(0, deg2rad(attributesOverride.paddingAngle ?? basePaddingAngle));
    const innerRadius = Math.max(0, attributesOverride.innerRadius ?? baseInnerRadius);
    const outerRadius = Math.max(0, attributesOverride.outerRadius ?? baseOuterRadius + attributesOverride.additionalRadius);
    const cornerRadius = attributesOverride.cornerRadius ?? baseCornerRadius;
    const arcLabelRadius = attributesOverride.arcLabelRadius ?? baseArcLabelRadius ?? (innerRadius + outerRadius) / 2;
    return _extends({}, item, attributesOverride, {
      dataIndex: itemIndex,
      isFaded,
      isHighlighted,
      paddingAngle,
      innerRadius,
      outerRadius,
      cornerRadius,
      arcLabelRadius
    });
  }), [baseCornerRadius, baseInnerRadius, baseOuterRadius, basePaddingAngle, baseArcLabelRadius, data, faded, highlighted, isItemFaded, isItemHighlighted, seriesId]);
  return dataWithHighlight;
}

// node_modules/@mui/x-charts/esm/PieChart/PieArcPlot.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var _excluded2 = ["slots", "slotProps", "innerRadius", "outerRadius", "cornerRadius", "paddingAngle", "id", "highlighted", "faded", "data", "onItemClick", "skipAnimation"];
function PieArcPlot(props) {
  const {
    slots,
    slotProps,
    innerRadius = 0,
    outerRadius,
    cornerRadius = 0,
    paddingAngle = 0,
    id,
    highlighted,
    faded = {
      additionalRadius: -5
    },
    data,
    onItemClick,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded2);
  const transformedData = useTransformData({
    innerRadius,
    outerRadius,
    cornerRadius,
    paddingAngle,
    id,
    highlighted,
    faded,
    data
  });
  if (data.length === 0) {
    return null;
  }
  const Arc = (slots == null ? void 0 : slots.pieArc) ?? PieArc;
  return (0, import_jsx_runtime2.jsx)("g", _extends({}, other, {
    children: transformedData.map((item, index) => (0, import_jsx_runtime2.jsx)(Arc, _extends({
      startAngle: item.startAngle,
      endAngle: item.endAngle,
      paddingAngle: item.paddingAngle,
      innerRadius: item.innerRadius,
      outerRadius: item.outerRadius,
      cornerRadius: item.cornerRadius,
      skipAnimation: skipAnimation ?? false,
      id,
      color: item.color,
      dataIndex: index,
      isFaded: item.isFaded,
      isHighlighted: item.isHighlighted,
      onClick: onItemClick && ((event) => {
        onItemClick(event, {
          type: "pie",
          seriesId: id,
          dataIndex: index
        }, item);
      })
    }, slotProps == null ? void 0 : slotProps.pieArc), item.dataIndex))
  }));
}
true ? PieArcPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The radius between circle center and the arc label in px.
   * @default (innerRadius - outerRadius) / 2
   */
  arcLabelRadius: import_prop_types2.default.number,
  /**
   * The radius applied to arc corners (similar to border radius).
   * @default 0
   */
  cornerRadius: import_prop_types2.default.number,
  data: import_prop_types2.default.arrayOf(import_prop_types2.default.shape({
    color: import_prop_types2.default.string.isRequired,
    endAngle: import_prop_types2.default.number.isRequired,
    formattedValue: import_prop_types2.default.string.isRequired,
    id: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]),
    index: import_prop_types2.default.number.isRequired,
    label: import_prop_types2.default.oneOfType([import_prop_types2.default.func, import_prop_types2.default.string]),
    labelMarkType: import_prop_types2.default.oneOfType([import_prop_types2.default.oneOf(["circle", "line", "square"]), import_prop_types2.default.func]),
    padAngle: import_prop_types2.default.number.isRequired,
    startAngle: import_prop_types2.default.number.isRequired,
    value: import_prop_types2.default.number.isRequired
  })).isRequired,
  /**
   * Override the arc attributes when it is faded.
   * @default { additionalRadius: -5 }
   */
  faded: import_prop_types2.default.shape({
    additionalRadius: import_prop_types2.default.number,
    arcLabelRadius: import_prop_types2.default.number,
    color: import_prop_types2.default.string,
    cornerRadius: import_prop_types2.default.number,
    innerRadius: import_prop_types2.default.number,
    outerRadius: import_prop_types2.default.number,
    paddingAngle: import_prop_types2.default.number
  }),
  /**
   * Override the arc attributes when it is highlighted.
   */
  highlighted: import_prop_types2.default.shape({
    additionalRadius: import_prop_types2.default.number,
    arcLabelRadius: import_prop_types2.default.number,
    color: import_prop_types2.default.string,
    cornerRadius: import_prop_types2.default.number,
    innerRadius: import_prop_types2.default.number,
    outerRadius: import_prop_types2.default.number,
    paddingAngle: import_prop_types2.default.number
  }),
  /**
   * The id of this series.
   */
  id: import_prop_types2.default.oneOfType([import_prop_types2.default.number, import_prop_types2.default.string]).isRequired,
  /**
   * The radius between circle center and the beginning of the arc.
   * @default 0
   */
  innerRadius: import_prop_types2.default.number,
  /**
   * Callback fired when a pie item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.
   * @param {DefaultizedPieValueType} item The pie item.
   */
  onItemClick: import_prop_types2.default.func,
  /**
   * The radius between circle center and the end of the arc.
   */
  outerRadius: import_prop_types2.default.number.isRequired,
  /**
   * The padding angle (deg) between two arcs.
   * @default 0
   */
  paddingAngle: import_prop_types2.default.number,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types2.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types2.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types2.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabelPlot.js
var React5 = __toESM(require_react(), 1);
var import_prop_types4 = __toESM(require_prop_types(), 1);

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabel.js
var React4 = __toESM(require_react(), 1);
var import_prop_types3 = __toESM(require_prop_types(), 1);
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var _excluded3 = ["id", "classes", "color", "startAngle", "endAngle", "paddingAngle", "arcLabelRadius", "innerRadius", "outerRadius", "cornerRadius", "formattedArcLabel", "isHighlighted", "isFaded", "style", "skipAnimation"];
function getPieArcLabelUtilityClass(slot) {
  return generateUtilityClass("MuiPieArcLabel", slot);
}
var pieArcLabelClasses = generateUtilityClasses("MuiPieArcLabel", ["root", "highlighted", "faded", "animate", "series"]);
var useUtilityClasses2 = (ownerState) => {
  const {
    classes,
    id,
    isFaded,
    isHighlighted,
    skipAnimation
  } = ownerState;
  const slots = {
    root: ["root", `series-${id}`, isHighlighted && "highlighted", isFaded && "faded", !skipAnimation && "animate"]
  };
  return composeClasses(slots, getPieArcLabelUtilityClass, classes);
};
var PieArcLabelRoot = styled_default("text", {
  name: "MuiPieArcLabel",
  slot: "Root"
})(({
  theme
}) => ({
  fill: (theme.vars || theme).palette.text.primary,
  textAnchor: "middle",
  dominantBaseline: "middle",
  pointerEvents: "none",
  animationName: "animate-opacity",
  animationDuration: "0s",
  animationTimingFunction: ANIMATION_TIMING_FUNCTION,
  [`&.${pieArcLabelClasses.animate}`]: {
    animationDuration: `${ANIMATION_DURATION_MS}ms`
  },
  "@keyframes animate-opacity": {
    from: {
      opacity: 0
    }
  }
}));
var PieArcLabel = React4.forwardRef(function PieArcLabel2(props, ref) {
  const {
    id,
    classes: innerClasses,
    color,
    startAngle,
    endAngle,
    paddingAngle,
    arcLabelRadius,
    cornerRadius,
    formattedArcLabel,
    isHighlighted,
    isFaded,
    skipAnimation
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded3);
  const ownerState = {
    id,
    classes: innerClasses,
    color,
    isFaded,
    isHighlighted,
    skipAnimation
  };
  const classes = useUtilityClasses2(ownerState);
  const animatedProps = useAnimatePieArcLabel({
    cornerRadius,
    startAngle,
    endAngle,
    innerRadius: arcLabelRadius,
    outerRadius: arcLabelRadius,
    paddingAngle,
    skipAnimation,
    ref
  });
  return (0, import_jsx_runtime3.jsx)(PieArcLabelRoot, _extends({
    className: classes.root
  }, other, animatedProps, {
    children: formattedArcLabel
  }));
});
if (true) PieArcLabel.displayName = "PieArcLabel";
true ? PieArcLabel.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  arcLabelRadius: import_prop_types3.default.number.isRequired,
  classes: import_prop_types3.default.object,
  color: import_prop_types3.default.string.isRequired,
  cornerRadius: import_prop_types3.default.number.isRequired,
  endAngle: import_prop_types3.default.number.isRequired,
  formattedArcLabel: import_prop_types3.default.string,
  id: import_prop_types3.default.oneOfType([import_prop_types3.default.number, import_prop_types3.default.string]).isRequired,
  innerRadius: import_prop_types3.default.number.isRequired,
  isFaded: import_prop_types3.default.bool.isRequired,
  isHighlighted: import_prop_types3.default.bool.isRequired,
  outerRadius: import_prop_types3.default.number.isRequired,
  paddingAngle: import_prop_types3.default.number.isRequired,
  skipAnimation: import_prop_types3.default.bool.isRequired,
  startAngle: import_prop_types3.default.number.isRequired
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieArcLabelPlot.js
var import_jsx_runtime4 = __toESM(require_jsx_runtime(), 1);
var _excluded4 = ["arcLabel", "arcLabelMinAngle", "arcLabelRadius", "cornerRadius", "data", "faded", "highlighted", "id", "innerRadius", "outerRadius", "paddingAngle", "skipAnimation", "slotProps", "slots"];
var RATIO = 180 / Math.PI;
function getItemLabel(arcLabel, arcLabelMinAngle, item) {
  var _a;
  if (!arcLabel) {
    return null;
  }
  const angle = (item.endAngle - item.startAngle) * RATIO;
  if (angle < arcLabelMinAngle) {
    return null;
  }
  switch (arcLabel) {
    case "label":
      return getLabel(item.label, "arc");
    case "value":
      return (_a = item.value) == null ? void 0 : _a.toString();
    case "formattedValue":
      return item.formattedValue;
    default:
      return arcLabel(_extends({}, item, {
        label: getLabel(item.label, "arc")
      }));
  }
}
function PieArcLabelPlot(props) {
  const {
    arcLabel,
    arcLabelMinAngle = 0,
    arcLabelRadius,
    cornerRadius = 0,
    data,
    faded = {
      additionalRadius: -5
    },
    highlighted,
    id,
    innerRadius,
    outerRadius,
    paddingAngle = 0,
    skipAnimation,
    slotProps,
    slots
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded4);
  const transformedData = useTransformData({
    innerRadius,
    outerRadius,
    arcLabelRadius,
    cornerRadius,
    paddingAngle,
    id,
    highlighted,
    faded,
    data
  });
  if (data.length === 0) {
    return null;
  }
  const ArcLabel = (slots == null ? void 0 : slots.pieArcLabel) ?? PieArcLabel;
  return (0, import_jsx_runtime4.jsx)("g", _extends({}, other, {
    children: transformedData.map((item) => (0, import_jsx_runtime4.jsx)(ArcLabel, _extends({
      startAngle: item.startAngle,
      endAngle: item.endAngle,
      paddingAngle: item.paddingAngle,
      innerRadius: item.innerRadius,
      outerRadius: item.outerRadius,
      arcLabelRadius: item.arcLabelRadius,
      cornerRadius: item.cornerRadius,
      id,
      color: item.color,
      isFaded: item.isFaded,
      isHighlighted: item.isHighlighted,
      formattedArcLabel: getItemLabel(arcLabel, arcLabelMinAngle, item),
      skipAnimation: skipAnimation ?? false
    }, slotProps == null ? void 0 : slotProps.pieArcLabel), item.id ?? item.dataIndex))
  }));
}
true ? PieArcLabelPlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * The label displayed into the arc.
   */
  arcLabel: import_prop_types4.default.oneOfType([import_prop_types4.default.oneOf(["formattedValue", "label", "value"]), import_prop_types4.default.func]),
  /**
   * The minimal angle required to display the arc label.
   * @default 0
   */
  arcLabelMinAngle: import_prop_types4.default.number,
  /**
   * The radius between circle center and the arc label in px.
   * @default (innerRadius - outerRadius) / 2
   */
  arcLabelRadius: import_prop_types4.default.number,
  /**
   * The radius applied to arc corners (similar to border radius).
   * @default 0
   */
  cornerRadius: import_prop_types4.default.number,
  data: import_prop_types4.default.arrayOf(import_prop_types4.default.shape({
    color: import_prop_types4.default.string.isRequired,
    endAngle: import_prop_types4.default.number.isRequired,
    formattedValue: import_prop_types4.default.string.isRequired,
    id: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.string]),
    index: import_prop_types4.default.number.isRequired,
    label: import_prop_types4.default.oneOfType([import_prop_types4.default.func, import_prop_types4.default.string]),
    labelMarkType: import_prop_types4.default.oneOfType([import_prop_types4.default.oneOf(["circle", "line", "square"]), import_prop_types4.default.func]),
    padAngle: import_prop_types4.default.number.isRequired,
    startAngle: import_prop_types4.default.number.isRequired,
    value: import_prop_types4.default.number.isRequired
  })).isRequired,
  /**
   * Override the arc attributes when it is faded.
   * @default { additionalRadius: -5 }
   */
  faded: import_prop_types4.default.shape({
    additionalRadius: import_prop_types4.default.number,
    arcLabelRadius: import_prop_types4.default.number,
    color: import_prop_types4.default.string,
    cornerRadius: import_prop_types4.default.number,
    innerRadius: import_prop_types4.default.number,
    outerRadius: import_prop_types4.default.number,
    paddingAngle: import_prop_types4.default.number
  }),
  /**
   * Override the arc attributes when it is highlighted.
   */
  highlighted: import_prop_types4.default.shape({
    additionalRadius: import_prop_types4.default.number,
    arcLabelRadius: import_prop_types4.default.number,
    color: import_prop_types4.default.string,
    cornerRadius: import_prop_types4.default.number,
    innerRadius: import_prop_types4.default.number,
    outerRadius: import_prop_types4.default.number,
    paddingAngle: import_prop_types4.default.number
  }),
  /**
   * The id of this series.
   */
  id: import_prop_types4.default.oneOfType([import_prop_types4.default.number, import_prop_types4.default.string]).isRequired,
  /**
   * The radius between circle center and the beginning of the arc.
   * @default 0
   */
  innerRadius: import_prop_types4.default.number,
  /**
   * The radius between circle center and the end of the arc.
   */
  outerRadius: import_prop_types4.default.number.isRequired,
  /**
   * The padding angle (deg) between two arcs.
   * @default 0
   */
  paddingAngle: import_prop_types4.default.number,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types4.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types4.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types4.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/internals/getPercentageValue.js
function getPercentageValue(value, refValue) {
  if (typeof value === "number") {
    return value;
  }
  if (value === "100%") {
    return refValue;
  }
  if (value.endsWith("%")) {
    const percentage = Number.parseFloat(value.slice(0, value.length - 1));
    if (!Number.isNaN(percentage)) {
      return percentage * refValue / 100;
    }
  }
  if (value.endsWith("px")) {
    const val = Number.parseFloat(value.slice(0, value.length - 2));
    if (!Number.isNaN(val)) {
      return val;
    }
  }
  throw new Error(`MUI X Charts: Received an unknown value "${value}". It should be a number, or a string with a percentage value.`);
}

// node_modules/@mui/x-charts/esm/PieChart/getPieCoordinates.js
function getPieCoordinates(series, drawing) {
  const {
    height,
    width
  } = drawing;
  const {
    cx: cxParam,
    cy: cyParam
  } = series;
  const availableRadius = Math.min(width, height) / 2;
  const cx = getPercentageValue(cxParam ?? "50%", width);
  const cy = getPercentageValue(cyParam ?? "50%", height);
  return {
    cx,
    cy,
    availableRadius
  };
}

// node_modules/@mui/x-charts/esm/PieChart/pieClasses.js
function getPieUtilityClass(slot) {
  return generateUtilityClass("MuiPieChart", slot);
}
var pieClasses = generateUtilityClasses("MuiPieChart", ["root", "series", "seriesLabels"]);
var useUtilityClasses3 = (classes) => {
  const slots = {
    root: ["root"],
    series: ["series"],
    seriesLabels: ["seriesLabels"]
  };
  return composeClasses(slots, getPieUtilityClass, classes);
};

// node_modules/@mui/x-charts/esm/PieChart/PiePlot.js
var import_jsx_runtime5 = __toESM(require_jsx_runtime(), 1);
function PiePlot(props) {
  const {
    skipAnimation: inSkipAnimation,
    slots,
    slotProps,
    onItemClick
  } = props;
  const seriesData = usePieSeriesContext();
  const {
    left,
    top,
    width,
    height
  } = useDrawingArea();
  const skipAnimation = useSkipAnimation(inSkipAnimation);
  const classes = useUtilityClasses3();
  if (seriesData === void 0) {
    return null;
  }
  const {
    series,
    seriesOrder
  } = seriesData;
  return (0, import_jsx_runtime5.jsxs)("g", {
    children: [seriesOrder.map((seriesId) => {
      const {
        innerRadius: innerRadiusParam,
        outerRadius: outerRadiusParam,
        cornerRadius,
        paddingAngle,
        data,
        cx: cxParam,
        cy: cyParam,
        highlighted,
        faded
      } = series[seriesId];
      const {
        cx,
        cy,
        availableRadius
      } = getPieCoordinates({
        cx: cxParam,
        cy: cyParam
      }, {
        width,
        height
      });
      const outerRadius = getPercentageValue(outerRadiusParam ?? availableRadius, availableRadius);
      const innerRadius = getPercentageValue(innerRadiusParam ?? 0, availableRadius);
      return (0, import_jsx_runtime5.jsx)("g", {
        className: classes.series,
        transform: `translate(${left + cx}, ${top + cy})`,
        "data-series": seriesId,
        children: (0, import_jsx_runtime5.jsx)(PieArcPlot, {
          innerRadius,
          outerRadius,
          cornerRadius,
          paddingAngle,
          id: seriesId,
          data,
          skipAnimation,
          highlighted,
          faded,
          onItemClick,
          slots,
          slotProps
        })
      }, seriesId);
    }), seriesOrder.map((seriesId) => {
      const {
        innerRadius: innerRadiusParam,
        outerRadius: outerRadiusParam,
        arcLabelRadius: arcLabelRadiusParam,
        cornerRadius,
        paddingAngle,
        arcLabel,
        arcLabelMinAngle,
        data,
        cx: cxParam,
        cy: cyParam
      } = series[seriesId];
      const {
        cx,
        cy,
        availableRadius
      } = getPieCoordinates({
        cx: cxParam,
        cy: cyParam
      }, {
        width,
        height
      });
      const outerRadius = getPercentageValue(outerRadiusParam ?? availableRadius, availableRadius);
      const innerRadius = getPercentageValue(innerRadiusParam ?? 0, availableRadius);
      const arcLabelRadius = arcLabelRadiusParam === void 0 ? (outerRadius + innerRadius) / 2 : getPercentageValue(arcLabelRadiusParam, availableRadius);
      return (0, import_jsx_runtime5.jsx)("g", {
        className: classes.seriesLabels,
        transform: `translate(${left + cx}, ${top + cy})`,
        "data-series": seriesId,
        children: (0, import_jsx_runtime5.jsx)(PieArcLabelPlot, {
          innerRadius,
          outerRadius: outerRadius ?? availableRadius,
          arcLabelRadius,
          cornerRadius,
          paddingAngle,
          id: seriesId,
          data,
          skipAnimation,
          arcLabel,
          arcLabelMinAngle,
          slots,
          slotProps
        })
      }, seriesId);
    })]
  });
}
true ? PiePlot.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Callback fired when a pie item is clicked.
   * @param {React.MouseEvent<SVGPathElement, MouseEvent>} event The event source of the callback.
   * @param {PieItemIdentifier} pieItemIdentifier The pie item identifier.
   * @param {DefaultizedPieValueType} item The pie item.
   */
  onItemClick: import_prop_types5.default.func,
  /**
   * If `true`, animations are skipped.
   * @default false
   */
  skipAnimation: import_prop_types5.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types5.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types5.default.object
} : void 0;

// node_modules/@mui/x-charts/esm/PieChart/PieChart.plugins.js
var PIE_CHART_PLUGINS = [useChartInteraction, useChartHighlight];

// node_modules/@mui/x-charts/esm/PieChart/PieChart.js
var import_jsx_runtime6 = __toESM(require_jsx_runtime(), 1);
var _excluded5 = ["series", "width", "height", "margin", "colors", "sx", "skipAnimation", "hideLegend", "children", "slots", "slotProps", "onItemClick", "loading", "highlightedItem", "onHighlightChange", "className", "showToolbar"];
var PieChart = React7.forwardRef(function PieChart2(inProps, ref) {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  const props = useThemeProps({
    props: inProps,
    name: "MuiPieChart"
  });
  const {
    series,
    width,
    height,
    margin: marginProps,
    colors,
    sx,
    skipAnimation,
    hideLegend,
    children,
    slots,
    slotProps,
    onItemClick,
    loading,
    highlightedItem,
    onHighlightChange,
    className,
    showToolbar
  } = props, other = _objectWithoutPropertiesLoose(props, _excluded5);
  const margin = defaultizeMargin(marginProps, DEFAULT_PIE_CHART_MARGIN);
  const {
    chartDataProviderProps,
    chartsSurfaceProps
  } = useChartContainerProps(_extends({}, other, {
    series: series.map((s) => _extends({
      type: "pie"
    }, s)),
    width,
    height,
    margin,
    colors,
    highlightedItem,
    onHighlightChange,
    className,
    skipAnimation,
    plugins: PIE_CHART_PLUGINS
  }), ref);
  const Tooltip = (slots == null ? void 0 : slots.tooltip) ?? ChartsTooltip;
  const Toolbar = (_a = props.slots) == null ? void 0 : _a.toolbar;
  return (0, import_jsx_runtime6.jsx)(ChartDataProvider, _extends({}, chartDataProviderProps, {
    children: (0, import_jsx_runtime6.jsxs)(ChartsWrapper, {
      legendPosition: (_c = (_b = props.slotProps) == null ? void 0 : _b.legend) == null ? void 0 : _c.position,
      legendDirection: ((_e = (_d = props == null ? void 0 : props.slotProps) == null ? void 0 : _d.legend) == null ? void 0 : _e.direction) ?? "vertical",
      sx,
      children: [showToolbar && Toolbar ? (0, import_jsx_runtime6.jsx)(Toolbar, _extends({}, (_f = props.slotProps) == null ? void 0 : _f.toolbar)) : null, !hideLegend && (0, import_jsx_runtime6.jsx)(ChartsLegend, {
        direction: ((_h = (_g = props == null ? void 0 : props.slotProps) == null ? void 0 : _g.legend) == null ? void 0 : _h.direction) ?? "vertical",
        slots,
        slotProps
      }), (0, import_jsx_runtime6.jsxs)(ChartsSurface, _extends({}, chartsSurfaceProps, {
        children: [(0, import_jsx_runtime6.jsx)(PiePlot, {
          slots,
          slotProps,
          onItemClick
        }), (0, import_jsx_runtime6.jsx)(ChartsOverlay, {
          loading,
          slots,
          slotProps
        }), children]
      })), !loading && (0, import_jsx_runtime6.jsx)(Tooltip, _extends({
        trigger: "item"
      }, slotProps == null ? void 0 : slotProps.tooltip))]
    })
  }));
});
if (true) PieChart.displayName = "PieChart";
true ? PieChart.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  apiRef: import_prop_types6.default.shape({
    current: import_prop_types6.default.object
  }),
  children: import_prop_types6.default.node,
  className: import_prop_types6.default.string,
  /**
   * Color palette used to colorize multiple series.
   * @default rainbowSurgePalette
   */
  colors: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.string), import_prop_types6.default.func]),
  /**
   * An array of objects that can be used to populate series and axes data using their `dataKey` property.
   */
  dataset: import_prop_types6.default.arrayOf(import_prop_types6.default.object),
  desc: import_prop_types6.default.string,
  /**
   * The height of the chart in px. If not defined, it takes the height of the parent element.
   */
  height: import_prop_types6.default.number,
  /**
   * If `true`, the legend is not rendered.
   */
  hideLegend: import_prop_types6.default.bool,
  /**
   * The highlighted item.
   * Used when the highlight is controlled.
   */
  highlightedItem: import_prop_types6.default.shape({
    dataIndex: import_prop_types6.default.number,
    seriesId: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.string]).isRequired
  }),
  /**
   * This prop is used to help implement the accessibility logic.
   * If you don't provide this prop. It falls back to a randomly generated id.
   */
  id: import_prop_types6.default.string,
  /**
   * If `true`, a loading overlay is displayed.
   * @default false
   */
  loading: import_prop_types6.default.bool,
  /**
   * Localized text for chart components.
   */
  localeText: import_prop_types6.default.object,
  /**
   * The margin between the SVG and the drawing area.
   * It's used for leaving some space for extra information such as the x- and y-axis or legend.
   *
   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.
   */
  margin: import_prop_types6.default.oneOfType([import_prop_types6.default.number, import_prop_types6.default.shape({
    bottom: import_prop_types6.default.number,
    left: import_prop_types6.default.number,
    right: import_prop_types6.default.number,
    top: import_prop_types6.default.number
  })]),
  /**
   * The callback fired when the highlighted item changes.
   *
   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.
   */
  onHighlightChange: import_prop_types6.default.func,
  /**
   * Callback fired when a pie arc is clicked.
   */
  onItemClick: import_prop_types6.default.func,
  /**
   * The series to display in the pie chart.
   * An array of [[PieSeries]] objects.
   */
  series: import_prop_types6.default.arrayOf(import_prop_types6.default.object).isRequired,
  /**
   * If true, shows the default chart toolbar.
   * @default false
   */
  showToolbar: import_prop_types6.default.bool,
  /**
   * If `true`, animations are skipped.
   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
   */
  skipAnimation: import_prop_types6.default.bool,
  /**
   * The props used for each component slot.
   * @default {}
   */
  slotProps: import_prop_types6.default.object,
  /**
   * Overridable component slots.
   * @default {}
   */
  slots: import_prop_types6.default.object,
  sx: import_prop_types6.default.oneOfType([import_prop_types6.default.arrayOf(import_prop_types6.default.oneOfType([import_prop_types6.default.func, import_prop_types6.default.object, import_prop_types6.default.bool])), import_prop_types6.default.func, import_prop_types6.default.object]),
  theme: import_prop_types6.default.oneOf(["dark", "light"]),
  title: import_prop_types6.default.string,
  /**
   * The width of the chart in px. If not defined, it takes the width of the parent element.
   */
  width: import_prop_types6.default.number
} : void 0;

export {
  getPieArcUtilityClass,
  pieArcClasses,
  PieArc,
  PieArcPlot,
  getPieArcLabelUtilityClass,
  pieArcLabelClasses,
  PieArcLabel,
  PieArcLabelPlot,
  getPercentageValue,
  getPieCoordinates,
  pieClasses,
  PiePlot,
  PieChart
};
//# sourceMappingURL=chunk-RI6DBWEH.js.map
