import mongoose, { Document, ObjectId } from 'mongoose';
import { UserInfos } from './userinfo.schema';
export declare class Trip extends Document {
    title: string;
    destination: {
        id: string;
        name: string;
        location: {
            lat: number;
            lng: number;
        };
    };
    coverImage?: ObjectId;
    startDate: Date;
    endDate: Date;
    budgetRange: string;
    tripmateRange: string;
    description: string;
    createdBy: UserInfos | ObjectId;
    visibility: boolean;
    tripmates: string[];
}
export declare const TripSchema: mongoose.Schema<Trip, mongoose.Model<Trip, any, any, any, mongoose.Document<unknown, any, Trip, any> & <PERSON> & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, mongoose.DefaultSchemaOptions, Trip, mongoose.Document<unknown, {}, mongoose.FlatRecord<Trip>, {}> & mongoose.FlatRecord<Trip> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
