@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

body {
  --tt-toolbar-height: 44px;
  --tt-theme-text: var(--tt-gray-light-900);

  .dark & {
    --tt-theme-text: var(--tt-gray-dark-900);
  }
}

body {
  font-family: "Inter", sans-serif;
  color: var(--tt-theme-text);
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
  padding: 0;
}

html,
body,
#root,
#app {
  height: 100%;
  background-color: var(--tt-bg-color);
}

.tiptap.ProseMirror {
  font-family: "DM Sans", sans-serif;
}

.content-wrapper {
  height: calc(100% - var(--tt-toolbar-height));
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: block;
    width: 0.5rem;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--tt-scrollbar-color);
    border-radius: 4px;
  }

  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: var(--tt-scrollbar-color) transparent;
}

.simple-editor-content {
  max-width: 100%;
  width: 100%;
  margin: 0 auto;
}

.simple-editor-content .tiptap.ProseMirror {
  padding: 1rem 1rem;
}

@media screen and (max-width: 480px) {
  .simple-editor-content .tiptap.ProseMirror {
    padding: 1rem 1.5rem;
  }
}
// styles.scss
.custom-node {
  border: 1px solid #ccc;
  padding: 12px;
  border-radius: 6px;
  background: #f9f9f9;
  font-style: italic;
}
