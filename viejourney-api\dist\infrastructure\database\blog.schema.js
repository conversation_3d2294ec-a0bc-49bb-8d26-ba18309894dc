"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BlogSchema = exports.Blog = exports.PlaceData = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const userinfo_schema_1 = require("./userinfo.schema");
let PlaceData = class PlaceData {
    placeId;
    displayName;
    latitude;
    longitude;
    editorialSummary;
    types;
    photos;
    googleMapsURI;
    showDetails;
};
exports.PlaceData = PlaceData;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PlaceData.prototype, "placeId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PlaceData.prototype, "displayName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Number)
], PlaceData.prototype, "latitude", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Number)
], PlaceData.prototype, "longitude", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], PlaceData.prototype, "editorialSummary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], required: false }),
    __metadata("design:type", Array)
], PlaceData.prototype, "types", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], required: false }),
    __metadata("design:type", Array)
], PlaceData.prototype, "photos", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], PlaceData.prototype, "googleMapsURI", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, default: false }),
    __metadata("design:type", Boolean)
], PlaceData.prototype, "showDetails", void 0);
exports.PlaceData = PlaceData = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], PlaceData);
const PlaceDataSchema = mongoose_1.SchemaFactory.createForClass(PlaceData);
let Blog = class Blog extends mongoose_2.Document {
    title;
    slug;
    content;
    summary;
    tags;
    coverImage;
    destination;
    places;
    createdBy;
    updatedBy;
    likes;
    status;
    flags;
    metrics;
};
exports.Blog = Blog;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Blog.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, index: true }),
    __metadata("design:type", String)
], Blog.prototype, "slug", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Blog.prototype, "content", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Blog.prototype, "summary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], required: false }),
    __metadata("design:type", Array)
], Blog.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", String)
], Blog.prototype, "coverImage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        default: null,
        required: false,
    }),
    __metadata("design:type", Object)
], Blog.prototype, "destination", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: [PlaceDataSchema],
        required: false,
        default: [],
    }),
    __metadata("design:type", Array)
], Blog.prototype, "places", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.default.Types.ObjectId, ref: 'UserInfos' }),
    __metadata("design:type", userinfo_schema_1.UserInfos)
], Blog.prototype, "createdBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.default.Types.ObjectId, ref: 'UserInfos' }),
    __metadata("design:type", userinfo_schema_1.UserInfos)
], Blog.prototype, "updatedBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: [{ type: mongoose_2.default.Types.ObjectId, ref: 'Like' }],
        required: false,
    }),
    __metadata("design:type", Array)
], Blog.prototype, "likes", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: String,
        enum: ['DRAFT', 'PENDING', 'APPROVED', 'REJECTED'],
        default: 'DRAFT',
    }),
    __metadata("design:type", String)
], Blog.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: [
            {
                userId: {
                    type: mongoose_2.default.Schema.Types.ObjectId,
                    ref: 'UserInfos',
                    required: true,
                },
                reason: { type: String, required: true },
                date: { type: Date, default: Date.now },
            },
        ],
        default: [],
        required: false,
    }),
    __metadata("design:type", Array)
], Blog.prototype, "flags", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: {
            viewCount: Number,
            likeCount: Number,
            commentCount: Number,
        },
        _id: false,
        required: false,
        default: {
            viewCount: 0,
            likeCount: 0,
            commentCount: 0,
        },
    }),
    __metadata("design:type", Object)
], Blog.prototype, "metrics", void 0);
exports.Blog = Blog = __decorate([
    (0, mongoose_1.Schema)({
        versionKey: false,
        timestamps: true,
    })
], Blog);
exports.BlogSchema = mongoose_1.SchemaFactory.createForClass(Blog);
//# sourceMappingURL=blog.schema.js.map