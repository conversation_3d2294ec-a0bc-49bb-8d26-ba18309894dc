{"version": 3, "sources": ["../../react-number-format/dist/react-number-format.es.js"], "sourcesContent": ["/**\n * react-number-format - 5.4.4\n * Author : <PERSON><PERSON><PERSON><PERSON>da<PERSON>\n * Copyright (c) 2016, 2025 to <PERSON><PERSON><PERSON><PERSON> Yadav, released under the MIT license.\n * https://github.com/s-yadav/react-number-format\n */\n\nimport React, { useState, useMemo, useRef, useEffect, useLayoutEffect } from 'react';\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CO<PERSON>EQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) { if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        { t[p] = s[p]; } }\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        { for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                { t[p[i]] = s[p[i]]; }\r\n        } }\r\n    return t;\r\n}\n\nvar SourceType;\n(function (SourceType) {\n    SourceType[\"event\"] = \"event\";\n    SourceType[\"props\"] = \"prop\";\n})(SourceType || (SourceType = {}));\n\n// basic noop function\nfunction noop() { }\nfunction memoizeOnce(cb) {\n    var lastArgs;\n    var lastValue = undefined;\n    return function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        if (lastArgs &&\n            args.length === lastArgs.length &&\n            args.every(function (value, index) { return value === lastArgs[index]; })) {\n            return lastValue;\n        }\n        lastArgs = args;\n        lastValue = cb.apply(void 0, args);\n        return lastValue;\n    };\n}\nfunction charIsNumber(char) {\n    return !!(char || '').match(/\\d/);\n}\nfunction isNil(val) {\n    return val === null || val === undefined;\n}\nfunction isNanValue(val) {\n    return typeof val === 'number' && isNaN(val);\n}\nfunction isNotValidValue(val) {\n    return isNil(val) || isNanValue(val) || (typeof val === 'number' && !isFinite(val));\n}\nfunction escapeRegExp(str) {\n    return str.replace(/[-[\\]/{}()*+?.\\\\^$|]/g, '\\\\$&');\n}\nfunction getThousandsGroupRegex(thousandsGroupStyle) {\n    switch (thousandsGroupStyle) {\n        case 'lakh':\n            return /(\\d+?)(?=(\\d\\d)+(\\d)(?!\\d))(\\.\\d+)?/g;\n        case 'wan':\n            return /(\\d)(?=(\\d{4})+(?!\\d))/g;\n        case 'thousand':\n        default:\n            return /(\\d)(?=(\\d{3})+(?!\\d))/g;\n    }\n}\nfunction applyThousandSeparator(str, thousandSeparator, thousandsGroupStyle) {\n    var thousandsGroupRegex = getThousandsGroupRegex(thousandsGroupStyle);\n    var index = str.search(/[1-9]/);\n    index = index === -1 ? str.length : index;\n    return (str.substring(0, index) +\n        str.substring(index, str.length).replace(thousandsGroupRegex, '$1' + thousandSeparator));\n}\nfunction usePersistentCallback(cb) {\n    var callbackRef = useRef(cb);\n    // keep the callback ref upto date\n    callbackRef.current = cb;\n    /**\n     * initialize a persistent callback which never changes\n     * through out the component lifecycle\n     */\n    var persistentCbRef = useRef(function () {\n        var args = [], len = arguments.length;\n        while ( len-- ) args[ len ] = arguments[ len ];\n\n        return callbackRef.current.apply(callbackRef, args);\n    });\n    return persistentCbRef.current;\n}\n//spilt a float number into different parts beforeDecimal, afterDecimal, and negation\nfunction splitDecimal(numStr, allowNegative) {\n    if ( allowNegative === void 0 ) allowNegative = true;\n\n    var hasNegation = numStr[0] === '-';\n    var addNegation = hasNegation && allowNegative;\n    numStr = numStr.replace('-', '');\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0];\n    var afterDecimal = parts[1] || '';\n    return {\n        beforeDecimal: beforeDecimal,\n        afterDecimal: afterDecimal,\n        hasNegation: hasNegation,\n        addNegation: addNegation,\n    };\n}\nfunction fixLeadingZero(numStr) {\n    if (!numStr)\n        { return numStr; }\n    var isNegative = numStr[0] === '-';\n    if (isNegative)\n        { numStr = numStr.substring(1, numStr.length); }\n    var parts = numStr.split('.');\n    var beforeDecimal = parts[0].replace(/^0+/, '') || '0';\n    var afterDecimal = parts[1] || '';\n    return (\"\" + (isNegative ? '-' : '') + beforeDecimal + (afterDecimal ? (\".\" + afterDecimal) : ''));\n}\n/**\n * limit decimal numbers to given scale\n * Not used .fixedTo because that will break with big numbers\n */\nfunction limitToScale(numStr, scale, fixedDecimalScale) {\n    var str = '';\n    var filler = fixedDecimalScale ? '0' : '';\n    for (var i = 0; i <= scale - 1; i++) {\n        str += numStr[i] || filler;\n    }\n    return str;\n}\nfunction repeat(str, count) {\n    return Array(count + 1).join(str);\n}\nfunction toNumericString(num) {\n    var _num = num + ''; // typecast number to string\n    // store the sign and remove it from the number.\n    var sign = _num[0] === '-' ? '-' : '';\n    if (sign)\n        { _num = _num.substring(1); }\n    // split the number into cofficient and exponent\n    var ref = _num.split(/[eE]/g);\n    var coefficient = ref[0];\n    var exponent = ref[1];\n    // covert exponent to number;\n    exponent = Number(exponent);\n    // if there is no exponent part or its 0, return the coffiecient with sign\n    if (!exponent)\n        { return sign + coefficient; }\n    coefficient = coefficient.replace('.', '');\n    /**\n     * for scientific notation the current decimal index will be after first number (index 0)\n     * So effective decimal index will always be 1 + exponent value\n     */\n    var decimalIndex = 1 + exponent;\n    var coffiecientLn = coefficient.length;\n    if (decimalIndex < 0) {\n        // if decimal index is less then 0 add preceding 0s\n        // add 1 as join will have\n        coefficient = '0.' + repeat('0', Math.abs(decimalIndex)) + coefficient;\n    }\n    else if (decimalIndex >= coffiecientLn) {\n        // if decimal index is less then 0 add leading 0s\n        coefficient = coefficient + repeat('0', decimalIndex - coffiecientLn);\n    }\n    else {\n        // else add decimal point at proper index\n        coefficient =\n            (coefficient.substring(0, decimalIndex) || '0') + '.' + coefficient.substring(decimalIndex);\n    }\n    return sign + coefficient;\n}\n/**\n * This method is required to round prop value to given scale.\n * Not used .round or .fixedTo because that will break with big numbers\n */\nfunction roundToPrecision(numStr, scale, fixedDecimalScale) {\n    //if number is empty don't do anything return empty string\n    if (['', '-'].indexOf(numStr) !== -1)\n        { return numStr; }\n    var shouldHaveDecimalSeparator = (numStr.indexOf('.') !== -1 || fixedDecimalScale) && scale;\n    var ref = splitDecimal(numStr);\n    var beforeDecimal = ref.beforeDecimal;\n    var afterDecimal = ref.afterDecimal;\n    var hasNegation = ref.hasNegation;\n    var floatValue = parseFloat((\"0.\" + (afterDecimal || '0')));\n    var floatValueStr = afterDecimal.length <= scale ? (\"0.\" + afterDecimal) : floatValue.toFixed(scale);\n    var roundedDecimalParts = floatValueStr.split('.');\n    var intPart = beforeDecimal;\n    // if we have cary over from rounding decimal part, add that on before decimal\n    if (beforeDecimal && Number(roundedDecimalParts[0])) {\n        intPart = beforeDecimal\n            .split('')\n            .reverse()\n            .reduce(function (roundedStr, current, idx) {\n            if (roundedStr.length > idx) {\n                return ((Number(roundedStr[0]) + Number(current)).toString() +\n                    roundedStr.substring(1, roundedStr.length));\n            }\n            return current + roundedStr;\n        }, roundedDecimalParts[0]);\n    }\n    var decimalPart = limitToScale(roundedDecimalParts[1] || '', scale, fixedDecimalScale);\n    var negation = hasNegation ? '-' : '';\n    var decimalSeparator = shouldHaveDecimalSeparator ? '.' : '';\n    return (\"\" + negation + intPart + decimalSeparator + decimalPart);\n}\n/** set the caret positon in an input field **/\nfunction setCaretPosition(el, caretPos) {\n    el.value = el.value;\n    // ^ this is used to not only get 'focus', but\n    // to make sure we don't have it everything -selected-\n    // (it causes an issue in chrome, and having it doesn't hurt any other browser)\n    if (el !== null) {\n        /* @ts-ignore */\n        if (el.createTextRange) {\n            /* @ts-ignore */\n            var range = el.createTextRange();\n            range.move('character', caretPos);\n            range.select();\n            return true;\n        }\n        // (el.selectionStart === 0 added for Firefox bug)\n        if (el.selectionStart || el.selectionStart === 0) {\n            el.focus();\n            el.setSelectionRange(caretPos, caretPos);\n            return true;\n        }\n        // fail city, fortunately this never happens (as far as I've tested) :)\n        el.focus();\n        return false;\n    }\n}\n/**\n * TODO: remove dependency of findChangeRange, findChangedRangeFromCaretPositions is better way to find what is changed\n * currently this is mostly required by test and isCharacterSame util\n * Given previous value and newValue it returns the index\n * start - end to which values have changed.\n * This function makes assumption about only consecutive\n * characters are changed which is correct assumption for caret input.\n */\nvar findChangeRange = memoizeOnce(function (prevValue, newValue) {\n    var i = 0, j = 0;\n    var prevLength = prevValue.length;\n    var newLength = newValue.length;\n    while (prevValue[i] === newValue[i] && i < prevLength)\n        { i++; }\n    //check what has been changed from last\n    while (prevValue[prevLength - 1 - j] === newValue[newLength - 1 - j] &&\n        newLength - j > i &&\n        prevLength - j > i) {\n        j++;\n    }\n    return {\n        from: { start: i, end: prevLength - j },\n        to: { start: i, end: newLength - j },\n    };\n});\nvar findChangedRangeFromCaretPositions = function (lastCaretPositions, currentCaretPosition) {\n    var startPosition = Math.min(lastCaretPositions.selectionStart, currentCaretPosition);\n    return {\n        from: { start: startPosition, end: lastCaretPositions.selectionEnd },\n        to: { start: startPosition, end: currentCaretPosition },\n    };\n};\n/*\n  Returns a number whose value is limited to the given range\n*/\nfunction clamp(num, min, max) {\n    return Math.min(Math.max(num, min), max);\n}\nfunction geInputCaretPosition(el) {\n    /*Max of selectionStart and selectionEnd is taken for the patch of pixel and other mobile device caret bug*/\n    return Math.max(el.selectionStart, el.selectionEnd);\n}\nfunction addInputMode() {\n    return (typeof navigator !== 'undefined' &&\n        !(navigator.platform && /iPhone|iPod/.test(navigator.platform)));\n}\nfunction getDefaultChangeMeta(value) {\n    return {\n        from: {\n            start: 0,\n            end: 0,\n        },\n        to: {\n            start: 0,\n            end: value.length,\n        },\n        lastValue: '',\n    };\n}\nfunction getMaskAtIndex(mask, index) {\n    if ( mask === void 0 ) mask = ' ';\n\n    if (typeof mask === 'string') {\n        return mask;\n    }\n    return mask[index] || ' ';\n}\nfunction defaultIsCharacterSame(ref) {\n    var currentValue = ref.currentValue;\n    var formattedValue = ref.formattedValue;\n    var currentValueIndex = ref.currentValueIndex;\n    var formattedValueIndex = ref.formattedValueIndex;\n\n    return currentValue[currentValueIndex] === formattedValue[formattedValueIndex];\n}\nfunction getCaretPosition(newFormattedValue, lastFormattedValue, curValue, curCaretPos, boundary, isValidInputCharacter, \n/**\n * format function can change the character, the caret engine relies on mapping old value and new value\n * In such case if character is changed, parent can tell which chars are equivalent\n * Some example, all allowedDecimalCharacters are updated to decimalCharacters, 2nd case if user is coverting\n * number to different numeric system.\n */\nisCharacterSame) {\n    if ( isCharacterSame === void 0 ) isCharacterSame = defaultIsCharacterSame;\n\n    /**\n     * if something got inserted on empty value, add the formatted character before the current value,\n     * This is to avoid the case where typed character is present on format characters\n     */\n    var firstAllowedPosition = boundary.findIndex(function (b) { return b; });\n    var prefixFormat = newFormattedValue.slice(0, firstAllowedPosition);\n    if (!lastFormattedValue && !curValue.startsWith(prefixFormat)) {\n        lastFormattedValue = prefixFormat;\n        curValue = prefixFormat + curValue;\n        curCaretPos = curCaretPos + prefixFormat.length;\n    }\n    var curValLn = curValue.length;\n    var formattedValueLn = newFormattedValue.length;\n    // create index map\n    var addedIndexMap = {};\n    var indexMap = new Array(curValLn);\n    for (var i = 0; i < curValLn; i++) {\n        indexMap[i] = -1;\n        for (var j = 0, jLn = formattedValueLn; j < jLn; j++) {\n            var isCharSame = isCharacterSame({\n                currentValue: curValue,\n                lastValue: lastFormattedValue,\n                formattedValue: newFormattedValue,\n                currentValueIndex: i,\n                formattedValueIndex: j,\n            });\n            if (isCharSame && addedIndexMap[j] !== true) {\n                indexMap[i] = j;\n                addedIndexMap[j] = true;\n                break;\n            }\n        }\n    }\n    /**\n     * For current caret position find closest characters (left and right side)\n     * which are properly mapped to formatted value.\n     * The idea is that the new caret position will exist always in the boundary of\n     * that mapped index\n     */\n    var pos = curCaretPos;\n    while (pos < curValLn && (indexMap[pos] === -1 || !isValidInputCharacter(curValue[pos]))) {\n        pos++;\n    }\n    // if the caret position is on last keep the endIndex as last for formatted value\n    var endIndex = pos === curValLn || indexMap[pos] === -1 ? formattedValueLn : indexMap[pos];\n    pos = curCaretPos - 1;\n    while (pos > 0 && indexMap[pos] === -1)\n        { pos--; }\n    var startIndex = pos === -1 || indexMap[pos] === -1 ? 0 : indexMap[pos] + 1;\n    /**\n     * case where a char is added on suffix and removed from middle, example 2sq345 becoming $2,345 sq\n     * there is still a mapping but the order of start index and end index is changed\n     */\n    if (startIndex > endIndex)\n        { return endIndex; }\n    /**\n     * given the current caret position if it closer to startIndex\n     * keep the new caret position on start index or keep it closer to endIndex\n     */\n    return curCaretPos - startIndex < endIndex - curCaretPos ? startIndex : endIndex;\n}\n/* This keeps the caret within typing area so people can't type in between prefix or suffix or format characters */\nfunction getCaretPosInBoundary(value, caretPos, boundary, direction) {\n    var valLn = value.length;\n    // clamp caret position to [0, value.length]\n    caretPos = clamp(caretPos, 0, valLn);\n    if (direction === 'left') {\n        while (caretPos >= 0 && !boundary[caretPos])\n            { caretPos--; }\n        // if we don't find any suitable caret position on left, set it on first allowed position\n        if (caretPos === -1)\n            { caretPos = boundary.indexOf(true); }\n    }\n    else {\n        while (caretPos <= valLn && !boundary[caretPos])\n            { caretPos++; }\n        // if we don't find any suitable caret position on right, set it on last allowed position\n        if (caretPos > valLn)\n            { caretPos = boundary.lastIndexOf(true); }\n    }\n    // if we still don't find caret position, set it at the end of value\n    if (caretPos === -1)\n        { caretPos = valLn; }\n    return caretPos;\n}\nfunction caretUnknownFormatBoundary(formattedValue) {\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        boundaryAry[i] = Boolean(charIsNumber(formattedValue[i]) || charIsNumber(formattedValue[i - 1]));\n    }\n    return boundaryAry;\n}\nfunction useInternalValues(value, defaultValue, valueIsNumericString, format, removeFormatting, onValueChange) {\n    if ( onValueChange === void 0 ) onValueChange = noop;\n\n    var getValues = usePersistentCallback(function (value, valueIsNumericString) {\n        var formattedValue, numAsString;\n        if (isNotValidValue(value)) {\n            numAsString = '';\n            formattedValue = '';\n        }\n        else if (typeof value === 'number' || valueIsNumericString) {\n            numAsString = typeof value === 'number' ? toNumericString(value) : value;\n            formattedValue = format(numAsString);\n        }\n        else {\n            numAsString = removeFormatting(value, undefined);\n            formattedValue = format(numAsString);\n        }\n        return { formattedValue: formattedValue, numAsString: numAsString };\n    });\n    var ref = useState(function () {\n        return getValues(isNil(value) ? defaultValue : value, valueIsNumericString);\n    });\n    var values = ref[0];\n    var setValues = ref[1];\n    var _onValueChange = function (newValues, sourceInfo) {\n        if (newValues.formattedValue !== values.formattedValue) {\n            setValues({\n                formattedValue: newValues.formattedValue,\n                numAsString: newValues.value,\n            });\n        }\n        // call parent on value change if only if formatted value is changed\n        onValueChange(newValues, sourceInfo);\n    };\n    // if value is switch from controlled to uncontrolled, use the internal state's value to format with new props\n    var _value = value;\n    var _valueIsNumericString = valueIsNumericString;\n    if (isNil(value)) {\n        _value = values.numAsString;\n        _valueIsNumericString = true;\n    }\n    var newValues = getValues(_value, _valueIsNumericString);\n    useMemo(function () {\n        setValues(newValues);\n    }, [newValues.formattedValue]);\n    return [values, _onValueChange];\n}\n\nfunction defaultRemoveFormatting(value) {\n    return value.replace(/[^0-9]/g, '');\n}\nfunction defaultFormat(value) {\n    return value;\n}\nfunction NumberFormatBase(props) {\n    var type = props.type; if ( type === void 0 ) type = 'text';\n    var displayType = props.displayType; if ( displayType === void 0 ) displayType = 'input';\n    var customInput = props.customInput;\n    var renderText = props.renderText;\n    var getInputRef = props.getInputRef;\n    var format = props.format; if ( format === void 0 ) format = defaultFormat;\n    var removeFormatting = props.removeFormatting; if ( removeFormatting === void 0 ) removeFormatting = defaultRemoveFormatting;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var isAllowed = props.isAllowed;\n    var onChange = props.onChange; if ( onChange === void 0 ) onChange = noop;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onMouseUp = props.onMouseUp; if ( onMouseUp === void 0 ) onMouseUp = noop;\n    var onFocus = props.onFocus; if ( onFocus === void 0 ) onFocus = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var propValue = props.value;\n    var getCaretBoundary = props.getCaretBoundary; if ( getCaretBoundary === void 0 ) getCaretBoundary = caretUnknownFormatBoundary;\n    var isValidInputCharacter = props.isValidInputCharacter; if ( isValidInputCharacter === void 0 ) isValidInputCharacter = charIsNumber;\n    var isCharacterSame = props.isCharacterSame;\n    var otherProps = __rest(props, [\"type\", \"displayType\", \"customInput\", \"renderText\", \"getInputRef\", \"format\", \"removeFormatting\", \"defaultValue\", \"valueIsNumericString\", \"onValueChange\", \"isAllowed\", \"onChange\", \"onKeyDown\", \"onMouseUp\", \"onFocus\", \"onBlur\", \"value\", \"getCaretBoundary\", \"isValidInputCharacter\", \"isCharacterSame\"]);\n    var ref = useInternalValues(propValue, defaultValue, Boolean(valueIsNumericString), format, removeFormatting, onValueChange);\n    var ref_0 = ref[0];\n    var formattedValue = ref_0.formattedValue;\n    var numAsString = ref_0.numAsString;\n    var onFormattedValueChange = ref[1];\n    var caretPositionBeforeChange = useRef();\n    var lastUpdatedValue = useRef({ formattedValue: formattedValue, numAsString: numAsString });\n    var _onValueChange = function (values, source) {\n        lastUpdatedValue.current = { formattedValue: values.formattedValue, numAsString: values.value };\n        onFormattedValueChange(values, source);\n    };\n    var ref$1 = useState(false);\n    var mounted = ref$1[0];\n    var setMounted = ref$1[1];\n    var focusedElm = useRef(null);\n    var timeout = useRef({\n        setCaretTimeout: null,\n        focusTimeout: null,\n    });\n    useEffect(function () {\n        setMounted(true);\n        return function () {\n            clearTimeout(timeout.current.setCaretTimeout);\n            clearTimeout(timeout.current.focusTimeout);\n        };\n    }, []);\n    var _format = format;\n    var getValueObject = function (formattedValue, numAsString) {\n        var floatValue = parseFloat(numAsString);\n        return {\n            formattedValue: formattedValue,\n            value: numAsString,\n            floatValue: isNaN(floatValue) ? undefined : floatValue,\n        };\n    };\n    var setPatchedCaretPosition = function (el, caretPos, currentValue) {\n        // don't reset the caret position when the whole input content is selected\n        if (el.selectionStart === 0 && el.selectionEnd === el.value.length)\n            { return; }\n        /* setting caret position within timeout of 0ms is required for mobile chrome,\n        otherwise browser resets the caret position after we set it\n        We are also setting it without timeout so that in normal browser we don't see the flickering */\n        setCaretPosition(el, caretPos);\n        timeout.current.setCaretTimeout = setTimeout(function () {\n            if (el.value === currentValue && el.selectionStart !== caretPos) {\n                setCaretPosition(el, caretPos);\n            }\n        }, 0);\n    };\n    /* This keeps the caret within typing area so people can't type in between prefix or suffix */\n    var correctCaretPosition = function (value, caretPos, direction) {\n        return getCaretPosInBoundary(value, caretPos, getCaretBoundary(value), direction);\n    };\n    var getNewCaretPosition = function (inputValue, newFormattedValue, caretPos) {\n        var caretBoundary = getCaretBoundary(newFormattedValue);\n        var updatedCaretPos = getCaretPosition(newFormattedValue, formattedValue, inputValue, caretPos, caretBoundary, isValidInputCharacter, isCharacterSame);\n        //correct caret position if its outside of editable area\n        updatedCaretPos = getCaretPosInBoundary(newFormattedValue, updatedCaretPos, caretBoundary);\n        return updatedCaretPos;\n    };\n    var updateValueAndCaretPosition = function (params) {\n        var newFormattedValue = params.formattedValue; if ( newFormattedValue === void 0 ) newFormattedValue = '';\n        var input = params.input;\n        var source = params.source;\n        var event = params.event;\n        var numAsString = params.numAsString;\n        var caretPos;\n        if (input) {\n            var inputValue = params.inputValue || input.value;\n            var currentCaretPosition = geInputCaretPosition(input);\n            /**\n             * set the value imperatively, this is required for IE fix\n             * This is also required as if new caret position is beyond the previous value.\n             * Caret position will not be set correctly\n             */\n            input.value = newFormattedValue;\n            //get the caret position\n            caretPos = getNewCaretPosition(inputValue, newFormattedValue, currentCaretPosition);\n            //set caret position imperatively\n            if (caretPos !== undefined) {\n                setPatchedCaretPosition(input, caretPos, newFormattedValue);\n            }\n        }\n        if (newFormattedValue !== formattedValue) {\n            // trigger onValueChange synchronously, so parent is updated along with the number format. Fix for #277, #287\n            _onValueChange(getValueObject(newFormattedValue, numAsString), { event: event, source: source });\n        }\n    };\n    /**\n     * if the formatted value is not synced to parent, or if the formatted value is different from last synced value sync it\n     * if the formatting props is removed, in which case last formatted value will be different from the numeric string value\n     * in such case we need to inform the parent.\n     */\n    useEffect(function () {\n        var ref = lastUpdatedValue.current;\n        var lastFormattedValue = ref.formattedValue;\n        var lastNumAsString = ref.numAsString;\n        if (formattedValue !== lastFormattedValue || numAsString !== lastNumAsString) {\n            _onValueChange(getValueObject(formattedValue, numAsString), {\n                event: undefined,\n                source: SourceType.props,\n            });\n        }\n    }, [formattedValue, numAsString]);\n    // also if formatted value is changed from the props, we need to update the caret position\n    // keep the last caret position if element is focused\n    var currentCaretPosition = focusedElm.current\n        ? geInputCaretPosition(focusedElm.current)\n        : undefined;\n    // needed to prevent warning with useLayoutEffect on server\n    var useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\n    useIsomorphicLayoutEffect(function () {\n        var input = focusedElm.current;\n        if (formattedValue !== lastUpdatedValue.current.formattedValue && input) {\n            var caretPos = getNewCaretPosition(lastUpdatedValue.current.formattedValue, formattedValue, currentCaretPosition);\n            /**\n             * set the value imperatively, as we set the caret position as well imperatively.\n             * This is to keep value and caret position in sync\n             */\n            input.value = formattedValue;\n            setPatchedCaretPosition(input, caretPos, formattedValue);\n        }\n    }, [formattedValue]);\n    var formatInputValue = function (inputValue, event, source) {\n        var input = event.target;\n        var changeRange = caretPositionBeforeChange.current\n            ? findChangedRangeFromCaretPositions(caretPositionBeforeChange.current, input.selectionEnd)\n            : findChangeRange(formattedValue, inputValue);\n        var changeMeta = Object.assign(Object.assign({}, changeRange), { lastValue: formattedValue });\n        var _numAsString = removeFormatting(inputValue, changeMeta);\n        var _formattedValue = _format(_numAsString);\n        // formatting can remove some of the number chars, so we need to fine number string again\n        _numAsString = removeFormatting(_formattedValue, undefined);\n        if (isAllowed && !isAllowed(getValueObject(_formattedValue, _numAsString))) {\n            //reset the caret position\n            var input$1 = event.target;\n            var currentCaretPosition = geInputCaretPosition(input$1);\n            var caretPos = getNewCaretPosition(inputValue, formattedValue, currentCaretPosition);\n            input$1.value = formattedValue;\n            setPatchedCaretPosition(input$1, caretPos, formattedValue);\n            return false;\n        }\n        updateValueAndCaretPosition({\n            formattedValue: _formattedValue,\n            numAsString: _numAsString,\n            inputValue: inputValue,\n            event: event,\n            source: source,\n            input: event.target,\n        });\n        return true;\n    };\n    var setCaretPositionInfoBeforeChange = function (el, endOffset) {\n        if ( endOffset === void 0 ) endOffset = 0;\n\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        caretPositionBeforeChange.current = { selectionStart: selectionStart, selectionEnd: selectionEnd + endOffset };\n    };\n    var _onChange = function (e) {\n        var el = e.target;\n        var inputValue = el.value;\n        var changed = formatInputValue(inputValue, e, SourceType.event);\n        if (changed)\n            { onChange(e); }\n        // reset the position, as we have already handled the caret position\n        caretPositionBeforeChange.current = undefined;\n    };\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        var expectedCaretPosition;\n        //Handle backspace and delete against non numerical/decimal characters or arrow keys\n        if (key === 'ArrowLeft' || key === 'Backspace') {\n            expectedCaretPosition = Math.max(selectionStart - 1, 0);\n        }\n        else if (key === 'ArrowRight') {\n            expectedCaretPosition = Math.min(selectionStart + 1, value.length);\n        }\n        else if (key === 'Delete') {\n            expectedCaretPosition = selectionStart;\n        }\n        // if key is delete and text is not selected keep the end offset to 1, as it deletes one character\n        // this is required as selection is not changed on delete case, which changes the change range calculation\n        var endOffset = 0;\n        if (key === 'Delete' && selectionStart === selectionEnd) {\n            endOffset = 1;\n        }\n        var isArrowKey = key === 'ArrowLeft' || key === 'ArrowRight';\n        //if expectedCaretPosition is not set it means we don't want to Handle keyDown\n        // also if multiple characters are selected don't handle\n        if (expectedCaretPosition === undefined || (selectionStart !== selectionEnd && !isArrowKey)) {\n            onKeyDown(e);\n            // keep information of what was the caret position before keyDown\n            // set it after onKeyDown, in case parent updates the position manually\n            setCaretPositionInfoBeforeChange(el, endOffset);\n            return;\n        }\n        var newCaretPosition = expectedCaretPosition;\n        if (isArrowKey) {\n            var direction = key === 'ArrowLeft' ? 'left' : 'right';\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, direction);\n            // arrow left or right only moves the caret, so no need to handle the event, if we are handling it manually\n            if (newCaretPosition !== expectedCaretPosition) {\n                e.preventDefault();\n            }\n        }\n        else if (key === 'Delete' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of delete go to closest caret boundary on the right side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'right');\n        }\n        else if (key === 'Backspace' && !isValidInputCharacter(value[expectedCaretPosition])) {\n            // in case of backspace go to closest caret boundary on the left side\n            newCaretPosition = correctCaretPosition(value, expectedCaretPosition, 'left');\n        }\n        if (newCaretPosition !== expectedCaretPosition) {\n            setPatchedCaretPosition(el, newCaretPosition, value);\n        }\n        onKeyDown(e);\n        setCaretPositionInfoBeforeChange(el, endOffset);\n    };\n    /** required to handle the caret position when click anywhere within the input **/\n    var _onMouseUp = function (e) {\n        var el = e.target;\n        /**\n         * NOTE: we have to give default value for value as in case when custom input is provided\n         * value can come as undefined when nothing is provided on value prop.\n         */\n        var correctCaretPositionIfRequired = function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            if (selectionStart === selectionEnd) {\n                var caretPosition = correctCaretPosition(value, selectionStart);\n                if (caretPosition !== selectionStart) {\n                    setPatchedCaretPosition(el, caretPosition, value);\n                }\n            }\n        };\n        correctCaretPositionIfRequired();\n        // try to correct after selection has updated by browser\n        // this case is required when user clicks on some position while a text is selected on input\n        requestAnimationFrame(function () {\n            correctCaretPositionIfRequired();\n        });\n        onMouseUp(e);\n        setCaretPositionInfoBeforeChange(el);\n    };\n    var _onFocus = function (e) {\n        // Workaround Chrome and Safari bug https://bugs.chromium.org/p/chromium/issues/detail?id=779328\n        // (onFocus event target selectionStart is always 0 before setTimeout)\n        if (e.persist)\n            { e.persist(); }\n        var el = e.target;\n        var currentTarget = e.currentTarget;\n        focusedElm.current = el;\n        timeout.current.focusTimeout = setTimeout(function () {\n            var selectionStart = el.selectionStart;\n            var selectionEnd = el.selectionEnd;\n            var value = el.value; if ( value === void 0 ) value = '';\n            var caretPosition = correctCaretPosition(value, selectionStart);\n            //setPatchedCaretPosition only when everything is not selected on focus (while tabbing into the field)\n            if (caretPosition !== selectionStart &&\n                !(selectionStart === 0 && selectionEnd === value.length)) {\n                setPatchedCaretPosition(el, caretPosition, value);\n            }\n            onFocus(Object.assign(Object.assign({}, e), { currentTarget: currentTarget }));\n        }, 0);\n    };\n    var _onBlur = function (e) {\n        focusedElm.current = null;\n        clearTimeout(timeout.current.focusTimeout);\n        clearTimeout(timeout.current.setCaretTimeout);\n        onBlur(e);\n    };\n    // add input mode on element based on format prop and device once the component is mounted\n    var inputMode = mounted && addInputMode() ? 'numeric' : undefined;\n    var inputProps = Object.assign({ inputMode: inputMode }, otherProps, {\n        type: type,\n        value: formattedValue,\n        onChange: _onChange,\n        onKeyDown: _onKeyDown,\n        onMouseUp: _onMouseUp,\n        onFocus: _onFocus,\n        onBlur: _onBlur,\n    });\n    if (displayType === 'text') {\n        return renderText ? (React.createElement(React.Fragment, null, renderText(formattedValue, otherProps) || null)) : (React.createElement(\"span\", Object.assign({}, otherProps, { ref: getInputRef }), formattedValue));\n    }\n    else if (customInput) {\n        var CustomInput = customInput;\n        /* @ts-ignore */\n        return React.createElement(CustomInput, Object.assign({}, inputProps, { ref: getInputRef }));\n    }\n    return React.createElement(\"input\", Object.assign({}, inputProps, { ref: getInputRef }));\n}\n\nfunction format(numStr, props) {\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var allowNegative = props.allowNegative;\n    var thousandsGroupStyle = props.thousandsGroupStyle; if ( thousandsGroupStyle === void 0 ) thousandsGroupStyle = 'thousand';\n    // don't apply formatting on empty string or '-'\n    if (numStr === '' || numStr === '-') {\n        return numStr;\n    }\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    /**\n     * Keep the decimal separator\n     * when decimalScale is not defined or non zero and the numStr has decimal in it\n     * Or if decimalScale is > 0 and fixeDecimalScale is true (even if numStr has no decimal)\n     */\n    var hasDecimalSeparator = (decimalScale !== 0 && numStr.indexOf('.') !== -1) || (decimalScale && fixedDecimalScale);\n    var ref$1 = splitDecimal(numStr, allowNegative);\n    var beforeDecimal = ref$1.beforeDecimal;\n    var afterDecimal = ref$1.afterDecimal;\n    var addNegation = ref$1.addNegation; // eslint-disable-line prefer-const\n    //apply decimal precision if its defined\n    if (decimalScale !== undefined) {\n        afterDecimal = limitToScale(afterDecimal, decimalScale, !!fixedDecimalScale);\n    }\n    if (thousandSeparator) {\n        beforeDecimal = applyThousandSeparator(beforeDecimal, thousandSeparator, thousandsGroupStyle);\n    }\n    //add prefix and suffix when there is a number present\n    if (prefix)\n        { beforeDecimal = prefix + beforeDecimal; }\n    if (suffix)\n        { afterDecimal = afterDecimal + suffix; }\n    //restore negation sign\n    if (addNegation)\n        { beforeDecimal = '-' + beforeDecimal; }\n    numStr = beforeDecimal + ((hasDecimalSeparator && decimalSeparator) || '') + afterDecimal;\n    return numStr;\n}\nfunction getSeparators(props) {\n    var decimalSeparator = props.decimalSeparator; if ( decimalSeparator === void 0 ) decimalSeparator = '.';\n    var thousandSeparator = props.thousandSeparator;\n    var allowedDecimalSeparators = props.allowedDecimalSeparators;\n    if (thousandSeparator === true) {\n        thousandSeparator = ',';\n    }\n    if (!allowedDecimalSeparators) {\n        allowedDecimalSeparators = [decimalSeparator, '.'];\n    }\n    return {\n        decimalSeparator: decimalSeparator,\n        thousandSeparator: thousandSeparator,\n        allowedDecimalSeparators: allowedDecimalSeparators,\n    };\n}\nfunction handleNegation(value, allowNegative) {\n    if ( value === void 0 ) value = '';\n\n    var negationRegex = new RegExp('(-)');\n    var doubleNegationRegex = new RegExp('(-)(.)*(-)');\n    // Check number has '-' value\n    var hasNegation = negationRegex.test(value);\n    // Check number has 2 or more '-' values\n    var removeNegation = doubleNegationRegex.test(value);\n    //remove negation\n    value = value.replace(/-/g, '');\n    if (hasNegation && !removeNegation && allowNegative) {\n        value = '-' + value;\n    }\n    return value;\n}\nfunction getNumberRegex(decimalSeparator, global) {\n    return new RegExp((\"(^-)|[0-9]|\" + (escapeRegExp(decimalSeparator))), global ? 'g' : undefined);\n}\nfunction isNumericString(val, prefix, suffix) {\n    // for empty value we can always treat it as numeric string\n    if (val === '')\n        { return true; }\n    return (!(prefix === null || prefix === void 0 ? void 0 : prefix.match(/\\d/)) && !(suffix === null || suffix === void 0 ? void 0 : suffix.match(/\\d/)) && typeof val === 'string' && !isNaN(Number(val)));\n}\nfunction removeFormatting(value, changeMeta, props) {\n    var assign;\n\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n    var allowNegative = props.allowNegative;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var decimalScale = props.decimalScale;\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var start = to.start;\n    var end = to.end;\n    var ref = getSeparators(props);\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var decimalSeparator = ref.decimalSeparator;\n    var isBeforeDecimalSeparator = value[end] === decimalSeparator;\n    /**\n     * If only a number is added on empty input which matches with the prefix or suffix,\n     * then don't remove it, just return the same\n     */\n    if (charIsNumber(value) &&\n        (value === prefix || value === suffix) &&\n        changeMeta.lastValue === '') {\n        return value;\n    }\n    /** Check for any allowed decimal separator is added in the numeric format and replace it with decimal separator */\n    if (end - start === 1 && allowedDecimalSeparators.indexOf(value[start]) !== -1) {\n        var separator = decimalScale === 0 ? '' : decimalSeparator;\n        value = value.substring(0, start) + separator + value.substring(start + 1, value.length);\n    }\n    var stripNegation = function (value, start, end) {\n        /**\n         * if prefix starts with - we don't allow negative number to avoid confusion\n         * if suffix starts with - and the value length is same as suffix length, then the - sign is from the suffix\n         * In other cases, if the value starts with - then it is a negation\n         */\n        var hasNegation = false;\n        var hasDoubleNegation = false;\n        if (prefix.startsWith('-')) {\n            hasNegation = false;\n        }\n        else if (value.startsWith('--')) {\n            hasNegation = false;\n            hasDoubleNegation = true;\n        }\n        else if (suffix.startsWith('-') && value.length === suffix.length) {\n            hasNegation = false;\n        }\n        else if (value[0] === '-') {\n            hasNegation = true;\n        }\n        var charsToRemove = hasNegation ? 1 : 0;\n        if (hasDoubleNegation)\n            { charsToRemove = 2; }\n        // remove negation/double negation from start to simplify prefix logic as negation comes before prefix\n        if (charsToRemove) {\n            value = value.substring(charsToRemove);\n            // account for the removal of the negation for start and end index\n            start -= charsToRemove;\n            end -= charsToRemove;\n        }\n        return { value: value, start: start, end: end, hasNegation: hasNegation };\n    };\n    var toMetadata = stripNegation(value, start, end);\n    var hasNegation = toMetadata.hasNegation;\n    ((assign = toMetadata, value = assign.value, start = assign.start, end = assign.end));\n    var ref$1 = stripNegation(changeMeta.lastValue, from.start, from.end);\n    var fromStart = ref$1.start;\n    var fromEnd = ref$1.end;\n    var lastValue = ref$1.value;\n    // if only prefix and suffix part is updated reset the value to last value\n    // if the changed range is from suffix in the updated value, and the the suffix starts with the same characters, allow the change\n    var updatedSuffixPart = value.substring(start, end);\n    if (value.length &&\n        lastValue.length &&\n        (fromStart > lastValue.length - suffix.length || fromEnd < prefix.length) &&\n        !(updatedSuffixPart && suffix.startsWith(updatedSuffixPart))) {\n        value = lastValue;\n    }\n    /**\n     * remove prefix\n     * Remove whole prefix part if its present on the value\n     * If the prefix is partially deleted (in which case change start index will be less the prefix length)\n     * Remove only partial part of prefix.\n     */\n    var startIndex = 0;\n    if (value.startsWith(prefix))\n        { startIndex += prefix.length; }\n    else if (start < prefix.length)\n        { startIndex = start; }\n    value = value.substring(startIndex);\n    // account for deleted prefix for end\n    end -= startIndex;\n    /**\n     * Remove suffix\n     * Remove whole suffix part if its present on the value\n     * If the suffix is partially deleted (in which case change end index will be greater than the suffixStartIndex)\n     * remove the partial part of suffix\n     */\n    var endIndex = value.length;\n    var suffixStartIndex = value.length - suffix.length;\n    if (value.endsWith(suffix))\n        { endIndex = suffixStartIndex; }\n    // if the suffix is removed from the end\n    else if (end > suffixStartIndex)\n        { endIndex = end; }\n    // if the suffix is removed from start\n    else if (end > value.length - suffix.length)\n        { endIndex = end; }\n    value = value.substring(0, endIndex);\n    // add the negation back and handle for double negation\n    value = handleNegation(hasNegation ? (\"-\" + value) : value, allowNegative);\n    // remove non numeric characters\n    value = (value.match(getNumberRegex(decimalSeparator, true)) || []).join('');\n    // replace the decimalSeparator with ., and only keep the first separator, ignore following ones\n    var firstIndex = value.indexOf(decimalSeparator);\n    value = value.replace(new RegExp(escapeRegExp(decimalSeparator), 'g'), function (match, index) {\n        return index === firstIndex ? '.' : '';\n    });\n    //check if beforeDecimal got deleted and there is nothing after decimal,\n    //clear all numbers in such case while keeping the - sign\n    var ref$2 = splitDecimal(value, allowNegative);\n    var beforeDecimal = ref$2.beforeDecimal;\n    var afterDecimal = ref$2.afterDecimal;\n    var addNegation = ref$2.addNegation; // eslint-disable-line prefer-const\n    //clear only if something got deleted before decimal (cursor is before decimal)\n    if (to.end - to.start < from.end - from.start &&\n        beforeDecimal === '' &&\n        isBeforeDecimalSeparator &&\n        !parseFloat(afterDecimal)) {\n        value = addNegation ? '-' : '';\n    }\n    return value;\n}\nfunction getCaretBoundary(formattedValue, props) {\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var suffix = props.suffix; if ( suffix === void 0 ) suffix = '';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hasNegation = formattedValue[0] === '-';\n    // fill for prefix and negation\n    boundaryAry.fill(false, 0, prefix.length + (hasNegation ? 1 : 0));\n    // fill for suffix\n    var valLn = formattedValue.length;\n    boundaryAry.fill(false, valLn - suffix.length + 1, valLn + 1);\n    return boundaryAry;\n}\nfunction validateAndUpdateProps(props) {\n    var ref = getSeparators(props);\n    var thousandSeparator = ref.thousandSeparator;\n    var decimalSeparator = ref.decimalSeparator;\n    // eslint-disable-next-line prefer-const\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var allowNegative = props.allowNegative; if ( allowNegative === void 0 ) allowNegative = true;\n    if (thousandSeparator === decimalSeparator) {\n        throw new Error((\"\\n        Decimal separator can't be same as thousand separator.\\n        thousandSeparator: \" + thousandSeparator + \" (thousandSeparator = {true} is same as thousandSeparator = \\\",\\\")\\n        decimalSeparator: \" + decimalSeparator + \" (default value for decimalSeparator is .)\\n     \"));\n    }\n    if (prefix.startsWith('-') && allowNegative) {\n        // TODO: throw error in next major version\n        console.error((\"\\n      Prefix can't start with '-' when allowNegative is true.\\n      prefix: \" + prefix + \"\\n      allowNegative: \" + allowNegative + \"\\n    \"));\n        allowNegative = false;\n    }\n    return Object.assign(Object.assign({}, props), { allowNegative: allowNegative });\n}\nfunction useNumericFormat(props) {\n    // validate props\n    props = validateAndUpdateProps(props);\n    var _decimalSeparator = props.decimalSeparator;\n    var _allowedDecimalSeparators = props.allowedDecimalSeparators;\n    var thousandsGroupStyle = props.thousandsGroupStyle;\n    var suffix = props.suffix;\n    var allowNegative = props.allowNegative;\n    var allowLeadingZeros = props.allowLeadingZeros;\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var onBlur = props.onBlur; if ( onBlur === void 0 ) onBlur = noop;\n    var thousandSeparator = props.thousandSeparator;\n    var decimalScale = props.decimalScale;\n    var fixedDecimalScale = props.fixedDecimalScale;\n    var prefix = props.prefix; if ( prefix === void 0 ) prefix = '';\n    var defaultValue = props.defaultValue;\n    var value = props.value;\n    var valueIsNumericString = props.valueIsNumericString;\n    var onValueChange = props.onValueChange;\n    var restProps = __rest(props, [\"decimalSeparator\", \"allowedDecimalSeparators\", \"thousandsGroupStyle\", \"suffix\", \"allowNegative\", \"allowLeadingZeros\", \"onKeyDown\", \"onBlur\", \"thousandSeparator\", \"decimalScale\", \"fixedDecimalScale\", \"prefix\", \"defaultValue\", \"value\", \"valueIsNumericString\", \"onValueChange\"]);\n    // get derived decimalSeparator and allowedDecimalSeparators\n    var ref = getSeparators(props);\n    var decimalSeparator = ref.decimalSeparator;\n    var allowedDecimalSeparators = ref.allowedDecimalSeparators;\n    var _format = function (numStr) { return format(numStr, props); };\n    var _removeFormatting = function (inputValue, changeMeta) { return removeFormatting(inputValue, changeMeta, props); };\n    var _value = isNil(value) ? defaultValue : value;\n    // try to figure out isValueNumericString based on format prop and value\n    var _valueIsNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString(_value, prefix, suffix);\n    if (!isNil(value)) {\n        _valueIsNumericString = _valueIsNumericString || typeof value === 'number';\n    }\n    else if (!isNil(defaultValue)) {\n        _valueIsNumericString = _valueIsNumericString || typeof defaultValue === 'number';\n    }\n    var roundIncomingValueToPrecision = function (value) {\n        if (isNotValidValue(value))\n            { return value; }\n        if (typeof value === 'number') {\n            value = toNumericString(value);\n        }\n        /**\n         * only round numeric or float string values coming through props,\n         * we don't need to do it for onChange events, as we want to prevent typing there\n         */\n        if (_valueIsNumericString && typeof decimalScale === 'number') {\n            return roundToPrecision(value, decimalScale, Boolean(fixedDecimalScale));\n        }\n        return value;\n    };\n    var ref$1 = useInternalValues(roundIncomingValueToPrecision(value), roundIncomingValueToPrecision(defaultValue), Boolean(_valueIsNumericString), _format, _removeFormatting, onValueChange);\n    var ref$1_0 = ref$1[0];\n    var numAsString = ref$1_0.numAsString;\n    var formattedValue = ref$1_0.formattedValue;\n    var _onValueChange = ref$1[1];\n    var _onKeyDown = function (e) {\n        var el = e.target;\n        var key = e.key;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value; if ( value === void 0 ) value = '';\n        // if user tries to delete partial prefix then ignore it\n        if ((key === 'Backspace' || key === 'Delete') && selectionEnd < prefix.length) {\n            e.preventDefault();\n            return;\n        }\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // if user hits backspace, while the cursor is before prefix, and the input has negation, remove the negation\n        if (key === 'Backspace' &&\n            value[0] === '-' &&\n            selectionStart === prefix.length + 1 &&\n            allowNegative) {\n            // bring the cursor to after negation\n            setCaretPosition(el, 1);\n        }\n        // don't allow user to delete decimal separator when decimalScale and fixedDecimalScale is set\n        if (decimalScale && fixedDecimalScale) {\n            if (key === 'Backspace' && value[selectionStart - 1] === decimalSeparator) {\n                setCaretPosition(el, selectionStart - 1);\n                e.preventDefault();\n            }\n            else if (key === 'Delete' && value[selectionStart] === decimalSeparator) {\n                e.preventDefault();\n            }\n        }\n        // if user presses the allowed decimal separator before the separator, move the cursor after the separator\n        if ((allowedDecimalSeparators === null || allowedDecimalSeparators === void 0 ? void 0 : allowedDecimalSeparators.includes(key)) && value[selectionStart] === decimalSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        var _thousandSeparator = thousandSeparator === true ? ',' : thousandSeparator;\n        // move cursor when delete or backspace is pressed before/after thousand separator\n        if (key === 'Backspace' && value[selectionStart - 1] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart - 1);\n        }\n        if (key === 'Delete' && value[selectionStart] === _thousandSeparator) {\n            setCaretPosition(el, selectionStart + 1);\n        }\n        onKeyDown(e);\n    };\n    var _onBlur = function (e) {\n        var _value = numAsString;\n        // if there no no numeric value, clear the input\n        if (!_value.match(/\\d/g)) {\n            _value = '';\n        }\n        // clear leading 0s\n        if (!allowLeadingZeros) {\n            _value = fixLeadingZero(_value);\n        }\n        // apply fixedDecimalScale on blur event\n        if (fixedDecimalScale && decimalScale) {\n            _value = roundToPrecision(_value, decimalScale, fixedDecimalScale);\n        }\n        if (_value !== numAsString) {\n            var formattedValue = format(_value, props);\n            _onValueChange({\n                formattedValue: formattedValue,\n                value: _value,\n                floatValue: parseFloat(_value),\n            }, {\n                event: e,\n                source: SourceType.event,\n            });\n        }\n        onBlur(e);\n    };\n    var isValidInputCharacter = function (inputChar) {\n        if (inputChar === decimalSeparator)\n            { return true; }\n        return charIsNumber(inputChar);\n    };\n    var isCharacterSame = function (ref) {\n        var currentValue = ref.currentValue;\n        var lastValue = ref.lastValue;\n        var formattedValue = ref.formattedValue;\n        var currentValueIndex = ref.currentValueIndex;\n        var formattedValueIndex = ref.formattedValueIndex;\n\n        var curChar = currentValue[currentValueIndex];\n        var newChar = formattedValue[formattedValueIndex];\n        /**\n         * NOTE: as thousand separator and allowedDecimalSeparators can be same, we need to check on\n         * typed range if we have typed any character from allowedDecimalSeparators, in that case we\n         * consider different characters like , and . same within the range of updated value.\n         */\n        var typedRange = findChangeRange(lastValue, currentValue);\n        var to = typedRange.to;\n        // handle corner case where if we user types a decimal separator with fixedDecimalScale\n        // and pass back float value the cursor jumps. #851\n        var getDecimalSeparatorIndex = function (value) {\n            return _removeFormatting(value).indexOf('.') + prefix.length;\n        };\n        if (value === 0 &&\n            fixedDecimalScale &&\n            decimalScale &&\n            currentValue[to.start] === decimalSeparator &&\n            getDecimalSeparatorIndex(currentValue) < currentValueIndex &&\n            getDecimalSeparatorIndex(formattedValue) > formattedValueIndex) {\n            return false;\n        }\n        if (currentValueIndex >= to.start &&\n            currentValueIndex < to.end &&\n            allowedDecimalSeparators &&\n            allowedDecimalSeparators.includes(curChar) &&\n            newChar === decimalSeparator) {\n            return true;\n        }\n        return curChar === newChar;\n    };\n    return Object.assign(Object.assign({}, restProps), { value: formattedValue, valueIsNumericString: false, isValidInputCharacter: isValidInputCharacter,\n        isCharacterSame: isCharacterSame, onValueChange: _onValueChange, format: _format, removeFormatting: _removeFormatting, getCaretBoundary: function (formattedValue) { return getCaretBoundary(formattedValue, props); }, onKeyDown: _onKeyDown, onBlur: _onBlur });\n}\nfunction NumericFormat(props) {\n    var numericFormatProps = useNumericFormat(props);\n    return React.createElement(NumberFormatBase, Object.assign({}, numericFormatProps));\n}\n\nfunction format$1(numStr, props) {\n    var format = props.format;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    if (numStr === '' && !allowEmptyFormatting)\n        { return ''; }\n    var hashCount = 0;\n    var formattedNumberAry = format.split('');\n    for (var i = 0, ln = format.length; i < ln; i++) {\n        if (format[i] === patternChar) {\n            formattedNumberAry[i] = numStr[hashCount] || getMaskAtIndex(mask, hashCount);\n            hashCount += 1;\n        }\n    }\n    return formattedNumberAry.join('');\n}\nfunction removeFormatting$1(value, changeMeta, props) {\n    if ( changeMeta === void 0 ) changeMeta = getDefaultChangeMeta(value);\n\n    var format = props.format;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var from = changeMeta.from;\n    var to = changeMeta.to;\n    var lastValue = changeMeta.lastValue; if ( lastValue === void 0 ) lastValue = '';\n    var isNumericSlot = function (caretPos) { return format[caretPos] === patternChar; };\n    var removeFormatChar = function (string, startIndex) {\n        var str = '';\n        for (var i = 0; i < string.length; i++) {\n            if (isNumericSlot(startIndex + i) && charIsNumber(string[i])) {\n                str += string[i];\n            }\n        }\n        return str;\n    };\n    var extractNumbers = function (str) { return str.replace(/[^0-9]/g, ''); };\n    // if format doesn't have any number, remove all the non numeric characters\n    if (!format.match(/\\d/)) {\n        return extractNumbers(value);\n    }\n    /**\n     * if user paste the whole formatted text in an empty input or doing select all and paste, check if matches to the pattern\n     * and remove the format characters, if there is a mismatch on the pattern, do plane number extract\n     */\n    if ((lastValue === '' || from.end - from.start === lastValue.length) &&\n        value.length === format.length) {\n        var str = '';\n        for (var i = 0; i < value.length; i++) {\n            if (isNumericSlot(i)) {\n                if (charIsNumber(value[i])) {\n                    str += value[i];\n                }\n            }\n            else if (value[i] !== format[i]) {\n                // if there is a mismatch on the pattern, do plane number extract\n                return extractNumbers(value);\n            }\n        }\n        return str;\n    }\n    /**\n     * For partial change,\n     * where ever there is a change on the input, we can break the number in three parts\n     * 1st: left part which is unchanged\n     * 2nd: middle part which is changed\n     * 3rd: right part which is unchanged\n     *\n     * The first and third section will be same as last value, only the middle part will change\n     * We can consider on the change part all the new characters are non format characters.\n     * And on the first and last section it can have partial format characters.\n     *\n     * We pick first and last section from the lastValue (as that has 1-1 mapping with format)\n     * and middle one from the update value.\n     */\n    var firstSection = lastValue.substring(0, from.start);\n    var middleSection = value.substring(to.start, to.end);\n    var lastSection = lastValue.substring(from.end);\n    return (\"\" + (removeFormatChar(firstSection, 0)) + (extractNumbers(middleSection)) + (removeFormatChar(lastSection, from.end)));\n}\nfunction getCaretBoundary$1(formattedValue, props) {\n    var format = props.format;\n    var mask = props.mask;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var boundaryAry = Array.from({ length: formattedValue.length + 1 }).map(function () { return true; });\n    var hashCount = 0;\n    var firstEmptySlot = -1;\n    var maskAndIndexMap = {};\n    format.split('').forEach(function (char, index) {\n        var maskAtIndex = undefined;\n        if (char === patternChar) {\n            hashCount++;\n            maskAtIndex = getMaskAtIndex(mask, hashCount - 1);\n            if (firstEmptySlot === -1 && formattedValue[index] === maskAtIndex) {\n                firstEmptySlot = index;\n            }\n        }\n        maskAndIndexMap[index] = maskAtIndex;\n    });\n    var isPosAllowed = function (pos) {\n        // the position is allowed if the position is not masked and valid number area\n        return format[pos] === patternChar && formattedValue[pos] !== maskAndIndexMap[pos];\n    };\n    for (var i = 0, ln = boundaryAry.length; i < ln; i++) {\n        // consider caret to be in boundary if it is before or after numeric value\n        // Note: on pattern based format its denoted by patternCharacter\n        // we should also allow user to put cursor on first empty slot\n        boundaryAry[i] = i === firstEmptySlot || isPosAllowed(i) || isPosAllowed(i - 1);\n    }\n    // the first patternChar position is always allowed\n    boundaryAry[format.indexOf(patternChar)] = true;\n    return boundaryAry;\n}\nfunction validateProps(props) {\n    var mask = props.mask;\n    if (mask) {\n        var maskAsStr = mask === 'string' ? mask : mask.toString();\n        if (maskAsStr.match(/\\d/g)) {\n            throw new Error((\"Mask \" + mask + \" should not contain numeric character;\"));\n        }\n    }\n}\nfunction isNumericString$1(val, format) {\n    //we can treat empty string as numeric string\n    if (val === '')\n        { return true; }\n    return !(format === null || format === void 0 ? void 0 : format.match(/\\d/)) && typeof val === 'string' && (!!val.match(/^\\d+$/) || val === '');\n}\nfunction usePatternFormat(props) {\n    var mask = props.mask;\n    var allowEmptyFormatting = props.allowEmptyFormatting;\n    var formatProp = props.format;\n    var inputMode = props.inputMode; if ( inputMode === void 0 ) inputMode = 'numeric';\n    var onKeyDown = props.onKeyDown; if ( onKeyDown === void 0 ) onKeyDown = noop;\n    var patternChar = props.patternChar; if ( patternChar === void 0 ) patternChar = '#';\n    var value = props.value;\n    var defaultValue = props.defaultValue;\n    var valueIsNumericString = props.valueIsNumericString;\n    var restProps = __rest(props, [\"mask\", \"allowEmptyFormatting\", \"format\", \"inputMode\", \"onKeyDown\", \"patternChar\", \"value\", \"defaultValue\", \"valueIsNumericString\"]);\n    // validate props\n    validateProps(props);\n    var _getCaretBoundary = function (formattedValue) {\n        return getCaretBoundary$1(formattedValue, props);\n    };\n    var _onKeyDown = function (e) {\n        var key = e.key;\n        var el = e.target;\n        var selectionStart = el.selectionStart;\n        var selectionEnd = el.selectionEnd;\n        var value = el.value;\n        // if multiple characters are selected and user hits backspace, no need to handle anything manually\n        if (selectionStart !== selectionEnd) {\n            onKeyDown(e);\n            return;\n        }\n        // bring the cursor to closest numeric section\n        var caretPos = selectionStart;\n        // if backspace is pressed after the format characters, bring it to numeric section\n        // if delete is pressed before the format characters, bring it to numeric section\n        if (key === 'Backspace' || key === 'Delete') {\n            var direction = 'right';\n            if (key === 'Backspace') {\n                while (caretPos > 0 && formatProp[caretPos - 1] !== patternChar) {\n                    caretPos--;\n                }\n                direction = 'left';\n            }\n            else {\n                var formatLn = formatProp.length;\n                while (caretPos < formatLn && formatProp[caretPos] !== patternChar) {\n                    caretPos++;\n                }\n                direction = 'right';\n            }\n            caretPos = getCaretPosInBoundary(value, caretPos, _getCaretBoundary(value), direction);\n        }\n        else if (formatProp[caretPos] !== patternChar &&\n            key !== 'ArrowLeft' &&\n            key !== 'ArrowRight') {\n            // if user is typing on format character position, bring user to next allowed caret position\n            caretPos = getCaretPosInBoundary(value, caretPos + 1, _getCaretBoundary(value), 'right');\n        }\n        // if we changing caret position, set the caret position\n        if (caretPos !== selectionStart) {\n            setCaretPosition(el, caretPos);\n        }\n        onKeyDown(e);\n    };\n    // try to figure out isValueNumericString based on format prop and value\n    var _value = isNil(value) ? defaultValue : value;\n    var isValueNumericString = valueIsNumericString !== null && valueIsNumericString !== void 0 ? valueIsNumericString : isNumericString$1(_value, formatProp);\n    var _props = Object.assign(Object.assign({}, props), { valueIsNumericString: isValueNumericString });\n    return Object.assign(Object.assign({}, restProps), { value: value,\n        defaultValue: defaultValue, valueIsNumericString: isValueNumericString, inputMode: inputMode, format: function (numStr) { return format$1(numStr, _props); }, removeFormatting: function (inputValue, changeMeta) { return removeFormatting$1(inputValue, changeMeta, _props); }, getCaretBoundary: _getCaretBoundary, onKeyDown: _onKeyDown });\n}\nfunction PatternFormat(props) {\n    var patternFormatProps = usePatternFormat(props);\n    return React.createElement(NumberFormatBase, Object.assign({}, patternFormatProps));\n}\n\nexport { NumberFormatBase, NumericFormat, PatternFormat, getCaretBoundary as getNumericCaretBoundary, getCaretBoundary$1 as getPatternCaretBoundary, format as numericFormatter, format$1 as patternFormatter, removeFormatting as removeNumericFormat, removeFormatting$1 as removePatternFormat, useNumericFormat, usePatternFormat };\n"], "mappings": ";;;;;;;;AAOA,mBAA6E;AAiB7E,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,GAAG;AAAE,QAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,GAChF;AAAE,QAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAAG;AAAA,EAAE;AACrB,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,YACrD;AAAE,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACtE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,GACzE;AAAE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,MAAG;AAAA,IAC7B;AAAA,EAAE;AACN,SAAO;AACX;AAEA,IAAI;AAAA,CACH,SAAUA,aAAY;AACnB,EAAAA,YAAW,OAAO,IAAI;AACtB,EAAAA,YAAW,OAAO,IAAI;AAC1B,GAAG,eAAe,aAAa,CAAC,EAAE;AAGlC,SAAS,OAAO;AAAE;AAClB,SAAS,YAAY,IAAI;AACrB,MAAI;AACJ,MAAI,YAAY;AAChB,SAAO,WAAY;AACf,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU;AAC/B,WAAQ,MAAQ,MAAM,GAAI,IAAI,UAAW,GAAI;AAE7C,QAAI,YACA,KAAK,WAAW,SAAS,UACzB,KAAK,MAAM,SAAU,OAAO,OAAO;AAAE,aAAO,UAAU,SAAS,KAAK;AAAA,IAAG,CAAC,GAAG;AAC3E,aAAO;AAAA,IACX;AACA,eAAW;AACX,gBAAY,GAAG,MAAM,QAAQ,IAAI;AACjC,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,SAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AACpC;AACA,SAAS,MAAM,KAAK;AAChB,SAAO,QAAQ,QAAQ,QAAQ;AACnC;AACA,SAAS,WAAW,KAAK;AACrB,SAAO,OAAO,QAAQ,YAAY,MAAM,GAAG;AAC/C;AACA,SAAS,gBAAgB,KAAK;AAC1B,SAAO,MAAM,GAAG,KAAK,WAAW,GAAG,KAAM,OAAO,QAAQ,YAAY,CAAC,SAAS,GAAG;AACrF;AACA,SAAS,aAAa,KAAK;AACvB,SAAO,IAAI,QAAQ,yBAAyB,MAAM;AACtD;AACA,SAAS,uBAAuB,qBAAqB;AACjD,UAAQ,qBAAqB;AAAA,IACzB,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAAS,uBAAuB,KAAK,mBAAmB,qBAAqB;AACzE,MAAI,sBAAsB,uBAAuB,mBAAmB;AACpE,MAAI,QAAQ,IAAI,OAAO,OAAO;AAC9B,UAAQ,UAAU,KAAK,IAAI,SAAS;AACpC,SAAQ,IAAI,UAAU,GAAG,KAAK,IAC1B,IAAI,UAAU,OAAO,IAAI,MAAM,EAAE,QAAQ,qBAAqB,OAAO,iBAAiB;AAC9F;AACA,SAAS,sBAAsB,IAAI;AAC/B,MAAI,kBAAc,qBAAO,EAAE;AAE3B,cAAY,UAAU;AAKtB,MAAI,sBAAkB,qBAAO,WAAY;AACrC,QAAI,OAAO,CAAC,GAAG,MAAM,UAAU;AAC/B,WAAQ,MAAQ,MAAM,GAAI,IAAI,UAAW,GAAI;AAE7C,WAAO,YAAY,QAAQ,MAAM,aAAa,IAAI;AAAA,EACtD,CAAC;AACD,SAAO,gBAAgB;AAC3B;AAEA,SAAS,aAAa,QAAQ,eAAe;AACzC,MAAK,kBAAkB,OAAS,iBAAgB;AAEhD,MAAI,cAAc,OAAO,CAAC,MAAM;AAChC,MAAI,cAAc,eAAe;AACjC,WAAS,OAAO,QAAQ,KAAK,EAAE;AAC/B,MAAI,QAAQ,OAAO,MAAM,GAAG;AAC5B,MAAI,gBAAgB,MAAM,CAAC;AAC3B,MAAI,eAAe,MAAM,CAAC,KAAK;AAC/B,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,QAAQ;AAC5B,MAAI,CAAC,QACD;AAAE,WAAO;AAAA,EAAQ;AACrB,MAAI,aAAa,OAAO,CAAC,MAAM;AAC/B,MAAI,YACA;AAAE,aAAS,OAAO,UAAU,GAAG,OAAO,MAAM;AAAA,EAAG;AACnD,MAAI,QAAQ,OAAO,MAAM,GAAG;AAC5B,MAAI,gBAAgB,MAAM,CAAC,EAAE,QAAQ,OAAO,EAAE,KAAK;AACnD,MAAI,eAAe,MAAM,CAAC,KAAK;AAC/B,UAAc,aAAa,MAAM,MAAM,iBAAiB,eAAgB,MAAM,eAAgB;AAClG;AAKA,SAAS,aAAa,QAAQ,OAAO,mBAAmB;AACpD,MAAI,MAAM;AACV,MAAI,SAAS,oBAAoB,MAAM;AACvC,WAAS,IAAI,GAAG,KAAK,QAAQ,GAAG,KAAK;AACjC,WAAO,OAAO,CAAC,KAAK;AAAA,EACxB;AACA,SAAO;AACX;AACA,SAAS,OAAO,KAAK,OAAO;AACxB,SAAO,MAAM,QAAQ,CAAC,EAAE,KAAK,GAAG;AACpC;AACA,SAAS,gBAAgB,KAAK;AAC1B,MAAI,OAAO,MAAM;AAEjB,MAAI,OAAO,KAAK,CAAC,MAAM,MAAM,MAAM;AACnC,MAAI,MACA;AAAE,WAAO,KAAK,UAAU,CAAC;AAAA,EAAG;AAEhC,MAAI,MAAM,KAAK,MAAM,OAAO;AAC5B,MAAI,cAAc,IAAI,CAAC;AACvB,MAAI,WAAW,IAAI,CAAC;AAEpB,aAAW,OAAO,QAAQ;AAE1B,MAAI,CAAC,UACD;AAAE,WAAO,OAAO;AAAA,EAAa;AACjC,gBAAc,YAAY,QAAQ,KAAK,EAAE;AAKzC,MAAI,eAAe,IAAI;AACvB,MAAI,gBAAgB,YAAY;AAChC,MAAI,eAAe,GAAG;AAGlB,kBAAc,OAAO,OAAO,KAAK,KAAK,IAAI,YAAY,CAAC,IAAI;AAAA,EAC/D,WACS,gBAAgB,eAAe;AAEpC,kBAAc,cAAc,OAAO,KAAK,eAAe,aAAa;AAAA,EACxE,OACK;AAED,mBACK,YAAY,UAAU,GAAG,YAAY,KAAK,OAAO,MAAM,YAAY,UAAU,YAAY;AAAA,EAClG;AACA,SAAO,OAAO;AAClB;AAKA,SAAS,iBAAiB,QAAQ,OAAO,mBAAmB;AAExD,MAAI,CAAC,IAAI,GAAG,EAAE,QAAQ,MAAM,MAAM,IAC9B;AAAE,WAAO;AAAA,EAAQ;AACrB,MAAI,8BAA8B,OAAO,QAAQ,GAAG,MAAM,MAAM,sBAAsB;AACtF,MAAI,MAAM,aAAa,MAAM;AAC7B,MAAI,gBAAgB,IAAI;AACxB,MAAI,eAAe,IAAI;AACvB,MAAI,cAAc,IAAI;AACtB,MAAI,aAAa,WAAY,QAAQ,gBAAgB,IAAK;AAC1D,MAAI,gBAAgB,aAAa,UAAU,QAAS,OAAO,eAAgB,WAAW,QAAQ,KAAK;AACnG,MAAI,sBAAsB,cAAc,MAAM,GAAG;AACjD,MAAI,UAAU;AAEd,MAAI,iBAAiB,OAAO,oBAAoB,CAAC,CAAC,GAAG;AACjD,cAAU,cACL,MAAM,EAAE,EACR,QAAQ,EACR,OAAO,SAAU,YAAY,SAAS,KAAK;AAC5C,UAAI,WAAW,SAAS,KAAK;AACzB,gBAAS,OAAO,WAAW,CAAC,CAAC,IAAI,OAAO,OAAO,GAAG,SAAS,IACvD,WAAW,UAAU,GAAG,WAAW,MAAM;AAAA,MACjD;AACA,aAAO,UAAU;AAAA,IACrB,GAAG,oBAAoB,CAAC,CAAC;AAAA,EAC7B;AACA,MAAI,cAAc,aAAa,oBAAoB,CAAC,KAAK,IAAI,OAAO,iBAAiB;AACrF,MAAI,WAAW,cAAc,MAAM;AACnC,MAAI,mBAAmB,6BAA6B,MAAM;AAC1D,SAAQ,KAAK,WAAW,UAAU,mBAAmB;AACzD;AAEA,SAAS,iBAAiB,IAAI,UAAU;AACpC,KAAG,QAAQ,GAAG;AAId,MAAI,OAAO,MAAM;AAEb,QAAI,GAAG,iBAAiB;AAEpB,UAAI,QAAQ,GAAG,gBAAgB;AAC/B,YAAM,KAAK,aAAa,QAAQ;AAChC,YAAM,OAAO;AACb,aAAO;AAAA,IACX;AAEA,QAAI,GAAG,kBAAkB,GAAG,mBAAmB,GAAG;AAC9C,SAAG,MAAM;AACT,SAAG,kBAAkB,UAAU,QAAQ;AACvC,aAAO;AAAA,IACX;AAEA,OAAG,MAAM;AACT,WAAO;AAAA,EACX;AACJ;AASA,IAAI,kBAAkB,YAAY,SAAU,WAAW,UAAU;AAC7D,MAAI,IAAI,GAAG,IAAI;AACf,MAAI,aAAa,UAAU;AAC3B,MAAI,YAAY,SAAS;AACzB,SAAO,UAAU,CAAC,MAAM,SAAS,CAAC,KAAK,IAAI,YACvC;AAAE;AAAA,EAAK;AAEX,SAAO,UAAU,aAAa,IAAI,CAAC,MAAM,SAAS,YAAY,IAAI,CAAC,KAC/D,YAAY,IAAI,KAChB,aAAa,IAAI,GAAG;AACpB;AAAA,EACJ;AACA,SAAO;AAAA,IACH,MAAM,EAAE,OAAO,GAAG,KAAK,aAAa,EAAE;AAAA,IACtC,IAAI,EAAE,OAAO,GAAG,KAAK,YAAY,EAAE;AAAA,EACvC;AACJ,CAAC;AACD,IAAI,qCAAqC,SAAU,oBAAoB,sBAAsB;AACzF,MAAI,gBAAgB,KAAK,IAAI,mBAAmB,gBAAgB,oBAAoB;AACpF,SAAO;AAAA,IACH,MAAM,EAAE,OAAO,eAAe,KAAK,mBAAmB,aAAa;AAAA,IACnE,IAAI,EAAE,OAAO,eAAe,KAAK,qBAAqB;AAAA,EAC1D;AACJ;AAIA,SAAS,MAAM,KAAK,KAAK,KAAK;AAC1B,SAAO,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;AAC3C;AACA,SAAS,qBAAqB,IAAI;AAE9B,SAAO,KAAK,IAAI,GAAG,gBAAgB,GAAG,YAAY;AACtD;AACA,SAAS,eAAe;AACpB,SAAQ,OAAO,cAAc,eACzB,EAAE,UAAU,YAAY,cAAc,KAAK,UAAU,QAAQ;AACrE;AACA,SAAS,qBAAqB,OAAO;AACjC,SAAO;AAAA,IACH,MAAM;AAAA,MACF,OAAO;AAAA,MACP,KAAK;AAAA,IACT;AAAA,IACA,IAAI;AAAA,MACA,OAAO;AAAA,MACP,KAAK,MAAM;AAAA,IACf;AAAA,IACA,WAAW;AAAA,EACf;AACJ;AACA,SAAS,eAAe,MAAM,OAAO;AACjC,MAAK,SAAS,OAAS,QAAO;AAE9B,MAAI,OAAO,SAAS,UAAU;AAC1B,WAAO;AAAA,EACX;AACA,SAAO,KAAK,KAAK,KAAK;AAC1B;AACA,SAAS,uBAAuB,KAAK;AACjC,MAAI,eAAe,IAAI;AACvB,MAAI,iBAAiB,IAAI;AACzB,MAAI,oBAAoB,IAAI;AAC5B,MAAI,sBAAsB,IAAI;AAE9B,SAAO,aAAa,iBAAiB,MAAM,eAAe,mBAAmB;AACjF;AACA,SAAS,iBAAiB,mBAAmB,oBAAoB,UAAU,aAAa,UAAU,uBAOlG,iBAAiB;AACb,MAAK,oBAAoB,OAAS,mBAAkB;AAMpD,MAAI,uBAAuB,SAAS,UAAU,SAAU,GAAG;AAAE,WAAO;AAAA,EAAG,CAAC;AACxE,MAAI,eAAe,kBAAkB,MAAM,GAAG,oBAAoB;AAClE,MAAI,CAAC,sBAAsB,CAAC,SAAS,WAAW,YAAY,GAAG;AAC3D,yBAAqB;AACrB,eAAW,eAAe;AAC1B,kBAAc,cAAc,aAAa;AAAA,EAC7C;AACA,MAAI,WAAW,SAAS;AACxB,MAAI,mBAAmB,kBAAkB;AAEzC,MAAI,gBAAgB,CAAC;AACrB,MAAI,WAAW,IAAI,MAAM,QAAQ;AACjC,WAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AAC/B,aAAS,CAAC,IAAI;AACd,aAAS,IAAI,GAAG,MAAM,kBAAkB,IAAI,KAAK,KAAK;AAClD,UAAI,aAAa,gBAAgB;AAAA,QAC7B,cAAc;AAAA,QACd,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,qBAAqB;AAAA,MACzB,CAAC;AACD,UAAI,cAAc,cAAc,CAAC,MAAM,MAAM;AACzC,iBAAS,CAAC,IAAI;AACd,sBAAc,CAAC,IAAI;AACnB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAOA,MAAI,MAAM;AACV,SAAO,MAAM,aAAa,SAAS,GAAG,MAAM,MAAM,CAAC,sBAAsB,SAAS,GAAG,CAAC,IAAI;AACtF;AAAA,EACJ;AAEA,MAAI,WAAW,QAAQ,YAAY,SAAS,GAAG,MAAM,KAAK,mBAAmB,SAAS,GAAG;AACzF,QAAM,cAAc;AACpB,SAAO,MAAM,KAAK,SAAS,GAAG,MAAM,IAChC;AAAE;AAAA,EAAO;AACb,MAAI,aAAa,QAAQ,MAAM,SAAS,GAAG,MAAM,KAAK,IAAI,SAAS,GAAG,IAAI;AAK1E,MAAI,aAAa,UACb;AAAE,WAAO;AAAA,EAAU;AAKvB,SAAO,cAAc,aAAa,WAAW,cAAc,aAAa;AAC5E;AAEA,SAAS,sBAAsB,OAAO,UAAU,UAAU,WAAW;AACjE,MAAI,QAAQ,MAAM;AAElB,aAAW,MAAM,UAAU,GAAG,KAAK;AACnC,MAAI,cAAc,QAAQ;AACtB,WAAO,YAAY,KAAK,CAAC,SAAS,QAAQ,GACtC;AAAE;AAAA,IAAY;AAElB,QAAI,aAAa,IACb;AAAE,iBAAW,SAAS,QAAQ,IAAI;AAAA,IAAG;AAAA,EAC7C,OACK;AACD,WAAO,YAAY,SAAS,CAAC,SAAS,QAAQ,GAC1C;AAAE;AAAA,IAAY;AAElB,QAAI,WAAW,OACX;AAAE,iBAAW,SAAS,YAAY,IAAI;AAAA,IAAG;AAAA,EACjD;AAEA,MAAI,aAAa,IACb;AAAE,eAAW;AAAA,EAAO;AACxB,SAAO;AACX;AACA,SAAS,2BAA2B,gBAAgB;AAChD,MAAI,cAAc,MAAM,KAAK,EAAE,QAAQ,eAAe,SAAS,EAAE,CAAC,EAAE,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,CAAC;AACpG,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AAElD,gBAAY,CAAC,IAAI,QAAQ,aAAa,eAAe,CAAC,CAAC,KAAK,aAAa,eAAe,IAAI,CAAC,CAAC,CAAC;AAAA,EACnG;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO,cAAc,sBAAsBC,SAAQC,mBAAkB,eAAe;AAC3G,MAAK,kBAAkB,OAAS,iBAAgB;AAEhD,MAAI,YAAY,sBAAsB,SAAUC,QAAOC,uBAAsB;AACzE,QAAI,gBAAgB;AACpB,QAAI,gBAAgBD,MAAK,GAAG;AACxB,oBAAc;AACd,uBAAiB;AAAA,IACrB,WACS,OAAOA,WAAU,YAAYC,uBAAsB;AACxD,oBAAc,OAAOD,WAAU,WAAW,gBAAgBA,MAAK,IAAIA;AACnE,uBAAiBF,QAAO,WAAW;AAAA,IACvC,OACK;AACD,oBAAcC,kBAAiBC,QAAO,MAAS;AAC/C,uBAAiBF,QAAO,WAAW;AAAA,IACvC;AACA,WAAO,EAAE,gBAAgC,YAAyB;AAAA,EACtE,CAAC;AACD,MAAI,UAAM,uBAAS,WAAY;AAC3B,WAAO,UAAU,MAAM,KAAK,IAAI,eAAe,OAAO,oBAAoB;AAAA,EAC9E,CAAC;AACD,MAAI,SAAS,IAAI,CAAC;AAClB,MAAI,YAAY,IAAI,CAAC;AACrB,MAAI,iBAAiB,SAAUI,YAAW,YAAY;AAClD,QAAIA,WAAU,mBAAmB,OAAO,gBAAgB;AACpD,gBAAU;AAAA,QACN,gBAAgBA,WAAU;AAAA,QAC1B,aAAaA,WAAU;AAAA,MAC3B,CAAC;AAAA,IACL;AAEA,kBAAcA,YAAW,UAAU;AAAA,EACvC;AAEA,MAAI,SAAS;AACb,MAAI,wBAAwB;AAC5B,MAAI,MAAM,KAAK,GAAG;AACd,aAAS,OAAO;AAChB,4BAAwB;AAAA,EAC5B;AACA,MAAI,YAAY,UAAU,QAAQ,qBAAqB;AACvD,4BAAQ,WAAY;AAChB,cAAU,SAAS;AAAA,EACvB,GAAG,CAAC,UAAU,cAAc,CAAC;AAC7B,SAAO,CAAC,QAAQ,cAAc;AAClC;AAEA,SAAS,wBAAwB,OAAO;AACpC,SAAO,MAAM,QAAQ,WAAW,EAAE;AACtC;AACA,SAAS,cAAc,OAAO;AAC1B,SAAO;AACX;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,MAAM;AAAM,MAAK,SAAS,OAAS,QAAO;AACrD,MAAI,cAAc,MAAM;AAAa,MAAK,gBAAgB,OAAS,eAAc;AACjF,MAAI,cAAc,MAAM;AACxB,MAAI,aAAa,MAAM;AACvB,MAAI,cAAc,MAAM;AACxB,MAAIJ,UAAS,MAAM;AAAQ,MAAKA,YAAW,OAAS,CAAAA,UAAS;AAC7D,MAAIC,oBAAmB,MAAM;AAAkB,MAAKA,sBAAqB,OAAS,CAAAA,oBAAmB;AACrG,MAAI,eAAe,MAAM;AACzB,MAAI,uBAAuB,MAAM;AACjC,MAAI,gBAAgB,MAAM;AAC1B,MAAI,YAAY,MAAM;AACtB,MAAI,WAAW,MAAM;AAAU,MAAK,aAAa,OAAS,YAAW;AACrE,MAAI,YAAY,MAAM;AAAW,MAAK,cAAc,OAAS,aAAY;AACzE,MAAI,YAAY,MAAM;AAAW,MAAK,cAAc,OAAS,aAAY;AACzE,MAAI,UAAU,MAAM;AAAS,MAAK,YAAY,OAAS,WAAU;AACjE,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,YAAY,MAAM;AACtB,MAAII,oBAAmB,MAAM;AAAkB,MAAKA,sBAAqB,OAAS,CAAAA,oBAAmB;AACrG,MAAI,wBAAwB,MAAM;AAAuB,MAAK,0BAA0B,OAAS,yBAAwB;AACzH,MAAI,kBAAkB,MAAM;AAC5B,MAAI,aAAa,OAAO,OAAO,CAAC,QAAQ,eAAe,eAAe,cAAc,eAAe,UAAU,oBAAoB,gBAAgB,wBAAwB,iBAAiB,aAAa,YAAY,aAAa,aAAa,WAAW,UAAU,SAAS,oBAAoB,yBAAyB,iBAAiB,CAAC;AAC1U,MAAI,MAAM,kBAAkB,WAAW,cAAc,QAAQ,oBAAoB,GAAGL,SAAQC,mBAAkB,aAAa;AAC3H,MAAI,QAAQ,IAAI,CAAC;AACjB,MAAI,iBAAiB,MAAM;AAC3B,MAAI,cAAc,MAAM;AACxB,MAAI,yBAAyB,IAAI,CAAC;AAClC,MAAI,gCAA4B,qBAAO;AACvC,MAAI,uBAAmB,qBAAO,EAAE,gBAAgC,YAAyB,CAAC;AAC1F,MAAI,iBAAiB,SAAU,QAAQ,QAAQ;AAC3C,qBAAiB,UAAU,EAAE,gBAAgB,OAAO,gBAAgB,aAAa,OAAO,MAAM;AAC9F,2BAAuB,QAAQ,MAAM;AAAA,EACzC;AACA,MAAI,YAAQ,uBAAS,KAAK;AAC1B,MAAI,UAAU,MAAM,CAAC;AACrB,MAAI,aAAa,MAAM,CAAC;AACxB,MAAI,iBAAa,qBAAO,IAAI;AAC5B,MAAI,cAAU,qBAAO;AAAA,IACjB,iBAAiB;AAAA,IACjB,cAAc;AAAA,EAClB,CAAC;AACD,8BAAU,WAAY;AAClB,eAAW,IAAI;AACf,WAAO,WAAY;AACf,mBAAa,QAAQ,QAAQ,eAAe;AAC5C,mBAAa,QAAQ,QAAQ,YAAY;AAAA,IAC7C;AAAA,EACJ,GAAG,CAAC,CAAC;AACL,MAAI,UAAUD;AACd,MAAI,iBAAiB,SAAUM,iBAAgBC,cAAa;AACxD,QAAI,aAAa,WAAWA,YAAW;AACvC,WAAO;AAAA,MACH,gBAAgBD;AAAA,MAChB,OAAOC;AAAA,MACP,YAAY,MAAM,UAAU,IAAI,SAAY;AAAA,IAChD;AAAA,EACJ;AACA,MAAI,0BAA0B,SAAU,IAAI,UAAU,cAAc;AAEhE,QAAI,GAAG,mBAAmB,KAAK,GAAG,iBAAiB,GAAG,MAAM,QACxD;AAAE;AAAA,IAAQ;AAId,qBAAiB,IAAI,QAAQ;AAC7B,YAAQ,QAAQ,kBAAkB,WAAW,WAAY;AACrD,UAAI,GAAG,UAAU,gBAAgB,GAAG,mBAAmB,UAAU;AAC7D,yBAAiB,IAAI,QAAQ;AAAA,MACjC;AAAA,IACJ,GAAG,CAAC;AAAA,EACR;AAEA,MAAI,uBAAuB,SAAU,OAAO,UAAU,WAAW;AAC7D,WAAO,sBAAsB,OAAO,UAAUF,kBAAiB,KAAK,GAAG,SAAS;AAAA,EACpF;AACA,MAAI,sBAAsB,SAAU,YAAY,mBAAmB,UAAU;AACzE,QAAI,gBAAgBA,kBAAiB,iBAAiB;AACtD,QAAI,kBAAkB,iBAAiB,mBAAmB,gBAAgB,YAAY,UAAU,eAAe,uBAAuB,eAAe;AAErJ,sBAAkB,sBAAsB,mBAAmB,iBAAiB,aAAa;AACzF,WAAO;AAAA,EACX;AACA,MAAI,8BAA8B,SAAU,QAAQ;AAChD,QAAI,oBAAoB,OAAO;AAAgB,QAAK,sBAAsB,OAAS,qBAAoB;AACvG,QAAI,QAAQ,OAAO;AACnB,QAAI,SAAS,OAAO;AACpB,QAAI,QAAQ,OAAO;AACnB,QAAIE,eAAc,OAAO;AACzB,QAAI;AACJ,QAAI,OAAO;AACP,UAAI,aAAa,OAAO,cAAc,MAAM;AAC5C,UAAIC,wBAAuB,qBAAqB,KAAK;AAMrD,YAAM,QAAQ;AAEd,iBAAW,oBAAoB,YAAY,mBAAmBA,qBAAoB;AAElF,UAAI,aAAa,QAAW;AACxB,gCAAwB,OAAO,UAAU,iBAAiB;AAAA,MAC9D;AAAA,IACJ;AACA,QAAI,sBAAsB,gBAAgB;AAEtC,qBAAe,eAAe,mBAAmBD,YAAW,GAAG,EAAE,OAAc,OAAe,CAAC;AAAA,IACnG;AAAA,EACJ;AAMA,8BAAU,WAAY;AAClB,QAAIE,OAAM,iBAAiB;AAC3B,QAAI,qBAAqBA,KAAI;AAC7B,QAAI,kBAAkBA,KAAI;AAC1B,QAAI,mBAAmB,sBAAsB,gBAAgB,iBAAiB;AAC1E,qBAAe,eAAe,gBAAgB,WAAW,GAAG;AAAA,QACxD,OAAO;AAAA,QACP,QAAQ,WAAW;AAAA,MACvB,CAAC;AAAA,IACL;AAAA,EACJ,GAAG,CAAC,gBAAgB,WAAW,CAAC;AAGhC,MAAI,uBAAuB,WAAW,UAChC,qBAAqB,WAAW,OAAO,IACvC;AAEN,MAAI,4BAA4B,OAAO,WAAW,cAAc,+BAAkB;AAClF,4BAA0B,WAAY;AAClC,QAAI,QAAQ,WAAW;AACvB,QAAI,mBAAmB,iBAAiB,QAAQ,kBAAkB,OAAO;AACrE,UAAI,WAAW,oBAAoB,iBAAiB,QAAQ,gBAAgB,gBAAgB,oBAAoB;AAKhH,YAAM,QAAQ;AACd,8BAAwB,OAAO,UAAU,cAAc;AAAA,IAC3D;AAAA,EACJ,GAAG,CAAC,cAAc,CAAC;AACnB,MAAI,mBAAmB,SAAU,YAAY,OAAO,QAAQ;AACxD,QAAI,QAAQ,MAAM;AAClB,QAAI,cAAc,0BAA0B,UACtC,mCAAmC,0BAA0B,SAAS,MAAM,YAAY,IACxF,gBAAgB,gBAAgB,UAAU;AAChD,QAAI,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,WAAW,GAAG,EAAE,WAAW,eAAe,CAAC;AAC5F,QAAI,eAAeR,kBAAiB,YAAY,UAAU;AAC1D,QAAI,kBAAkB,QAAQ,YAAY;AAE1C,mBAAeA,kBAAiB,iBAAiB,MAAS;AAC1D,QAAI,aAAa,CAAC,UAAU,eAAe,iBAAiB,YAAY,CAAC,GAAG;AAExE,UAAI,UAAU,MAAM;AACpB,UAAIO,wBAAuB,qBAAqB,OAAO;AACvD,UAAI,WAAW,oBAAoB,YAAY,gBAAgBA,qBAAoB;AACnF,cAAQ,QAAQ;AAChB,8BAAwB,SAAS,UAAU,cAAc;AACzD,aAAO;AAAA,IACX;AACA,gCAA4B;AAAA,MACxB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA,OAAO,MAAM;AAAA,IACjB,CAAC;AACD,WAAO;AAAA,EACX;AACA,MAAI,mCAAmC,SAAU,IAAI,WAAW;AAC5D,QAAK,cAAc,OAAS,aAAY;AAExC,QAAI,iBAAiB,GAAG;AACxB,QAAI,eAAe,GAAG;AACtB,8BAA0B,UAAU,EAAE,gBAAgC,cAAc,eAAe,UAAU;AAAA,EACjH;AACA,MAAI,YAAY,SAAU,GAAG;AACzB,QAAI,KAAK,EAAE;AACX,QAAI,aAAa,GAAG;AACpB,QAAI,UAAU,iBAAiB,YAAY,GAAG,WAAW,KAAK;AAC9D,QAAI,SACA;AAAE,eAAS,CAAC;AAAA,IAAG;AAEnB,8BAA0B,UAAU;AAAA,EACxC;AACA,MAAI,aAAa,SAAU,GAAG;AAC1B,QAAI,KAAK,EAAE;AACX,QAAI,MAAM,EAAE;AACZ,QAAI,iBAAiB,GAAG;AACxB,QAAI,eAAe,GAAG;AACtB,QAAI,QAAQ,GAAG;AAAO,QAAK,UAAU,OAAS,SAAQ;AACtD,QAAI;AAEJ,QAAI,QAAQ,eAAe,QAAQ,aAAa;AAC5C,8BAAwB,KAAK,IAAI,iBAAiB,GAAG,CAAC;AAAA,IAC1D,WACS,QAAQ,cAAc;AAC3B,8BAAwB,KAAK,IAAI,iBAAiB,GAAG,MAAM,MAAM;AAAA,IACrE,WACS,QAAQ,UAAU;AACvB,8BAAwB;AAAA,IAC5B;AAGA,QAAI,YAAY;AAChB,QAAI,QAAQ,YAAY,mBAAmB,cAAc;AACrD,kBAAY;AAAA,IAChB;AACA,QAAI,aAAa,QAAQ,eAAe,QAAQ;AAGhD,QAAI,0BAA0B,UAAc,mBAAmB,gBAAgB,CAAC,YAAa;AACzF,gBAAU,CAAC;AAGX,uCAAiC,IAAI,SAAS;AAC9C;AAAA,IACJ;AACA,QAAI,mBAAmB;AACvB,QAAI,YAAY;AACZ,UAAI,YAAY,QAAQ,cAAc,SAAS;AAC/C,yBAAmB,qBAAqB,OAAO,uBAAuB,SAAS;AAE/E,UAAI,qBAAqB,uBAAuB;AAC5C,UAAE,eAAe;AAAA,MACrB;AAAA,IACJ,WACS,QAAQ,YAAY,CAAC,sBAAsB,MAAM,qBAAqB,CAAC,GAAG;AAE/E,yBAAmB,qBAAqB,OAAO,uBAAuB,OAAO;AAAA,IACjF,WACS,QAAQ,eAAe,CAAC,sBAAsB,MAAM,qBAAqB,CAAC,GAAG;AAElF,yBAAmB,qBAAqB,OAAO,uBAAuB,MAAM;AAAA,IAChF;AACA,QAAI,qBAAqB,uBAAuB;AAC5C,8BAAwB,IAAI,kBAAkB,KAAK;AAAA,IACvD;AACA,cAAU,CAAC;AACX,qCAAiC,IAAI,SAAS;AAAA,EAClD;AAEA,MAAI,aAAa,SAAU,GAAG;AAC1B,QAAI,KAAK,EAAE;AAKX,QAAI,iCAAiC,WAAY;AAC7C,UAAI,iBAAiB,GAAG;AACxB,UAAI,eAAe,GAAG;AACtB,UAAI,QAAQ,GAAG;AAAO,UAAK,UAAU,OAAS,SAAQ;AACtD,UAAI,mBAAmB,cAAc;AACjC,YAAI,gBAAgB,qBAAqB,OAAO,cAAc;AAC9D,YAAI,kBAAkB,gBAAgB;AAClC,kCAAwB,IAAI,eAAe,KAAK;AAAA,QACpD;AAAA,MACJ;AAAA,IACJ;AACA,mCAA+B;AAG/B,0BAAsB,WAAY;AAC9B,qCAA+B;AAAA,IACnC,CAAC;AACD,cAAU,CAAC;AACX,qCAAiC,EAAE;AAAA,EACvC;AACA,MAAI,WAAW,SAAU,GAAG;AAGxB,QAAI,EAAE,SACF;AAAE,QAAE,QAAQ;AAAA,IAAG;AACnB,QAAI,KAAK,EAAE;AACX,QAAI,gBAAgB,EAAE;AACtB,eAAW,UAAU;AACrB,YAAQ,QAAQ,eAAe,WAAW,WAAY;AAClD,UAAI,iBAAiB,GAAG;AACxB,UAAI,eAAe,GAAG;AACtB,UAAI,QAAQ,GAAG;AAAO,UAAK,UAAU,OAAS,SAAQ;AACtD,UAAI,gBAAgB,qBAAqB,OAAO,cAAc;AAE9D,UAAI,kBAAkB,kBAClB,EAAE,mBAAmB,KAAK,iBAAiB,MAAM,SAAS;AAC1D,gCAAwB,IAAI,eAAe,KAAK;AAAA,MACpD;AACA,cAAQ,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,cAA6B,CAAC,CAAC;AAAA,IACjF,GAAG,CAAC;AAAA,EACR;AACA,MAAI,UAAU,SAAU,GAAG;AACvB,eAAW,UAAU;AACrB,iBAAa,QAAQ,QAAQ,YAAY;AACzC,iBAAa,QAAQ,QAAQ,eAAe;AAC5C,WAAO,CAAC;AAAA,EACZ;AAEA,MAAI,YAAY,WAAW,aAAa,IAAI,YAAY;AACxD,MAAI,aAAa,OAAO,OAAO,EAAE,UAAqB,GAAG,YAAY;AAAA,IACjE;AAAA,IACA,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,WAAW;AAAA,IACX,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ,CAAC;AACD,MAAI,gBAAgB,QAAQ;AACxB,WAAO,aAAc,aAAAE,QAAM,cAAc,aAAAA,QAAM,UAAU,MAAM,WAAW,gBAAgB,UAAU,KAAK,IAAI,IAAM,aAAAA,QAAM,cAAc,QAAQ,OAAO,OAAO,CAAC,GAAG,YAAY,EAAE,KAAK,YAAY,CAAC,GAAG,cAAc;AAAA,EACtN,WACS,aAAa;AAClB,QAAI,cAAc;AAElB,WAAO,aAAAA,QAAM,cAAc,aAAa,OAAO,OAAO,CAAC,GAAG,YAAY,EAAE,KAAK,YAAY,CAAC,CAAC;AAAA,EAC/F;AACA,SAAO,aAAAA,QAAM,cAAc,SAAS,OAAO,OAAO,CAAC,GAAG,YAAY,EAAE,KAAK,YAAY,CAAC,CAAC;AAC3F;AAEA,SAAS,OAAO,QAAQ,OAAO;AAC3B,MAAI,eAAe,MAAM;AACzB,MAAI,oBAAoB,MAAM;AAC9B,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,gBAAgB,MAAM;AAC1B,MAAI,sBAAsB,MAAM;AAAqB,MAAK,wBAAwB,OAAS,uBAAsB;AAEjH,MAAI,WAAW,MAAM,WAAW,KAAK;AACjC,WAAO;AAAA,EACX;AACA,MAAI,MAAM,cAAc,KAAK;AAC7B,MAAI,oBAAoB,IAAI;AAC5B,MAAI,mBAAmB,IAAI;AAM3B,MAAI,sBAAuB,iBAAiB,KAAK,OAAO,QAAQ,GAAG,MAAM,MAAQ,gBAAgB;AACjG,MAAI,QAAQ,aAAa,QAAQ,aAAa;AAC9C,MAAI,gBAAgB,MAAM;AAC1B,MAAI,eAAe,MAAM;AACzB,MAAI,cAAc,MAAM;AAExB,MAAI,iBAAiB,QAAW;AAC5B,mBAAe,aAAa,cAAc,cAAc,CAAC,CAAC,iBAAiB;AAAA,EAC/E;AACA,MAAI,mBAAmB;AACnB,oBAAgB,uBAAuB,eAAe,mBAAmB,mBAAmB;AAAA,EAChG;AAEA,MAAI,QACA;AAAE,oBAAgB,SAAS;AAAA,EAAe;AAC9C,MAAI,QACA;AAAE,mBAAe,eAAe;AAAA,EAAQ;AAE5C,MAAI,aACA;AAAE,oBAAgB,MAAM;AAAA,EAAe;AAC3C,WAAS,iBAAkB,uBAAuB,oBAAqB,MAAM;AAC7E,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,mBAAmB,MAAM;AAAkB,MAAK,qBAAqB,OAAS,oBAAmB;AACrG,MAAI,oBAAoB,MAAM;AAC9B,MAAI,2BAA2B,MAAM;AACrC,MAAI,sBAAsB,MAAM;AAC5B,wBAAoB;AAAA,EACxB;AACA,MAAI,CAAC,0BAA0B;AAC3B,+BAA2B,CAAC,kBAAkB,GAAG;AAAA,EACrD;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,OAAO,eAAe;AAC1C,MAAK,UAAU,OAAS,SAAQ;AAEhC,MAAI,gBAAgB,IAAI,OAAO,KAAK;AACpC,MAAI,sBAAsB,IAAI,OAAO,YAAY;AAEjD,MAAI,cAAc,cAAc,KAAK,KAAK;AAE1C,MAAI,iBAAiB,oBAAoB,KAAK,KAAK;AAEnD,UAAQ,MAAM,QAAQ,MAAM,EAAE;AAC9B,MAAI,eAAe,CAAC,kBAAkB,eAAe;AACjD,YAAQ,MAAM;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,eAAe,kBAAkB,QAAQ;AAC9C,SAAO,IAAI,OAAQ,gBAAiB,aAAa,gBAAgB,GAAK,SAAS,MAAM,MAAS;AAClG;AACA,SAAS,gBAAgB,KAAK,QAAQ,QAAQ;AAE1C,MAAI,QAAQ,IACR;AAAE,WAAO;AAAA,EAAM;AACnB,SAAQ,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM,IAAI,MAAM,EAAE,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,MAAM,IAAI,MAAM,OAAO,QAAQ,YAAY,CAAC,MAAM,OAAO,GAAG,CAAC;AAC3M;AACA,SAAS,iBAAiB,OAAO,YAAY,OAAO;AAChD,MAAI;AAEJ,MAAK,eAAe,OAAS,cAAa,qBAAqB,KAAK;AACpE,MAAI,gBAAgB,MAAM;AAC1B,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,eAAe,MAAM;AACzB,MAAI,OAAO,WAAW;AACtB,MAAI,KAAK,WAAW;AACpB,MAAI,QAAQ,GAAG;AACf,MAAI,MAAM,GAAG;AACb,MAAI,MAAM,cAAc,KAAK;AAC7B,MAAI,2BAA2B,IAAI;AACnC,MAAI,mBAAmB,IAAI;AAC3B,MAAI,2BAA2B,MAAM,GAAG,MAAM;AAK9C,MAAI,aAAa,KAAK,MACjB,UAAU,UAAU,UAAU,WAC/B,WAAW,cAAc,IAAI;AAC7B,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,UAAU,KAAK,yBAAyB,QAAQ,MAAM,KAAK,CAAC,MAAM,IAAI;AAC5E,QAAI,YAAY,iBAAiB,IAAI,KAAK;AAC1C,YAAQ,MAAM,UAAU,GAAG,KAAK,IAAI,YAAY,MAAM,UAAU,QAAQ,GAAG,MAAM,MAAM;AAAA,EAC3F;AACA,MAAI,gBAAgB,SAAUR,QAAOS,QAAOC,MAAK;AAM7C,QAAIC,eAAc;AAClB,QAAI,oBAAoB;AACxB,QAAI,OAAO,WAAW,GAAG,GAAG;AACxB,MAAAA,eAAc;AAAA,IAClB,WACSX,OAAM,WAAW,IAAI,GAAG;AAC7B,MAAAW,eAAc;AACd,0BAAoB;AAAA,IACxB,WACS,OAAO,WAAW,GAAG,KAAKX,OAAM,WAAW,OAAO,QAAQ;AAC/D,MAAAW,eAAc;AAAA,IAClB,WACSX,OAAM,CAAC,MAAM,KAAK;AACvB,MAAAW,eAAc;AAAA,IAClB;AACA,QAAI,gBAAgBA,eAAc,IAAI;AACtC,QAAI,mBACA;AAAE,sBAAgB;AAAA,IAAG;AAEzB,QAAI,eAAe;AACf,MAAAX,SAAQA,OAAM,UAAU,aAAa;AAErC,MAAAS,UAAS;AACT,MAAAC,QAAO;AAAA,IACX;AACA,WAAO,EAAE,OAAOV,QAAO,OAAOS,QAAO,KAAKC,MAAK,aAAaC,aAAY;AAAA,EAC5E;AACA,MAAI,aAAa,cAAc,OAAO,OAAO,GAAG;AAChD,MAAI,cAAc,WAAW;AAC7B,EAAE,SAAS,YAAY,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,MAAM,OAAO;AAChF,MAAI,QAAQ,cAAc,WAAW,WAAW,KAAK,OAAO,KAAK,GAAG;AACpE,MAAI,YAAY,MAAM;AACtB,MAAI,UAAU,MAAM;AACpB,MAAI,YAAY,MAAM;AAGtB,MAAI,oBAAoB,MAAM,UAAU,OAAO,GAAG;AAClD,MAAI,MAAM,UACN,UAAU,WACT,YAAY,UAAU,SAAS,OAAO,UAAU,UAAU,OAAO,WAClE,EAAE,qBAAqB,OAAO,WAAW,iBAAiB,IAAI;AAC9D,YAAQ;AAAA,EACZ;AAOA,MAAI,aAAa;AACjB,MAAI,MAAM,WAAW,MAAM,GACvB;AAAE,kBAAc,OAAO;AAAA,EAAQ,WAC1B,QAAQ,OAAO,QACpB;AAAE,iBAAa;AAAA,EAAO;AAC1B,UAAQ,MAAM,UAAU,UAAU;AAElC,SAAO;AAOP,MAAI,WAAW,MAAM;AACrB,MAAI,mBAAmB,MAAM,SAAS,OAAO;AAC7C,MAAI,MAAM,SAAS,MAAM,GACrB;AAAE,eAAW;AAAA,EAAkB,WAE1B,MAAM,kBACX;AAAE,eAAW;AAAA,EAAK,WAEb,MAAM,MAAM,SAAS,OAAO,QACjC;AAAE,eAAW;AAAA,EAAK;AACtB,UAAQ,MAAM,UAAU,GAAG,QAAQ;AAEnC,UAAQ,eAAe,cAAe,MAAM,QAAS,OAAO,aAAa;AAEzE,WAAS,MAAM,MAAM,eAAe,kBAAkB,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE;AAE3E,MAAI,aAAa,MAAM,QAAQ,gBAAgB;AAC/C,UAAQ,MAAM,QAAQ,IAAI,OAAO,aAAa,gBAAgB,GAAG,GAAG,GAAG,SAAU,OAAO,OAAO;AAC3F,WAAO,UAAU,aAAa,MAAM;AAAA,EACxC,CAAC;AAGD,MAAI,QAAQ,aAAa,OAAO,aAAa;AAC7C,MAAI,gBAAgB,MAAM;AAC1B,MAAI,eAAe,MAAM;AACzB,MAAI,cAAc,MAAM;AAExB,MAAI,GAAG,MAAM,GAAG,QAAQ,KAAK,MAAM,KAAK,SACpC,kBAAkB,MAClB,4BACA,CAAC,WAAW,YAAY,GAAG;AAC3B,YAAQ,cAAc,MAAM;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,gBAAgB,OAAO;AAC7C,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,cAAc,MAAM,KAAK,EAAE,QAAQ,eAAe,SAAS,EAAE,CAAC,EAAE,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,CAAC;AACpG,MAAI,cAAc,eAAe,CAAC,MAAM;AAExC,cAAY,KAAK,OAAO,GAAG,OAAO,UAAU,cAAc,IAAI,EAAE;AAEhE,MAAI,QAAQ,eAAe;AAC3B,cAAY,KAAK,OAAO,QAAQ,OAAO,SAAS,GAAG,QAAQ,CAAC;AAC5D,SAAO;AACX;AACA,SAAS,uBAAuB,OAAO;AACnC,MAAI,MAAM,cAAc,KAAK;AAC7B,MAAI,oBAAoB,IAAI;AAC5B,MAAI,mBAAmB,IAAI;AAE3B,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,gBAAgB,MAAM;AAAe,MAAK,kBAAkB,OAAS,iBAAgB;AACzF,MAAI,sBAAsB,kBAAkB;AACxC,UAAM,IAAI,MAAO,kGAAkG,oBAAoB,iGAAmG,mBAAmB,mDAAoD;AAAA,EACrT;AACA,MAAI,OAAO,WAAW,GAAG,KAAK,eAAe;AAEzC,YAAQ,MAAO,oFAAoF,SAAS,4BAA4B,gBAAgB,QAAS;AACjK,oBAAgB;AAAA,EACpB;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,cAA6B,CAAC;AACnF;AACA,SAAS,iBAAiB,OAAO;AAE7B,UAAQ,uBAAuB,KAAK;AACpC,MAAI,oBAAoB,MAAM;AAC9B,MAAI,4BAA4B,MAAM;AACtC,MAAI,sBAAsB,MAAM;AAChC,MAAI,SAAS,MAAM;AACnB,MAAI,gBAAgB,MAAM;AAC1B,MAAI,oBAAoB,MAAM;AAC9B,MAAI,YAAY,MAAM;AAAW,MAAK,cAAc,OAAS,aAAY;AACzE,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,oBAAoB,MAAM;AAC9B,MAAI,eAAe,MAAM;AACzB,MAAI,oBAAoB,MAAM;AAC9B,MAAI,SAAS,MAAM;AAAQ,MAAK,WAAW,OAAS,UAAS;AAC7D,MAAI,eAAe,MAAM;AACzB,MAAI,QAAQ,MAAM;AAClB,MAAI,uBAAuB,MAAM;AACjC,MAAI,gBAAgB,MAAM;AAC1B,MAAI,YAAY,OAAO,OAAO,CAAC,oBAAoB,4BAA4B,uBAAuB,UAAU,iBAAiB,qBAAqB,aAAa,UAAU,qBAAqB,gBAAgB,qBAAqB,UAAU,gBAAgB,SAAS,wBAAwB,eAAe,CAAC;AAElT,MAAI,MAAM,cAAc,KAAK;AAC7B,MAAI,mBAAmB,IAAI;AAC3B,MAAI,2BAA2B,IAAI;AACnC,MAAI,UAAU,SAAU,QAAQ;AAAE,WAAO,OAAO,QAAQ,KAAK;AAAA,EAAG;AAChE,MAAI,oBAAoB,SAAU,YAAY,YAAY;AAAE,WAAO,iBAAiB,YAAY,YAAY,KAAK;AAAA,EAAG;AACpH,MAAI,SAAS,MAAM,KAAK,IAAI,eAAe;AAE3C,MAAI,wBAAwB,yBAAyB,QAAQ,yBAAyB,SAAS,uBAAuB,gBAAgB,QAAQ,QAAQ,MAAM;AAC5J,MAAI,CAAC,MAAM,KAAK,GAAG;AACf,4BAAwB,yBAAyB,OAAO,UAAU;AAAA,EACtE,WACS,CAAC,MAAM,YAAY,GAAG;AAC3B,4BAAwB,yBAAyB,OAAO,iBAAiB;AAAA,EAC7E;AACA,MAAI,gCAAgC,SAAUX,QAAO;AACjD,QAAI,gBAAgBA,MAAK,GACrB;AAAE,aAAOA;AAAA,IAAO;AACpB,QAAI,OAAOA,WAAU,UAAU;AAC3B,MAAAA,SAAQ,gBAAgBA,MAAK;AAAA,IACjC;AAKA,QAAI,yBAAyB,OAAO,iBAAiB,UAAU;AAC3D,aAAO,iBAAiBA,QAAO,cAAc,QAAQ,iBAAiB,CAAC;AAAA,IAC3E;AACA,WAAOA;AAAA,EACX;AACA,MAAI,QAAQ,kBAAkB,8BAA8B,KAAK,GAAG,8BAA8B,YAAY,GAAG,QAAQ,qBAAqB,GAAG,SAAS,mBAAmB,aAAa;AAC1L,MAAI,UAAU,MAAM,CAAC;AACrB,MAAI,cAAc,QAAQ;AAC1B,MAAI,iBAAiB,QAAQ;AAC7B,MAAI,iBAAiB,MAAM,CAAC;AAC5B,MAAI,aAAa,SAAU,GAAG;AAC1B,QAAI,KAAK,EAAE;AACX,QAAI,MAAM,EAAE;AACZ,QAAI,iBAAiB,GAAG;AACxB,QAAI,eAAe,GAAG;AACtB,QAAIA,SAAQ,GAAG;AAAO,QAAKA,WAAU,OAAS,CAAAA,SAAQ;AAEtD,SAAK,QAAQ,eAAe,QAAQ,aAAa,eAAe,OAAO,QAAQ;AAC3E,QAAE,eAAe;AACjB;AAAA,IACJ;AAEA,QAAI,mBAAmB,cAAc;AACjC,gBAAU,CAAC;AACX;AAAA,IACJ;AAEA,QAAI,QAAQ,eACRA,OAAM,CAAC,MAAM,OACb,mBAAmB,OAAO,SAAS,KACnC,eAAe;AAEf,uBAAiB,IAAI,CAAC;AAAA,IAC1B;AAEA,QAAI,gBAAgB,mBAAmB;AACnC,UAAI,QAAQ,eAAeA,OAAM,iBAAiB,CAAC,MAAM,kBAAkB;AACvE,yBAAiB,IAAI,iBAAiB,CAAC;AACvC,UAAE,eAAe;AAAA,MACrB,WACS,QAAQ,YAAYA,OAAM,cAAc,MAAM,kBAAkB;AACrE,UAAE,eAAe;AAAA,MACrB;AAAA,IACJ;AAEA,SAAK,6BAA6B,QAAQ,6BAA6B,SAAS,SAAS,yBAAyB,SAAS,GAAG,MAAMA,OAAM,cAAc,MAAM,kBAAkB;AAC5K,uBAAiB,IAAI,iBAAiB,CAAC;AAAA,IAC3C;AACA,QAAI,qBAAqB,sBAAsB,OAAO,MAAM;AAE5D,QAAI,QAAQ,eAAeA,OAAM,iBAAiB,CAAC,MAAM,oBAAoB;AACzE,uBAAiB,IAAI,iBAAiB,CAAC;AAAA,IAC3C;AACA,QAAI,QAAQ,YAAYA,OAAM,cAAc,MAAM,oBAAoB;AAClE,uBAAiB,IAAI,iBAAiB,CAAC;AAAA,IAC3C;AACA,cAAU,CAAC;AAAA,EACf;AACA,MAAI,UAAU,SAAU,GAAG;AACvB,QAAIY,UAAS;AAEb,QAAI,CAACA,QAAO,MAAM,KAAK,GAAG;AACtB,MAAAA,UAAS;AAAA,IACb;AAEA,QAAI,CAAC,mBAAmB;AACpB,MAAAA,UAAS,eAAeA,OAAM;AAAA,IAClC;AAEA,QAAI,qBAAqB,cAAc;AACnC,MAAAA,UAAS,iBAAiBA,SAAQ,cAAc,iBAAiB;AAAA,IACrE;AACA,QAAIA,YAAW,aAAa;AACxB,UAAIR,kBAAiB,OAAOQ,SAAQ,KAAK;AACzC,qBAAe;AAAA,QACX,gBAAgBR;AAAA,QAChB,OAAOQ;AAAA,QACP,YAAY,WAAWA,OAAM;AAAA,MACjC,GAAG;AAAA,QACC,OAAO;AAAA,QACP,QAAQ,WAAW;AAAA,MACvB,CAAC;AAAA,IACL;AACA,WAAO,CAAC;AAAA,EACZ;AACA,MAAI,wBAAwB,SAAU,WAAW;AAC7C,QAAI,cAAc,kBACd;AAAE,aAAO;AAAA,IAAM;AACnB,WAAO,aAAa,SAAS;AAAA,EACjC;AACA,MAAI,kBAAkB,SAAUL,MAAK;AACjC,QAAI,eAAeA,KAAI;AACvB,QAAI,YAAYA,KAAI;AACpB,QAAIH,kBAAiBG,KAAI;AACzB,QAAI,oBAAoBA,KAAI;AAC5B,QAAI,sBAAsBA,KAAI;AAE9B,QAAI,UAAU,aAAa,iBAAiB;AAC5C,QAAI,UAAUH,gBAAe,mBAAmB;AAMhD,QAAI,aAAa,gBAAgB,WAAW,YAAY;AACxD,QAAI,KAAK,WAAW;AAGpB,QAAI,2BAA2B,SAAUJ,QAAO;AAC5C,aAAO,kBAAkBA,MAAK,EAAE,QAAQ,GAAG,IAAI,OAAO;AAAA,IAC1D;AACA,QAAI,UAAU,KACV,qBACA,gBACA,aAAa,GAAG,KAAK,MAAM,oBAC3B,yBAAyB,YAAY,IAAI,qBACzC,yBAAyBI,eAAc,IAAI,qBAAqB;AAChE,aAAO;AAAA,IACX;AACA,QAAI,qBAAqB,GAAG,SACxB,oBAAoB,GAAG,OACvB,4BACA,yBAAyB,SAAS,OAAO,KACzC,YAAY,kBAAkB;AAC9B,aAAO;AAAA,IACX;AACA,WAAO,YAAY;AAAA,EACvB;AACA,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG;AAAA,IAAE,OAAO;AAAA,IAAgB,sBAAsB;AAAA,IAAO;AAAA,IACrG;AAAA,IAAkC,eAAe;AAAA,IAAgB,QAAQ;AAAA,IAAS,kBAAkB;AAAA,IAAmB,kBAAkB,SAAUA,iBAAgB;AAAE,aAAO,iBAAiBA,iBAAgB,KAAK;AAAA,IAAG;AAAA,IAAG,WAAW;AAAA,IAAY,QAAQ;AAAA,EAAQ,CAAC;AACxQ;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,qBAAqB,iBAAiB,KAAK;AAC/C,SAAO,aAAAI,QAAM,cAAc,kBAAkB,OAAO,OAAO,CAAC,GAAG,kBAAkB,CAAC;AACtF;AAEA,SAAS,SAAS,QAAQ,OAAO;AAC7B,MAAIV,UAAS,MAAM;AACnB,MAAI,uBAAuB,MAAM;AACjC,MAAI,OAAO,MAAM;AACjB,MAAI,cAAc,MAAM;AAAa,MAAK,gBAAgB,OAAS,eAAc;AACjF,MAAI,WAAW,MAAM,CAAC,sBAClB;AAAE,WAAO;AAAA,EAAI;AACjB,MAAI,YAAY;AAChB,MAAI,qBAAqBA,QAAO,MAAM,EAAE;AACxC,WAAS,IAAI,GAAG,KAAKA,QAAO,QAAQ,IAAI,IAAI,KAAK;AAC7C,QAAIA,QAAO,CAAC,MAAM,aAAa;AAC3B,yBAAmB,CAAC,IAAI,OAAO,SAAS,KAAK,eAAe,MAAM,SAAS;AAC3E,mBAAa;AAAA,IACjB;AAAA,EACJ;AACA,SAAO,mBAAmB,KAAK,EAAE;AACrC;AACA,SAAS,mBAAmB,OAAO,YAAY,OAAO;AAClD,MAAK,eAAe,OAAS,cAAa,qBAAqB,KAAK;AAEpE,MAAIA,UAAS,MAAM;AACnB,MAAI,cAAc,MAAM;AAAa,MAAK,gBAAgB,OAAS,eAAc;AACjF,MAAI,OAAO,WAAW;AACtB,MAAI,KAAK,WAAW;AACpB,MAAI,YAAY,WAAW;AAAW,MAAK,cAAc,OAAS,aAAY;AAC9E,MAAI,gBAAgB,SAAU,UAAU;AAAE,WAAOA,QAAO,QAAQ,MAAM;AAAA,EAAa;AACnF,MAAI,mBAAmB,SAAU,QAAQ,YAAY;AACjD,QAAIe,OAAM;AACV,aAASC,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACpC,UAAI,cAAc,aAAaA,EAAC,KAAK,aAAa,OAAOA,EAAC,CAAC,GAAG;AAC1D,QAAAD,QAAO,OAAOC,EAAC;AAAA,MACnB;AAAA,IACJ;AACA,WAAOD;AAAA,EACX;AACA,MAAI,iBAAiB,SAAUA,MAAK;AAAE,WAAOA,KAAI,QAAQ,WAAW,EAAE;AAAA,EAAG;AAEzE,MAAI,CAACf,QAAO,MAAM,IAAI,GAAG;AACrB,WAAO,eAAe,KAAK;AAAA,EAC/B;AAKA,OAAK,cAAc,MAAM,KAAK,MAAM,KAAK,UAAU,UAAU,WACzD,MAAM,WAAWA,QAAO,QAAQ;AAChC,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,cAAc,CAAC,GAAG;AAClB,YAAI,aAAa,MAAM,CAAC,CAAC,GAAG;AACxB,iBAAO,MAAM,CAAC;AAAA,QAClB;AAAA,MACJ,WACS,MAAM,CAAC,MAAMA,QAAO,CAAC,GAAG;AAE7B,eAAO,eAAe,KAAK;AAAA,MAC/B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAeA,MAAI,eAAe,UAAU,UAAU,GAAG,KAAK,KAAK;AACpD,MAAI,gBAAgB,MAAM,UAAU,GAAG,OAAO,GAAG,GAAG;AACpD,MAAI,cAAc,UAAU,UAAU,KAAK,GAAG;AAC9C,SAAQ,KAAM,iBAAiB,cAAc,CAAC,IAAM,eAAe,aAAa,IAAM,iBAAiB,aAAa,KAAK,GAAG;AAChI;AACA,SAAS,mBAAmB,gBAAgB,OAAO;AAC/C,MAAIA,UAAS,MAAM;AACnB,MAAI,OAAO,MAAM;AACjB,MAAI,cAAc,MAAM;AAAa,MAAK,gBAAgB,OAAS,eAAc;AACjF,MAAI,cAAc,MAAM,KAAK,EAAE,QAAQ,eAAe,SAAS,EAAE,CAAC,EAAE,IAAI,WAAY;AAAE,WAAO;AAAA,EAAM,CAAC;AACpG,MAAI,YAAY;AAChB,MAAI,iBAAiB;AACrB,MAAI,kBAAkB,CAAC;AACvB,EAAAA,QAAO,MAAM,EAAE,EAAE,QAAQ,SAAU,MAAM,OAAO;AAC5C,QAAI,cAAc;AAClB,QAAI,SAAS,aAAa;AACtB;AACA,oBAAc,eAAe,MAAM,YAAY,CAAC;AAChD,UAAI,mBAAmB,MAAM,eAAe,KAAK,MAAM,aAAa;AAChE,yBAAiB;AAAA,MACrB;AAAA,IACJ;AACA,oBAAgB,KAAK,IAAI;AAAA,EAC7B,CAAC;AACD,MAAI,eAAe,SAAU,KAAK;AAE9B,WAAOA,QAAO,GAAG,MAAM,eAAe,eAAe,GAAG,MAAM,gBAAgB,GAAG;AAAA,EACrF;AACA,WAAS,IAAI,GAAG,KAAK,YAAY,QAAQ,IAAI,IAAI,KAAK;AAIlD,gBAAY,CAAC,IAAI,MAAM,kBAAkB,aAAa,CAAC,KAAK,aAAa,IAAI,CAAC;AAAA,EAClF;AAEA,cAAYA,QAAO,QAAQ,WAAW,CAAC,IAAI;AAC3C,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,OAAO,MAAM;AACjB,MAAI,MAAM;AACN,QAAI,YAAY,SAAS,WAAW,OAAO,KAAK,SAAS;AACzD,QAAI,UAAU,MAAM,KAAK,GAAG;AACxB,YAAM,IAAI,MAAO,UAAU,OAAO,wCAAyC;AAAA,IAC/E;AAAA,EACJ;AACJ;AACA,SAAS,kBAAkB,KAAKA,SAAQ;AAEpC,MAAI,QAAQ,IACR;AAAE,WAAO;AAAA,EAAM;AACnB,SAAO,EAAEA,YAAW,QAAQA,YAAW,SAAS,SAASA,QAAO,MAAM,IAAI,MAAM,OAAO,QAAQ,aAAa,CAAC,CAAC,IAAI,MAAM,OAAO,KAAK,QAAQ;AAChJ;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,MAAM;AACjB,MAAI,uBAAuB,MAAM;AACjC,MAAI,aAAa,MAAM;AACvB,MAAI,YAAY,MAAM;AAAW,MAAK,cAAc,OAAS,aAAY;AACzE,MAAI,YAAY,MAAM;AAAW,MAAK,cAAc,OAAS,aAAY;AACzE,MAAI,cAAc,MAAM;AAAa,MAAK,gBAAgB,OAAS,eAAc;AACjF,MAAI,QAAQ,MAAM;AAClB,MAAI,eAAe,MAAM;AACzB,MAAI,uBAAuB,MAAM;AACjC,MAAI,YAAY,OAAO,OAAO,CAAC,QAAQ,wBAAwB,UAAU,aAAa,aAAa,eAAe,SAAS,gBAAgB,sBAAsB,CAAC;AAElK,gBAAc,KAAK;AACnB,MAAI,oBAAoB,SAAU,gBAAgB;AAC9C,WAAO,mBAAmB,gBAAgB,KAAK;AAAA,EACnD;AACA,MAAI,aAAa,SAAU,GAAG;AAC1B,QAAI,MAAM,EAAE;AACZ,QAAI,KAAK,EAAE;AACX,QAAI,iBAAiB,GAAG;AACxB,QAAI,eAAe,GAAG;AACtB,QAAIE,SAAQ,GAAG;AAEf,QAAI,mBAAmB,cAAc;AACjC,gBAAU,CAAC;AACX;AAAA,IACJ;AAEA,QAAI,WAAW;AAGf,QAAI,QAAQ,eAAe,QAAQ,UAAU;AACzC,UAAI,YAAY;AAChB,UAAI,QAAQ,aAAa;AACrB,eAAO,WAAW,KAAK,WAAW,WAAW,CAAC,MAAM,aAAa;AAC7D;AAAA,QACJ;AACA,oBAAY;AAAA,MAChB,OACK;AACD,YAAI,WAAW,WAAW;AAC1B,eAAO,WAAW,YAAY,WAAW,QAAQ,MAAM,aAAa;AAChE;AAAA,QACJ;AACA,oBAAY;AAAA,MAChB;AACA,iBAAW,sBAAsBA,QAAO,UAAU,kBAAkBA,MAAK,GAAG,SAAS;AAAA,IACzF,WACS,WAAW,QAAQ,MAAM,eAC9B,QAAQ,eACR,QAAQ,cAAc;AAEtB,iBAAW,sBAAsBA,QAAO,WAAW,GAAG,kBAAkBA,MAAK,GAAG,OAAO;AAAA,IAC3F;AAEA,QAAI,aAAa,gBAAgB;AAC7B,uBAAiB,IAAI,QAAQ;AAAA,IACjC;AACA,cAAU,CAAC;AAAA,EACf;AAEA,MAAI,SAAS,MAAM,KAAK,IAAI,eAAe;AAC3C,MAAI,uBAAuB,yBAAyB,QAAQ,yBAAyB,SAAS,uBAAuB,kBAAkB,QAAQ,UAAU;AACzJ,MAAI,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,GAAG,EAAE,sBAAsB,qBAAqB,CAAC;AACnG,SAAO,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,SAAS,GAAG;AAAA,IAAE;AAAA,IACjD;AAAA,IAA4B,sBAAsB;AAAA,IAAsB;AAAA,IAAsB,QAAQ,SAAU,QAAQ;AAAE,aAAO,SAAS,QAAQ,MAAM;AAAA,IAAG;AAAA,IAAG,kBAAkB,SAAU,YAAY,YAAY;AAAE,aAAO,mBAAmB,YAAY,YAAY,MAAM;AAAA,IAAG;AAAA,IAAG,kBAAkB;AAAA,IAAmB,WAAW;AAAA,EAAW,CAAC;AACtV;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,qBAAqB,iBAAiB,KAAK;AAC/C,SAAO,aAAAQ,QAAM,cAAc,kBAAkB,OAAO,OAAO,CAAC,GAAG,kBAAkB,CAAC;AACtF;", "names": ["SourceType", "format", "removeFormatting", "value", "valueIsNumericString", "newValues", "getCaretBoundary", "formattedValue", "numAsString", "currentCaretPosition", "ref", "React", "start", "end", "hasNegation", "_value", "str", "i"]}