{"version": 3, "sources": ["../../@mui/icons-material/esm/LocalBar.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M21 5V3H3v2l8 9v5H6v2h12v-2h-5v-5zM7.43 7 5.66 5h12.69l-1.78 2z\"\n}), 'LocalBar');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,mBAAQ,kBAA2B,mBAAAA,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,UAAU;", "names": ["_jsx"]}