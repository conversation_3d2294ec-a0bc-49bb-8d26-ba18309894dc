import { Model } from 'mongoose';
import { Hotel } from 'src/infrastructure/database/hotel.schema';
import { UpdateHotelDto } from 'src/common/dtos/update-hotel.dto';
import { CreateHotelDto } from 'src/common/dtos/create-hotel.dto';
export declare class HotelService {
    private readonly hotelModel;
    constructor(hotelModel: Model<Hotel>);
    getHotelList(): Promise<(import("mongoose").Document<unknown, {}, Hotel, {}> & Hotel & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    getHotelDetail(id: string): Promise<Hotel>;
    addHotel(createHotelDto: CreateHotelDto): Promise<Hotel>;
    deleteHotel(id: string): Promise<Hotel>;
    updateHotel(id: string, updateHotelDto: UpdateHotelDto): Promise<Hotel>;
    addListOfHotels(hotels: Partial<Hotel>[]): Promise<void>;
}
