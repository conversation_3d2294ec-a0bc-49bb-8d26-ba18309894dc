{"version": 3, "sources": ["../../@mui/x-charts/esm/BarChart/BarChart.js", "../../@mui/x-charts/esm/BarChart/BarPlot.js", "../../@mui/x-charts/esm/BarChart/barElementClasses.js", "../../@mui/x-charts/esm/BarChart/BarElement.js", "../../@mui/x-charts/esm/BarChart/AnimatedBarElement.js", "../../@mui/x-charts/esm/BarChart/BarClipPath.js", "../../@mui/x-charts/esm/BarChart/getRadius.js", "../../@mui/x-charts/esm/BarChart/BarLabel/BarLabelPlot.js", "../../@mui/x-charts/esm/BarChart/BarLabel/BarLabelItem.js", "../../@mui/x-charts/esm/BarChart/BarLabel/barLabelClasses.js", "../../@mui/x-charts/esm/BarChart/BarLabel/getBarLabel.js", "../../@mui/x-charts/esm/BarChart/BarLabel/BarLabel.js", "../../@mui/x-charts/esm/BarChart/barClasses.js", "../../@mui/x-charts/esm/internals/plugins/featurePlugins/useChartCartesianAxis/useInternalIsZoomInteracting.js", "../../@mui/x-charts/esm/BarChart/checkScaleErrors.js", "../../@mui/x-charts/esm/BarChart/useBarPlotData.js", "../../@mui/x-charts/esm/ChartsAxis/ChartsAxis.js", "../../@mui/x-charts/esm/ChartsXAxis/ChartsXAxis.js", "../../@mui/x-charts/esm/hooks/useIsHydrated.js", "../../@mui/x-charts/esm/internals/domUtils.js", "../../@mui/x-charts/esm/hooks/useTicks.js", "../../@mui/x-charts/esm/internals/isInfinity.js", "../../@mui/x-charts/esm/ChartsAxis/axisClasses.js", "../../@mui/x-charts/esm/internals/components/AxisSharedComponents.js", "../../@mui/x-charts/esm/ChartsText/ChartsText.js", "../../@mui/x-charts/esm/internals/getWordsByLines.js", "../../@mui/x-charts/esm/hooks/useMounted.js", "../../@mui/x-charts/esm/ChartsText/defaultTextPlacement.js", "../../@mui/x-charts/esm/internals/invertTextAnchor.js", "../../@mui/x-charts/esm/internals/getGraphemeCount.js", "../../@mui/x-charts/esm/internals/degToRad.js", "../../@mui/x-charts/esm/internals/sliceUntil.js", "../../@mui/x-charts/esm/internals/ellipsize.js", "../../@mui/x-charts/esm/ChartsXAxis/shortenLabels.js", "../../@mui/x-charts/esm/internals/geometry.js", "../../@mui/x-charts/esm/ChartsXAxis/getVisibleLabels.js", "../../@mui/x-charts/esm/ChartsYAxis/ChartsYAxis.js", "../../@mui/x-charts/esm/ChartsYAxis/shortenLabels.js", "../../@mui/x-charts/esm/ChartsAxisHighlight/ChartsAxisHighlight.js", "../../@mui/x-charts/esm/ChartsAxisHighlight/chartsAxisHighlightClasses.js", "../../@mui/x-charts/esm/ChartsAxisHighlight/ChartsYAxisHighlight.js", "../../@mui/x-charts/esm/ChartsAxisHighlight/ChartsAxisHighlightPath.js", "../../@mui/x-charts/esm/ChartsAxisHighlight/ChartsXAxisHighlight.js", "../../@mui/x-charts/esm/ChartsClipPath/ChartsClipPath.js", "../../@mui/x-charts/esm/ChartsGrid/ChartsGrid.js", "../../@mui/x-charts/esm/ChartsGrid/chartsGridClasses.js", "../../@mui/x-charts/esm/ChartsGrid/styledComponents.js", "../../@mui/x-charts/esm/ChartsGrid/ChartsVerticalGrid.js", "../../@mui/x-charts/esm/ChartsGrid/ChartsHorizontalGrid.js", "../../@mui/x-charts/esm/BarChart/useBarChartProps.js", "../../@mui/x-charts/esm/BarChart/BarChart.plugins.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '@mui/material/styles';\nimport { BarPlot } from \"./BarPlot.js\";\nimport { ChartsAxis } from \"../ChartsAxis/index.js\";\nimport { ChartsTooltip } from \"../ChartsTooltip/index.js\";\nimport { ChartsLegend } from \"../ChartsLegend/index.js\";\nimport { ChartsAxisHighlight } from \"../ChartsAxisHighlight/index.js\";\nimport { ChartsClipPath } from \"../ChartsClipPath/index.js\";\nimport { ChartsGrid } from \"../ChartsGrid/index.js\";\nimport { ChartsOverlay } from \"../ChartsOverlay/ChartsOverlay.js\";\nimport { useBarChartProps } from \"./useBarChartProps.js\";\nimport { ChartDataProvider } from \"../ChartDataProvider/index.js\";\nimport { ChartsSurface } from \"../ChartsSurface/index.js\";\nimport { useChartContainerProps } from \"../ChartContainer/useChartContainerProps.js\";\nimport { ChartsWrapper } from \"../internals/components/ChartsWrapper/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Bars](https://mui.com/x/react-charts/bars/)\n * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)\n * - [Stacking](https://mui.com/x/react-charts/stacking/)\n *\n * API:\n *\n * - [BarChart API](https://mui.com/x/api/charts/bar-chart/)\n */\nconst BarChart = /*#__PURE__*/React.forwardRef(function BarChart(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiBarChart'\n  });\n  const {\n    chartsWrapperProps,\n    chartContainerProps,\n    barPlotProps,\n    gridProps,\n    clipPathProps,\n    clipPathGroupProps,\n    overlayProps,\n    chartsAxisProps,\n    axisHighlightProps,\n    legendProps,\n    children\n  } = useBarChartProps(props);\n  const {\n    chartDataProviderProps,\n    chartsSurfaceProps\n  } = useChartContainerProps(chartContainerProps, ref);\n  const Tooltip = props.slots?.tooltip ?? ChartsTooltip;\n  const Toolbar = props.slots?.toolbar;\n  return /*#__PURE__*/_jsx(ChartDataProvider, _extends({}, chartDataProviderProps, {\n    children: /*#__PURE__*/_jsxs(ChartsWrapper, _extends({}, chartsWrapperProps, {\n      children: [props.showToolbar && Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, props.slotProps?.toolbar)) : null, !props.hideLegend && /*#__PURE__*/_jsx(ChartsLegend, _extends({}, legendProps)), /*#__PURE__*/_jsxs(ChartsSurface, _extends({}, chartsSurfaceProps, {\n        children: [/*#__PURE__*/_jsx(ChartsGrid, _extends({}, gridProps)), /*#__PURE__*/_jsxs(\"g\", _extends({}, clipPathGroupProps, {\n          children: [/*#__PURE__*/_jsx(BarPlot, _extends({}, barPlotProps)), /*#__PURE__*/_jsx(ChartsOverlay, _extends({}, overlayProps)), /*#__PURE__*/_jsx(ChartsAxisHighlight, _extends({}, axisHighlightProps))]\n        })), /*#__PURE__*/_jsx(ChartsAxis, _extends({}, chartsAxisProps)), /*#__PURE__*/_jsx(ChartsClipPath, _extends({}, clipPathProps)), children]\n      })), !props.loading && /*#__PURE__*/_jsx(Tooltip, _extends({}, props.slotProps?.tooltip))]\n    }))\n  }));\n});\nif (process.env.NODE_ENV !== \"production\") BarChart.displayName = \"BarChart\";\nprocess.env.NODE_ENV !== \"production\" ? BarChart.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  apiRef: PropTypes.shape({\n    current: PropTypes.object\n  }),\n  /**\n   * The configuration of axes highlight.\n   * Default is set to 'band' in the bar direction.\n   * Depends on `layout` prop.\n   * @see See {@link https://mui.com/x/react-charts/highlighting/ highlighting docs} for more details.\n   */\n  axisHighlight: PropTypes.shape({\n    x: PropTypes.oneOf(['band', 'line', 'none']),\n    y: PropTypes.oneOf(['band', 'line', 'none'])\n  }),\n  /**\n   * If provided, the function will be used to format the label of the bar.\n   * It can be set to 'value' to display the current value.\n   * @param {BarItem} item The item to format.\n   * @param {BarLabelContext} context data about the bar.\n   * @returns {string} The formatted label.\n   */\n  barLabel: PropTypes.oneOfType([PropTypes.oneOf(['value']), PropTypes.func]),\n  /**\n   * Defines the border radius of the bar element.\n   */\n  borderRadius: PropTypes.number,\n  children: PropTypes.node,\n  className: PropTypes.string,\n  /**\n   * Color palette used to colorize multiple series.\n   * @default rainbowSurgePalette\n   */\n  colors: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.func]),\n  /**\n   * An array of objects that can be used to populate series and axes data using their `dataKey` property.\n   */\n  dataset: PropTypes.arrayOf(PropTypes.object),\n  desc: PropTypes.string,\n  /**\n   * If `true`, the charts will not listen to the mouse move event.\n   * It might break interactive features, but will improve performance.\n   * @default false\n   */\n  disableAxisListener: PropTypes.bool,\n  /**\n   * Option to display a cartesian grid in the background.\n   */\n  grid: PropTypes.shape({\n    horizontal: PropTypes.bool,\n    vertical: PropTypes.bool\n  }),\n  /**\n   * The height of the chart in px. If not defined, it takes the height of the parent element.\n   */\n  height: PropTypes.number,\n  /**\n   * If `true`, the legend is not rendered.\n   */\n  hideLegend: PropTypes.bool,\n  /**\n   * The controlled axis highlight.\n   * Identified by the axis id, and data index.\n   */\n  highlightedAxis: PropTypes.arrayOf(PropTypes.shape({\n    axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n    dataIndex: PropTypes.number.isRequired\n  })),\n  /**\n   * The highlighted item.\n   * Used when the highlight is controlled.\n   */\n  highlightedItem: PropTypes.shape({\n    dataIndex: PropTypes.number,\n    seriesId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n  }),\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The direction of the bar elements.\n   * @default 'vertical'\n   */\n  layout: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * If `true`, a loading overlay is displayed.\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Localized text for chart components.\n   */\n  localeText: PropTypes.object,\n  /**\n   * The margin between the SVG and the drawing area.\n   * It's used for leaving some space for extra information such as the x- and y-axis or legend.\n   *\n   * Accepts a `number` to be used on all sides or an object with the optional properties: `top`, `bottom`, `left`, and `right`.\n   */\n  margin: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  })]),\n  /**\n   * The function called for onClick events.\n   * The second argument contains information about all line/bar elements at the current mouse position.\n   * @param {MouseEvent} event The mouse event recorded on the `<svg/>` element.\n   * @param {null | ChartsAxisData} data The data about the clicked axis and items associated with it.\n   */\n  onAxisClick: PropTypes.func,\n  /**\n   * The callback fired when the highlighted item changes.\n   *\n   * @param {HighlightItemData | null} highlightedItem  The newly highlighted item.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * The function called when the pointer position corresponds to a new axis data item.\n   * This update can either be caused by a pointer movement, or an axis update.\n   * In case of multiple axes, the function is called if at least one axis is updated.\n   * The argument contains the identifier for all axes with a `data` property.\n   * @param {AxisItemIdentifier[]} axisItems The array of axes item identifiers.\n   */\n  onHighlightedAxisChange: PropTypes.func,\n  /**\n   * Callback fired when a bar item is clicked.\n   * @param {React.MouseEvent<SVGElement, MouseEvent>} event The event source of the callback.\n   * @param {BarItemIdentifier} barItemIdentifier The bar item identifier.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * The series to display in the bar chart.\n   * An array of [[BarSeries]] objects.\n   */\n  series: PropTypes.arrayOf(PropTypes.object).isRequired,\n  /**\n   * If true, shows the default chart toolbar.\n   * @default false\n   */\n  showToolbar: PropTypes.bool,\n  /**\n   * If `true`, animations are skipped.\n   * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  theme: PropTypes.oneOf(['dark', 'light']),\n  title: PropTypes.string,\n  /**\n   * The width of the chart in px. If not defined, it takes the width of the parent element.\n   */\n  width: PropTypes.number,\n  /**\n   * The configuration of the x-axes.\n   * If not provided, a default axis config is used.\n   * An array of [[AxisConfig]] objects.\n   */\n  xAxis: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    barGapRatio: PropTypes.number,\n    categoryGapRatio: PropTypes.number,\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      type: PropTypes.oneOf(['ordinal']).isRequired,\n      unknownColor: PropTypes.string,\n      values: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired)\n    }), PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      type: PropTypes.oneOf(['ordinal']).isRequired,\n      unknownColor: PropTypes.string,\n      values: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired)\n    }), PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['point']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['log']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['pow']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['sqrt']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['time']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['x']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    height: PropTypes.number,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['bottom', 'none', 'top']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['linear']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelMinGap: PropTypes.number,\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func\n  })]).isRequired),\n  /**\n   * The configuration of the y-axes.\n   * If not provided, a default axis config is used.\n   * An array of [[AxisConfig]] objects.\n   */\n  yAxis: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    barGapRatio: PropTypes.number,\n    categoryGapRatio: PropTypes.number,\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      type: PropTypes.oneOf(['ordinal']).isRequired,\n      unknownColor: PropTypes.string,\n      values: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired)\n    }), PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['band']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      type: PropTypes.oneOf(['ordinal']).isRequired,\n      unknownColor: PropTypes.string,\n      values: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]).isRequired)\n    }), PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['point']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['log']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['pow']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['sqrt']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['time']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['utc']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  }), PropTypes.shape({\n    axis: PropTypes.oneOf(['y']),\n    classes: PropTypes.object,\n    colorMap: PropTypes.oneOfType([PropTypes.shape({\n      color: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string.isRequired), PropTypes.func]).isRequired,\n      max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n      type: PropTypes.oneOf(['continuous']).isRequired\n    }), PropTypes.shape({\n      colors: PropTypes.arrayOf(PropTypes.string).isRequired,\n      thresholds: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]).isRequired).isRequired,\n      type: PropTypes.oneOf(['piecewise']).isRequired\n    })]),\n    data: PropTypes.array,\n    dataKey: PropTypes.string,\n    disableLine: PropTypes.bool,\n    disableTicks: PropTypes.bool,\n    domainLimit: PropTypes.oneOfType([PropTypes.oneOf(['nice', 'strict']), PropTypes.func]),\n    fill: PropTypes.string,\n    hideTooltip: PropTypes.bool,\n    id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n    ignoreTooltip: PropTypes.bool,\n    label: PropTypes.string,\n    labelStyle: PropTypes.object,\n    max: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    min: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number]),\n    offset: PropTypes.number,\n    position: PropTypes.oneOf(['left', 'none', 'right']),\n    reverse: PropTypes.bool,\n    scaleType: PropTypes.oneOf(['linear']),\n    slotProps: PropTypes.object,\n    slots: PropTypes.object,\n    stroke: PropTypes.string,\n    sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n    tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n    tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n    tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n    tickLabelStyle: PropTypes.object,\n    tickMaxStep: PropTypes.number,\n    tickMinStep: PropTypes.number,\n    tickNumber: PropTypes.number,\n    tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n    tickSize: PropTypes.number,\n    valueFormatter: PropTypes.func,\n    width: PropTypes.number\n  })]).isRequired)\n} : void 0;\nexport { BarChart };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"skipAnimation\", \"onItemClick\", \"borderRadius\", \"barLabel\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { styled } from '@mui/material/styles';\nimport { barElementClasses } from \"./barElementClasses.js\";\nimport { BarElement } from \"./BarElement.js\";\nimport { useDrawingArea, useXAxes, useYAxes } from \"../hooks/index.js\";\nimport { BarClipPath } from \"./BarClipPath.js\";\nimport { BarLabelPlot } from \"./BarLabel/BarLabelPlot.js\";\nimport { useSkipAnimation } from \"../hooks/useSkipAnimation.js\";\nimport { useInternalIsZoomInteracting } from \"../internals/plugins/featurePlugins/useChartCartesianAxis/useInternalIsZoomInteracting.js\";\nimport { useBarPlotData } from \"./useBarPlotData.js\";\nimport { useUtilityClasses } from \"./barClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst BarPlotRoot = styled('g', {\n  name: 'MuiBarPlot',\n  slot: 'Root'\n})({\n  [`& .${barElementClasses.root}`]: {\n    transition: 'opacity 0.2s ease-in, fill 0.2s ease-in'\n  }\n});\n\n/**\n * Demos:\n *\n * - [Bars](https://mui.com/x/react-charts/bars/)\n * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)\n * - [Stacking](https://mui.com/x/react-charts/stacking/)\n *\n * API:\n *\n * - [BarPlot API](https://mui.com/x/api/charts/bar-plot/)\n */\nfunction BarPlot(props) {\n  const {\n      skipAnimation: inSkipAnimation,\n      onItemClick,\n      borderRadius,\n      barLabel\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const isZoomInteracting = useInternalIsZoomInteracting();\n  const skipAnimation = useSkipAnimation(isZoomInteracting || inSkipAnimation);\n  const {\n    xAxis: xAxes\n  } = useXAxes();\n  const {\n    yAxis: yAxes\n  } = useYAxes();\n  const {\n    completedData,\n    masksData\n  } = useBarPlotData(useDrawingArea(), xAxes, yAxes);\n  const withoutBorderRadius = !borderRadius || borderRadius <= 0;\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsxs(BarPlotRoot, {\n    className: classes.root,\n    children: [!withoutBorderRadius && masksData.map(({\n      id,\n      x,\n      y,\n      width,\n      height,\n      hasPositive,\n      hasNegative,\n      layout\n    }) => {\n      return /*#__PURE__*/_jsx(BarClipPath, {\n        maskId: id,\n        borderRadius: borderRadius,\n        hasNegative: hasNegative,\n        hasPositive: hasPositive,\n        layout: layout,\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        skipAnimation: skipAnimation ?? false\n      }, id);\n    }), completedData.map(({\n      seriesId,\n      data\n    }) => {\n      return /*#__PURE__*/_jsx(\"g\", {\n        \"data-series\": seriesId,\n        className: classes.series,\n        children: data.map(({\n          dataIndex,\n          color,\n          maskId,\n          layout,\n          x,\n          xOrigin,\n          y,\n          yOrigin,\n          width,\n          height\n        }) => {\n          const barElement = /*#__PURE__*/_jsx(BarElement, _extends({\n            id: seriesId,\n            dataIndex: dataIndex,\n            color: color,\n            skipAnimation: skipAnimation ?? false,\n            layout: layout ?? 'vertical',\n            x: x,\n            xOrigin: xOrigin,\n            y: y,\n            yOrigin: yOrigin,\n            width: width,\n            height: height\n          }, other, {\n            onClick: onItemClick && (event => {\n              onItemClick(event, {\n                type: 'bar',\n                seriesId,\n                dataIndex\n              });\n            })\n          }), dataIndex);\n          if (withoutBorderRadius) {\n            return barElement;\n          }\n          return /*#__PURE__*/_jsx(\"g\", {\n            clipPath: `url(#${maskId})`,\n            children: barElement\n          }, dataIndex);\n        })\n      }, seriesId);\n    }), barLabel && /*#__PURE__*/_jsx(BarLabelPlot, _extends({\n      bars: completedData,\n      skipAnimation: skipAnimation,\n      barLabel: barLabel\n    }, other))]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? BarPlot.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If provided, the function will be used to format the label of the bar.\n   * It can be set to 'value' to display the current value.\n   * @param {BarItem} item The item to format.\n   * @param {BarLabelContext} context data about the bar.\n   * @returns {string} The formatted label.\n   */\n  barLabel: PropTypes.oneOfType([PropTypes.oneOf(['value']), PropTypes.func]),\n  /**\n   * Defines the border radius of the bar element.\n   */\n  borderRadius: PropTypes.number,\n  /**\n   * Callback fired when a bar item is clicked.\n   * @param {React.MouseEvent<SVGElement, MouseEvent>} event The event source of the callback.\n   * @param {BarItemIdentifier} barItemIdentifier The bar item identifier.\n   */\n  onItemClick: PropTypes.func,\n  /**\n   * If `true`, animations are skipped.\n   * @default undefined\n   */\n  skipAnimation: PropTypes.bool,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { BarPlot };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getBarElementUtilityClass(slot) {\n  return generateUtilityClass('MuiBarElement', slot);\n}\nexport const barElementClasses = generateUtilityClasses('MuiBarElement', ['root', 'highlighted', 'faded', 'series']);\nexport const useUtilityClasses = ownerState => {\n  const {\n    classes,\n    id,\n    isHighlighted,\n    isFaded\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${id}`, isHighlighted && 'highlighted', isFaded && 'faded']\n  };\n  return composeClasses(slots, getBarElementUtilityClass, classes);\n};", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"id\", \"dataIndex\", \"classes\", \"color\", \"slots\", \"slotProps\", \"style\", \"onClick\", \"skipAnimation\", \"layout\", \"x\", \"xOrigin\", \"y\", \"yOrigin\", \"width\", \"height\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport { useUtilityClasses } from \"./barElementClasses.js\";\nimport { useInteractionItemProps } from \"../hooks/useInteractionItemProps.js\";\nimport { useItemHighlighted } from \"../hooks/useItemHighlighted.js\";\nimport { AnimatedBarElement } from \"./AnimatedBarElement.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction BarElement(props) {\n  const {\n      id,\n      dataIndex,\n      classes: innerClasses,\n      color,\n      slots,\n      slotProps,\n      style,\n      onClick,\n      skipAnimation,\n      layout,\n      x,\n      xOrigin,\n      y,\n      yOrigin,\n      width,\n      height\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const interactionProps = useInteractionItemProps({\n    type: 'bar',\n    seriesId: id,\n    dataIndex\n  });\n  const {\n    isFaded,\n    isHighlighted\n  } = useItemHighlighted({\n    seriesId: id,\n    dataIndex\n  });\n  const ownerState = {\n    id,\n    dataIndex,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted\n  };\n  const classes = useUtilityClasses(ownerState);\n  const Bar = slots?.bar ?? AnimatedBarElement;\n  const barProps = useSlotProps({\n    elementType: Bar,\n    externalSlotProps: slotProps?.bar,\n    externalForwardedProps: other,\n    additionalProps: _extends({}, interactionProps, {\n      id,\n      dataIndex,\n      color,\n      x,\n      xOrigin,\n      y,\n      yOrigin,\n      width,\n      height,\n      style,\n      onClick,\n      cursor: onClick ? 'pointer' : 'unset',\n      stroke: 'none',\n      fill: color,\n      skipAnimation,\n      layout\n    }),\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Bar, _extends({}, barProps));\n}\nprocess.env.NODE_ENV !== \"production\" ? BarElement.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  dataIndex: PropTypes.number.isRequired,\n  id: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  layout: PropTypes.oneOf(['horizontal', 'vertical']).isRequired,\n  skipAnimation: PropTypes.bool.isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  xOrigin: PropTypes.number.isRequired,\n  yOrigin: PropTypes.number.isRequired\n} : void 0;\nexport { BarElement };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"ownerState\", \"skipAnimation\", \"id\", \"dataIndex\", \"xOrigin\", \"yOrigin\"];\nimport * as React from 'react';\nimport { useAnimateBar } from \"../hooks/animation/useAnimateBar.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function AnimatedBarElement(props) {\n  const {\n      ownerState\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const animatedProps = useAnimateBar(props);\n  return /*#__PURE__*/_jsx(\"rect\", _extends({}, other, {\n    filter: ownerState.isHighlighted ? 'brightness(120%)' : undefined,\n    opacity: ownerState.isFaded ? 0.3 : 1,\n    \"data-highlighted\": ownerState.isHighlighted || undefined,\n    \"data-faded\": ownerState.isFaded || undefined\n  }, animatedProps));\n}", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"maskId\", \"x\", \"y\", \"width\", \"height\", \"skipAnimation\"];\nimport * as React from 'react';\nimport { interpolateNumber } from '@mui/x-charts-vendor/d3-interpolate';\nimport { useAnimate } from \"../hooks/animation/index.js\";\nimport { getRadius } from \"./getRadius.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction buildClipPath(size, borderRadius, ownerState) {\n  const radiusData = _extends({}, ownerState, {\n    borderRadius\n  });\n  const topLeft = Math.min(size, getRadius('top-left', radiusData));\n  const topRight = Math.min(size, getRadius('top-right', radiusData));\n  const bottomRight = Math.min(size, getRadius('bottom-right', radiusData));\n  const bottomLeft = Math.min(size, getRadius('bottom-left', radiusData));\n  return `inset(0px round ${topLeft}px ${topRight}px ${bottomRight}px ${bottomLeft}px)`;\n}\nfunction barClipRectPropsInterpolator(from, to) {\n  const interpolateX = interpolateNumber(from.x, to.x);\n  const interpolateY = interpolateNumber(from.y, to.y);\n  const interpolateWidth = interpolateNumber(from.width, to.width);\n  const interpolateHeight = interpolateNumber(from.height, to.height);\n  const interpolateBorderRadius = interpolateNumber(from.borderRadius, to.borderRadius);\n  return t => {\n    return {\n      x: interpolateX(t),\n      y: interpolateY(t),\n      width: interpolateWidth(t),\n      height: interpolateHeight(t),\n      borderRadius: interpolateBorderRadius(t)\n    };\n  };\n}\nexport function useAnimateBarClipRect(props) {\n  const initialProps = {\n    x: props.x,\n    y: props.y + (props.ownerState.layout === 'vertical' ? props.height : 0),\n    width: props.ownerState.layout === 'vertical' ? props.width : 0,\n    height: props.ownerState.layout === 'vertical' ? 0 : props.height,\n    borderRadius: props.borderRadius\n  };\n  return useAnimate({\n    x: props.x,\n    y: props.y,\n    width: props.width,\n    height: props.height,\n    borderRadius: props.borderRadius\n  }, {\n    createInterpolator: barClipRectPropsInterpolator,\n    transformProps: p => ({\n      x: p.x,\n      y: p.y,\n      width: p.width,\n      height: p.height,\n      style: {\n        clipPath: buildClipPath(props.ownerState.layout === 'vertical' ? p.height : p.width, p.borderRadius, props.ownerState)\n      }\n    }),\n    applyProps(element, animatedProps) {\n      element.setAttribute('x', animatedProps.x.toString());\n      element.setAttribute('y', animatedProps.y.toString());\n      element.setAttribute('width', animatedProps.width.toString());\n      element.setAttribute('height', animatedProps.height.toString());\n      element.style.clipPath = animatedProps.style.clipPath;\n    },\n    initialProps,\n    skip: props.skipAnimation,\n    ref: props.ref\n  });\n}\nfunction BarClipRect(props) {\n  const animatedProps = useAnimateBarClipRect(_extends({}, props, {\n    borderRadius: props.ownerState.borderRadius ?? 0\n  }));\n  return /*#__PURE__*/_jsx(\"rect\", _extends({}, animatedProps));\n}\n/**\n * @ignore - internal component.\n */\nfunction BarClipPath(props) {\n  const {\n      maskId,\n      x,\n      y,\n      width,\n      height,\n      skipAnimation\n    } = props,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  if (!props.borderRadius || props.borderRadius <= 0) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(\"clipPath\", {\n    id: maskId,\n    children: /*#__PURE__*/_jsx(BarClipRect, {\n      ownerState: rest,\n      x: x,\n      y: y,\n      width: width,\n      height: height,\n      skipAnimation: skipAnimation\n    })\n  });\n}\nexport { BarClipPath };", "/**\n * Returns if the corner should have a radius or not based on the layout and the data.\n * @param {GetRadiusCorner} corner The corner to check.\n * @param {GetRadiusData} cornerData The data for the corner.\n * @returns {number} The radius for the corner.\n */\nexport const getRadius = (corner, {\n  hasNegative,\n  hasPositive,\n  borderRadius,\n  layout\n}) => {\n  if (!borderRadius) {\n    return 0;\n  }\n  const isVertical = layout === 'vertical';\n  if (corner === 'top-left' && (isVertical && hasPositive || !isVertical && hasNegative)) {\n    return borderRadius;\n  }\n  if (corner === 'top-right' && (isVertical && hasPositive || !isVertical && hasPositive)) {\n    return borderRadius;\n  }\n  if (corner === 'bottom-right' && (isVertical && hasNegative || !isVertical && hasPositive)) {\n    return borderRadius;\n  }\n  if (corner === 'bottom-left' && (isVertical && hasNegative || !isVertical && hasNegative)) {\n    return borderRadius;\n  }\n  return 0;\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"bars\", \"skipAnimation\"];\nimport * as React from 'react';\nimport { BarLabelItem } from \"./BarLabelItem.js\";\nimport { useUtilityClasses } from \"../barClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nfunction BarLabelPlot(props) {\n  const {\n      bars,\n      skipAnimation\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: bars.flatMap(({\n      seriesId,\n      data\n    }) => /*#__PURE__*/_jsx(\"g\", {\n      className: classes.seriesLabels,\n      \"data-series\": seriesId,\n      children: data.map(({\n        xOrigin,\n        yOrigin,\n        x,\n        y,\n        dataIndex,\n        color,\n        value,\n        width,\n        height,\n        layout\n      }) => /*#__PURE__*/_jsx(BarLabelItem, _extends({\n        seriesId: seriesId,\n        dataIndex: dataIndex,\n        value: value,\n        color: color,\n        xOrigin: xOrigin,\n        yOrigin: yOrigin,\n        x: x,\n        y: y,\n        width: width,\n        height: height,\n        skipAnimation: skipAnimation ?? false,\n        layout: layout ?? 'vertical'\n      }, other), dataIndex))\n    }, seriesId))\n  });\n}\nexport { BarLabelPlot };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"seriesId\", \"classes\", \"color\", \"dataIndex\", \"barLabel\", \"slots\", \"slotProps\", \"xOrigin\", \"yOrigin\", \"x\", \"y\", \"width\", \"height\", \"value\", \"skipAnimation\", \"layout\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport PropTypes from 'prop-types';\nimport { useUtilityClasses } from \"./barLabelClasses.js\";\nimport { getBarLabel } from \"./getBarLabel.js\";\nimport { BarLabel } from \"./BarLabel.js\";\nimport { useItemHighlighted } from \"../../hooks/useItemHighlighted.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nfunction BarLabelItem(props) {\n  const {\n      seriesId,\n      classes: innerClasses,\n      color,\n      dataIndex,\n      barLabel,\n      slots,\n      slotProps,\n      xOrigin,\n      yOrigin,\n      x,\n      y,\n      width,\n      height,\n      value,\n      skipAnimation,\n      layout\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isFaded,\n    isHighlighted\n  } = useItemHighlighted({\n    seriesId,\n    dataIndex\n  });\n  const ownerState = {\n    seriesId,\n    classes: innerClasses,\n    color,\n    isFaded,\n    isHighlighted,\n    dataIndex,\n    skipAnimation,\n    layout\n  };\n  const classes = useUtilityClasses(ownerState);\n  const Component = slots?.barLabel ?? BarLabel;\n  const _useSlotProps = useSlotProps({\n      elementType: Component,\n      externalSlotProps: slotProps?.barLabel,\n      additionalProps: _extends({}, other, {\n        xOrigin,\n        yOrigin,\n        x,\n        y,\n        width,\n        height,\n        className: classes.root\n      }),\n      ownerState\n    }),\n    {\n      ownerState: barLabelOwnerState\n    } = _useSlotProps,\n    barLabelProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  if (!barLabel) {\n    return null;\n  }\n  const formattedLabelText = getBarLabel({\n    barLabel,\n    value,\n    dataIndex,\n    seriesId,\n    height,\n    width\n  });\n  if (!formattedLabelText) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Component, _extends({}, barLabelProps, barLabelOwnerState, {\n    children: formattedLabelText\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? BarLabelItem.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * If provided, the function will be used to format the label of the bar.\n   * It can be set to 'value' to display the current value.\n   * @param {BarItem} item The item to format.\n   * @param {BarLabelContext} context data about the bar.\n   * @returns {string} The formatted label.\n   */\n  barLabel: PropTypes.oneOfType([PropTypes.oneOf(['value']), PropTypes.func]),\n  classes: PropTypes.object,\n  color: PropTypes.string.isRequired,\n  dataIndex: PropTypes.number.isRequired,\n  /**\n   * The height of the bar.\n   */\n  height: PropTypes.number.isRequired,\n  seriesId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The value of the data point.\n   */\n  value: PropTypes.number,\n  /**\n   * The width of the bar.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport { BarLabelItem };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport composeClasses from '@mui/utils/composeClasses';\nexport function getBarLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiBarLabel', slot);\n}\nexport const barLabelClasses = generateUtilityClasses('MuiBarLabel', ['root', 'highlighted', 'faded', 'animate']);\nexport const useUtilityClasses = ownerState => {\n  const {\n    classes,\n    seriesId,\n    isFaded,\n    isHighlighted,\n    skipAnimation\n  } = ownerState;\n  const slots = {\n    root: ['root', `series-${seriesId}`, isHighlighted && 'highlighted', isFaded && 'faded', !skipAnimation && 'animate']\n  };\n  return composeClasses(slots, getBarLabelUtilityClass, classes);\n};", "export const getBarLabel = options => {\n  const {\n    barLabel,\n    value,\n    dataIndex,\n    seriesId,\n    height,\n    width\n  } = options;\n  if (barLabel === 'value') {\n    // We don't want to show the label if the value is 0\n    return value ? value?.toString() : null;\n  }\n  return barLabel({\n    seriesId,\n    dataIndex,\n    value\n  }, {\n    bar: {\n      height,\n      width\n    }\n  });\n};", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"seriesId\", \"dataIndex\", \"color\", \"isFaded\", \"isHighlighted\", \"classes\", \"skipAnimation\", \"layout\", \"xOrigin\", \"yOrigin\"];\nimport * as React from 'react';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport PropTypes from 'prop-types';\nimport { useAnimateBarLabel } from \"../../hooks/animation/useAnimateBarLabel.js\";\nimport { barLabelClasses } from \"./barLabelClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const BarLabelComponent = styled('text', {\n  name: 'MuiBarLabel',\n  slot: 'Root',\n  overridesResolver: (_, styles) => [{\n    [`&.${barLabelClasses.faded}`]: styles.faded\n  }, {\n    [`&.${barLabelClasses.highlighted}`]: styles.highlighted\n  }, styles.root]\n})(({\n  theme\n}) => _extends({}, theme?.typography?.body2, {\n  stroke: 'none',\n  fill: (theme.vars || theme)?.palette?.text?.primary,\n  transition: 'opacity 0.2s ease-in, fill 0.2s ease-in',\n  textAnchor: 'middle',\n  dominantBaseline: 'central',\n  pointerEvents: 'none',\n  opacity: 1,\n  [`&.${barLabelClasses.faded}`]: {\n    opacity: 0.3\n  }\n}));\nfunction BarLabel(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiBarLabel'\n  });\n  const otherProps = _objectWithoutPropertiesLoose(props, _excluded);\n  const animatedProps = useAnimateBarLabel(props);\n  return /*#__PURE__*/_jsx(BarLabelComponent, _extends({}, otherProps, animatedProps));\n}\nprocess.env.NODE_ENV !== \"production\" ? BarLabel.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  classes: PropTypes.object,\n  dataIndex: PropTypes.number.isRequired,\n  /**\n   * Height of the bar this label belongs to.\n   */\n  height: PropTypes.number.isRequired,\n  isFaded: PropTypes.bool.isRequired,\n  isHighlighted: PropTypes.bool.isRequired,\n  layout: PropTypes.oneOf(['horizontal', 'vertical']).isRequired,\n  seriesId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,\n  skipAnimation: PropTypes.bool.isRequired,\n  /**\n   * Width of the bar this label belongs to.\n   */\n  width: PropTypes.number.isRequired,\n  /**\n   * Position in the x-axis of the bar this label belongs to.\n   */\n  x: PropTypes.number.isRequired,\n  /**\n   * The x-coordinate of the stack this bar label belongs to.\n   */\n  xOrigin: PropTypes.number.isRequired,\n  /**\n   * Position in the y-axis of the bar this label belongs to.\n   */\n  y: PropTypes.number.isRequired,\n  /**\n   * The y-coordinate of the stack this bar label belongs to.\n   */\n  yOrigin: PropTypes.number.isRequired\n} : void 0;\nexport { BarLabel };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport composeClasses from '@mui/utils/composeClasses';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getBarUtilityClass(slot) {\n  return generateUtilityClass('MuiBar', slot);\n}\nexport const barClasses = generateUtilityClasses('MuiBar', ['root', 'series', 'seriesLabels']);\nexport const useUtilityClasses = classes => {\n  const slots = {\n    root: ['root'],\n    series: ['series'],\n    seriesLabels: ['seriesLabels']\n  };\n  return composeClasses(slots, getBarUtilityClass, classes);\n};", "'use client';\n\nimport { useSelector } from \"../../../store/useSelector.js\";\nimport { useStore } from \"../../../store/useStore.js\";\nimport { selectorChartZoomIsInteracting } from \"./useChartCartesianAxisRendering.selectors.js\";\n/**\n * Check if the zoom is interacting.\n *\n * This should probably be moved/merged to the AnimationContext when we move it to the new API.\n *\n * @ignore Internal hook, similar to the PRO one.\n *\n * @returns {boolean} Inform the zoom is interacting.\n */\nexport function useInternalIsZoomInteracting() {\n  const store = useStore();\n  const isInteracting = useSelector(store, selectorChartZoomIsInteracting);\n  return isInteracting;\n}", "import { warnOnce } from '@mui/x-internals/warning';\nimport { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from \"../constants/index.js\";\nimport { isBandScaleConfig, isPointScaleConfig } from \"../models/axis.js\";\nconst getAxisMessage = (axisDirection, axisId) => {\n  const axisName = `${axisDirection}-axis`;\n  const axisIdName = `${axisDirection}Axis`;\n  const axisDefaultKey = axisDirection === 'x' ? DEFAULT_X_AXIS_KEY : DEFAULT_Y_AXIS_KEY;\n  return axisId === axisDefaultKey ? `The first \\`${axisIdName}\\`` : `The ${axisName} with id \"${axisId}\"`;\n};\nexport function checkScaleErrors(verticalLayout, seriesId, series, xAxisId, xAxis, yAxisId, yAxis) {\n  const xAxisConfig = xAxis[xAxisId];\n  const yAxisConfig = yAxis[yAxisId];\n  const discreteAxisConfig = verticalLayout ? xAxisConfig : yAxisConfig;\n  const continuousAxisConfig = verticalLayout ? yAxisConfig : xAxisConfig;\n  const discreteAxisId = verticalLayout ? xAxisId : yAxisId;\n  const continuousAxisId = verticalLayout ? yAxisId : xAxisId;\n  const discreteAxisDirection = verticalLayout ? 'x' : 'y';\n  const continuousAxisDirection = verticalLayout ? 'y' : 'x';\n  if (!isBandScaleConfig(discreteAxisConfig)) {\n    throw new Error(`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} should be of type \"band\" to display the bar series of id \"${seriesId}\".`);\n  }\n  if (discreteAxisConfig.data === undefined) {\n    throw new Error(`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} should have data property.`);\n  }\n  if (isBandScaleConfig(continuousAxisConfig) || isPointScaleConfig(continuousAxisConfig)) {\n    throw new Error(`MUI X Charts: ${getAxisMessage(continuousAxisDirection, continuousAxisId)} should be a continuous type to display the bar series of id \"${seriesId}\".`);\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (discreteAxisConfig.data.length < series.stackedData.length) {\n      warnOnce([`MUI X Charts: ${getAxisMessage(discreteAxisDirection, discreteAxisId)} has less data (${discreteAxisConfig.data.length} values) than the bar series of id \"${seriesId}\" (${series.stackedData.length} values).`, 'The axis data should have at least the same length than the series using it.'], 'error');\n    }\n  }\n}", "import getColor from \"./seriesConfig/getColor.js\";\nimport { useChartId, useXAxes, useYAxes } from \"../hooks/index.js\";\nimport { checkScaleErrors } from \"./checkScaleErrors.js\";\nimport { useBarSeriesContext } from \"../hooks/useBarSeries.js\";\nexport function useBarPlotData(drawingArea, xAxes, yAxes) {\n  const seriesData = useBarSeriesContext() ?? {\n    series: {},\n    stackingGroups: [],\n    seriesOrder: []\n  };\n  const defaultXAxisId = useXAxes().xAxisIds[0];\n  const defaultYAxisId = useYAxes().yAxisIds[0];\n  const chartId = useChartId();\n  const {\n    series,\n    stackingGroups\n  } = seriesData;\n  const masks = {};\n  const data = stackingGroups.flatMap(({\n    ids: seriesIds\n  }, groupIndex) => {\n    const xMin = drawingArea.left;\n    const xMax = drawingArea.left + drawingArea.width;\n    const yMin = drawingArea.top;\n    const yMax = drawingArea.top + drawingArea.height;\n    return seriesIds.map(seriesId => {\n      const xAxisId = series[seriesId].xAxisId ?? defaultXAxisId;\n      const yAxisId = series[seriesId].yAxisId ?? defaultYAxisId;\n      const xAxisConfig = xAxes[xAxisId];\n      const yAxisConfig = yAxes[yAxisId];\n      const verticalLayout = series[seriesId].layout === 'vertical';\n      checkScaleErrors(verticalLayout, seriesId, series[seriesId], xAxisId, xAxes, yAxisId, yAxes);\n      const baseScaleConfig = verticalLayout ? xAxisConfig : yAxisConfig;\n      const xScale = xAxisConfig.scale;\n      const yScale = yAxisConfig.scale;\n      const colorGetter = getColor(series[seriesId], xAxes[xAxisId], yAxes[yAxisId]);\n      const bandWidth = baseScaleConfig.scale.bandwidth();\n      const {\n        barWidth,\n        offset\n      } = getBandSize({\n        bandWidth,\n        numberOfGroups: stackingGroups.length,\n        gapRatio: baseScaleConfig.barGapRatio\n      });\n      const barOffset = groupIndex * (barWidth + offset);\n      const {\n        stackedData,\n        data: currentSeriesData,\n        layout,\n        minBarSize\n      } = series[seriesId];\n      const seriesDataPoints = baseScaleConfig.data.map((baseValue, dataIndex) => {\n        if (currentSeriesData[dataIndex] == null) {\n          return null;\n        }\n        const values = stackedData[dataIndex];\n        const valueCoordinates = values.map(v => verticalLayout ? yScale(v) : xScale(v));\n        const minValueCoord = Math.round(Math.min(...valueCoordinates));\n        const maxValueCoord = Math.round(Math.max(...valueCoordinates));\n        const stackId = series[seriesId].stack;\n        const {\n          barSize,\n          startCoordinate\n        } = getValueCoordinate(verticalLayout, minValueCoord, maxValueCoord, currentSeriesData[dataIndex], minBarSize);\n        const result = {\n          seriesId,\n          dataIndex,\n          layout,\n          x: verticalLayout ? xScale(baseValue) + barOffset : startCoordinate,\n          y: verticalLayout ? startCoordinate : yScale(baseValue) + barOffset,\n          xOrigin: xScale(0) ?? 0,\n          yOrigin: yScale(0) ?? 0,\n          height: verticalLayout ? barSize : barWidth,\n          width: verticalLayout ? barWidth : barSize,\n          color: colorGetter(dataIndex),\n          value: currentSeriesData[dataIndex],\n          maskId: `${chartId}_${stackId || seriesId}_${groupIndex}_${dataIndex}`\n        };\n        if (result.x > xMax || result.x + result.width < xMin || result.y > yMax || result.y + result.height < yMin) {\n          return null;\n        }\n        if (!masks[result.maskId]) {\n          masks[result.maskId] = {\n            id: result.maskId,\n            width: 0,\n            height: 0,\n            hasNegative: false,\n            hasPositive: false,\n            layout: result.layout,\n            xOrigin: xScale(0),\n            yOrigin: yScale(0),\n            x: 0,\n            y: 0\n          };\n        }\n        const mask = masks[result.maskId];\n        mask.width = result.layout === 'vertical' ? result.width : mask.width + result.width;\n        mask.height = result.layout === 'vertical' ? mask.height + result.height : result.height;\n        mask.x = Math.min(mask.x === 0 ? Infinity : mask.x, result.x);\n        mask.y = Math.min(mask.y === 0 ? Infinity : mask.y, result.y);\n        mask.hasNegative = mask.hasNegative || (result.value ?? 0) < 0;\n        mask.hasPositive = mask.hasPositive || (result.value ?? 0) > 0;\n        return result;\n      }).filter(rectangle => rectangle !== null);\n      return {\n        seriesId,\n        data: seriesDataPoints\n      };\n    });\n  });\n  return {\n    completedData: data,\n    masksData: Object.values(masks)\n  };\n}\n\n/**\n * Solution of the equations\n * W = barWidth * N + offset * (N-1)\n * offset / (offset + barWidth) = r\n * @param bandWidth The width available to place bars.\n * @param numberOfGroups The number of bars to place in that space.\n * @param gapRatio The ratio of the gap between bars over the bar width.\n * @returns The bar width and the offset between bars.\n */\nfunction getBandSize({\n  bandWidth: W,\n  numberOfGroups: N,\n  gapRatio: r\n}) {\n  if (r === 0) {\n    return {\n      barWidth: W / N,\n      offset: 0\n    };\n  }\n  const barWidth = W / (N + (N - 1) * r);\n  const offset = r * barWidth;\n  return {\n    barWidth,\n    offset\n  };\n}\nfunction getValueCoordinate(isVertical, minValueCoord, maxValueCoord, baseValue, minBarSize) {\n  if (baseValue === 0 || baseValue == null) {\n    return {\n      barSize: 0,\n      startCoordinate: minValueCoord\n    };\n  }\n  const isSizeLessThanMin = maxValueCoord - minValueCoord < minBarSize;\n  const barSize = isSizeLessThanMin ? minBarSize : maxValueCoord - minValueCoord;\n  const isVerticalAndPositive = isVertical && baseValue >= 0;\n  const isHorizontalAndNegative = !isVertical && baseValue < 0;\n  if (isSizeLessThanMin && (isVerticalAndPositive || isHorizontalAndNegative)) {\n    return {\n      barSize,\n      startCoordinate: maxValueCoord - barSize\n    };\n  }\n  return {\n    barSize,\n    startCoordinate: minValueCoord\n  };\n}", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { ChartsXAxis } from \"../ChartsXAxis/index.js\";\nimport { ChartsYAxis } from \"../ChartsYAxis/index.js\";\nimport { useXAxes, useYAxes } from \"../hooks/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Demos:\n *\n * - [Axis](https://mui.com/x/react-charts/axis/)\n *\n * API:\n *\n * - [ChartsAxis API](https://mui.com/x/api/charts/charts-axis/)\n */\nfunction ChartsAxis(props) {\n  const {\n    slots,\n    slotProps\n  } = props;\n  const {\n    xAxisIds,\n    xAxis\n  } = useXAxes();\n  const {\n    yAxisIds,\n    yAxis\n  } = useYAxes();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [xAxisIds.map(axisId => {\n      if (!xAxis[axisId].position || xAxis[axisId].position === 'none') {\n        return null;\n      }\n      return /*#__PURE__*/_jsx(ChartsXAxis, {\n        slots: slots,\n        slotProps: slotProps,\n        axisId: axisId\n      }, axisId);\n    }), yAxisIds.map(axisId => {\n      if (!yAxis[axisId].position || yAxis[axisId].position === 'none') {\n        return null;\n      }\n      return /*#__PURE__*/_jsx(ChartsYAxis, {\n        slots: slots,\n        slotProps: slotProps,\n        axisId: axisId\n      }, axisId);\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsAxis.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object\n} : void 0;\nexport { ChartsAxis };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"scale\", \"tickNumber\", \"reverse\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useThemeProps, useTheme, styled } from '@mui/material/styles';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsHydrated } from \"../hooks/useIsHydrated.js\";\nimport { getStringSize } from \"../internals/domUtils.js\";\nimport { useTicks } from \"../hooks/useTicks.js\";\nimport { getAxisUtilityClass } from \"../ChartsAxis/axisClasses.js\";\nimport { AxisRoot } from \"../internals/components/AxisSharedComponents.js\";\nimport { ChartsText } from \"../ChartsText/index.js\";\nimport { useMounted } from \"../hooks/useMounted.js\";\nimport { useDrawingArea } from \"../hooks/useDrawingArea.js\";\nimport { isInfinity } from \"../internals/isInfinity.js\";\nimport { isBandScale } from \"../internals/isBandScale.js\";\nimport { useChartContext } from \"../context/ChartProvider/useChartContext.js\";\nimport { useXAxes } from \"../hooks/useAxis.js\";\nimport { getDefaultBaseline, getDefaultTextAnchor } from \"../ChartsText/defaultTextPlacement.js\";\nimport { invertTextAnchor } from \"../internals/invertTextAnchor.js\";\nimport { shortenLabels } from \"./shortenLabels.js\";\nimport { getVisibleLabels } from \"./getVisibleLabels.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    position,\n    id\n  } = ownerState;\n  const slots = {\n    root: ['root', 'directionX', position, `id-${id}`],\n    line: ['line'],\n    tickContainer: ['tickContainer'],\n    tick: ['tick'],\n    tickLabel: ['tickLabel'],\n    label: ['label']\n  };\n  return composeClasses(slots, getAxisUtilityClass, classes);\n};\n\n/* Gap between a tick and its label. */\nconst TICK_LABEL_GAP = 3;\n/* Gap between the axis label and tick labels. */\nconst AXIS_LABEL_TICK_LABEL_GAP = 4;\nconst XAxisRoot = styled(AxisRoot, {\n  name: 'MuiChartsXAxis',\n  slot: 'Root'\n})({});\nconst defaultProps = {\n  disableLine: false,\n  disableTicks: false,\n  tickSize: 6,\n  tickLabelMinGap: 4\n};\n\n/**\n * Demos:\n *\n * - [Axis](https://mui.com/x/react-charts/axis/)\n *\n * API:\n *\n * - [ChartsXAxis API](https://mui.com/x/api/charts/charts-x-axis/)\n */\nfunction ChartsXAxis(inProps) {\n  const {\n    xAxis,\n    xAxisIds\n  } = useXAxes();\n  const _xAxis = xAxis[inProps.axisId ?? xAxisIds[0]],\n    {\n      scale: xScale,\n      tickNumber,\n      reverse\n    } = _xAxis,\n    settings = _objectWithoutPropertiesLoose(_xAxis, _excluded);\n  const isMounted = useMounted();\n  const themedProps = useThemeProps({\n    props: _extends({}, settings, inProps),\n    name: 'MuiChartsXAxis'\n  });\n  const defaultizedProps = _extends({}, defaultProps, themedProps);\n  const {\n    position,\n    disableLine,\n    disableTicks,\n    tickLabelStyle,\n    label,\n    labelStyle,\n    tickSize: tickSizeProp,\n    valueFormatter,\n    slots,\n    slotProps,\n    tickInterval,\n    tickLabelInterval,\n    tickPlacement,\n    tickLabelPlacement,\n    tickLabelMinGap,\n    sx,\n    offset,\n    height: axisHeight\n  } = defaultizedProps;\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const classes = useUtilityClasses(defaultizedProps);\n  const drawingArea = useDrawingArea();\n  const {\n    left,\n    top,\n    width,\n    height\n  } = drawingArea;\n  const {\n    instance\n  } = useChartContext();\n  const isHydrated = useIsHydrated();\n  const tickSize = disableTicks ? 4 : tickSizeProp;\n  const positionSign = position === 'bottom' ? 1 : -1;\n  const Line = slots?.axisLine ?? 'line';\n  const Tick = slots?.axisTick ?? 'line';\n  const TickLabel = slots?.axisTickLabel ?? ChartsText;\n  const Label = slots?.axisLabel ?? ChartsText;\n  const defaultTextAnchor = getDefaultTextAnchor((position === 'bottom' ? 0 : 180) - (tickLabelStyle?.angle ?? 0));\n  const defaultDominantBaseline = getDefaultBaseline((position === 'bottom' ? 0 : 180) - (tickLabelStyle?.angle ?? 0));\n  const axisTickLabelProps = useSlotProps({\n    elementType: TickLabel,\n    externalSlotProps: slotProps?.axisTickLabel,\n    additionalProps: {\n      style: _extends({}, theme.typography.caption, {\n        fontSize: 12,\n        lineHeight: 1.25,\n        textAnchor: isRtl ? invertTextAnchor(defaultTextAnchor) : defaultTextAnchor,\n        dominantBaseline: defaultDominantBaseline\n      }, tickLabelStyle)\n    },\n    className: classes.tickLabel,\n    ownerState: {}\n  });\n  const xTicks = useTicks({\n    scale: xScale,\n    tickNumber,\n    valueFormatter,\n    tickInterval,\n    tickPlacement,\n    tickLabelPlacement,\n    direction: 'x'\n  });\n  const visibleLabels = getVisibleLabels(xTicks, {\n    tickLabelStyle: axisTickLabelProps.style,\n    tickLabelInterval,\n    tickLabelMinGap,\n    reverse,\n    isMounted,\n    isXInside: instance.isXInside\n  });\n  const axisLabelProps = useSlotProps({\n    elementType: Label,\n    externalSlotProps: slotProps?.axisLabel,\n    additionalProps: {\n      style: _extends({}, theme.typography.body1, {\n        lineHeight: 1,\n        fontSize: 14,\n        textAnchor: 'middle',\n        dominantBaseline: position === 'bottom' ? 'text-after-edge' : 'text-before-edge'\n      }, labelStyle)\n    },\n    ownerState: {}\n  });\n  const domain = xScale.domain();\n  const ordinalAxis = isBandScale(xScale);\n  // Skip axis rendering if no data is available\n  // - The domain is an empty array for band/point scales.\n  // - The domains contains Infinity for continuous scales.\n  // - The position is set to 'none'.\n  if (ordinalAxis && domain.length === 0 || !ordinalAxis && domain.some(isInfinity) || position === 'none') {\n    return null;\n  }\n  const labelHeight = label ? getStringSize(label, axisLabelProps.style).height : 0;\n  const labelRefPoint = {\n    x: left + width / 2,\n    y: positionSign * axisHeight\n  };\n\n  /* If there's an axis title, the tick labels have less space to render  */\n  const tickLabelsMaxHeight = Math.max(0, axisHeight - (label ? labelHeight + AXIS_LABEL_TICK_LABEL_GAP : 0) - tickSize - TICK_LABEL_GAP);\n  const tickLabels = isHydrated ? shortenLabels(visibleLabels, drawingArea, tickLabelsMaxHeight, isRtl, axisTickLabelProps.style) : new Map(Array.from(visibleLabels).map(item => [item, item.formattedValue]));\n  return /*#__PURE__*/_jsxs(XAxisRoot, {\n    transform: `translate(0, ${position === 'bottom' ? top + height + offset : top - offset})`,\n    className: classes.root,\n    sx: sx,\n    children: [!disableLine && /*#__PURE__*/_jsx(Line, _extends({\n      x1: left,\n      x2: left + width,\n      className: classes.line\n    }, slotProps?.axisLine)), xTicks.map((item, index) => {\n      const {\n        offset: tickOffset,\n        labelOffset\n      } = item;\n      const xTickLabel = labelOffset ?? 0;\n      const yTickLabel = positionSign * (tickSize + TICK_LABEL_GAP);\n      const showTick = instance.isXInside(tickOffset);\n      const tickLabel = tickLabels.get(item);\n      const showTickLabel = visibleLabels.has(item);\n      return /*#__PURE__*/_jsxs(\"g\", {\n        transform: `translate(${tickOffset}, 0)`,\n        className: classes.tickContainer,\n        children: [!disableTicks && showTick && /*#__PURE__*/_jsx(Tick, _extends({\n          y2: positionSign * tickSize,\n          className: classes.tick\n        }, slotProps?.axisTick)), tickLabel !== undefined && showTickLabel && /*#__PURE__*/_jsx(TickLabel, _extends({\n          x: xTickLabel,\n          y: yTickLabel\n        }, axisTickLabelProps, {\n          text: tickLabel\n        }))]\n      }, index);\n    }), label && /*#__PURE__*/_jsx(\"g\", {\n      className: classes.label,\n      children: /*#__PURE__*/_jsx(Label, _extends({}, labelRefPoint, axisLabelProps, {\n        text: label\n      }))\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsXAxis.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  axis: PropTypes.oneOf(['x']),\n  /**\n   * The id of the axis to render.\n   * If undefined, it will be the first defined axis.\n   */\n  axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * If true, the axis line is disabled.\n   * @default false\n   */\n  disableLine: PropTypes.bool,\n  /**\n   * If true, the ticks are disabled.\n   * @default false\n   */\n  disableTicks: PropTypes.bool,\n  /**\n   * The fill color of the axis text.\n   * @default 'currentColor'\n   */\n  fill: PropTypes.string,\n  /**\n   * The label of the axis.\n   */\n  label: PropTypes.string,\n  /**\n   * The style applied to the axis label.\n   */\n  labelStyle: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The stroke color of the axis line.\n   * @default 'currentColor'\n   */\n  stroke: PropTypes.string,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Defines which ticks are displayed.\n   * Its value can be:\n   * - 'auto' In such case the ticks are computed based on axis scale and other parameters.\n   * - a filtering function of the form `(value, index) => boolean` which is available only if the axis has \"point\" scale.\n   * - an array containing the values where ticks should be displayed.\n   * @see See {@link https://mui.com/x/react-charts/axis/#fixed-tick-positions}\n   * @default 'auto'\n   */\n  tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n  /**\n   * Defines which ticks get its label displayed. Its value can be:\n   * - 'auto' In such case, labels are displayed if they do not overlap with the previous one.\n   * - a filtering function of the form (value, index) => boolean. Warning: the index is tick index, not data ones.\n   * @default 'auto'\n   */\n  tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n  /**\n   * The minimum gap in pixels between two tick labels.\n   * If two tick labels are closer than this minimum gap, one of them will be hidden.\n   * @default 4\n   */\n  tickLabelMinGap: PropTypes.number,\n  /**\n   * The placement of ticks label. Can be the middle of the band, or the tick position.\n   * Only used if scale is 'band'.\n   * @default 'middle'\n   */\n  tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n  /**\n   * The style applied to ticks text.\n   */\n  tickLabelStyle: PropTypes.object,\n  /**\n   * Maximal step between two ticks.\n   * When using time data, the value is assumed to be in ms.\n   * Not supported by categorical axis (band, points).\n   */\n  tickMaxStep: PropTypes.number,\n  /**\n   * Minimal step between two ticks.\n   * When using time data, the value is assumed to be in ms.\n   * Not supported by categorical axis (band, points).\n   */\n  tickMinStep: PropTypes.number,\n  /**\n   * The number of ticks. This number is not guaranteed.\n   * Not supported by categorical axis (band, points).\n   */\n  tickNumber: PropTypes.number,\n  /**\n   * The placement of ticks in regard to the band interval.\n   * Only used if scale is 'band'.\n   * @default 'extremities'\n   */\n  tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n  /**\n   * The size of the ticks.\n   * @default 6\n   */\n  tickSize: PropTypes.number\n} : void 0;\nexport { ChartsXAxis };", "'use client';\n\nimport * as React from 'react';\n\n/** Returns true after hydration is done on the client.\n *\n * Basically a implementation of Option 2 of this gist: https://gist.github.com/gaearon/e7d97cdf38a2907924ea12e4ebdf3c85#option-2-lazily-show-component-with-uselayouteffect. */\nexport function useIsHydrated() {\n  const [isHydrated, setIsHydrated] = React.useState(typeof window !== 'undefined' || process.env.NODE_ENV === 'test');\n  React.useEffect(() => {\n    setIsHydrated(true);\n  }, []);\n  return isHydrated;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// DOM utils taken from\n// https://github.com/recharts/recharts/blob/master/src/util/DOMUtils.ts\n\nfunction isSsr() {\n  return typeof window === 'undefined';\n}\nconst stringCache = new Map();\nconst MAX_CACHE_NUM = 2000;\nconst SPAN_STYLE = {\n  position: 'absolute',\n  top: '-20000px',\n  left: 0,\n  padding: 0,\n  margin: 0,\n  border: 'none',\n  whiteSpace: 'pre'\n};\nconst STYLE_LIST = ['minWidth', 'maxWidth', 'width', 'minHeight', 'maxHeight', 'height', 'top', 'left', 'fontSize', 'padding', 'margin', 'paddingLeft', 'paddingRight', 'paddingTop', 'paddingBottom', 'marginLeft', 'marginRight', 'marginTop', 'marginBottom'];\nexport const MEASUREMENT_SPAN_ID = 'mui_measurement_span';\n\n/**\n *\n * @param name CSS property name\n * @param value\n * @returns add 'px' for distance properties\n */\nfunction autoCompleteStyle(name, value) {\n  if (STYLE_LIST.indexOf(name) >= 0 && value === +value) {\n    return `${value}px`;\n  }\n  return value;\n}\n\n/**\n *\n * @param text camelcase css property\n * @returns css property\n */\nfunction camelToMiddleLine(text) {\n  const strs = text.split('');\n  const formatStrs = strs.reduce((result, entry) => {\n    if (entry === entry.toUpperCase()) {\n      return [...result, '-', entry.toLowerCase()];\n    }\n    return [...result, entry];\n  }, []);\n  return formatStrs.join('');\n}\n\n/**\n *\n * @param style React style object\n * @returns CSS styling string\n */\nexport const getStyleString = style => Object.keys(style).sort().reduce((result, s) => `${result}${camelToMiddleLine(s)}:${autoCompleteStyle(s, style[s])};`, '');\nlet domCleanTimeout;\n\n/**\n *\n * @param text The string to estimate\n * @param style The style applied\n * @returns width and height of the text\n */\nexport const getStringSize = (text, style = {}) => {\n  if (text === undefined || text === null || isSsr()) {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n  const str = `${text}`;\n  const styleString = getStyleString(style);\n  const cacheKey = `${str}-${styleString}`;\n  const size = stringCache.get(cacheKey);\n  if (size) {\n    return size;\n  }\n  try {\n    let measurementSpan = document.getElementById(MEASUREMENT_SPAN_ID);\n    if (measurementSpan === null) {\n      measurementSpan = document.createElement('span');\n      measurementSpan.setAttribute('id', MEASUREMENT_SPAN_ID);\n      measurementSpan.setAttribute('aria-hidden', 'true');\n      document.body.appendChild(measurementSpan);\n    }\n    // Need to use CSS Object Model (CSSOM) to be able to comply with Content Security Policy (CSP)\n    // https://en.wikipedia.org/wiki/Content_Security_Policy\n    const measurementSpanStyle = _extends({}, SPAN_STYLE, style);\n    Object.keys(measurementSpanStyle).map(styleKey => {\n      measurementSpan.style[camelToMiddleLine(styleKey)] = autoCompleteStyle(styleKey, measurementSpanStyle[styleKey]);\n      return styleKey;\n    });\n    measurementSpan.textContent = str;\n    const rect = measurementSpan.getBoundingClientRect();\n    const result = {\n      width: rect.width,\n      height: rect.height\n    };\n    stringCache.set(cacheKey, result);\n    if (stringCache.size + 1 > MAX_CACHE_NUM) {\n      stringCache.clear();\n    }\n    if (process.env.NODE_ENV === 'test') {\n      // In test environment, we clean the measurement span immediately\n      measurementSpan.textContent = '';\n    } else {\n      if (domCleanTimeout) {\n        clearTimeout(domCleanTimeout);\n      }\n      domCleanTimeout = setTimeout(() => {\n        // Limit node cleaning to once per render cycle\n        measurementSpan.textContent = '';\n      }, 0);\n    }\n    return result;\n  } catch {\n    return {\n      width: 0,\n      height: 0\n    };\n  }\n};", "'use client';\n\nimport * as React from 'react';\nimport { useChartContext } from \"../context/ChartProvider/index.js\";\nimport { isBandScale } from \"../internals/isBandScale.js\";\nimport { isInfinity } from \"../internals/isInfinity.js\";\nconst offsetRatio = {\n  start: 0,\n  extremities: 0,\n  end: 1,\n  middle: 0.5\n};\nexport function getTicks(options) {\n  const {\n    scale,\n    tickNumber,\n    valueFormatter,\n    tickInterval,\n    tickPlacement = 'extremities',\n    tickLabelPlacement: tickLabelPlacementProp,\n    isInside\n  } = options;\n\n  // band scale\n  if (isBandScale(scale)) {\n    const domain = scale.domain();\n    const tickLabelPlacement = tickLabelPlacementProp ?? 'middle';\n    if (scale.bandwidth() > 0) {\n      // scale type = 'band'\n      const filteredDomain = typeof tickInterval === 'function' && domain.filter(tickInterval) || typeof tickInterval === 'object' && tickInterval || domain;\n      return [...filteredDomain.map(value => {\n        const defaultTickLabel = `${value}`;\n        return {\n          value,\n          formattedValue: valueFormatter?.(value, {\n            location: 'tick',\n            scale,\n            tickNumber,\n            defaultTickLabel\n          }) ?? defaultTickLabel,\n          offset: scale(value) - (scale.step() - scale.bandwidth()) / 2 + offsetRatio[tickPlacement] * scale.step(),\n          labelOffset: tickLabelPlacement === 'tick' ? 0 : scale.step() * (offsetRatio[tickLabelPlacement] - offsetRatio[tickPlacement])\n        };\n      }), ...(tickPlacement === 'extremities' ? [{\n        formattedValue: undefined,\n        offset: scale.range()[1],\n        labelOffset: 0\n      }] : [])];\n    }\n\n    // scale type = 'point'\n    const filteredDomain = typeof tickInterval === 'function' && domain.filter(tickInterval) || typeof tickInterval === 'object' && tickInterval || domain;\n    return filteredDomain.map(value => {\n      const defaultTickLabel = `${value}`;\n      return {\n        value,\n        formattedValue: valueFormatter?.(value, {\n          location: 'tick',\n          scale,\n          tickNumber,\n          defaultTickLabel\n        }) ?? defaultTickLabel,\n        offset: scale(value),\n        labelOffset: 0\n      };\n    });\n  }\n  const domain = scale.domain();\n  // Skip axis rendering if no data is available\n  // - The domains contains Infinity for continuous scales.\n  if (domain.some(isInfinity)) {\n    return [];\n  }\n  const tickLabelPlacement = tickLabelPlacementProp;\n  const ticks = typeof tickInterval === 'object' ? tickInterval : scale.ticks(tickNumber);\n\n  // Ticks inside the drawing area\n  const visibleTicks = [];\n  for (let i = 0; i < ticks.length; i += 1) {\n    const value = ticks[i];\n    const offset = scale(value);\n    if (isInside(offset)) {\n      /* If d3 returns an empty string, it means that a tick should be shown, but its label shouldn't.\n       * This is especially useful in a log scale where we want to show ticks to demonstrate it's a log\n       * scale, but don't want to show labels because they would overlap.\n       * https://github.com/mui/mui-x/issues/18239 */\n      const defaultTickLabel = scale.tickFormat(tickNumber)(value);\n      visibleTicks.push({\n        value,\n        formattedValue: valueFormatter?.(value, {\n          location: 'tick',\n          scale,\n          tickNumber,\n          defaultTickLabel\n        }) ?? defaultTickLabel,\n        offset,\n        // Allowing the label to be placed in the middle of a continuous scale is weird.\n        // But it is useful in some cases, like funnel categories with a linear scale.\n        labelOffset: tickLabelPlacement === 'middle' ? scale(ticks[i - 1] ?? 0) - (offset + scale(ticks[i - 1] ?? 0)) / 2 : 0\n      });\n    }\n  }\n  return visibleTicks;\n}\nexport function useTicks(options) {\n  const {\n    scale,\n    tickNumber,\n    valueFormatter,\n    tickInterval,\n    tickPlacement = 'extremities',\n    tickLabelPlacement,\n    direction\n  } = options;\n  const {\n    instance\n  } = useChartContext();\n  const isInside = direction === 'x' ? instance.isXInside : instance.isYInside;\n  return React.useMemo(() => getTicks({\n    scale,\n    tickNumber,\n    tickPlacement,\n    tickInterval,\n    tickLabelPlacement,\n    valueFormatter,\n    isInside\n  }), [scale, tickNumber, tickPlacement, tickInterval, tickLabelPlacement, valueFormatter, isInside]);\n}", "export function isInfinity(v) {\n  return typeof v === 'number' && !Number.isFinite(v);\n}", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getAxisUtilityClass(slot) {\n  return generateUtilityClass('MuiChartsAxis', slot);\n}\nexport const axisClasses = generateUtilityClasses('MuiChartsAxis', ['root', 'line', 'tickContainer', 'tick', 'tickLabel', 'label', 'directionX', 'directionY', 'top', 'bottom', 'left', 'right', 'id']);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { styled } from '@mui/material/styles';\nimport { axisClasses } from \"../../ChartsAxis/axisClasses.js\";\nexport const AxisRoot = styled('g', {\n  name: '<PERSON><PERSON><PERSON><PERSON>sAxi<PERSON>',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  [`& .${axisClasses.tickLabel}`]: _extends({}, theme.typography.caption, {\n    fill: (theme.vars || theme).palette.text.primary\n  }),\n  [`& .${axisClasses.label}`]: {\n    fill: (theme.vars || theme).palette.text.primary\n  },\n  [`& .${axisClasses.line}`]: {\n    stroke: (theme.vars || theme).palette.text.primary,\n    shapeRendering: 'crispEdges',\n    strokeWidth: 1\n  },\n  [`& .${axisClasses.tick}`]: {\n    stroke: (theme.vars || theme).palette.text.primary,\n    shapeRendering: 'crispEdges'\n  }\n}));", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"x\", \"y\", \"style\", \"text\", \"ownerState\"],\n  _excluded2 = [\"angle\", \"textAnchor\", \"dominantBaseline\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getWordsByLines } from \"../internals/getWordsByLines.js\";\nimport { useIsHydrated } from \"../hooks/useIsHydrated.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * Helper component to manage multiline text in SVG\n */\nfunction ChartsText(props) {\n  const {\n      x,\n      y,\n      style: styleProps,\n      text\n    } = props,\n    textProps = _objectWithoutPropertiesLoose(props, _excluded);\n  const _ref = styleProps ?? {},\n    {\n      angle,\n      textAnchor,\n      dominantBaseline\n    } = _ref,\n    style = _objectWithoutPropertiesLoose(_ref, _excluded2);\n  const isHydrated = useIsHydrated();\n  const wordsByLines = React.useMemo(() => getWordsByLines({\n    style,\n    needsComputation: isHydrated && text.includes('\\n'),\n    text\n  }), [style, text, isHydrated]);\n  let startDy;\n  switch (dominantBaseline) {\n    case 'hanging':\n    case 'text-before-edge':\n      startDy = 0;\n      break;\n    case 'central':\n      startDy = (wordsByLines.length - 1) / 2 * -wordsByLines[0].height;\n      break;\n    default:\n      startDy = (wordsByLines.length - 1) * -wordsByLines[0].height;\n      break;\n  }\n  return /*#__PURE__*/_jsx(\"text\", _extends({}, textProps, {\n    transform: angle ? `rotate(${angle}, ${x}, ${y})` : undefined,\n    x: x,\n    y: y,\n    textAnchor: textAnchor,\n    dominantBaseline: dominantBaseline,\n    style: style,\n    children: wordsByLines.map((line, index) => /*#__PURE__*/_jsx(\"tspan\", {\n      x: x,\n      dy: `${index === 0 ? startDy : wordsByLines[0].height}px`,\n      dominantBaseline: dominantBaseline // Propagated to fix Safari issue: https://github.com/mui/mui-x/issues/10808\n      ,\n      children: line.text\n    }, index))\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsText.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Height of a text line (in `em`).\n   */\n  lineHeight: PropTypes.number,\n  /**\n   * If `true`, the line width is computed.\n   * @default false\n   */\n  needsComputation: PropTypes.bool,\n  ownerState: PropTypes.any,\n  /**\n   * Style applied to text elements.\n   */\n  style: PropTypes.object,\n  /**\n   * Text displayed.\n   */\n  text: PropTypes.string.isRequired\n} : void 0;\nexport { ChartsText };", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { getStringSize } from \"./domUtils.js\";\nexport function getWordsByLines({\n  style,\n  needsComputation,\n  text\n}) {\n  return text.split('\\n').map(subText => _extends({\n    text: subText\n  }, needsComputation ? getStringSize(subText, style) : {\n    width: 0,\n    height: 0\n  }));\n}", "'use client';\n\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nexport function useMounted(defer = false) {\n  const [mountedState, setMountedState] = React.useState(false);\n  useEnhancedEffect(() => {\n    if (!defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  React.useEffect(() => {\n    if (defer) {\n      setMountedState(true);\n    }\n  }, [defer]);\n  return mountedState;\n}", "import { clampAngle } from \"../internals/clampAngle.js\";\n/**\n * Provide the text-anchor based on the angle between the text and the associated element.\n * - 0 means the element is on top of the text, 180 bellow, and 90 on the right of the text.\n * @param {number} angle The angle between the text and the element.\n * @returns\n */\nexport function getDefaultTextAnchor(angle) {\n  const adjustedAngle = clampAngle(angle);\n  if (adjustedAngle <= 30 || adjustedAngle >= 330) {\n    // +/-30° around 0°\n    return 'middle';\n  }\n  if (adjustedAngle <= 210 && adjustedAngle >= 150) {\n    // +/-30° around 180°\n    return 'middle';\n  }\n  if (adjustedAngle <= 180) {\n    return 'end';\n  }\n  return 'start';\n}\nexport function getDefaultBaseline(angle) {\n  const adjustedAngle = clampAngle(angle);\n  if (adjustedAngle <= 30 || adjustedAngle >= 330) {\n    // +/-60° around 0°\n    return 'hanging';\n  }\n  if (adjustedAngle <= 210 && adjustedAngle >= 150) {\n    // +/-60° around 180°\n    return 'auto';\n  }\n  return 'central';\n}", "export function invertTextAnchor(textAnchor) {\n  switch (textAnchor) {\n    case 'start':\n      return 'end';\n    case 'end':\n      return 'start';\n    default:\n      return textAnchor;\n  }\n}", "const segmenter = typeof window !== 'undefined' && 'Intl' in window && 'Segmenter' in Intl ? new Intl.Segmenter(undefined, {\n  granularity: 'grapheme'\n}) : null;\nfunction getGraphemeCountFallback(text) {\n  return text.length;\n}\nfunction getGraphemeCountModern(text) {\n  const segments = segmenter.segment(text);\n  let count = 0;\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/naming-convention,no-underscore-dangle\n  for (const _unused of segments) {\n    count += 1;\n  }\n  return count;\n}\n\n/** Returns the number of graphemes (basically characters) present in {@link text}. */\nexport const getGraphemeCount = segmenter ? getGraphemeCountModern : getGraphemeCountFallback;", "/** Converts degrees to radians. */\nexport function degToRad(degrees) {\n  return degrees * (Math.PI / 180);\n}", "const segmenter = typeof window !== 'undefined' && 'Intl' in window && 'Segmenter' in Intl ? new Intl.Segmenter(undefined, {\n  granularity: 'grapheme'\n}) : null;\nfunction sliceUntilFallback(text, endIndex) {\n  return text.slice(0, endIndex);\n}\nfunction sliceUntilModern(text, endIndex) {\n  const segments = segmenter.segment(text);\n  let newText = '';\n  let i = 0;\n  for (const segment of segments) {\n    newText += segment.segment;\n    i += 1;\n    if (i >= endIndex) {\n      break;\n    }\n  }\n  return newText;\n}\n\n/** Creates a slice of {@link text} from the start until the {@link endIndex}th grapheme (basically character). */\nexport const sliceUntil = segmenter ? sliceUntilModern : sliceUntilFallback;", "import { getGraphemeCount } from \"./getGraphemeCount.js\";\nimport { degToRad } from \"./degToRad.js\";\nimport { sliceUntil } from \"./sliceUntil.js\";\nconst ELLIPSIS = '…';\nexport function doesTextFitInRect(text, config) {\n  const {\n    width,\n    height,\n    measureText\n  } = config;\n  const angle = degToRad(config.angle);\n  const textSize = measureText(text);\n  const angledWidth = Math.abs(textSize.width * Math.cos(angle)) + Math.abs(textSize.height * Math.sin(angle));\n  const angledHeight = Math.abs(textSize.width * Math.sin(angle)) + Math.abs(textSize.height * Math.cos(angle));\n  return angledWidth <= width && angledHeight <= height;\n}\n\n/** This function finds the best place to clip the text to add an ellipsis.\n *  This function assumes that the {@link doesTextFit} never returns true for longer text after returning false for\n *  shorter text.\n *\n *  @param text Text to ellipsize if needed\n *  @param doesTextFit a function that returns whether a string fits inside a container.\n */\nexport function ellipsize(text, doesTextFit) {\n  if (doesTextFit(text)) {\n    return text;\n  }\n  let shortenedText = text;\n  let step = 1;\n  let by = 1 / 2;\n  const graphemeCount = getGraphemeCount(text);\n  let newLength = graphemeCount;\n  let lastLength = graphemeCount;\n  let longestFittingText = null;\n  do {\n    lastLength = newLength;\n    newLength = Math.floor(graphemeCount * by);\n    if (newLength === 0) {\n      break;\n    }\n    shortenedText = sliceUntil(text, newLength).trim();\n    const fits = doesTextFit(shortenedText + ELLIPSIS);\n    step += 1;\n    if (fits) {\n      longestFittingText = shortenedText;\n      by += 1 / 2 ** step;\n    } else {\n      by -= 1 / 2 ** step;\n    }\n  } while (Math.abs(newLength - lastLength) !== 1);\n  return longestFittingText ? longestFittingText + ELLIPSIS : '';\n}", "import { clampAngle } from \"../internals/clampAngle.js\";\nimport { doesTextFitInRect, ellipsize } from \"../internals/ellipsize.js\";\nimport { getStringSize } from \"../internals/domUtils.js\";\nexport function shortenLabels(visibleLabels, drawingArea, maxHeight, isRtl, tickLabelStyle) {\n  const shortenedLabels = new Map();\n  const angle = clampAngle(tickLabelStyle?.angle ?? 0);\n\n  // Multiplying the space available to the left of the text position by leftBoundFactor returns the max width of the text.\n  // Same for rightBoundFactor\n  let leftBoundFactor = 1;\n  let rightBoundFactor = 1;\n  if (tickLabelStyle?.textAnchor === 'start') {\n    leftBoundFactor = Infinity;\n    rightBoundFactor = 1;\n  } else if (tickLabelStyle?.textAnchor === 'end') {\n    leftBoundFactor = 1;\n    rightBoundFactor = Infinity;\n  } else {\n    leftBoundFactor = 2;\n    rightBoundFactor = 2;\n  }\n  if (angle > 90 && angle < 270) {\n    [leftBoundFactor, rightBoundFactor] = [rightBoundFactor, leftBoundFactor];\n  }\n  if (isRtl) {\n    [leftBoundFactor, rightBoundFactor] = [rightBoundFactor, leftBoundFactor];\n  }\n  for (const item of visibleLabels) {\n    if (item.formattedValue) {\n      // That maximum width of the tick depends on its proximity to the axis bounds.\n      const width = Math.min((item.offset + item.labelOffset) * leftBoundFactor, (drawingArea.left + drawingArea.width + drawingArea.right - item.offset - item.labelOffset) * rightBoundFactor);\n      const doesTextFit = text => doesTextFitInRect(text, {\n        width,\n        height: maxHeight,\n        angle,\n        measureText: string => getStringSize(string, tickLabelStyle)\n      });\n      shortenedLabels.set(item, ellipsize(item.formattedValue.toString(), doesTextFit));\n    }\n  }\n  return shortenedLabels;\n}", "import { warnOnce } from '@mui/x-internals/warning';\nimport { deg2rad } from \"./angleConversion.js\";\nconst ANGLE_APPROX = 5; // Angle (in deg) for which we approximate the rectangle as perfectly horizontal/vertical\n\n/**\n * Return the minimal translation along the x-axis to avoid overflow of a rectangle of a given width, height, and rotation.\n * This assumes that all rectangles have the same height and angle between -90 and 90.\n * Otherwise it would be problematic because you need the height/width of the next rectangle to do the correct computation.\n * @param width the side along the x-axis.\n * @param height the side along the y-axis.\n * @param angle the rotation in degrees.\n */\nexport function getMinXTranslation(width, height, angle = 0) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (angle > 90 && angle < -90) {\n      warnOnce([`MUI X Charts: It seems you applied an angle larger than 90° or smaller than -90° to an axis text.`, `This could cause some text overlapping.`, `If you encounter a use case where it's needed, please open an issue.`]);\n    }\n  }\n  const standardAngle = Math.min(Math.abs(angle) % 180, Math.abs(Math.abs(angle) % 180 - 180) % 180); // Map from R to [0, 90]\n\n  if (standardAngle < ANGLE_APPROX) {\n    // It's nearly horizontal\n    return width;\n  }\n  if (standardAngle > 90 - ANGLE_APPROX) {\n    // It's nearly vertical\n    return height;\n  }\n  const radAngle = deg2rad(standardAngle);\n  const angleSwich = Math.atan2(height, width);\n  if (radAngle < angleSwich) {\n    return width / Math.cos(radAngle);\n  }\n  return height / Math.sin(radAngle);\n}", "'use client';\n\nimport { getMinXTranslation } from \"../internals/geometry.js\";\nimport { getWordsByLines } from \"../internals/getWordsByLines.js\";\n\n/* Returns a set of indices of the tick labels that should be visible.  */\nexport function getVisibleLabels(xTicks, {\n  tickLabelStyle: style,\n  tickLabelInterval,\n  tickLabelMinGap,\n  reverse,\n  isMounted,\n  isXInside\n}) {\n  const getTickLabelSize = tick => {\n    if (!isMounted || tick.formattedValue === undefined) {\n      return {\n        width: 0,\n        height: 0\n      };\n    }\n    const tickSizes = getWordsByLines({\n      style,\n      needsComputation: true,\n      text: tick.formattedValue\n    });\n    return {\n      width: Math.max(...tickSizes.map(size => size.width)),\n      height: Math.max(tickSizes.length * tickSizes[0].height)\n    };\n  };\n  if (typeof tickLabelInterval === 'function') {\n    return new Set(xTicks.filter((item, index) => tickLabelInterval(item.value, index)));\n  }\n\n  // Filter label to avoid overlap\n  let previousTextLimit = 0;\n  const direction = reverse ? -1 : 1;\n  return new Set(xTicks.filter((item, labelIndex) => {\n    const {\n      offset,\n      labelOffset\n    } = item;\n    const textPosition = offset + labelOffset;\n    if (labelIndex > 0 && direction * textPosition < direction * (previousTextLimit + tickLabelMinGap)) {\n      return false;\n    }\n    if (!isXInside(textPosition)) {\n      return false;\n    }\n\n    /* Measuring text width is expensive, so we need to delay it as much as possible to improve performance. */\n    const {\n      width,\n      height\n    } = getTickLabelSize(item);\n    const distance = getMinXTranslation(width, height, style?.angle);\n    const currentTextLimit = textPosition - direction * distance / 2;\n    if (labelIndex > 0 && direction * currentTextLimit < direction * (previousTextLimit + tickLabelMinGap)) {\n      // Except for the first label, we skip all label that overlap with the last accepted.\n      // Notice that the early return prevents `previousTextLimit` from being updated.\n      return false;\n    }\n    previousTextLimit = textPosition + direction * distance / 2;\n    return true;\n  }));\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"scale\", \"tickNumber\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useThemeProps, styled, useTheme } from '@mui/material/styles';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsHydrated } from \"../hooks/useIsHydrated.js\";\nimport { getDefaultBaseline, getDefaultTextAnchor } from \"../ChartsText/defaultTextPlacement.js\";\nimport { getStringSize } from \"../internals/domUtils.js\";\nimport { useTicks } from \"../hooks/useTicks.js\";\nimport { useDrawingArea } from \"../hooks/useDrawingArea.js\";\nimport { AxisRoot } from \"../internals/components/AxisSharedComponents.js\";\nimport { ChartsText } from \"../ChartsText/index.js\";\nimport { getAxisUtilityClass } from \"../ChartsAxis/axisClasses.js\";\nimport { isInfinity } from \"../internals/isInfinity.js\";\nimport { isBandScale } from \"../internals/isBandScale.js\";\nimport { useChartContext } from \"../context/ChartProvider/index.js\";\nimport { useYAxes } from \"../hooks/index.js\";\nimport { invertTextAnchor } from \"../internals/invertTextAnchor.js\";\nimport { shortenLabels } from \"./shortenLabels.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    position,\n    id\n  } = ownerState;\n  const slots = {\n    root: ['root', 'directionY', position, `id-${id}`],\n    line: ['line'],\n    tickContainer: ['tickContainer'],\n    tick: ['tick'],\n    tickLabel: ['tickLabel'],\n    label: ['label']\n  };\n  return composeClasses(slots, getAxisUtilityClass, classes);\n};\n\n/* Gap between a tick and its label. */\nconst TICK_LABEL_GAP = 2;\n/* Gap between the axis label and tick labels. */\nconst AXIS_LABEL_TICK_LABEL_GAP = 2;\nconst YAxisRoot = styled(AxisRoot, {\n  name: 'MuiChartsYAxis',\n  slot: 'Root'\n})({});\nconst defaultProps = {\n  disableLine: false,\n  disableTicks: false,\n  tickSize: 6\n};\n\n/**\n * Demos:\n *\n * - [Axis](https://mui.com/x/react-charts/axis/)\n *\n * API:\n *\n * - [ChartsYAxis API](https://mui.com/x/api/charts/charts-y-axis/)\n */\nfunction ChartsYAxis(inProps) {\n  const {\n    yAxisIds,\n    yAxis\n  } = useYAxes();\n  const _yAxis = yAxis[inProps.axisId ?? yAxisIds[0]],\n    {\n      scale: yScale,\n      tickNumber\n    } = _yAxis,\n    settings = _objectWithoutPropertiesLoose(_yAxis, _excluded);\n  const themedProps = useThemeProps({\n    props: _extends({}, settings, inProps),\n    name: 'MuiChartsYAxis'\n  });\n  const defaultizedProps = _extends({}, defaultProps, themedProps);\n  const {\n    position,\n    disableLine,\n    disableTicks,\n    label,\n    labelStyle,\n    tickLabelStyle,\n    tickSize: tickSizeProp,\n    valueFormatter,\n    slots,\n    slotProps,\n    tickPlacement,\n    tickLabelPlacement,\n    tickInterval,\n    tickLabelInterval,\n    sx,\n    offset,\n    width: axisWidth\n  } = defaultizedProps;\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const isHydrated = useIsHydrated();\n  const classes = useUtilityClasses(defaultizedProps);\n  const {\n    instance\n  } = useChartContext();\n  const drawingArea = useDrawingArea();\n  const {\n    left,\n    top,\n    width,\n    height\n  } = drawingArea;\n  const tickSize = disableTicks ? 4 : tickSizeProp;\n  const yTicks = useTicks({\n    scale: yScale,\n    tickNumber,\n    valueFormatter,\n    tickPlacement,\n    tickLabelPlacement,\n    tickInterval,\n    direction: 'y'\n  });\n  const positionSign = position === 'right' ? 1 : -1;\n  const tickFontSize = typeof tickLabelStyle?.fontSize === 'number' ? tickLabelStyle.fontSize : 12;\n  const Line = slots?.axisLine ?? 'line';\n  const Tick = slots?.axisTick ?? 'line';\n  const TickLabel = slots?.axisTickLabel ?? ChartsText;\n  const Label = slots?.axisLabel ?? ChartsText;\n  const defaultTextAnchor = getDefaultTextAnchor((position === 'right' ? -90 : 90) - (tickLabelStyle?.angle ?? 0));\n  const defaultDominantBaseline = getDefaultBaseline((position === 'right' ? -90 : 90) - (tickLabelStyle?.angle ?? 0));\n  const axisTickLabelProps = useSlotProps({\n    elementType: TickLabel,\n    externalSlotProps: slotProps?.axisTickLabel,\n    additionalProps: {\n      style: _extends({}, theme.typography.caption, {\n        fontSize: tickFontSize,\n        textAnchor: isRtl ? invertTextAnchor(defaultTextAnchor) : defaultTextAnchor,\n        dominantBaseline: defaultDominantBaseline\n      }, tickLabelStyle)\n    },\n    className: classes.tickLabel,\n    ownerState: {}\n  });\n  const axisLabelProps = useSlotProps({\n    elementType: Label,\n    externalSlotProps: slotProps?.axisLabel,\n    additionalProps: {\n      style: _extends({}, theme.typography.body1, {\n        lineHeight: 1,\n        fontSize: 14,\n        angle: positionSign * 90,\n        textAnchor: 'middle',\n        dominantBaseline: 'text-before-edge'\n      }, labelStyle)\n    },\n    ownerState: {}\n  });\n  const lineSlotProps = useSlotProps({\n    elementType: Line,\n    externalSlotProps: slotProps?.axisLine,\n    additionalProps: {\n      strokeLinecap: 'square'\n    },\n    ownerState: {}\n  });\n  const domain = yScale.domain();\n  const ordinalAxis = isBandScale(yScale);\n\n  // Skip axis rendering if no data is available\n  // - The domain is an empty array for band/point scales.\n  // - The domains contains Infinity for continuous scales.\n  // - The position is 'none'.\n  if (ordinalAxis && domain.length === 0 || !ordinalAxis && domain.some(isInfinity) || position === 'none') {\n    return null;\n  }\n  const labelRefPoint = {\n    x: positionSign * axisWidth,\n    y: top + height / 2\n  };\n  /* If there's an axis title, the tick labels have less space to render  */\n  const tickLabelsMaxWidth = Math.max(0, axisWidth - (label ? getStringSize(label, axisLabelProps.style).height + AXIS_LABEL_TICK_LABEL_GAP : 0) - tickSize - TICK_LABEL_GAP);\n  const tickLabels = isHydrated ? shortenLabels(yTicks, drawingArea, tickLabelsMaxWidth, isRtl, axisTickLabelProps.style) : new Map(Array.from(yTicks).map(item => [item, item.formattedValue]));\n  return /*#__PURE__*/_jsxs(YAxisRoot, {\n    transform: `translate(${position === 'right' ? left + width + offset : left - offset}, 0)`,\n    className: classes.root,\n    sx: sx,\n    children: [!disableLine && /*#__PURE__*/_jsx(Line, _extends({\n      y1: top,\n      y2: top + height,\n      className: classes.line\n    }, lineSlotProps)), yTicks.map((item, index) => {\n      const {\n        offset: tickOffset,\n        labelOffset,\n        value\n      } = item;\n      const xTickLabel = positionSign * (tickSize + TICK_LABEL_GAP);\n      const yTickLabel = labelOffset;\n      const skipLabel = typeof tickLabelInterval === 'function' && !tickLabelInterval?.(value, index);\n      const showLabel = instance.isYInside(tickOffset);\n      const tickLabel = tickLabels.get(item);\n      if (!showLabel) {\n        return null;\n      }\n      return /*#__PURE__*/_jsxs(\"g\", {\n        transform: `translate(0, ${tickOffset})`,\n        className: classes.tickContainer,\n        children: [!disableTicks && /*#__PURE__*/_jsx(Tick, _extends({\n          x2: positionSign * tickSize,\n          className: classes.tick\n        }, slotProps?.axisTick)), tickLabel !== undefined && !skipLabel && /*#__PURE__*/_jsx(TickLabel, _extends({\n          x: xTickLabel,\n          y: yTickLabel,\n          text: tickLabel\n        }, axisTickLabelProps))]\n      }, index);\n    }), label && isHydrated && /*#__PURE__*/_jsx(\"g\", {\n      className: classes.label,\n      children: /*#__PURE__*/_jsx(Label, _extends({}, labelRefPoint, axisLabelProps, {\n        text: label\n      }))\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsYAxis.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  axis: PropTypes.oneOf(['y']),\n  /**\n   * The id of the axis to render.\n   * If undefined, it will be the first defined axis.\n   */\n  axisId: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * If true, the axis line is disabled.\n   * @default false\n   */\n  disableLine: PropTypes.bool,\n  /**\n   * If true, the ticks are disabled.\n   * @default false\n   */\n  disableTicks: PropTypes.bool,\n  /**\n   * The fill color of the axis text.\n   * @default 'currentColor'\n   */\n  fill: PropTypes.string,\n  /**\n   * The label of the axis.\n   */\n  label: PropTypes.string,\n  /**\n   * The style applied to the axis label.\n   */\n  labelStyle: PropTypes.object,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The stroke color of the axis line.\n   * @default 'currentColor'\n   */\n  stroke: PropTypes.string,\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Defines which ticks are displayed.\n   * Its value can be:\n   * - 'auto' In such case the ticks are computed based on axis scale and other parameters.\n   * - a filtering function of the form `(value, index) => boolean` which is available only if the axis has \"point\" scale.\n   * - an array containing the values where ticks should be displayed.\n   * @see See {@link https://mui.com/x/react-charts/axis/#fixed-tick-positions}\n   * @default 'auto'\n   */\n  tickInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.array, PropTypes.func]),\n  /**\n   * Defines which ticks get its label displayed. Its value can be:\n   * - 'auto' In such case, labels are displayed if they do not overlap with the previous one.\n   * - a filtering function of the form (value, index) => boolean. Warning: the index is tick index, not data ones.\n   * @default 'auto'\n   */\n  tickLabelInterval: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.func]),\n  /**\n   * The placement of ticks label. Can be the middle of the band, or the tick position.\n   * Only used if scale is 'band'.\n   * @default 'middle'\n   */\n  tickLabelPlacement: PropTypes.oneOf(['middle', 'tick']),\n  /**\n   * The style applied to ticks text.\n   */\n  tickLabelStyle: PropTypes.object,\n  /**\n   * Maximal step between two ticks.\n   * When using time data, the value is assumed to be in ms.\n   * Not supported by categorical axis (band, points).\n   */\n  tickMaxStep: PropTypes.number,\n  /**\n   * Minimal step between two ticks.\n   * When using time data, the value is assumed to be in ms.\n   * Not supported by categorical axis (band, points).\n   */\n  tickMinStep: PropTypes.number,\n  /**\n   * The number of ticks. This number is not guaranteed.\n   * Not supported by categorical axis (band, points).\n   */\n  tickNumber: PropTypes.number,\n  /**\n   * The placement of ticks in regard to the band interval.\n   * Only used if scale is 'band'.\n   * @default 'extremities'\n   */\n  tickPlacement: PropTypes.oneOf(['end', 'extremities', 'middle', 'start']),\n  /**\n   * The size of the ticks.\n   * @default 6\n   */\n  tickSize: PropTypes.number\n} : void 0;\nexport { ChartsYAxis };", "'use client';\n\nimport { clampAngle } from \"../internals/clampAngle.js\";\nimport { doesTextFitInRect, ellipsize } from \"../internals/ellipsize.js\";\nimport { getStringSize } from \"../internals/domUtils.js\";\nexport function shortenLabels(visibleLabels, drawingArea, maxWidth, isRtl, tickLabelStyle) {\n  const shortenedLabels = new Map();\n  const angle = clampAngle(tickLabelStyle?.angle ?? 0);\n  let topBoundFactor = 1;\n  let bottomBoundFactor = 1;\n  if (tickLabelStyle?.textAnchor === 'start') {\n    topBoundFactor = Infinity;\n    bottomBoundFactor = 1;\n  } else if (tickLabelStyle?.textAnchor === 'end') {\n    topBoundFactor = 1;\n    bottomBoundFactor = Infinity;\n  } else {\n    topBoundFactor = 2;\n    bottomBoundFactor = 2;\n  }\n  if (angle > 180) {\n    [topBoundFactor, bottomBoundFactor] = [bottomBoundFactor, topBoundFactor];\n  }\n  if (isRtl) {\n    [topBoundFactor, bottomBoundFactor] = [bottomBoundFactor, topBoundFactor];\n  }\n  for (const item of visibleLabels) {\n    if (item.formattedValue) {\n      // That maximum height of the tick depends on its proximity to the axis bounds.\n      const height = Math.min((item.offset + item.labelOffset) * topBoundFactor, (drawingArea.top + drawingArea.height + drawingArea.bottom - item.offset - item.labelOffset) * bottomBoundFactor);\n      const doesTextFit = text => doesTextFitInRect(text, {\n        width: maxWidth,\n        height,\n        angle,\n        measureText: string => getStringSize(string, tickLabelStyle)\n      });\n      shortenedLabels.set(item, ellipsize(item.formattedValue.toString(), doesTextFit));\n    }\n  }\n  return shortenedLabels;\n}", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getAxisHighlightUtilityClass } from \"./chartsAxisHighlightClasses.js\";\nimport ChartsYHighlight from \"./ChartsYAxisHighlight.js\";\nimport ChartsXHighlight from \"./ChartsXAxisHighlight.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAxisHighlightUtilityClass);\n};\n\n/**\n * Demos:\n *\n * - [Custom components](https://mui.com/x/react-charts/components/)\n *\n * API:\n *\n * - [ChartsAxisHighlight API](https://mui.com/x/api/charts/charts-axis-highlight/)\n */\nfunction ChartsAxisHighlight(props) {\n  const {\n    x: xAxisHighlight,\n    y: yAxisHighlight\n  } = props;\n  const classes = useUtilityClasses();\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [xAxisHighlight && /*#__PURE__*/_jsx(ChartsXHighlight, {\n      type: xAxisHighlight,\n      classes: classes\n    }), yAxisHighlight && /*#__PURE__*/_jsx(ChartsYHighlight, {\n      type: yAxisHighlight,\n      classes: classes\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsAxisHighlight.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  x: PropTypes.oneOf(['band', 'line', 'none']),\n  y: PropTypes.oneOf(['band', 'line', 'none'])\n} : void 0;\nexport { ChartsAxisHighlight };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getAxisHighlightUtilityClass(slot) {\n  return generateUtilityClass('MuiChartsAxisHighlight', slot);\n}\nexport const chartsAxisHighlightClasses = generateUtilityClasses('MuiChartsAxisHighlight', ['root']);", "'use client';\n\nimport * as React from 'react';\nimport { getValueToPositionMapper } from \"../hooks/useScale.js\";\nimport { isBandScale } from \"../internals/isBandScale.js\";\nimport { useSelector } from \"../internals/store/useSelector.js\";\nimport { useStore } from \"../internals/store/useStore.js\";\nimport { selectorChartsHighlightYAxisValue, selectorChartYAxis } from \"../internals/plugins/featurePlugins/useChartCartesianAxis/index.js\";\nimport { useDrawingArea } from \"../hooks/index.js\";\nimport { ChartsAxisHighlightPath } from \"./ChartsAxisHighlightPath.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function ChartsYHighlight(props) {\n  const {\n    type,\n    classes\n  } = props;\n  const {\n    left,\n    width\n  } = useDrawingArea();\n  const store = useStore();\n  const axisYValues = useSelector(store, selectorChartsHighlightYAxisValue);\n  const yAxes = useSelector(store, selectorChartYAxis);\n  if (axisYValues.length === 0) {\n    return null;\n  }\n  return axisYValues.map(({\n    axisId,\n    value\n  }) => {\n    const yAxis = yAxes.axis[axisId];\n    const yScale = yAxis.scale;\n    const getYPosition = getValueToPositionMapper(yScale);\n    const isBandScaleY = type === 'band' && value !== null && isBandScale(yScale);\n    if (process.env.NODE_ENV !== 'production') {\n      const isError = isBandScaleY && yScale(value) === undefined;\n      if (isError) {\n        console.error([`MUI X Charts: The position value provided for the axis is not valid for the current scale.`, `This probably means something is wrong with the data passed to the chart.`, `The ChartsAxisHighlight component will not be displayed.`].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [isBandScaleY && yScale(value) !== undefined && /*#__PURE__*/_jsx(ChartsAxisHighlightPath, {\n        d: `M ${left} ${\n        // @ts-expect-error, yScale value is checked in the statement above\n        yScale(value) - (yScale.step() - yScale.bandwidth()) / 2} l 0 ${yScale.step()} l ${width} 0 l 0 ${-yScale.step()} Z`,\n        className: classes.root,\n        ownerState: {\n          axisHighlight: 'band'\n        }\n      }), type === 'line' && value !== null && /*#__PURE__*/_jsx(ChartsAxisHighlightPath, {\n        d: `M ${left} ${getYPosition(value)} L ${left + width} ${getYPosition(value)}`,\n        className: classes.root,\n        ownerState: {\n          axisHighlight: 'line'\n        }\n      })]\n    }, `${axisId}-${value}`);\n  });\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { styled } from '@mui/material/styles';\nexport const ChartsAxisHighlightPath = styled('path', {\n  name: 'MuiChartsAxisHighlight',\n  slot: 'Root'\n})(({\n  theme\n}) => ({\n  pointerEvents: 'none',\n  variants: [{\n    props: {\n      axisHighlight: 'band'\n    },\n    style: _extends({\n      fill: 'white',\n      fillOpacity: 0.1\n    }, theme.applyStyles('light', {\n      fill: 'gray'\n    }))\n  }, {\n    props: {\n      axisHighlight: 'line'\n    },\n    style: _extends({\n      strokeDasharray: '5 2',\n      stroke: '#ffffff'\n    }, theme.applyStyles('light', {\n      stroke: '#000000'\n    }))\n  }]\n}));", "'use client';\n\nimport * as React from 'react';\nimport { getValueToPositionMapper } from \"../hooks/useScale.js\";\nimport { isBandScale } from \"../internals/isBandScale.js\";\nimport { useSelector } from \"../internals/store/useSelector.js\";\nimport { useStore } from \"../internals/store/useStore.js\";\nimport { selectorChartsHighlightXAxisValue, selectorChartXAxis } from \"../internals/plugins/featurePlugins/useChartCartesianAxis/index.js\";\nimport { useDrawingArea } from \"../hooks/index.js\";\nimport { ChartsAxisHighlightPath } from \"./ChartsAxisHighlightPath.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default function ChartsXHighlight(props) {\n  const {\n    type,\n    classes\n  } = props;\n  const {\n    top,\n    height\n  } = useDrawingArea();\n  const store = useStore();\n  const axisXValues = useSelector(store, selectorChartsHighlightXAxisValue);\n  const xAxes = useSelector(store, selectorChartXAxis);\n  if (axisXValues.length === 0) {\n    return null;\n  }\n  return axisXValues.map(({\n    axisId,\n    value\n  }) => {\n    const xAxis = xAxes.axis[axisId];\n    const xScale = xAxis.scale;\n    const getXPosition = getValueToPositionMapper(xScale);\n    const isBandScaleX = type === 'band' && value !== null && isBandScale(xScale);\n    if (process.env.NODE_ENV !== 'production') {\n      const isError = isBandScaleX && xScale(value) === undefined;\n      if (isError) {\n        console.error([`MUI X Charts: The position value provided for the axis is not valid for the current scale.`, `This probably means something is wrong with the data passed to the chart.`, `The ChartsAxisHighlight component will not be displayed.`].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/_jsxs(React.Fragment, {\n      children: [isBandScaleX && xScale(value) !== undefined && /*#__PURE__*/_jsx(ChartsAxisHighlightPath\n      // @ts-expect-error, xScale value is checked in the statement above\n      , {\n        d: `M ${xScale(value) - (xScale.step() - xScale.bandwidth()) / 2} ${top} l ${xScale.step()} 0 l 0 ${height} l ${-xScale.step()} 0 Z`,\n        className: classes.root,\n        ownerState: {\n          axisHighlight: 'band'\n        }\n      }), type === 'line' && value !== null && /*#__PURE__*/_jsx(ChartsAxisHighlightPath, {\n        d: `M ${getXPosition(value)} ${top} L ${getXPosition(value)} ${top + height}`,\n        className: classes.root,\n        ownerState: {\n          axisHighlight: 'line'\n        }\n      })]\n    }, `${axisId}-${value}`);\n  });\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useDrawingArea } from \"../hooks/useDrawingArea.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * API:\n *\n * - [ChartsClipPath API](https://mui.com/x/api/charts/charts-clip-path/)\n */\nfunction ChartsClipPath(props) {\n  const {\n    id,\n    offset: offsetProps\n  } = props;\n  const {\n    left,\n    top,\n    width,\n    height\n  } = useDrawingArea();\n  const offset = _extends({\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  }, offsetProps);\n  return /*#__PURE__*/_jsx(\"clipPath\", {\n    id: id,\n    children: /*#__PURE__*/_jsx(\"rect\", {\n      x: left - offset.left,\n      y: top - offset.top,\n      width: width + offset.left + offset.right,\n      height: height + offset.top + offset.bottom\n    })\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsClipPath.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * The id of the clip path.\n   */\n  id: PropTypes.string.isRequired,\n  /**\n   * Offset, in pixels, of the clip path rectangle from the drawing area.\n   *\n   * A positive value will move the rectangle outside the drawing area.\n   */\n  offset: PropTypes.shape({\n    bottom: PropTypes.number,\n    left: PropTypes.number,\n    right: PropTypes.number,\n    top: PropTypes.number\n  })\n} : void 0;\nexport { ChartsClipPath };", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"vertical\", \"horizontal\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useThemeProps } from '@mui/material/styles';\nimport { getChartsGridUtilityClass } from \"./chartsGridClasses.js\";\nimport { useDrawingArea } from \"../hooks/useDrawingArea.js\";\nimport { GridRoot } from \"./styledComponents.js\";\nimport { ChartsGridVertical } from \"./ChartsVerticalGrid.js\";\nimport { ChartsGridHorizontal } from \"./ChartsHorizontalGrid.js\";\nimport { useXAxes, useYAxes } from \"../hooks/useAxis.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ({\n  classes\n}) => {\n  const slots = {\n    root: ['root'],\n    verticalLine: ['line', 'verticalLine'],\n    horizontalLine: ['line', 'horizontalLine']\n  };\n  return composeClasses(slots, getChartsGridUtilityClass, classes);\n};\n/**\n * Demos:\n *\n * - [Axis](https://mui.com/x/react-charts/axis/)\n *\n * API:\n *\n * - [ChartsGrid API](https://mui.com/x/api/charts/charts-axis/)\n */\nfunction ChartsGrid(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiChartsGrid'\n  });\n  const drawingArea = useDrawingArea();\n  const {\n      vertical,\n      horizontal\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    xAxis,\n    xAxisIds\n  } = useXAxes();\n  const {\n    yAxis,\n    yAxisIds\n  } = useYAxes();\n  const classes = useUtilityClasses(props);\n  const horizontalAxis = yAxis[yAxisIds[0]];\n  const verticalAxis = xAxis[xAxisIds[0]];\n  return /*#__PURE__*/_jsxs(GridRoot, _extends({}, other, {\n    className: classes.root,\n    children: [vertical && /*#__PURE__*/_jsx(ChartsGridVertical, {\n      axis: verticalAxis,\n      start: drawingArea.top,\n      end: drawingArea.height + drawingArea.top,\n      classes: classes\n    }), horizontal && /*#__PURE__*/_jsx(ChartsGridHorizontal, {\n      axis: horizontalAxis,\n      start: drawingArea.left,\n      end: drawingArea.width + drawingArea.left,\n      classes: classes\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? ChartsGrid.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * Displays horizontal grid.\n   */\n  horizontal: PropTypes.bool,\n  /**\n   * Displays vertical grid.\n   */\n  vertical: PropTypes.bool\n} : void 0;\nexport { ChartsGrid };", "import generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nexport function getChartsGridUtilityClass(slot) {\n  return generateUtilityClass('MuiChartsGrid', slot);\n}\nexport const chartsGridClasses = generateUtilityClasses('MuiChartsGrid', ['root', 'line', 'horizontalLine', 'verticalLine']);", "import { styled } from '@mui/material/styles';\nimport { chartsGridClasses } from \"./chartsGridClasses.js\";\nexport const GridRoot = styled('g', {\n  name: 'MuiChartsGrid',\n  slot: 'Root',\n  overridesResolver: (props, styles) => [{\n    [`&.${chartsGridClasses.verticalLine}`]: styles.verticalLine\n  }, {\n    [`&.${chartsGridClasses.horizontalLine}`]: styles.horizontalLine\n  }, styles.root]\n})({});\nexport const GridLine = styled('line', {\n  name: 'MuiChartsGrid',\n  slot: 'Line'\n})(({\n  theme\n}) => ({\n  stroke: (theme.vars || theme).palette.divider,\n  shapeRendering: 'crispEdges',\n  strokeWidth: 1\n}));", "import * as React from 'react';\nimport { useTicks } from \"../hooks/useTicks.js\";\nimport { GridLine } from \"./styledComponents.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nexport function ChartsGridVertical(props) {\n  const {\n    axis,\n    start,\n    end,\n    classes\n  } = props;\n  const {\n    scale,\n    tickNumber,\n    tickInterval\n  } = axis;\n  const xTicks = useTicks({\n    scale,\n    tickNumber,\n    tickInterval,\n    direction: 'x'\n  });\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: xTicks.map(({\n      value,\n      offset\n    }) => /*#__PURE__*/_jsx(GridLine, {\n      y1: start,\n      y2: end,\n      x1: offset,\n      x2: offset,\n      className: classes.verticalLine\n    }, `vertical-${value?.getTime?.() ?? value}`))\n  });\n}", "import * as React from 'react';\nimport { useTicks } from \"../hooks/useTicks.js\";\nimport { GridLine } from \"./styledComponents.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @ignore - internal component.\n */\nexport function ChartsGridHorizontal(props) {\n  const {\n    axis,\n    start,\n    end,\n    classes\n  } = props;\n  const {\n    scale,\n    tickNumber,\n    tickInterval\n  } = axis;\n  const yTicks = useTicks({\n    scale,\n    tickNumber,\n    tickInterval,\n    direction: 'y'\n  });\n  return /*#__PURE__*/_jsx(React.Fragment, {\n    children: yTicks.map(({\n      value,\n      offset\n    }) => /*#__PURE__*/_jsx(GridLine, {\n      y1: offset,\n      y2: offset,\n      x1: start,\n      x2: end,\n      className: classes.horizontalLine\n    }, `horizontal-${value?.getTime?.() ?? value}`))\n  });\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"xAxis\", \"yAxis\", \"series\", \"width\", \"height\", \"margin\", \"colors\", \"dataset\", \"sx\", \"axisHighlight\", \"grid\", \"children\", \"slots\", \"slotProps\", \"skipAnimation\", \"loading\", \"layout\", \"onItemClick\", \"highlightedItem\", \"onHighlightChange\", \"borderRadius\", \"barLabel\", \"className\", \"hideLegend\", \"showToolbar\"];\nimport * as React from 'react';\nimport useId from '@mui/utils/useId';\nimport { DEFAULT_X_AXIS_KEY, DEFAULT_Y_AXIS_KEY } from \"../constants/index.js\";\nimport { BAR_CHART_PLUGINS } from \"./BarChart.plugins.js\";\n\n/**\n * A helper function that extracts BarChartProps from the input props\n * and returns an object with props for the children components of BarChart.\n *\n * @param props The input props for BarChart\n * @returns An object with props for the children components of BarChart\n */\nexport const useBarChartProps = props => {\n  const {\n      xAxis,\n      yAxis,\n      series,\n      width,\n      height,\n      margin,\n      colors,\n      dataset,\n      sx,\n      axisHighlight,\n      grid,\n      children,\n      slots,\n      slotProps,\n      skipAnimation,\n      loading,\n      layout,\n      onItemClick,\n      highlightedItem,\n      onHighlightChange,\n      borderRadius,\n      barLabel,\n      className\n    } = props,\n    rest = _objectWithoutPropertiesLoose(props, _excluded);\n  const id = useId();\n  const clipPathId = `${id}-clip-path`;\n  const hasHorizontalSeries = layout === 'horizontal' || layout === undefined && series.some(item => item.layout === 'horizontal');\n  const defaultBandXAxis = React.useMemo(() => [{\n    id: DEFAULT_X_AXIS_KEY,\n    scaleType: 'band',\n    data: Array.from({\n      length: Math.max(...series.map(s => (s.data ?? dataset ?? []).length))\n    }, (_, index) => index)\n  }], [dataset, series]);\n  const defaultBandYAxis = React.useMemo(() => [{\n    id: DEFAULT_Y_AXIS_KEY,\n    scaleType: 'band',\n    data: Array.from({\n      length: Math.max(...series.map(s => (s.data ?? dataset ?? []).length))\n    }, (_, index) => index)\n  }], [dataset, series]);\n  const seriesWithDefault = React.useMemo(() => series.map(s => _extends({\n    type: 'bar'\n  }, s, {\n    layout: hasHorizontalSeries ? 'horizontal' : 'vertical'\n  })), [hasHorizontalSeries, series]);\n  const defaultXAxis = hasHorizontalSeries ? undefined : defaultBandXAxis;\n  const processedXAxis = React.useMemo(() => {\n    if (!xAxis) {\n      return defaultXAxis;\n    }\n    return hasHorizontalSeries ? xAxis : xAxis.map(axis => _extends({\n      scaleType: 'band'\n    }, axis));\n  }, [defaultXAxis, hasHorizontalSeries, xAxis]);\n  const defaultYAxis = hasHorizontalSeries ? defaultBandYAxis : undefined;\n  const processedYAxis = React.useMemo(() => {\n    if (!yAxis) {\n      return defaultYAxis;\n    }\n    return hasHorizontalSeries ? yAxis.map(axis => _extends({\n      scaleType: 'band'\n    }, axis)) : yAxis;\n  }, [defaultYAxis, hasHorizontalSeries, yAxis]);\n  const chartContainerProps = _extends({}, rest, {\n    series: seriesWithDefault,\n    width,\n    height,\n    margin,\n    colors,\n    dataset,\n    xAxis: processedXAxis,\n    yAxis: processedYAxis,\n    highlightedItem,\n    onHighlightChange,\n    disableAxisListener: slotProps?.tooltip?.trigger !== 'axis' && axisHighlight?.x === 'none' && axisHighlight?.y === 'none',\n    className,\n    skipAnimation,\n    plugins: BAR_CHART_PLUGINS\n  });\n  const barPlotProps = {\n    onItemClick,\n    slots,\n    slotProps,\n    borderRadius,\n    barLabel\n  };\n  const gridProps = {\n    vertical: grid?.vertical,\n    horizontal: grid?.horizontal\n  };\n  const clipPathGroupProps = {\n    clipPath: `url(#${clipPathId})`\n  };\n  const clipPathProps = {\n    id: clipPathId\n  };\n  const overlayProps = {\n    slots,\n    slotProps,\n    loading\n  };\n  const chartsAxisProps = {\n    slots,\n    slotProps\n  };\n  const axisHighlightProps = _extends({}, hasHorizontalSeries ? {\n    y: 'band'\n  } : {\n    x: 'band'\n  }, axisHighlight);\n  const legendProps = {\n    slots,\n    slotProps\n  };\n  const chartsWrapperProps = {\n    sx,\n    legendPosition: props.slotProps?.legend?.position,\n    legendDirection: props.slotProps?.legend?.direction\n  };\n  return {\n    chartsWrapperProps,\n    chartContainerProps,\n    barPlotProps,\n    gridProps,\n    clipPathProps,\n    clipPathGroupProps,\n    overlayProps,\n    chartsAxisProps,\n    axisHighlightProps,\n    legendProps,\n    children\n  };\n};", "import { useChartZAxis } from \"../internals/plugins/featurePlugins/useChartZAxis/index.js\";\nimport { useChartCartesianAxis } from \"../internals/plugins/featurePlugins/useChartCartesianAxis/index.js\";\nimport { useChartInteraction } from \"../internals/plugins/featurePlugins/useChartInteraction/index.js\";\nimport { useChartHighlight } from \"../internals/plugins/featurePlugins/useChartHighlight/index.js\";\nexport const BAR_CHART_PLUGINS = [useChartZAxis, useChartCartesianAxis, useChartInteraction, useChartHighlight];"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAAA,UAAuB;AACvB,IAAAC,sBAAsB;;;ACCtB,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;;;ACHf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACO,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,eAAe,SAAS,QAAQ,CAAC;AAC5G,IAAM,oBAAoB,gBAAc;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,EAAE,IAAI,iBAAiB,eAAe,WAAW,OAAO;AAAA,EACnF;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;;;ACbA,IAAAC,SAAuB;AACvB,wBAAsB;;;ACDtB,YAAuB;AAEvB,yBAA4B;AAH5B,IAAM,YAAY,CAAC,cAAc,iBAAiB,MAAM,aAAa,WAAW,SAAS;AAIlF,SAAS,mBAAmB,OAAO;AACxC,QAAM;AAAA,IACF;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,gBAAgB,cAAc,KAAK;AACzC,aAAoB,mBAAAC,KAAK,QAAQ,SAAS,CAAC,GAAG,OAAO;AAAA,IACnD,QAAQ,WAAW,gBAAgB,qBAAqB;AAAA,IACxD,SAAS,WAAW,UAAU,MAAM;AAAA,IACpC,oBAAoB,WAAW,iBAAiB;AAAA,IAChD,cAAc,WAAW,WAAW;AAAA,EACtC,GAAG,aAAa,CAAC;AACnB;;;ADRA,IAAAC,sBAA4B;AAR5B,IAAMC,aAAY,CAAC,MAAM,aAAa,WAAW,SAAS,SAAS,aAAa,SAAS,WAAW,iBAAiB,UAAU,KAAK,WAAW,KAAK,WAAW,SAAS,QAAQ;AAShL,SAAS,WAAW,OAAO;AACzB,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOA,UAAS;AACxD,QAAM,mBAAmB,wBAAwB;AAAA,IAC/C,MAAM;AAAA,IACN,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AAAA,IACrB,UAAU;AAAA,IACV;AAAA,EACF,CAAC;AACD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,OAAM,+BAAO,QAAO;AAC1B,QAAM,WAAW,qBAAa;AAAA,IAC5B,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,wBAAwB;AAAA,IACxB,iBAAiB,SAAS,CAAC,GAAG,kBAAkB;AAAA,MAC9C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ,UAAU,YAAY;AAAA,MAC9B,QAAQ;AAAA,MACR,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,CAAC;AAAA,IACD,WAAW,QAAQ;AAAA,IACnB;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAC,KAAK,KAAK,SAAS,CAAC,GAAG,QAAQ,CAAC;AACtD;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,SAAS,kBAAAC,QAAU;AAAA,EACnB,WAAW,kBAAAA,QAAU,OAAO;AAAA,EAC5B,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EAC9D,QAAQ,kBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC,EAAE;AAAA,EACpD,eAAe,kBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,kBAAAA,QAAU;AAAA,EACjB,SAAS,kBAAAA,QAAU,OAAO;AAAA,EAC1B,SAAS,kBAAAA,QAAU,OAAO;AAC5B,IAAI;;;AEnGJ,IAAAC,SAAuB;;;ACChB,IAAM,YAAY,CAAC,QAAQ;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,MAAM;AACJ,MAAI,CAAC,cAAc;AACjB,WAAO;AAAA,EACT;AACA,QAAM,aAAa,WAAW;AAC9B,MAAI,WAAW,eAAe,cAAc,eAAe,CAAC,cAAc,cAAc;AACtF,WAAO;AAAA,EACT;AACA,MAAI,WAAW,gBAAgB,cAAc,eAAe,CAAC,cAAc,cAAc;AACvF,WAAO;AAAA,EACT;AACA,MAAI,WAAW,mBAAmB,cAAc,eAAe,CAAC,cAAc,cAAc;AAC1F,WAAO;AAAA,EACT;AACA,MAAI,WAAW,kBAAkB,cAAc,eAAe,CAAC,cAAc,cAAc;AACzF,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;ADpBA,IAAAC,sBAA4B;AAL5B,IAAMC,aAAY,CAAC,UAAU,KAAK,KAAK,SAAS,UAAU,eAAe;AAMzE,SAAS,cAAc,MAAM,cAAc,YAAY;AACrD,QAAM,aAAa,SAAS,CAAC,GAAG,YAAY;AAAA,IAC1C;AAAA,EACF,CAAC;AACD,QAAM,UAAU,KAAK,IAAI,MAAM,UAAU,YAAY,UAAU,CAAC;AAChE,QAAM,WAAW,KAAK,IAAI,MAAM,UAAU,aAAa,UAAU,CAAC;AAClE,QAAM,cAAc,KAAK,IAAI,MAAM,UAAU,gBAAgB,UAAU,CAAC;AACxE,QAAM,aAAa,KAAK,IAAI,MAAM,UAAU,eAAe,UAAU,CAAC;AACtE,SAAO,mBAAmB,OAAO,MAAM,QAAQ,MAAM,WAAW,MAAM,UAAU;AAClF;AACA,SAAS,6BAA6B,MAAM,IAAI;AAC9C,QAAM,eAAe,eAAkB,KAAK,GAAG,GAAG,CAAC;AACnD,QAAM,eAAe,eAAkB,KAAK,GAAG,GAAG,CAAC;AACnD,QAAM,mBAAmB,eAAkB,KAAK,OAAO,GAAG,KAAK;AAC/D,QAAM,oBAAoB,eAAkB,KAAK,QAAQ,GAAG,MAAM;AAClE,QAAM,0BAA0B,eAAkB,KAAK,cAAc,GAAG,YAAY;AACpF,SAAO,OAAK;AACV,WAAO;AAAA,MACL,GAAG,aAAa,CAAC;AAAA,MACjB,GAAG,aAAa,CAAC;AAAA,MACjB,OAAO,iBAAiB,CAAC;AAAA,MACzB,QAAQ,kBAAkB,CAAC;AAAA,MAC3B,cAAc,wBAAwB,CAAC;AAAA,IACzC;AAAA,EACF;AACF;AACO,SAAS,sBAAsB,OAAO;AAC3C,QAAM,eAAe;AAAA,IACnB,GAAG,MAAM;AAAA,IACT,GAAG,MAAM,KAAK,MAAM,WAAW,WAAW,aAAa,MAAM,SAAS;AAAA,IACtE,OAAO,MAAM,WAAW,WAAW,aAAa,MAAM,QAAQ;AAAA,IAC9D,QAAQ,MAAM,WAAW,WAAW,aAAa,IAAI,MAAM;AAAA,IAC3D,cAAc,MAAM;AAAA,EACtB;AACA,SAAO,WAAW;AAAA,IAChB,GAAG,MAAM;AAAA,IACT,GAAG,MAAM;AAAA,IACT,OAAO,MAAM;AAAA,IACb,QAAQ,MAAM;AAAA,IACd,cAAc,MAAM;AAAA,EACtB,GAAG;AAAA,IACD,oBAAoB;AAAA,IACpB,gBAAgB,QAAM;AAAA,MACpB,GAAG,EAAE;AAAA,MACL,GAAG,EAAE;AAAA,MACL,OAAO,EAAE;AAAA,MACT,QAAQ,EAAE;AAAA,MACV,OAAO;AAAA,QACL,UAAU,cAAc,MAAM,WAAW,WAAW,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,MAAM,UAAU;AAAA,MACvH;AAAA,IACF;AAAA,IACA,WAAW,SAAS,eAAe;AACjC,cAAQ,aAAa,KAAK,cAAc,EAAE,SAAS,CAAC;AACpD,cAAQ,aAAa,KAAK,cAAc,EAAE,SAAS,CAAC;AACpD,cAAQ,aAAa,SAAS,cAAc,MAAM,SAAS,CAAC;AAC5D,cAAQ,aAAa,UAAU,cAAc,OAAO,SAAS,CAAC;AAC9D,cAAQ,MAAM,WAAW,cAAc,MAAM;AAAA,IAC/C;AAAA,IACA;AAAA,IACA,MAAM,MAAM;AAAA,IACZ,KAAK,MAAM;AAAA,EACb,CAAC;AACH;AACA,SAAS,YAAY,OAAO;AAC1B,QAAM,gBAAgB,sBAAsB,SAAS,CAAC,GAAG,OAAO;AAAA,IAC9D,cAAc,MAAM,WAAW,gBAAgB;AAAA,EACjD,CAAC,CAAC;AACF,aAAoB,oBAAAC,KAAK,QAAQ,SAAS,CAAC,GAAG,aAAa,CAAC;AAC9D;AAIA,SAAS,YAAY,OAAO;AAC1B,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,OAAO,8BAA8B,OAAOD,UAAS;AACvD,MAAI,CAAC,MAAM,gBAAgB,MAAM,gBAAgB,GAAG;AAClD,WAAO;AAAA,EACT;AACA,aAAoB,oBAAAC,KAAK,YAAY;AAAA,IACnC,IAAI;AAAA,IACJ,cAAuB,oBAAAA,KAAK,aAAa;AAAA,MACvC,YAAY;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;;;AEvGA,IAAAC,SAAuB;;;ACCvB,IAAAC,SAAuB;AAEvB,IAAAC,qBAAsB;;;ACHf,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACO,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,eAAe,SAAS,SAAS,CAAC;AACzG,IAAMC,qBAAoB,gBAAc;AAC7C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,UAAU,QAAQ,IAAI,iBAAiB,eAAe,WAAW,SAAS,CAAC,iBAAiB,SAAS;AAAA,EACtH;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;;;ACnBO,IAAM,cAAc,aAAW;AACpC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,aAAa,SAAS;AAExB,WAAO,QAAQ,+BAAO,aAAa;AAAA,EACrC;AACA,SAAO,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,MACH;AAAA,MACA;AAAA,IACF;AAAA,EACF,CAAC;AACH;;;AClBA,IAAAC,SAAuB;AAEvB,IAAAC,qBAAsB;AAGtB,IAAAC,sBAA4B;AAN5B,IAAMC,aAAY,CAAC,YAAY,aAAa,SAAS,WAAW,iBAAiB,WAAW,iBAAiB,UAAU,WAAW,SAAS;AAOpI,IAAM,oBAAoB,eAAO,QAAQ;AAAA,EAC9C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,GAAG,WAAW,CAAC;AAAA,IACjC,CAAC,KAAK,gBAAgB,KAAK,EAAE,GAAG,OAAO;AAAA,EACzC,GAAG;AAAA,IACD,CAAC,KAAK,gBAAgB,WAAW,EAAE,GAAG,OAAO;AAAA,EAC/C,GAAG,OAAO,IAAI;AAChB,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAG;AArBH;AAqBM,kBAAS,CAAC,IAAG,oCAAO,eAAP,mBAAmB,OAAO;AAAA,IAC3C,QAAQ;AAAA,IACR,OAAO,uBAAM,QAAQ,UAAd,mBAAsB,YAAtB,mBAA+B,SAA/B,mBAAqC;AAAA,IAC5C,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,SAAS;AAAA,IACT,CAAC,KAAK,gBAAgB,KAAK,EAAE,GAAG;AAAA,MAC9B,SAAS;AAAA,IACX;AAAA,EACF,CAAC;AAAA,CAAC;AACF,SAAS,SAAS,SAAS;AACzB,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,aAAa,8BAA8B,OAAOA,UAAS;AACjE,QAAM,gBAAgB,mBAAmB,KAAK;AAC9C,aAAoB,oBAAAC,KAAK,mBAAmB,SAAS,CAAC,GAAG,YAAY,aAAa,CAAC;AACrF;AACA,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,SAAS,mBAAAC,QAAU;AAAA,EACnB,WAAW,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI5B,QAAQ,mBAAAA,QAAU,OAAO;AAAA,EACzB,SAAS,mBAAAA,QAAU,KAAK;AAAA,EACxB,eAAe,mBAAAA,QAAU,KAAK;AAAA,EAC9B,QAAQ,mBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC,EAAE;AAAA,EACpD,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EACpE,eAAe,mBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA,EAI9B,OAAO,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxB,GAAG,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,GAAG,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU,OAAO;AAC5B,IAAI;;;AHnEJ,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,YAAY,WAAW,SAAS,aAAa,YAAY,SAAS,aAAa,WAAW,WAAW,KAAK,KAAK,SAAS,UAAU,SAAS,iBAAiB,QAAQ;AAAvL,IACEC,cAAa,CAAC,YAAY;AAY5B,SAAS,aAAa,OAAO;AAC3B,QAAM;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,UAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,mBAAmB;AAAA,IACrB;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,aAAa;AAAA,IACjB;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAUE,mBAAkB,UAAU;AAC5C,QAAM,aAAY,+BAAO,aAAY;AACrC,QAAM,gBAAgB,qBAAa;AAAA,IAC/B,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB,SAAS,CAAC,GAAG,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,IACD;AAAA,EACF,CAAC,GACD;AAAA,IACE,YAAY;AAAA,EACd,IAAI,eACJ,gBAAgB,8BAA8B,eAAeD,WAAU;AACzE,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AACA,QAAM,qBAAqB,YAAY;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,CAAC,oBAAoB;AACvB,WAAO;AAAA,EACT;AACA,aAAoB,oBAAAE,KAAK,WAAW,SAAS,CAAC,GAAG,eAAe,oBAAoB;AAAA,IAClF,UAAU;AAAA,EACZ,CAAC,CAAC;AACJ;AACA,OAAwC,aAAa,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY/D,UAAU,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA,EAC1E,SAAS,mBAAAA,QAAU;AAAA,EACnB,OAAO,mBAAAA,QAAU,OAAO;AAAA,EACxB,WAAW,mBAAAA,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA,EAI5B,QAAQ,mBAAAA,QAAU,OAAO;AAAA,EACzB,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpE,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,mBAAAA,QAAU,OAAO;AAC1B,IAAI;;;AI9HG,SAAS,mBAAmB,MAAM;AACvC,SAAO,qBAAqB,UAAU,IAAI;AAC5C;AACO,IAAM,aAAa,uBAAuB,UAAU,CAAC,QAAQ,UAAU,cAAc,CAAC;AACtF,IAAMC,qBAAoB,aAAW;AAC1C,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,QAAQ,CAAC,QAAQ;AAAA,IACjB,cAAc,CAAC,cAAc;AAAA,EAC/B;AACA,SAAO,eAAe,OAAO,oBAAoB,OAAO;AAC1D;;;ALRA,IAAAC,sBAA4B;AAJ5B,IAAMC,aAAY,CAAC,QAAQ,eAAe;AAQ1C,SAAS,aAAa,OAAO;AAC3B,QAAM;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOA,UAAS;AACxD,QAAM,UAAUC,mBAAkB;AAClC,aAAoB,oBAAAC,KAAW,iBAAU;AAAA,IACvC,UAAU,KAAK,QAAQ,CAAC;AAAA,MACtB;AAAA,MACA;AAAA,IACF,UAAmB,oBAAAA,KAAK,KAAK;AAAA,MAC3B,WAAW,QAAQ;AAAA,MACnB,eAAe;AAAA,MACf,UAAU,KAAK,IAAI,CAAC;AAAA,QAClB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,UAAmB,oBAAAA,KAAK,cAAc,SAAS;AAAA,QAC7C;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,iBAAiB;AAAA,QAChC,QAAQ,UAAU;AAAA,MACpB,GAAG,KAAK,GAAG,SAAS,CAAC;AAAA,IACvB,GAAG,QAAQ,CAAC;AAAA,EACd,CAAC;AACH;;;AMrCO,SAAS,+BAA+B;AAC7C,QAAM,QAAQ,SAAS;AACvB,QAAM,gBAAgB,YAAY,OAAO,8BAA8B;AACvE,SAAO;AACT;;;ACfA,IAAM,iBAAiB,CAAC,eAAe,WAAW;AAChD,QAAM,WAAW,GAAG,aAAa;AACjC,QAAM,aAAa,GAAG,aAAa;AACnC,QAAM,iBAAiB,kBAAkB,MAAM,qBAAqB;AACpE,SAAO,WAAW,iBAAiB,eAAe,UAAU,OAAO,OAAO,QAAQ,aAAa,MAAM;AACvG;AACO,SAAS,iBAAiB,gBAAgB,UAAU,QAAQ,SAAS,OAAO,SAAS,OAAO;AACjG,QAAM,cAAc,MAAM,OAAO;AACjC,QAAM,cAAc,MAAM,OAAO;AACjC,QAAM,qBAAqB,iBAAiB,cAAc;AAC1D,QAAM,uBAAuB,iBAAiB,cAAc;AAC5D,QAAM,iBAAiB,iBAAiB,UAAU;AAClD,QAAM,mBAAmB,iBAAiB,UAAU;AACpD,QAAM,wBAAwB,iBAAiB,MAAM;AACrD,QAAM,0BAA0B,iBAAiB,MAAM;AACvD,MAAI,CAAC,kBAAkB,kBAAkB,GAAG;AAC1C,UAAM,IAAI,MAAM,iBAAiB,eAAe,uBAAuB,cAAc,CAAC,8DAA8D,QAAQ,IAAI;AAAA,EAClK;AACA,MAAI,mBAAmB,SAAS,QAAW;AACzC,UAAM,IAAI,MAAM,iBAAiB,eAAe,uBAAuB,cAAc,CAAC,6BAA6B;AAAA,EACrH;AACA,MAAI,kBAAkB,oBAAoB,KAAK,mBAAmB,oBAAoB,GAAG;AACvF,UAAM,IAAI,MAAM,iBAAiB,eAAe,yBAAyB,gBAAgB,CAAC,iEAAiE,QAAQ,IAAI;AAAA,EACzK;AACA,MAAI,MAAuC;AACzC,QAAI,mBAAmB,KAAK,SAAS,OAAO,YAAY,QAAQ;AAC9D,eAAS,CAAC,iBAAiB,eAAe,uBAAuB,cAAc,CAAC,mBAAmB,mBAAmB,KAAK,MAAM,uCAAuC,QAAQ,MAAM,OAAO,YAAY,MAAM,aAAa,8EAA8E,GAAG,OAAO;AAAA,IACtT;AAAA,EACF;AACF;;;AC5BO,SAAS,eAAe,aAAa,OAAO,OAAO;AACxD,QAAM,aAAa,oBAAoB,KAAK;AAAA,IAC1C,QAAQ,CAAC;AAAA,IACT,gBAAgB,CAAC;AAAA,IACjB,aAAa,CAAC;AAAA,EAChB;AACA,QAAM,iBAAiB,SAAS,EAAE,SAAS,CAAC;AAC5C,QAAM,iBAAiB,SAAS,EAAE,SAAS,CAAC;AAC5C,QAAM,UAAU,WAAW;AAC3B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,CAAC;AACf,QAAM,OAAO,eAAe,QAAQ,CAAC;AAAA,IACnC,KAAK;AAAA,EACP,GAAG,eAAe;AAChB,UAAM,OAAO,YAAY;AACzB,UAAM,OAAO,YAAY,OAAO,YAAY;AAC5C,UAAM,OAAO,YAAY;AACzB,UAAM,OAAO,YAAY,MAAM,YAAY;AAC3C,WAAO,UAAU,IAAI,cAAY;AAC/B,YAAM,UAAU,OAAO,QAAQ,EAAE,WAAW;AAC5C,YAAM,UAAU,OAAO,QAAQ,EAAE,WAAW;AAC5C,YAAM,cAAc,MAAM,OAAO;AACjC,YAAM,cAAc,MAAM,OAAO;AACjC,YAAM,iBAAiB,OAAO,QAAQ,EAAE,WAAW;AACnD,uBAAiB,gBAAgB,UAAU,OAAO,QAAQ,GAAG,SAAS,OAAO,SAAS,KAAK;AAC3F,YAAM,kBAAkB,iBAAiB,cAAc;AACvD,YAAM,SAAS,YAAY;AAC3B,YAAM,SAAS,YAAY;AAC3B,YAAM,cAAc,iBAAS,OAAO,QAAQ,GAAG,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC;AAC7E,YAAM,YAAY,gBAAgB,MAAM,UAAU;AAClD,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,YAAY;AAAA,QACd;AAAA,QACA,gBAAgB,eAAe;AAAA,QAC/B,UAAU,gBAAgB;AAAA,MAC5B,CAAC;AACD,YAAM,YAAY,cAAc,WAAW;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA,MAAM;AAAA,QACN;AAAA,QACA;AAAA,MACF,IAAI,OAAO,QAAQ;AACnB,YAAM,mBAAmB,gBAAgB,KAAK,IAAI,CAAC,WAAW,cAAc;AAC1E,YAAI,kBAAkB,SAAS,KAAK,MAAM;AACxC,iBAAO;AAAA,QACT;AACA,cAAM,SAAS,YAAY,SAAS;AACpC,cAAM,mBAAmB,OAAO,IAAI,OAAK,iBAAiB,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC;AAC/E,cAAM,gBAAgB,KAAK,MAAM,KAAK,IAAI,GAAG,gBAAgB,CAAC;AAC9D,cAAM,gBAAgB,KAAK,MAAM,KAAK,IAAI,GAAG,gBAAgB,CAAC;AAC9D,cAAM,UAAU,OAAO,QAAQ,EAAE;AACjC,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI,mBAAmB,gBAAgB,eAAe,eAAe,kBAAkB,SAAS,GAAG,UAAU;AAC7G,cAAM,SAAS;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG,iBAAiB,OAAO,SAAS,IAAI,YAAY;AAAA,UACpD,GAAG,iBAAiB,kBAAkB,OAAO,SAAS,IAAI;AAAA,UAC1D,SAAS,OAAO,CAAC,KAAK;AAAA,UACtB,SAAS,OAAO,CAAC,KAAK;AAAA,UACtB,QAAQ,iBAAiB,UAAU;AAAA,UACnC,OAAO,iBAAiB,WAAW;AAAA,UACnC,OAAO,YAAY,SAAS;AAAA,UAC5B,OAAO,kBAAkB,SAAS;AAAA,UAClC,QAAQ,GAAG,OAAO,IAAI,WAAW,QAAQ,IAAI,UAAU,IAAI,SAAS;AAAA,QACtE;AACA,YAAI,OAAO,IAAI,QAAQ,OAAO,IAAI,OAAO,QAAQ,QAAQ,OAAO,IAAI,QAAQ,OAAO,IAAI,OAAO,SAAS,MAAM;AAC3G,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,MAAM,OAAO,MAAM,GAAG;AACzB,gBAAM,OAAO,MAAM,IAAI;AAAA,YACrB,IAAI,OAAO;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,aAAa;AAAA,YACb,QAAQ,OAAO;AAAA,YACf,SAAS,OAAO,CAAC;AAAA,YACjB,SAAS,OAAO,CAAC;AAAA,YACjB,GAAG;AAAA,YACH,GAAG;AAAA,UACL;AAAA,QACF;AACA,cAAM,OAAO,MAAM,OAAO,MAAM;AAChC,aAAK,QAAQ,OAAO,WAAW,aAAa,OAAO,QAAQ,KAAK,QAAQ,OAAO;AAC/E,aAAK,SAAS,OAAO,WAAW,aAAa,KAAK,SAAS,OAAO,SAAS,OAAO;AAClF,aAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,WAAW,KAAK,GAAG,OAAO,CAAC;AAC5D,aAAK,IAAI,KAAK,IAAI,KAAK,MAAM,IAAI,WAAW,KAAK,GAAG,OAAO,CAAC;AAC5D,aAAK,cAAc,KAAK,gBAAgB,OAAO,SAAS,KAAK;AAC7D,aAAK,cAAc,KAAK,gBAAgB,OAAO,SAAS,KAAK;AAC7D,eAAO;AAAA,MACT,CAAC,EAAE,OAAO,eAAa,cAAc,IAAI;AACzC,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AAAA,IACL,eAAe;AAAA,IACf,WAAW,OAAO,OAAO,KAAK;AAAA,EAChC;AACF;AAWA,SAAS,YAAY;AAAA,EACnB,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,UAAU;AACZ,GAAG;AACD,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,MACL,UAAU,IAAI;AAAA,MACd,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,WAAW,KAAK,KAAK,IAAI,KAAK;AACpC,QAAM,SAAS,IAAI;AACnB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,mBAAmB,YAAY,eAAe,eAAe,WAAW,YAAY;AAC3F,MAAI,cAAc,KAAK,aAAa,MAAM;AACxC,WAAO;AAAA,MACL,SAAS;AAAA,MACT,iBAAiB;AAAA,IACnB;AAAA,EACF;AACA,QAAM,oBAAoB,gBAAgB,gBAAgB;AAC1D,QAAM,UAAU,oBAAoB,aAAa,gBAAgB;AACjE,QAAM,wBAAwB,cAAc,aAAa;AACzD,QAAM,0BAA0B,CAAC,cAAc,YAAY;AAC3D,MAAI,sBAAsB,yBAAyB,0BAA0B;AAC3E,WAAO;AAAA,MACL;AAAA,MACA,iBAAiB,gBAAgB;AAAA,IACnC;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB;AAAA,EACnB;AACF;;;AdpJA,IAAAC,sBAA2C;AAb3C,IAAMC,aAAY,CAAC,iBAAiB,eAAe,gBAAgB,UAAU;AAc7E,IAAM,cAAc,eAAO,KAAK;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE;AAAA,EACD,CAAC,MAAM,kBAAkB,IAAI,EAAE,GAAG;AAAA,IAChC,YAAY;AAAA,EACd;AACF,CAAC;AAaD,SAAS,QAAQ,OAAO;AACtB,QAAM;AAAA,IACF,eAAe;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOA,UAAS;AACxD,QAAM,oBAAoB,6BAA6B;AACvD,QAAM,gBAAgB,iBAAiB,qBAAqB,eAAe;AAC3E,QAAM;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,SAAS;AACb,QAAM;AAAA,IACJ,OAAO;AAAA,EACT,IAAI,SAAS;AACb,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe,eAAe,GAAG,OAAO,KAAK;AACjD,QAAM,sBAAsB,CAAC,gBAAgB,gBAAgB;AAC7D,QAAM,UAAUC,mBAAkB;AAClC,aAAoB,oBAAAC,MAAM,aAAa;AAAA,IACrC,WAAW,QAAQ;AAAA,IACnB,UAAU,CAAC,CAAC,uBAAuB,UAAU,IAAI,CAAC;AAAA,MAChD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,iBAAoB,oBAAAC,KAAK,aAAa;AAAA,QACpC,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,eAAe,iBAAiB;AAAA,MAClC,GAAG,EAAE;AAAA,IACP,CAAC,GAAG,cAAc,IAAI,CAAC;AAAA,MACrB;AAAA,MACA;AAAA,IACF,MAAM;AACJ,iBAAoB,oBAAAA,KAAK,KAAK;AAAA,QAC5B,eAAe;AAAA,QACf,WAAW,QAAQ;AAAA,QACnB,UAAU,KAAK,IAAI,CAAC;AAAA,UAClB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,MAAM;AACJ,gBAAM,iBAA0B,oBAAAA,KAAK,YAAY,SAAS;AAAA,YACxD,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,YACA,eAAe,iBAAiB;AAAA,YAChC,QAAQ,UAAU;AAAA,YAClB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF,GAAG,OAAO;AAAA,YACR,SAAS,gBAAgB,WAAS;AAChC,0BAAY,OAAO;AAAA,gBACjB,MAAM;AAAA,gBACN;AAAA,gBACA;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,CAAC,GAAG,SAAS;AACb,cAAI,qBAAqB;AACvB,mBAAO;AAAA,UACT;AACA,qBAAoB,oBAAAA,KAAK,KAAK;AAAA,YAC5B,UAAU,QAAQ,MAAM;AAAA,YACxB,UAAU;AAAA,UACZ,GAAG,SAAS;AAAA,QACd,CAAC;AAAA,MACH,GAAG,QAAQ;AAAA,IACb,CAAC,GAAG,gBAAyB,oBAAAA,KAAK,cAAc,SAAS;AAAA,MACvD,MAAM;AAAA,MACN;AAAA,MACA;AAAA,IACF,GAAG,KAAK,CAAC,CAAC;AAAA,EACZ,CAAC;AACH;AACA,OAAwC,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAY1D,UAAU,mBAAAC,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAI1E,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMxB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AehLJ,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACEtB,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACJtB,IAAAC,SAAuB;AAKhB,SAAS,gBAAgB;AAC9B,QAAM,CAAC,YAAY,aAAa,IAAU,gBAAS,OAAO,WAAW,eAAe,KAA+B;AACnH,EAAM,iBAAU,MAAM;AACpB,kBAAc,IAAI;AAAA,EACpB,GAAG,CAAC,CAAC;AACL,SAAO;AACT;;;ACTA,SAAS,QAAQ;AACf,SAAO,OAAO,WAAW;AAC3B;AACA,IAAM,cAAc,oBAAI,IAAI;AAC5B,IAAM,gBAAgB;AACtB,IAAM,aAAa;AAAA,EACjB,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AACd;AACA,IAAM,aAAa,CAAC,YAAY,YAAY,SAAS,aAAa,aAAa,UAAU,OAAO,QAAQ,YAAY,WAAW,UAAU,eAAe,gBAAgB,cAAc,iBAAiB,cAAc,eAAe,aAAa,cAAc;AACxP,IAAM,sBAAsB;AAQnC,SAAS,kBAAkB,MAAM,OAAO;AACtC,MAAI,WAAW,QAAQ,IAAI,KAAK,KAAK,UAAU,CAAC,OAAO;AACrD,WAAO,GAAG,KAAK;AAAA,EACjB;AACA,SAAO;AACT;AAOA,SAAS,kBAAkB,MAAM;AAC/B,QAAM,OAAO,KAAK,MAAM,EAAE;AAC1B,QAAM,aAAa,KAAK,OAAO,CAAC,QAAQ,UAAU;AAChD,QAAI,UAAU,MAAM,YAAY,GAAG;AACjC,aAAO,CAAC,GAAG,QAAQ,KAAK,MAAM,YAAY,CAAC;AAAA,IAC7C;AACA,WAAO,CAAC,GAAG,QAAQ,KAAK;AAAA,EAC1B,GAAG,CAAC,CAAC;AACL,SAAO,WAAW,KAAK,EAAE;AAC3B;AAOO,IAAM,iBAAiB,WAAS,OAAO,KAAK,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,MAAM,GAAG,MAAM,GAAG,kBAAkB,CAAC,CAAC,IAAI,kBAAkB,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE;AAChK,IAAI;AAQG,IAAM,gBAAgB,CAAC,MAAM,QAAQ,CAAC,MAAM;AACjD,MAAI,SAAS,UAAa,SAAS,QAAQ,MAAM,GAAG;AAClD,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACA,QAAM,MAAM,GAAG,IAAI;AACnB,QAAM,cAAc,eAAe,KAAK;AACxC,QAAM,WAAW,GAAG,GAAG,IAAI,WAAW;AACtC,QAAM,OAAO,YAAY,IAAI,QAAQ;AACrC,MAAI,MAAM;AACR,WAAO;AAAA,EACT;AACA,MAAI;AACF,QAAI,kBAAkB,SAAS,eAAe,mBAAmB;AACjE,QAAI,oBAAoB,MAAM;AAC5B,wBAAkB,SAAS,cAAc,MAAM;AAC/C,sBAAgB,aAAa,MAAM,mBAAmB;AACtD,sBAAgB,aAAa,eAAe,MAAM;AAClD,eAAS,KAAK,YAAY,eAAe;AAAA,IAC3C;AAGA,UAAM,uBAAuB,SAAS,CAAC,GAAG,YAAY,KAAK;AAC3D,WAAO,KAAK,oBAAoB,EAAE,IAAI,cAAY;AAChD,sBAAgB,MAAM,kBAAkB,QAAQ,CAAC,IAAI,kBAAkB,UAAU,qBAAqB,QAAQ,CAAC;AAC/G,aAAO;AAAA,IACT,CAAC;AACD,oBAAgB,cAAc;AAC9B,UAAM,OAAO,gBAAgB,sBAAsB;AACnD,UAAM,SAAS;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK;AAAA,IACf;AACA,gBAAY,IAAI,UAAU,MAAM;AAChC,QAAI,YAAY,OAAO,IAAI,eAAe;AACxC,kBAAY,MAAM;AAAA,IACpB;AACA,QAAI,OAAiC;AAEnC,sBAAgB,cAAc;AAAA,IAChC,OAAO;AACL,UAAI,iBAAiB;AACnB,qBAAa,eAAe;AAAA,MAC9B;AACA,wBAAkB,WAAW,MAAM;AAEjC,wBAAgB,cAAc;AAAA,MAChC,GAAG,CAAC;AAAA,IACN;AACA,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF;AACF;;;ACxHA,IAAAC,SAAuB;;;ACFhB,SAAS,WAAW,GAAG;AAC5B,SAAO,OAAO,MAAM,YAAY,CAAC,OAAO,SAAS,CAAC;AACpD;;;ADIA,IAAM,cAAc;AAAA,EAClB,OAAO;AAAA,EACP,aAAa;AAAA,EACb,KAAK;AAAA,EACL,QAAQ;AACV;AACO,SAAS,SAAS,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB;AAAA,EACF,IAAI;AAGJ,MAAI,YAAY,KAAK,GAAG;AACtB,UAAMC,UAAS,MAAM,OAAO;AAC5B,UAAMC,sBAAqB,0BAA0B;AACrD,QAAI,MAAM,UAAU,IAAI,GAAG;AAEzB,YAAMC,kBAAiB,OAAO,iBAAiB,cAAcF,QAAO,OAAO,YAAY,KAAK,OAAO,iBAAiB,YAAY,gBAAgBA;AAChJ,aAAO,CAAC,GAAGE,gBAAe,IAAI,WAAS;AACrC,cAAM,mBAAmB,GAAG,KAAK;AACjC,eAAO;AAAA,UACL;AAAA,UACA,iBAAgB,iDAAiB,OAAO;AAAA,YACtC,UAAU;AAAA,YACV;AAAA,YACA;AAAA,YACA;AAAA,UACF,OAAM;AAAA,UACN,QAAQ,MAAM,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,UAAU,KAAK,IAAI,YAAY,aAAa,IAAI,MAAM,KAAK;AAAA,UACxG,aAAaD,wBAAuB,SAAS,IAAI,MAAM,KAAK,KAAK,YAAYA,mBAAkB,IAAI,YAAY,aAAa;AAAA,QAC9H;AAAA,MACF,CAAC,GAAG,GAAI,kBAAkB,gBAAgB,CAAC;AAAA,QACzC,gBAAgB;AAAA,QAChB,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,QACvB,aAAa;AAAA,MACf,CAAC,IAAI,CAAC,CAAE;AAAA,IACV;AAGA,UAAM,iBAAiB,OAAO,iBAAiB,cAAcD,QAAO,OAAO,YAAY,KAAK,OAAO,iBAAiB,YAAY,gBAAgBA;AAChJ,WAAO,eAAe,IAAI,WAAS;AACjC,YAAM,mBAAmB,GAAG,KAAK;AACjC,aAAO;AAAA,QACL;AAAA,QACA,iBAAgB,iDAAiB,OAAO;AAAA,UACtC,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF,OAAM;AAAA,QACN,QAAQ,MAAM,KAAK;AAAA,QACnB,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAM,SAAS,MAAM,OAAO;AAG5B,MAAI,OAAO,KAAK,UAAU,GAAG;AAC3B,WAAO,CAAC;AAAA,EACV;AACA,QAAM,qBAAqB;AAC3B,QAAM,QAAQ,OAAO,iBAAiB,WAAW,eAAe,MAAM,MAAM,UAAU;AAGtF,QAAM,eAAe,CAAC;AACtB,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,UAAM,QAAQ,MAAM,CAAC;AACrB,UAAM,SAAS,MAAM,KAAK;AAC1B,QAAI,SAAS,MAAM,GAAG;AAKpB,YAAM,mBAAmB,MAAM,WAAW,UAAU,EAAE,KAAK;AAC3D,mBAAa,KAAK;AAAA,QAChB;AAAA,QACA,iBAAgB,iDAAiB,OAAO;AAAA,UACtC,UAAU;AAAA,UACV;AAAA,UACA;AAAA,UACA;AAAA,QACF,OAAM;AAAA,QACN;AAAA;AAAA;AAAA,QAGA,aAAa,uBAAuB,WAAW,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,MAAM,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI;AAAA,MACtH,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AACO,SAAS,SAAS,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,WAAW,cAAc,MAAM,SAAS,YAAY,SAAS;AACnE,SAAa,eAAQ,MAAM,SAAS;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,GAAG,CAAC,OAAO,YAAY,eAAe,cAAc,oBAAoB,gBAAgB,QAAQ,CAAC;AACpG;;;AE7HO,SAAS,oBAAoB,MAAM;AACxC,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACO,IAAM,cAAc,uBAAuB,iBAAiB,CAAC,QAAQ,QAAQ,iBAAiB,QAAQ,aAAa,SAAS,cAAc,cAAc,OAAO,UAAU,QAAQ,SAAS,IAAI,CAAC;;;ACF/L,IAAM,WAAW,eAAO,KAAK;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,CAAC,MAAM,YAAY,SAAS,EAAE,GAAG,SAAS,CAAC,GAAG,MAAM,WAAW,SAAS;AAAA,IACtE,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC3C,CAAC;AAAA,EACD,CAAC,MAAM,YAAY,KAAK,EAAE,GAAG;AAAA,IAC3B,OAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,EAC3C;AAAA,EACA,CAAC,MAAM,YAAY,IAAI,EAAE,GAAG;AAAA,IAC1B,SAAS,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC3C,gBAAgB;AAAA,IAChB,aAAa;AAAA,EACf;AAAA,EACA,CAAC,MAAM,YAAY,IAAI,EAAE,GAAG;AAAA,IAC1B,SAAS,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC3C,gBAAgB;AAAA,EAClB;AACF,EAAE;;;AClBF,IAAAG,UAAuB;AACvB,IAAAC,qBAAsB;;;ACLf,SAAS,gBAAgB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,SAAO,KAAK,MAAM,IAAI,EAAE,IAAI,aAAW,SAAS;AAAA,IAC9C,MAAM;AAAA,EACR,GAAG,mBAAmB,cAAc,SAAS,KAAK,IAAI;AAAA,IACpD,OAAO;AAAA,IACP,QAAQ;AAAA,EACV,CAAC,CAAC;AACJ;;;ADHA,IAAAC,sBAA4B;AAN5B,IAAMC,aAAY,CAAC,KAAK,KAAK,SAAS,QAAQ,YAAY;AAA1D,IACEC,cAAa,CAAC,SAAS,cAAc,kBAAkB;AASzD,SAAS,WAAW,OAAO;AACzB,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP;AAAA,EACF,IAAI,OACJ,YAAY,8BAA8B,OAAOD,UAAS;AAC5D,QAAM,OAAO,cAAc,CAAC,GAC1B;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,MACJ,QAAQ,8BAA8B,MAAMC,WAAU;AACxD,QAAM,aAAa,cAAc;AACjC,QAAM,eAAqB,gBAAQ,MAAM,gBAAgB;AAAA,IACvD;AAAA,IACA,kBAAkB,cAAc,KAAK,SAAS,IAAI;AAAA,IAClD;AAAA,EACF,CAAC,GAAG,CAAC,OAAO,MAAM,UAAU,CAAC;AAC7B,MAAI;AACJ,UAAQ,kBAAkB;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,gBAAU;AACV;AAAA,IACF,KAAK;AACH,iBAAW,aAAa,SAAS,KAAK,IAAI,CAAC,aAAa,CAAC,EAAE;AAC3D;AAAA,IACF;AACE,iBAAW,aAAa,SAAS,KAAK,CAAC,aAAa,CAAC,EAAE;AACvD;AAAA,EACJ;AACA,aAAoB,oBAAAC,KAAK,QAAQ,SAAS,CAAC,GAAG,WAAW;AAAA,IACvD,WAAW,QAAQ,UAAU,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM;AAAA,IACpD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,aAAa,IAAI,CAAC,MAAM,cAAuB,oBAAAA,KAAK,SAAS;AAAA,MACrE;AAAA,MACA,IAAI,GAAG,UAAU,IAAI,UAAU,aAAa,CAAC,EAAE,MAAM;AAAA,MACrD;AAAA,MAEA,UAAU,KAAK;AAAA,IACjB,GAAG,KAAK,CAAC;AAAA,EACX,CAAC,CAAC;AACJ;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7D,YAAY,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,kBAAkB,mBAAAA,QAAU;AAAA,EAC5B,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,MAAM,mBAAAA,QAAU,OAAO;AACzB,IAAI;;;AErFJ,IAAAC,UAAuB;AAEhB,SAAS,WAAW,QAAQ,OAAO;AACxC,QAAM,CAAC,cAAc,eAAe,IAAU,iBAAS,KAAK;AAC5D,4BAAkB,MAAM;AACtB,QAAI,CAAC,OAAO;AACV,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,EAAM,kBAAU,MAAM;AACpB,QAAI,OAAO;AACT,sBAAgB,IAAI;AAAA,IACtB;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,SAAO;AACT;;;ACVO,SAAS,qBAAqB,OAAO;AAC1C,QAAM,gBAAgB,WAAW,KAAK;AACtC,MAAI,iBAAiB,MAAM,iBAAiB,KAAK;AAE/C,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,OAAO,iBAAiB,KAAK;AAEhD,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,KAAK;AACxB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACO,SAAS,mBAAmB,OAAO;AACxC,QAAM,gBAAgB,WAAW,KAAK;AACtC,MAAI,iBAAiB,MAAM,iBAAiB,KAAK;AAE/C,WAAO;AAAA,EACT;AACA,MAAI,iBAAiB,OAAO,iBAAiB,KAAK;AAEhD,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;ACjCO,SAAS,iBAAiB,YAAY;AAC3C,UAAQ,YAAY;AAAA,IAClB,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;;;ACTA,IAAM,YAAY,OAAO,WAAW,eAAe,UAAU,UAAU,eAAe,OAAO,IAAI,KAAK,UAAU,QAAW;AAAA,EACzH,aAAa;AACf,CAAC,IAAI;AACL,SAAS,yBAAyB,MAAM;AACtC,SAAO,KAAK;AACd;AACA,SAAS,uBAAuB,MAAM;AACpC,QAAM,WAAW,UAAU,QAAQ,IAAI;AACvC,MAAI,QAAQ;AAGZ,aAAW,WAAW,UAAU;AAC9B,aAAS;AAAA,EACX;AACA,SAAO;AACT;AAGO,IAAM,mBAAmB,YAAY,yBAAyB;;;ACjB9D,SAAS,SAAS,SAAS;AAChC,SAAO,WAAW,KAAK,KAAK;AAC9B;;;ACHA,IAAMC,aAAY,OAAO,WAAW,eAAe,UAAU,UAAU,eAAe,OAAO,IAAI,KAAK,UAAU,QAAW;AAAA,EACzH,aAAa;AACf,CAAC,IAAI;AACL,SAAS,mBAAmB,MAAM,UAAU;AAC1C,SAAO,KAAK,MAAM,GAAG,QAAQ;AAC/B;AACA,SAAS,iBAAiB,MAAM,UAAU;AACxC,QAAM,WAAWA,WAAU,QAAQ,IAAI;AACvC,MAAI,UAAU;AACd,MAAI,IAAI;AACR,aAAW,WAAW,UAAU;AAC9B,eAAW,QAAQ;AACnB,SAAK;AACL,QAAI,KAAK,UAAU;AACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAGO,IAAM,aAAaA,aAAY,mBAAmB;;;AClBzD,IAAM,WAAW;AACV,SAAS,kBAAkB,MAAM,QAAQ;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ,SAAS,OAAO,KAAK;AACnC,QAAM,WAAW,YAAY,IAAI;AACjC,QAAM,cAAc,KAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,KAAK,CAAC;AAC3G,QAAM,eAAe,KAAK,IAAI,SAAS,QAAQ,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,SAAS,KAAK,IAAI,KAAK,CAAC;AAC5G,SAAO,eAAe,SAAS,gBAAgB;AACjD;AASO,SAAS,UAAU,MAAM,aAAa;AAC3C,MAAI,YAAY,IAAI,GAAG;AACrB,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB;AACpB,MAAI,OAAO;AACX,MAAI,KAAK,IAAI;AACb,QAAM,gBAAgB,iBAAiB,IAAI;AAC3C,MAAI,YAAY;AAChB,MAAI,aAAa;AACjB,MAAI,qBAAqB;AACzB,KAAG;AACD,iBAAa;AACb,gBAAY,KAAK,MAAM,gBAAgB,EAAE;AACzC,QAAI,cAAc,GAAG;AACnB;AAAA,IACF;AACA,oBAAgB,WAAW,MAAM,SAAS,EAAE,KAAK;AACjD,UAAM,OAAO,YAAY,gBAAgB,QAAQ;AACjD,YAAQ;AACR,QAAI,MAAM;AACR,2BAAqB;AACrB,YAAM,IAAI,KAAK;AAAA,IACjB,OAAO;AACL,YAAM,IAAI,KAAK;AAAA,IACjB;AAAA,EACF,SAAS,KAAK,IAAI,YAAY,UAAU,MAAM;AAC9C,SAAO,qBAAqB,qBAAqB,WAAW;AAC9D;;;ACjDO,SAAS,cAAc,eAAe,aAAa,WAAW,OAAO,gBAAgB;AAC1F,QAAM,kBAAkB,oBAAI,IAAI;AAChC,QAAM,QAAQ,YAAW,iDAAgB,UAAS,CAAC;AAInD,MAAI,kBAAkB;AACtB,MAAI,mBAAmB;AACvB,OAAI,iDAAgB,gBAAe,SAAS;AAC1C,sBAAkB;AAClB,uBAAmB;AAAA,EACrB,YAAW,iDAAgB,gBAAe,OAAO;AAC/C,sBAAkB;AAClB,uBAAmB;AAAA,EACrB,OAAO;AACL,sBAAkB;AAClB,uBAAmB;AAAA,EACrB;AACA,MAAI,QAAQ,MAAM,QAAQ,KAAK;AAC7B,KAAC,iBAAiB,gBAAgB,IAAI,CAAC,kBAAkB,eAAe;AAAA,EAC1E;AACA,MAAI,OAAO;AACT,KAAC,iBAAiB,gBAAgB,IAAI,CAAC,kBAAkB,eAAe;AAAA,EAC1E;AACA,aAAW,QAAQ,eAAe;AAChC,QAAI,KAAK,gBAAgB;AAEvB,YAAM,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,eAAe,kBAAkB,YAAY,OAAO,YAAY,QAAQ,YAAY,QAAQ,KAAK,SAAS,KAAK,eAAe,gBAAgB;AACzL,YAAM,cAAc,UAAQ,kBAAkB,MAAM;AAAA,QAClD;AAAA,QACA,QAAQ;AAAA,QACR;AAAA,QACA,aAAa,YAAU,cAAc,QAAQ,cAAc;AAAA,MAC7D,CAAC;AACD,sBAAgB,IAAI,MAAM,UAAU,KAAK,eAAe,SAAS,GAAG,WAAW,CAAC;AAAA,IAClF;AAAA,EACF;AACA,SAAO;AACT;;;ACvCA,IAAM,eAAe;AAUd,SAAS,mBAAmB,OAAO,QAAQ,QAAQ,GAAG;AAC3D,MAAI,MAAuC;AACzC,QAAI,QAAQ,MAAM,QAAQ,KAAK;AAC7B,eAAS,CAAC,qGAAqG,2CAA2C,sEAAsE,CAAC;AAAA,IACnO;AAAA,EACF;AACA,QAAM,gBAAgB,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,IAAI,GAAG;AAEjG,MAAI,gBAAgB,cAAc;AAEhC,WAAO;AAAA,EACT;AACA,MAAI,gBAAgB,KAAK,cAAc;AAErC,WAAO;AAAA,EACT;AACA,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,aAAa,KAAK,MAAM,QAAQ,KAAK;AAC3C,MAAI,WAAW,YAAY;AACzB,WAAO,QAAQ,KAAK,IAAI,QAAQ;AAAA,EAClC;AACA,SAAO,SAAS,KAAK,IAAI,QAAQ;AACnC;;;AC5BO,SAAS,iBAAiB,QAAQ;AAAA,EACvC,gBAAgB;AAAA,EAChB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,mBAAmB,UAAQ;AAC/B,QAAI,CAAC,aAAa,KAAK,mBAAmB,QAAW;AACnD,aAAO;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,IACF;AACA,UAAM,YAAY,gBAAgB;AAAA,MAChC;AAAA,MACA,kBAAkB;AAAA,MAClB,MAAM,KAAK;AAAA,IACb,CAAC;AACD,WAAO;AAAA,MACL,OAAO,KAAK,IAAI,GAAG,UAAU,IAAI,UAAQ,KAAK,KAAK,CAAC;AAAA,MACpD,QAAQ,KAAK,IAAI,UAAU,SAAS,UAAU,CAAC,EAAE,MAAM;AAAA,IACzD;AAAA,EACF;AACA,MAAI,OAAO,sBAAsB,YAAY;AAC3C,WAAO,IAAI,IAAI,OAAO,OAAO,CAAC,MAAM,UAAU,kBAAkB,KAAK,OAAO,KAAK,CAAC,CAAC;AAAA,EACrF;AAGA,MAAI,oBAAoB;AACxB,QAAM,YAAY,UAAU,KAAK;AACjC,SAAO,IAAI,IAAI,OAAO,OAAO,CAAC,MAAM,eAAe;AACjD,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,eAAe,SAAS;AAC9B,QAAI,aAAa,KAAK,YAAY,eAAe,aAAa,oBAAoB,kBAAkB;AAClG,aAAO;AAAA,IACT;AACA,QAAI,CAAC,UAAU,YAAY,GAAG;AAC5B,aAAO;AAAA,IACT;AAGA,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,iBAAiB,IAAI;AACzB,UAAM,WAAW,mBAAmB,OAAO,QAAQ,+BAAO,KAAK;AAC/D,UAAM,mBAAmB,eAAe,YAAY,WAAW;AAC/D,QAAI,aAAa,KAAK,YAAY,mBAAmB,aAAa,oBAAoB,kBAAkB;AAGtG,aAAO;AAAA,IACT;AACA,wBAAoB,eAAe,YAAY,WAAW;AAC1D,WAAO;AAAA,EACT,CAAC,CAAC;AACJ;;;AlBvCA,IAAAC,sBAA2C;AAvB3C,IAAMC,aAAY,CAAC,SAAS,cAAc,SAAS;AAwBnD,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,cAAc,UAAU,MAAM,EAAE,EAAE;AAAA,IACjD,MAAM,CAAC,MAAM;AAAA,IACb,eAAe,CAAC,eAAe;AAAA,IAC/B,MAAM,CAAC,MAAM;AAAA,IACb,WAAW,CAAC,WAAW;AAAA,IACvB,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AAGA,IAAM,iBAAiB;AAEvB,IAAM,4BAA4B;AAClC,IAAM,YAAY,eAAO,UAAU;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC,CAAC;AACL,IAAM,eAAe;AAAA,EACnB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AAAA,EACV,iBAAiB;AACnB;AAWA,SAAS,YAAY,SAAS;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,QAAM,SAAS,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAC,GAChD;AAAA,IACE,OAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF,IAAI,QACJ,WAAW,8BAA8B,QAAQD,UAAS;AAC5D,QAAM,YAAY,WAAW;AAC7B,QAAM,cAAc,cAAc;AAAA,IAChC,OAAO,SAAS,CAAC,GAAG,UAAU,OAAO;AAAA,IACrC,MAAM;AAAA,EACR,CAAC;AACD,QAAM,mBAAmB,SAAS,CAAC,GAAG,cAAc,WAAW;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,OAAO;AACrB,QAAM,UAAUC,mBAAkB,gBAAgB;AAClD,QAAM,cAAc,eAAe;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,aAAa,cAAc;AACjC,QAAM,WAAW,eAAe,IAAI;AACpC,QAAM,eAAe,aAAa,WAAW,IAAI;AACjD,QAAM,QAAO,+BAAO,aAAY;AAChC,QAAM,QAAO,+BAAO,aAAY;AAChC,QAAM,aAAY,+BAAO,kBAAiB;AAC1C,QAAM,SAAQ,+BAAO,cAAa;AAClC,QAAM,oBAAoB,sBAAsB,aAAa,WAAW,IAAI,SAAQ,iDAAgB,UAAS,EAAE;AAC/G,QAAM,0BAA0B,oBAAoB,aAAa,WAAW,IAAI,SAAQ,iDAAgB,UAAS,EAAE;AACnH,QAAM,qBAAqB,qBAAa;AAAA,IACtC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf,OAAO,SAAS,CAAC,GAAG,MAAM,WAAW,SAAS;AAAA,QAC5C,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,YAAY,QAAQ,iBAAiB,iBAAiB,IAAI;AAAA,QAC1D,kBAAkB;AAAA,MACpB,GAAG,cAAc;AAAA,IACnB;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,SAAS,SAAS;AAAA,IACtB,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AACD,QAAM,gBAAgB,iBAAiB,QAAQ;AAAA,IAC7C,gBAAgB,mBAAmB;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,SAAS;AAAA,EACtB,CAAC;AACD,QAAM,iBAAiB,qBAAa;AAAA,IAClC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf,OAAO,SAAS,CAAC,GAAG,MAAM,WAAW,OAAO;AAAA,QAC1C,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,kBAAkB,aAAa,WAAW,oBAAoB;AAAA,MAChE,GAAG,UAAU;AAAA,IACf;AAAA,IACA,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,SAAS,OAAO,OAAO;AAC7B,QAAM,cAAc,YAAY,MAAM;AAKtC,MAAI,eAAe,OAAO,WAAW,KAAK,CAAC,eAAe,OAAO,KAAK,UAAU,KAAK,aAAa,QAAQ;AACxG,WAAO;AAAA,EACT;AACA,QAAM,cAAc,QAAQ,cAAc,OAAO,eAAe,KAAK,EAAE,SAAS;AAChF,QAAM,gBAAgB;AAAA,IACpB,GAAG,OAAO,QAAQ;AAAA,IAClB,GAAG,eAAe;AAAA,EACpB;AAGA,QAAM,sBAAsB,KAAK,IAAI,GAAG,cAAc,QAAQ,cAAc,4BAA4B,KAAK,WAAW,cAAc;AACtI,QAAM,aAAa,aAAa,cAAc,eAAe,aAAa,qBAAqB,OAAO,mBAAmB,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK,aAAa,EAAE,IAAI,UAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,CAAC;AAC5M,aAAoB,oBAAAC,MAAM,WAAW;AAAA,IACnC,WAAW,gBAAgB,aAAa,WAAW,MAAM,SAAS,SAAS,MAAM,MAAM;AAAA,IACvF,WAAW,QAAQ;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,CAAC,mBAA4B,oBAAAC,KAAK,MAAM,SAAS;AAAA,MAC1D,IAAI;AAAA,MACJ,IAAI,OAAO;AAAA,MACX,WAAW,QAAQ;AAAA,IACrB,GAAG,uCAAW,QAAQ,CAAC,GAAG,OAAO,IAAI,CAAC,MAAM,UAAU;AACpD,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,eAAe;AAClC,YAAM,aAAa,gBAAgB,WAAW;AAC9C,YAAM,WAAW,SAAS,UAAU,UAAU;AAC9C,YAAM,YAAY,WAAW,IAAI,IAAI;AACrC,YAAM,gBAAgB,cAAc,IAAI,IAAI;AAC5C,iBAAoB,oBAAAD,MAAM,KAAK;AAAA,QAC7B,WAAW,aAAa,UAAU;AAAA,QAClC,WAAW,QAAQ;AAAA,QACnB,UAAU,CAAC,CAAC,gBAAgB,gBAAyB,oBAAAC,KAAK,MAAM,SAAS;AAAA,UACvE,IAAI,eAAe;AAAA,UACnB,WAAW,QAAQ;AAAA,QACrB,GAAG,uCAAW,QAAQ,CAAC,GAAG,cAAc,UAAa,qBAA8B,oBAAAA,KAAK,WAAW,SAAS;AAAA,UAC1G,GAAG;AAAA,UACH,GAAG;AAAA,QACL,GAAG,oBAAoB;AAAA,UACrB,MAAM;AAAA,QACR,CAAC,CAAC,CAAC;AAAA,MACL,GAAG,KAAK;AAAA,IACV,CAAC,GAAG,aAAsB,oBAAAA,KAAK,KAAK;AAAA,MAClC,WAAW,QAAQ;AAAA,MACnB,cAAuB,oBAAAA,KAAK,OAAO,SAAS,CAAC,GAAG,eAAe,gBAAgB;AAAA,QAC7E,MAAM;AAAA,MACR,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,YAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,MAAM,mBAAAC,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhE,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtJ,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlF,iBAAiB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3B,oBAAoB,mBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItD,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,eAAe,mBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,UAAU,mBAAAA,QAAU;AACtB,IAAI;;;AmBpVJ,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACDf,SAASC,eAAc,eAAe,aAAa,UAAU,OAAO,gBAAgB;AACzF,QAAM,kBAAkB,oBAAI,IAAI;AAChC,QAAM,QAAQ,YAAW,iDAAgB,UAAS,CAAC;AACnD,MAAI,iBAAiB;AACrB,MAAI,oBAAoB;AACxB,OAAI,iDAAgB,gBAAe,SAAS;AAC1C,qBAAiB;AACjB,wBAAoB;AAAA,EACtB,YAAW,iDAAgB,gBAAe,OAAO;AAC/C,qBAAiB;AACjB,wBAAoB;AAAA,EACtB,OAAO;AACL,qBAAiB;AACjB,wBAAoB;AAAA,EACtB;AACA,MAAI,QAAQ,KAAK;AACf,KAAC,gBAAgB,iBAAiB,IAAI,CAAC,mBAAmB,cAAc;AAAA,EAC1E;AACA,MAAI,OAAO;AACT,KAAC,gBAAgB,iBAAiB,IAAI,CAAC,mBAAmB,cAAc;AAAA,EAC1E;AACA,aAAW,QAAQ,eAAe;AAChC,QAAI,KAAK,gBAAgB;AAEvB,YAAM,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,eAAe,iBAAiB,YAAY,MAAM,YAAY,SAAS,YAAY,SAAS,KAAK,SAAS,KAAK,eAAe,iBAAiB;AAC3L,YAAM,cAAc,UAAQ,kBAAkB,MAAM;AAAA,QAClD,OAAO;AAAA,QACP;AAAA,QACA;AAAA,QACA,aAAa,YAAU,cAAc,QAAQ,cAAc;AAAA,MAC7D,CAAC;AACD,sBAAgB,IAAI,MAAM,UAAU,KAAK,eAAe,SAAS,GAAG,WAAW,CAAC;AAAA,IAClF;AAAA,EACF;AACA,SAAO;AACT;;;ADfA,IAAAC,uBAA2C;AArB3C,IAAMC,cAAY,CAAC,SAAS,YAAY;AAsBxC,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,cAAc,UAAU,MAAM,EAAE,EAAE;AAAA,IACjD,MAAM,CAAC,MAAM;AAAA,IACb,eAAe,CAAC,eAAe;AAAA,IAC/B,MAAM,CAAC,MAAM;AAAA,IACb,WAAW,CAAC,WAAW;AAAA,IACvB,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,SAAO,eAAe,OAAO,qBAAqB,OAAO;AAC3D;AAGA,IAAMC,kBAAiB;AAEvB,IAAMC,6BAA4B;AAClC,IAAM,YAAY,eAAO,UAAU;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC,CAAC;AACL,IAAMC,gBAAe;AAAA,EACnB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,UAAU;AACZ;AAWA,SAAS,YAAY,SAAS;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,QAAM,SAAS,MAAM,QAAQ,UAAU,SAAS,CAAC,CAAC,GAChD;AAAA,IACE,OAAO;AAAA,IACP;AAAA,EACF,IAAI,QACJ,WAAW,8BAA8B,QAAQJ,WAAS;AAC5D,QAAM,cAAc,cAAc;AAAA,IAChC,OAAO,SAAS,CAAC,GAAG,UAAU,OAAO;AAAA,IACrC,MAAM;AAAA,EACR,CAAC;AACD,QAAM,mBAAmB,SAAS,CAAC,GAAGI,eAAc,WAAW;AAC/D,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,EACT,IAAI;AACJ,QAAM,QAAQ,SAAS;AACvB,QAAM,QAAQ,OAAO;AACrB,QAAM,aAAa,cAAc;AACjC,QAAM,UAAUH,mBAAkB,gBAAgB;AAClD,QAAM;AAAA,IACJ;AAAA,EACF,IAAI,gBAAgB;AACpB,QAAM,cAAc,eAAe;AACnC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,eAAe,IAAI;AACpC,QAAM,SAAS,SAAS;AAAA,IACtB,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AACD,QAAM,eAAe,aAAa,UAAU,IAAI;AAChD,QAAM,eAAe,QAAO,iDAAgB,cAAa,WAAW,eAAe,WAAW;AAC9F,QAAM,QAAO,+BAAO,aAAY;AAChC,QAAM,QAAO,+BAAO,aAAY;AAChC,QAAM,aAAY,+BAAO,kBAAiB;AAC1C,QAAM,SAAQ,+BAAO,cAAa;AAClC,QAAM,oBAAoB,sBAAsB,aAAa,UAAU,MAAM,QAAO,iDAAgB,UAAS,EAAE;AAC/G,QAAM,0BAA0B,oBAAoB,aAAa,UAAU,MAAM,QAAO,iDAAgB,UAAS,EAAE;AACnH,QAAM,qBAAqB,qBAAa;AAAA,IACtC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf,OAAO,SAAS,CAAC,GAAG,MAAM,WAAW,SAAS;AAAA,QAC5C,UAAU;AAAA,QACV,YAAY,QAAQ,iBAAiB,iBAAiB,IAAI;AAAA,QAC1D,kBAAkB;AAAA,MACpB,GAAG,cAAc;AAAA,IACnB;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,iBAAiB,qBAAa;AAAA,IAClC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf,OAAO,SAAS,CAAC,GAAG,MAAM,WAAW,OAAO;AAAA,QAC1C,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,OAAO,eAAe;AAAA,QACtB,YAAY;AAAA,QACZ,kBAAkB;AAAA,MACpB,GAAG,UAAU;AAAA,IACf;AAAA,IACA,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,gBAAgB,qBAAa;AAAA,IACjC,aAAa;AAAA,IACb,mBAAmB,uCAAW;AAAA,IAC9B,iBAAiB;AAAA,MACf,eAAe;AAAA,IACjB;AAAA,IACA,YAAY,CAAC;AAAA,EACf,CAAC;AACD,QAAM,SAAS,OAAO,OAAO;AAC7B,QAAM,cAAc,YAAY,MAAM;AAMtC,MAAI,eAAe,OAAO,WAAW,KAAK,CAAC,eAAe,OAAO,KAAK,UAAU,KAAK,aAAa,QAAQ;AACxG,WAAO;AAAA,EACT;AACA,QAAM,gBAAgB;AAAA,IACpB,GAAG,eAAe;AAAA,IAClB,GAAG,MAAM,SAAS;AAAA,EACpB;AAEA,QAAM,qBAAqB,KAAK,IAAI,GAAG,aAAa,QAAQ,cAAc,OAAO,eAAe,KAAK,EAAE,SAASE,6BAA4B,KAAK,WAAWD,eAAc;AAC1K,QAAM,aAAa,aAAaG,eAAc,QAAQ,aAAa,oBAAoB,OAAO,mBAAmB,KAAK,IAAI,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE,IAAI,UAAQ,CAAC,MAAM,KAAK,cAAc,CAAC,CAAC;AAC7L,aAAoB,qBAAAC,MAAM,WAAW;AAAA,IACnC,WAAW,aAAa,aAAa,UAAU,OAAO,QAAQ,SAAS,OAAO,MAAM;AAAA,IACpF,WAAW,QAAQ;AAAA,IACnB;AAAA,IACA,UAAU,CAAC,CAAC,mBAA4B,qBAAAC,KAAK,MAAM,SAAS;AAAA,MAC1D,IAAI;AAAA,MACJ,IAAI,MAAM;AAAA,MACV,WAAW,QAAQ;AAAA,IACrB,GAAG,aAAa,CAAC,GAAG,OAAO,IAAI,CAAC,MAAM,UAAU;AAC9C,YAAM;AAAA,QACJ,QAAQ;AAAA,QACR;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,gBAAgB,WAAWL;AAC9C,YAAM,aAAa;AACnB,YAAM,YAAY,OAAO,sBAAsB,cAAc,EAAC,uDAAoB,OAAO;AACzF,YAAM,YAAY,SAAS,UAAU,UAAU;AAC/C,YAAM,YAAY,WAAW,IAAI,IAAI;AACrC,UAAI,CAAC,WAAW;AACd,eAAO;AAAA,MACT;AACA,iBAAoB,qBAAAI,MAAM,KAAK;AAAA,QAC7B,WAAW,gBAAgB,UAAU;AAAA,QACrC,WAAW,QAAQ;AAAA,QACnB,UAAU,CAAC,CAAC,oBAA6B,qBAAAC,KAAK,MAAM,SAAS;AAAA,UAC3D,IAAI,eAAe;AAAA,UACnB,WAAW,QAAQ;AAAA,QACrB,GAAG,uCAAW,QAAQ,CAAC,GAAG,cAAc,UAAa,CAAC,iBAA0B,qBAAAA,KAAK,WAAW,SAAS;AAAA,UACvG,GAAG;AAAA,UACH,GAAG;AAAA,UACH,MAAM;AAAA,QACR,GAAG,kBAAkB,CAAC,CAAC;AAAA,MACzB,GAAG,KAAK;AAAA,IACV,CAAC,GAAG,SAAS,kBAA2B,qBAAAA,KAAK,KAAK;AAAA,MAChD,WAAW,QAAQ;AAAA,MACnB,cAAuB,qBAAAA,KAAK,OAAO,SAAS,CAAC,GAAG,eAAe,gBAAgB;AAAA,QAC7E,MAAM;AAAA,MACR,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,YAAY,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9D,MAAM,mBAAAC,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3B,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhE,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,MAAM,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,QAAQ,mBAAAA,QAAU;AAAA,EAClB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtJ,cAAc,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,OAAO,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9F,mBAAmB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlF,oBAAoB,mBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItD,gBAAgB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,YAAY,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtB,eAAe,mBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxE,UAAU,mBAAAA,QAAU;AACtB,IAAI;;;ApBzUJ,IAAAC,uBAA2C;AAU3C,SAAS,WAAW,OAAO;AACzB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,aAAoB,qBAAAC,MAAY,kBAAU;AAAA,IACxC,UAAU,CAAC,SAAS,IAAI,YAAU;AAChC,UAAI,CAAC,MAAM,MAAM,EAAE,YAAY,MAAM,MAAM,EAAE,aAAa,QAAQ;AAChE,eAAO;AAAA,MACT;AACA,iBAAoB,qBAAAC,KAAK,aAAa;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,MAAM;AAAA,IACX,CAAC,GAAG,SAAS,IAAI,YAAU;AACzB,UAAI,CAAC,MAAM,MAAM,EAAE,YAAY,MAAM,MAAM,EAAE,aAAa,QAAQ;AAChE,eAAO;AAAA,MACT;AACA,iBAAoB,qBAAAA,KAAK,aAAa;AAAA,QACpC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,MAAM;AAAA,IACX,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7D,WAAW,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,mBAAAA,QAAU;AACnB,IAAI;;;AsBjEJ,IAAAC,UAAuB;AACvB,IAAAC,qBAAsB;;;ACDf,SAAS,6BAA6B,MAAM;AACjD,SAAO,qBAAqB,0BAA0B,IAAI;AAC5D;AACO,IAAM,6BAA6B,uBAAuB,0BAA0B,CAAC,MAAM,CAAC;;;ACHnG,IAAAC,UAAuB;;;ACEhB,IAAM,0BAA0B,eAAO,QAAQ;AAAA,EACpD,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,eAAe;AAAA,EACf,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,IACA,OAAO,SAAS;AAAA,MACd,MAAM;AAAA,MACN,aAAa;AAAA,IACf,GAAG,MAAM,YAAY,SAAS;AAAA,MAC5B,MAAM;AAAA,IACR,CAAC,CAAC;AAAA,EACJ,GAAG;AAAA,IACD,OAAO;AAAA,MACL,eAAe;AAAA,IACjB;AAAA,IACA,OAAO,SAAS;AAAA,MACd,iBAAiB;AAAA,MACjB,QAAQ;AAAA,IACV,GAAG,MAAM,YAAY,SAAS;AAAA,MAC5B,QAAQ;AAAA,IACV,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,EAAE;;;ADlBF,IAAAC,uBAA2C;AAC5B,SAAR,iBAAkC,OAAO;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,QAAQ,SAAS;AACvB,QAAM,cAAc,YAAY,OAAO,iCAAiC;AACxE,QAAM,QAAQ,YAAY,OAAO,kBAAkB;AACnD,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,YAAY,IAAI,CAAC;AAAA,IACtB;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,QAAQ,MAAM,KAAK,MAAM;AAC/B,UAAM,SAAS,MAAM;AACrB,UAAM,eAAe,yBAAyB,MAAM;AACpD,UAAM,eAAe,SAAS,UAAU,UAAU,QAAQ,YAAY,MAAM;AAC5E,QAAI,MAAuC;AACzC,YAAM,UAAU,gBAAgB,OAAO,KAAK,MAAM;AAClD,UAAI,SAAS;AACX,gBAAQ,MAAM,CAAC,8FAA8F,6EAA6E,0DAA0D,EAAE,KAAK,IAAI,CAAC;AAAA,MAClQ;AAAA,IACF;AACA,eAAoB,qBAAAC,MAAY,kBAAU;AAAA,MACxC,UAAU,CAAC,gBAAgB,OAAO,KAAK,MAAM,cAA0B,qBAAAC,KAAK,yBAAyB;AAAA,QACnG,GAAG,KAAK,IAAI;AAAA,QAEZ,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,OAAO,UAAU,KAAK,CAAC,QAAQ,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,CAAC,OAAO,KAAK,CAAC;AAAA,QAChH,WAAW,QAAQ;AAAA,QACnB,YAAY;AAAA,UACV,eAAe;AAAA,QACjB;AAAA,MACF,CAAC,GAAG,SAAS,UAAU,UAAU,YAAqB,qBAAAA,KAAK,yBAAyB;AAAA,QAClF,GAAG,KAAK,IAAI,IAAI,aAAa,KAAK,CAAC,MAAM,OAAO,KAAK,IAAI,aAAa,KAAK,CAAC;AAAA,QAC5E,WAAW,QAAQ;AAAA,QACnB,YAAY;AAAA,UACV,eAAe;AAAA,QACjB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,GAAG,GAAG,MAAM,IAAI,KAAK,EAAE;AAAA,EACzB,CAAC;AACH;;;AE5DA,IAAAC,UAAuB;AAYvB,IAAAC,uBAA2C;AAC5B,SAAR,iBAAkC,OAAO;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,QAAQ,SAAS;AACvB,QAAM,cAAc,YAAY,OAAO,iCAAiC;AACxE,QAAM,QAAQ,YAAY,OAAO,kBAAkB;AACnD,MAAI,YAAY,WAAW,GAAG;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,YAAY,IAAI,CAAC;AAAA,IACtB;AAAA,IACA;AAAA,EACF,MAAM;AACJ,UAAM,QAAQ,MAAM,KAAK,MAAM;AAC/B,UAAM,SAAS,MAAM;AACrB,UAAM,eAAe,yBAAyB,MAAM;AACpD,UAAM,eAAe,SAAS,UAAU,UAAU,QAAQ,YAAY,MAAM;AAC5E,QAAI,MAAuC;AACzC,YAAM,UAAU,gBAAgB,OAAO,KAAK,MAAM;AAClD,UAAI,SAAS;AACX,gBAAQ,MAAM,CAAC,8FAA8F,6EAA6E,0DAA0D,EAAE,KAAK,IAAI,CAAC;AAAA,MAClQ;AAAA,IACF;AACA,eAAoB,qBAAAC,MAAY,kBAAU;AAAA,MACxC,UAAU,CAAC,gBAAgB,OAAO,KAAK,MAAM,cAA0B,qBAAAC;AAAA,QAAK;AAAA,QAE1E;AAAA,UACA,GAAG,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,IAAI,OAAO,UAAU,KAAK,CAAC,IAAI,GAAG,MAAM,OAAO,KAAK,CAAC,UAAU,MAAM,MAAM,CAAC,OAAO,KAAK,CAAC;AAAA,UAC9H,WAAW,QAAQ;AAAA,UACnB,YAAY;AAAA,YACV,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MAAC,GAAG,SAAS,UAAU,UAAU,YAAqB,qBAAAA,KAAK,yBAAyB;AAAA,QAClF,GAAG,KAAK,aAAa,KAAK,CAAC,IAAI,GAAG,MAAM,aAAa,KAAK,CAAC,IAAI,MAAM,MAAM;AAAA,QAC3E,WAAW,QAAQ;AAAA,QACnB,YAAY;AAAA,UACV,eAAe;AAAA,QACjB;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,GAAG,GAAG,MAAM,IAAI,KAAK,EAAE;AAAA,EACzB,CAAC;AACH;;;AJtDA,IAAAC,uBAA2C;AAC3C,IAAMC,qBAAoB,MAAM;AAC9B,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,EACf;AACA,SAAO,eAAe,OAAO,4BAA4B;AAC3D;AAWA,SAAS,oBAAoB,OAAO;AAClC,QAAM;AAAA,IACJ,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,UAAUA,mBAAkB;AAClC,aAAoB,qBAAAC,MAAY,kBAAU;AAAA,IACxC,UAAU,CAAC,sBAA+B,qBAAAC,KAAK,kBAAkB;AAAA,MAC/D,MAAM;AAAA,MACN;AAAA,IACF,CAAC,GAAG,sBAA+B,qBAAAA,KAAK,kBAAkB;AAAA,MACxD,MAAM;AAAA,MACN;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,oBAAoB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtE,GAAG,mBAAAC,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC3C,GAAG,mBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAC7C,IAAI;;;AK7CJ,IAAAC,UAAuB;AACvB,IAAAC,sBAAsB;AAEtB,IAAAC,uBAA4B;AAM5B,SAAS,eAAe,OAAO;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,EACV,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,eAAe;AACnB,QAAM,SAAS,SAAS;AAAA,IACtB,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR,GAAG,WAAW;AACd,aAAoB,qBAAAC,KAAK,YAAY;AAAA,IACnC;AAAA,IACA,cAAuB,qBAAAA,KAAK,QAAQ;AAAA,MAClC,GAAG,OAAO,OAAO;AAAA,MACjB,GAAG,MAAM,OAAO;AAAA,MAChB,OAAO,QAAQ,OAAO,OAAO,OAAO;AAAA,MACpC,QAAQ,SAAS,OAAO,MAAM,OAAO;AAAA,IACvC,CAAC;AAAA,EACH,CAAC;AACH;AACA,OAAwC,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjE,IAAI,oBAAAC,QAAU,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,QAAQ,oBAAAA,QAAU,MAAM;AAAA,IACtB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,MAAM,oBAAAA,QAAU;AAAA,IAChB,OAAO,oBAAAA,QAAU;AAAA,IACjB,KAAK,oBAAAA,QAAU;AAAA,EACjB,CAAC;AACH,IAAI;;;ACtDJ,IAAAC,UAAuB;AACvB,IAAAC,sBAAsB;;;ACJf,SAAS,0BAA0B,MAAM;AAC9C,SAAO,qBAAqB,iBAAiB,IAAI;AACnD;AACO,IAAM,oBAAoB,uBAAuB,iBAAiB,CAAC,QAAQ,QAAQ,kBAAkB,cAAc,CAAC;;;ACHpH,IAAM,WAAW,eAAO,KAAK;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,CAAC;AAAA,IACrC,CAAC,KAAK,kBAAkB,YAAY,EAAE,GAAG,OAAO;AAAA,EAClD,GAAG;AAAA,IACD,CAAC,KAAK,kBAAkB,cAAc,EAAE,GAAG,OAAO;AAAA,EACpD,GAAG,OAAO,IAAI;AAChB,CAAC,EAAE,CAAC,CAAC;AACE,IAAM,WAAW,eAAO,QAAQ;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AACR,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,SAAS,MAAM,QAAQ,OAAO,QAAQ;AAAA,EACtC,gBAAgB;AAAA,EAChB,aAAa;AACf,EAAE;;;ACpBF,IAAAC,UAAuB;AAGvB,IAAAC,uBAA4B;AAIrB,SAAS,mBAAmB,OAAO;AACxC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,SAAS;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AACD,aAAoB,qBAAAC,KAAW,kBAAU;AAAA,IACvC,UAAU,OAAO,IAAI,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,IACF,MAAG;AA7BP;AA6BuB,sCAAAA,KAAK,UAAU;AAAA,QAChC,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,WAAW,QAAQ;AAAA,MACrB,GAAG,cAAY,oCAAO,YAAP,mCAAsB,KAAK,EAAE;AAAA,KAAC;AAAA,EAC/C,CAAC;AACH;;;ACrCA,IAAAC,UAAuB;AAGvB,IAAAC,uBAA4B;AAIrB,SAAS,qBAAqB,OAAO;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,SAAS,SAAS;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW;AAAA,EACb,CAAC;AACD,aAAoB,qBAAAC,KAAW,kBAAU;AAAA,IACvC,UAAU,OAAO,IAAI,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,IACF,MAAG;AA7BP;AA6BuB,sCAAAA,KAAK,UAAU;AAAA,QAChC,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,IAAI;AAAA,QACJ,WAAW,QAAQ;AAAA,MACrB,GAAG,gBAAc,oCAAO,YAAP,mCAAsB,KAAK,EAAE;AAAA,KAAC;AAAA,EACjD,CAAC;AACH;;;AJtBA,IAAAC,uBAA2C;AAX3C,IAAMC,cAAY,CAAC,YAAY,YAAY;AAY3C,IAAMC,qBAAoB,CAAC;AAAA,EACzB;AACF,MAAM;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,cAAc,CAAC,QAAQ,cAAc;AAAA,IACrC,gBAAgB,CAAC,QAAQ,gBAAgB;AAAA,EAC3C;AACA,SAAO,eAAe,OAAO,2BAA2B,OAAO;AACjE;AAUA,SAAS,WAAW,SAAS;AAC3B,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM,cAAc,eAAe;AACnC,QAAM;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOD,WAAS;AACxD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,SAAS;AACb,QAAM,UAAUC,mBAAkB,KAAK;AACvC,QAAM,iBAAiB,MAAM,SAAS,CAAC,CAAC;AACxC,QAAM,eAAe,MAAM,SAAS,CAAC,CAAC;AACtC,aAAoB,qBAAAC,MAAM,UAAU,SAAS,CAAC,GAAG,OAAO;AAAA,IACtD,WAAW,QAAQ;AAAA,IACnB,UAAU,CAAC,gBAAyB,qBAAAC,KAAK,oBAAoB;AAAA,MAC3D,MAAM;AAAA,MACN,OAAO,YAAY;AAAA,MACnB,KAAK,YAAY,SAAS,YAAY;AAAA,MACtC;AAAA,IACF,CAAC,GAAG,kBAA2B,qBAAAA,KAAK,sBAAsB;AAAA,MACxD,MAAM;AAAA,MACN,OAAO,YAAY;AAAA,MACnB,KAAK,YAAY,QAAQ,YAAY;AAAA,MACrC;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ;AACA,OAAwC,WAAW,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7D,SAAS,oBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,YAAY,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,UAAU,oBAAAA,QAAU;AACtB,IAAI;;;AKpFJ,IAAAC,UAAuB;;;ACDhB,IAAM,oBAAoB,CAAC,eAAe,uBAAuB,qBAAqB,iBAAiB;;;ADA9G,IAAMC,cAAY,CAAC,SAAS,SAAS,UAAU,SAAS,UAAU,UAAU,UAAU,WAAW,MAAM,iBAAiB,QAAQ,YAAY,SAAS,aAAa,iBAAiB,WAAW,UAAU,eAAe,mBAAmB,qBAAqB,gBAAgB,YAAY,aAAa,cAAc,aAAa;AAa5T,IAAM,mBAAmB,WAAS;AAjBzC;AAkBE,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OACJ,OAAO,8BAA8B,OAAOA,WAAS;AACvD,QAAM,KAAK,MAAM;AACjB,QAAM,aAAa,GAAG,EAAE;AACxB,QAAM,sBAAsB,WAAW,gBAAgB,WAAW,UAAa,OAAO,KAAK,UAAQ,KAAK,WAAW,YAAY;AAC/H,QAAM,mBAAyB,gBAAQ,MAAM,CAAC;AAAA,IAC5C,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,MAAM,MAAM,KAAK;AAAA,MACf,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI,QAAM,EAAE,QAAQ,WAAW,CAAC,GAAG,MAAM,CAAC;AAAA,IACvE,GAAG,CAAC,GAAG,UAAU,KAAK;AAAA,EACxB,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC;AACrB,QAAM,mBAAyB,gBAAQ,MAAM,CAAC;AAAA,IAC5C,IAAI;AAAA,IACJ,WAAW;AAAA,IACX,MAAM,MAAM,KAAK;AAAA,MACf,QAAQ,KAAK,IAAI,GAAG,OAAO,IAAI,QAAM,EAAE,QAAQ,WAAW,CAAC,GAAG,MAAM,CAAC;AAAA,IACvE,GAAG,CAAC,GAAG,UAAU,KAAK;AAAA,EACxB,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC;AACrB,QAAM,oBAA0B,gBAAQ,MAAM,OAAO,IAAI,OAAK,SAAS;AAAA,IACrE,MAAM;AAAA,EACR,GAAG,GAAG;AAAA,IACJ,QAAQ,sBAAsB,eAAe;AAAA,EAC/C,CAAC,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC;AAClC,QAAM,eAAe,sBAAsB,SAAY;AACvD,QAAM,iBAAuB,gBAAQ,MAAM;AACzC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,WAAO,sBAAsB,QAAQ,MAAM,IAAI,UAAQ,SAAS;AAAA,MAC9D,WAAW;AAAA,IACb,GAAG,IAAI,CAAC;AAAA,EACV,GAAG,CAAC,cAAc,qBAAqB,KAAK,CAAC;AAC7C,QAAM,eAAe,sBAAsB,mBAAmB;AAC9D,QAAM,iBAAuB,gBAAQ,MAAM;AACzC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,WAAO,sBAAsB,MAAM,IAAI,UAAQ,SAAS;AAAA,MACtD,WAAW;AAAA,IACb,GAAG,IAAI,CAAC,IAAI;AAAA,EACd,GAAG,CAAC,cAAc,qBAAqB,KAAK,CAAC;AAC7C,QAAM,sBAAsB,SAAS,CAAC,GAAG,MAAM;AAAA,IAC7C,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA;AAAA,IACA,uBAAqB,4CAAW,YAAX,mBAAoB,aAAY,WAAU,+CAAe,OAAM,WAAU,+CAAe,OAAM;AAAA,IACnH;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACX,CAAC;AACD,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,YAAY;AAAA,IAChB,UAAU,6BAAM;AAAA,IAChB,YAAY,6BAAM;AAAA,EACpB;AACA,QAAM,qBAAqB;AAAA,IACzB,UAAU,QAAQ,UAAU;AAAA,EAC9B;AACA,QAAM,gBAAgB;AAAA,IACpB,IAAI;AAAA,EACN;AACA,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,kBAAkB;AAAA,IACtB;AAAA,IACA;AAAA,EACF;AACA,QAAM,qBAAqB,SAAS,CAAC,GAAG,sBAAsB;AAAA,IAC5D,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,EACL,GAAG,aAAa;AAChB,QAAM,cAAc;AAAA,IAClB;AAAA,IACA;AAAA,EACF;AACA,QAAM,qBAAqB;AAAA,IACzB;AAAA,IACA,iBAAgB,iBAAM,cAAN,mBAAiB,WAAjB,mBAAyB;AAAA,IACzC,kBAAiB,iBAAM,cAAN,mBAAiB,WAAjB,mBAAyB;AAAA,EAC5C;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AjDtIA,IAAAC,uBAA2C;AAY3C,IAAM,WAA8B,mBAAW,SAASC,UAAS,SAAS,KAAK;AA/B/E;AAgCE,QAAM,QAAQ,cAAc;AAAA,IAC1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,KAAK;AAC1B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,uBAAuB,qBAAqB,GAAG;AACnD,QAAM,YAAU,WAAM,UAAN,mBAAa,YAAW;AACxC,QAAM,WAAU,WAAM,UAAN,mBAAa;AAC7B,aAAoB,qBAAAC,KAAK,mBAAmB,SAAS,CAAC,GAAG,wBAAwB;AAAA,IAC/E,cAAuB,qBAAAC,MAAM,eAAe,SAAS,CAAC,GAAG,oBAAoB;AAAA,MAC3E,UAAU,CAAC,MAAM,eAAe,cAAuB,qBAAAD,KAAK,SAAS,SAAS,CAAC,IAAG,WAAM,cAAN,mBAAiB,OAAO,CAAC,IAAI,MAAM,CAAC,MAAM,kBAA2B,qBAAAA,KAAK,cAAc,SAAS,CAAC,GAAG,WAAW,CAAC,OAAgB,qBAAAC,MAAM,eAAe,SAAS,CAAC,GAAG,oBAAoB;AAAA,QACvQ,UAAU,KAAc,qBAAAD,KAAK,YAAY,SAAS,CAAC,GAAG,SAAS,CAAC,OAAgB,qBAAAC,MAAM,KAAK,SAAS,CAAC,GAAG,oBAAoB;AAAA,UAC1H,UAAU,KAAc,qBAAAD,KAAK,SAAS,SAAS,CAAC,GAAG,YAAY,CAAC,OAAgB,qBAAAA,KAAK,eAAe,SAAS,CAAC,GAAG,YAAY,CAAC,OAAgB,qBAAAA,KAAK,qBAAqB,SAAS,CAAC,GAAG,kBAAkB,CAAC,CAAC;AAAA,QAC3M,CAAC,CAAC,OAAgB,qBAAAA,KAAK,YAAY,SAAS,CAAC,GAAG,eAAe,CAAC,OAAgB,qBAAAA,KAAK,gBAAgB,SAAS,CAAC,GAAG,aAAa,CAAC,GAAG,QAAQ;AAAA,MAC7I,CAAC,CAAC,GAAG,CAAC,MAAM,eAAwB,qBAAAA,KAAK,SAAS,SAAS,CAAC,IAAG,WAAM,cAAN,mBAAiB,OAAO,CAAC,CAAC;AAAA,IAC3F,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ,CAAC;AACD,IAAI,KAAuC,UAAS,cAAc;AAClE,OAAwC,SAAS,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,QAAQ,oBAAAE,QAAU,MAAM;AAAA,IACtB,SAAS,oBAAAA,QAAU;AAAA,EACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe,oBAAAA,QAAU,MAAM;AAAA,IAC7B,GAAG,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,IAC3C,GAAG,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,MAAM,CAAC;AAAA,EAC7C,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAI1E,cAAc,oBAAAA,QAAU;AAAA,EACxB,UAAU,oBAAAA,QAAU;AAAA,EACpB,WAAW,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjF,SAAS,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM;AAAA,EAC3C,MAAM,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMhB,qBAAqB,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI/B,MAAM,oBAAAA,QAAU,MAAM;AAAA,IACpB,YAAY,oBAAAA,QAAU;AAAA,IACtB,UAAU,oBAAAA,QAAU;AAAA,EACtB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,QAAQ,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,YAAY,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,iBAAiB,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM;AAAA,IACjD,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAClE,WAAW,oBAAAA,QAAU,OAAO;AAAA,EAC9B,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKF,iBAAiB,oBAAAA,QAAU,MAAM;AAAA,IAC/B,WAAW,oBAAAA,QAAU;AAAA,IACrB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,EACtE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,IAAI,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,QAAQ,oBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlD,SAAS,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,YAAY,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtB,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM;AAAA,IAC7D,QAAQ,oBAAAA,QAAU;AAAA,IAClB,MAAM,oBAAAA,QAAU;AAAA,IAChB,OAAO,oBAAAA,QAAU;AAAA,IACjB,KAAK,oBAAAA,QAAU;AAAA,EACjB,CAAC,CAAC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOH,aAAa,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,mBAAmB,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,yBAAyB,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMnC,aAAa,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5C,aAAa,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,OAAO,oBAAAA,QAAU;AAAA,EACjB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,EACtJ,OAAO,oBAAAA,QAAU,MAAM,CAAC,QAAQ,OAAO,CAAC;AAAA,EACxC,OAAO,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,oBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,OAAO,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,IAC5D,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,aAAa,oBAAAA,QAAU;AAAA,IACvB,kBAAkB,oBAAAA,QAAU;AAAA,IAC5B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,MAAM,oBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,MACnC,cAAc,oBAAAA,QAAU;AAAA,MACxB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU;AAAA,IAC5H,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,MAAM,oBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,MACnC,cAAc,oBAAAA,QAAU;AAAA,MACxB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU;AAAA,IAC5H,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC;AAAA,IACpC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,KAAK,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,QAAQ,CAAC;AAAA,IACrC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,iBAAiB,oBAAAA,QAAU;AAAA,IAC3B,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,EAC5B,CAAC,CAAC,CAAC,EAAE,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMf,OAAO,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,IAC5D,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,aAAa,oBAAAA,QAAU;AAAA,IACvB,kBAAkB,oBAAAA,QAAU;AAAA,IAC5B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,MAAM,oBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,MACnC,cAAc,oBAAAA,QAAU;AAAA,MACxB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU;AAAA,IAC5H,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,MAAM,oBAAAA,QAAU,MAAM,CAAC,SAAS,CAAC,EAAE;AAAA,MACnC,cAAc,oBAAAA,QAAU;AAAA,MACxB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU;AAAA,IAC5H,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,OAAO,CAAC;AAAA,IACpC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC;AAAA,IACnC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,KAAK,CAAC;AAAA,IAClC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,IAClB,MAAM,oBAAAA,QAAU,MAAM,CAAC,GAAG,CAAC;AAAA,IAC3B,SAAS,oBAAAA,QAAU;AAAA,IACnB,UAAU,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM;AAAA,MAC7C,OAAO,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,OAAO,UAAU,GAAG,oBAAAA,QAAU,IAAI,CAAC,EAAE;AAAA,MAC7F,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,MACvE,MAAM,oBAAAA,QAAU,MAAM,CAAC,YAAY,CAAC,EAAE;AAAA,IACxC,CAAC,GAAG,oBAAAA,QAAU,MAAM;AAAA,MAClB,QAAQ,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,EAAE;AAAA,MAC5C,YAAY,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC,EAAE,UAAU,EAAE;AAAA,MAC9G,MAAM,oBAAAA,QAAU,MAAM,CAAC,WAAW,CAAC,EAAE;AAAA,IACvC,CAAC,CAAC,CAAC;AAAA,IACH,MAAM,oBAAAA,QAAU;AAAA,IAChB,SAAS,oBAAAA,QAAU;AAAA,IACnB,aAAa,oBAAAA,QAAU;AAAA,IACvB,cAAc,oBAAAA,QAAU;AAAA,IACxB,aAAa,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IACtF,MAAM,oBAAAA,QAAU;AAAA,IAChB,aAAa,oBAAAA,QAAU;AAAA,IACvB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,eAAe,oBAAAA,QAAU;AAAA,IACzB,OAAO,oBAAAA,QAAU;AAAA,IACjB,YAAY,oBAAAA,QAAU;AAAA,IACtB,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,KAAK,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,WAAW,IAAI,GAAG,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACvE,QAAQ,oBAAAA,QAAU;AAAA,IAClB,UAAU,oBAAAA,QAAU,MAAM,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAAA,IACnD,SAAS,oBAAAA,QAAU;AAAA,IACnB,WAAW,oBAAAA,QAAU,MAAM,CAAC,QAAQ,CAAC;AAAA,IACrC,WAAW,oBAAAA,QAAU;AAAA,IACrB,OAAO,oBAAAA,QAAU;AAAA,IACjB,QAAQ,oBAAAA,QAAU;AAAA,IAClB,IAAI,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,QAAQ,oBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,oBAAAA,QAAU,MAAM,oBAAAA,QAAU,MAAM,CAAC;AAAA,IACtJ,cAAc,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,OAAO,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAC9F,mBAAmB,oBAAAA,QAAU,UAAU,CAAC,oBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,oBAAAA,QAAU,IAAI,CAAC;AAAA,IAClF,oBAAoB,oBAAAA,QAAU,MAAM,CAAC,UAAU,MAAM,CAAC;AAAA,IACtD,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,aAAa,oBAAAA,QAAU;AAAA,IACvB,aAAa,oBAAAA,QAAU;AAAA,IACvB,YAAY,oBAAAA,QAAU;AAAA,IACtB,eAAe,oBAAAA,QAAU,MAAM,CAAC,OAAO,eAAe,UAAU,OAAO,CAAC;AAAA,IACxE,UAAU,oBAAAA,QAAU;AAAA,IACpB,gBAAgB,oBAAAA,QAAU;AAAA,IAC1B,OAAO,oBAAAA,QAAU;AAAA,EACnB,CAAC,CAAC,CAAC,EAAE,UAAU;AACjB,IAAI;", "names": ["React", "import_prop_types", "React", "import_prop_types", "React", "_jsx", "import_jsx_runtime", "_excluded", "_jsx", "PropTypes", "React", "import_jsx_runtime", "_excluded", "_jsx", "React", "React", "import_prop_types", "useUtilityClasses", "React", "import_prop_types", "import_jsx_runtime", "_excluded", "_jsx", "PropTypes", "import_jsx_runtime", "_excluded", "_excluded2", "useUtilityClasses", "_jsx", "PropTypes", "useUtilityClasses", "import_jsx_runtime", "_excluded", "useUtilityClasses", "_jsx", "import_jsx_runtime", "_excluded", "useUtilityClasses", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_prop_types", "React", "React", "domain", "tickLabelPlacement", "filteredDomain", "React", "import_prop_types", "import_jsx_runtime", "_excluded", "_excluded2", "_jsx", "PropTypes", "React", "segmenter", "import_jsx_runtime", "_excluded", "useUtilityClasses", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "shorten<PERSON><PERSON><PERSON>", "import_jsx_runtime", "_excluded", "useUtilityClasses", "TICK_LABEL_GAP", "AXIS_LABEL_TICK_LABEL_GAP", "defaultProps", "shorten<PERSON><PERSON><PERSON>", "_jsxs", "_jsx", "PropTypes", "import_jsx_runtime", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_jsx_runtime", "_jsxs", "_jsx", "React", "import_jsx_runtime", "_jsxs", "_jsx", "import_jsx_runtime", "useUtilityClasses", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "import_jsx_runtime", "_jsx", "PropTypes", "React", "import_prop_types", "React", "import_jsx_runtime", "_jsx", "React", "import_jsx_runtime", "_jsx", "import_jsx_runtime", "_excluded", "useUtilityClasses", "_jsxs", "_jsx", "PropTypes", "React", "_excluded", "import_jsx_runtime", "<PERSON><PERSON><PERSON>", "_jsx", "_jsxs", "PropTypes"]}