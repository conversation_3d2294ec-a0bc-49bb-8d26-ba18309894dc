import { HotelService } from './hotel.service';
import { UpdateHotelDto } from 'src/common/dtos/update-hotel.dto';
export declare class HotelController {
    private readonly hotelService;
    constructor(hotelService: HotelService);
    getHotelList(): Promise<(import("mongoose").Document<unknown, {}, import("../../infrastructure/database/hotel.schema").Hotel, {}> & import("../../infrastructure/database/hotel.schema").Hotel & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
    getHotelDetail(id: string): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    deleteHotel(id: string): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
    updateHotel(id: string, updateHotelDto: UpdateHotelDto): Promise<import("../../infrastructure/database/hotel.schema").Hotel>;
}
