"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserInfosSchema = exports.UserInfos = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const mongoose = require("mongoose");
const asset_schema_1 = require("./asset.schema");
const account_schema_1 = require("./account.schema");
let UserInfos = class UserInfos extends mongoose_2.Document {
    userId;
    fullName;
    dob;
    phone;
    address;
    avatar;
    lastLoginAt;
    flaggedCount;
    banReason;
    bannedAt;
};
exports.UserInfos = UserInfos;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Types.ObjectId, ref: 'Account' }),
    __metadata("design:type", account_schema_1.Account)
], UserInfos.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true, maxLength: 100 }),
    __metadata("design:type", String)
], UserInfos.prototype, "fullName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ trim: true }),
    __metadata("design:type", String)
], UserInfos.prototype, "dob", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserInfos.prototype, "phone", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserInfos.prototype, "address", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose.Types.ObjectId, ref: 'Asset' }),
    __metadata("design:type", asset_schema_1.Asset)
], UserInfos.prototype, "avatar", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date, default: null }),
    __metadata("design:type", Date)
], UserInfos.prototype, "lastLoginAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], UserInfos.prototype, "flaggedCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: null }),
    __metadata("design:type", String)
], UserInfos.prototype, "banReason", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: Date, default: null }),
    __metadata("design:type", Date)
], UserInfos.prototype, "bannedAt", void 0);
exports.UserInfos = UserInfos = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        versionKey: false,
    })
], UserInfos);
exports.UserInfosSchema = mongoose_1.SchemaFactory.createForClass(UserInfos);
//# sourceMappingURL=userinfo.schema.js.map