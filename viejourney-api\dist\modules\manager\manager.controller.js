"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManagerController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const create_hotel_dto_1 = require("../../common/dtos/create-hotel.dto");
const update_hotel_dto_1 = require("../../common/dtos/update-hotel.dto");
const XLSX = require("xlsx");
const hotel_service_1 = require("../hotel/hotel.service");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const role_enum_1 = require("../../common/enums/role.enum");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
let ManagerController = class ManagerController {
    hotelService;
    constructor(hotelService) {
        this.hotelService = hotelService;
    }
    async getHotelList() {
        return this.hotelService.getHotelList();
    }
    async createHotel(createHotelDto) {
        return await this.hotelService.addHotel(createHotelDto);
    }
    async getHotelDetail(id) {
        const hotel = await this.hotelService.getHotelDetail(id);
        if (!hotel) {
            throw new common_1.NotFoundException(`Hotel with ID ${id} not found`);
        }
        return hotel;
    }
    async deleteHotel(id) {
        return this.hotelService.deleteHotel(id);
    }
    async updateHotel(id, updateHotelDto) {
        return this.hotelService.updateHotel(id, updateHotelDto);
    }
    async importHotels(file) {
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const hotels = XLSX.utils.sheet_to_json(sheet);
        await this.hotelService.addListOfHotels(hotels);
        return { count: hotels.length };
    }
};
exports.ManagerController = ManagerController;
__decorate([
    (0, common_1.Get)('hotel'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "getHotelList", null);
__decorate([
    (0, common_1.Post)('hotel/add'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_hotel_dto_1.CreateHotelDto]),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "createHotel", null);
__decorate([
    (0, common_1.Get)('hotel/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "getHotelDetail", null);
__decorate([
    (0, common_1.Delete)('hotel/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "deleteHotel", null);
__decorate([
    (0, common_1.Patch)('hotel/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_hotel_dto_1.UpdateHotelDto]),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "updateHotel", null);
__decorate([
    (0, common_1.Post)('hotel/import'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ManagerController.prototype, "importHotels", null);
exports.ManagerController = ManagerController = __decorate([
    (0, common_1.Controller)('manager'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.Manager),
    __metadata("design:paramtypes", [hotel_service_1.HotelService])
], ManagerController);
//# sourceMappingURL=manager.controller.js.map