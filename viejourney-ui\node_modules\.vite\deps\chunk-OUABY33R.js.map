{"version": 3, "sources": ["../../@mui/material/useMediaQuery/index.js"], "sourcesContent": ["import { unstable_createUseMediaQuery } from '@mui/system/useMediaQuery';\nimport THEME_ID from \"../styles/identifier.js\";\nconst useMediaQuery = unstable_createUseMediaQuery({\n  themeId: THEME_ID\n});\nexport default useMediaQuery;"], "mappings": ";;;;;;AAEA,IAAM,gBAAgB,6BAA6B;AAAA,EACjD,SAAS;AACX,CAAC;AACD,IAAO,wBAAQ;", "names": []}