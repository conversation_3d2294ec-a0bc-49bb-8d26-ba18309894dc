"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const assets_service_1 = require("../assets/assets.service");
let UserService = class UserService {
    userInfosModel;
    accountModel;
    assetModel;
    tripModel;
    blogModel;
    assetsService;
    constructor(userInfosModel, accountModel, assetModel, tripModel, blogModel, assetsService) {
        this.userInfosModel = userInfosModel;
        this.accountModel = accountModel;
        this.assetModel = assetModel;
        this.tripModel = tripModel;
        this.blogModel = blogModel;
        this.assetsService = assetsService;
    }
    async getAllUser(filter, pagination) {
        let query = this.userInfosModel.find();
        if (filter?.username) {
            query = query.where('fullName', new RegExp(filter.username, 'i'));
        }
        if (filter?.userId) {
            query = query.where('_id', filter.userId);
        }
        const populateQuery = {
            path: 'userId',
            model: 'Account',
            select: 'email role status createdAt',
        };
        if (filter?.role || filter?.status || filter?.email) {
            const match = {};
            if (filter.role) {
                match.role = filter.role;
            }
            if (filter.status) {
                match.status = filter.status;
            }
            if (filter.email) {
                match.email = new RegExp(filter.email, 'i');
            }
            populateQuery.match = match;
        }
        const totalItems = await this.userInfosModel.countDocuments(query.getQuery());
        if (pagination?.page && pagination?.pageSize) {
            const skip = (pagination.page - 1) * pagination.pageSize;
            query = query.skip(skip).limit(pagination.pageSize);
        }
        const users = await query.populate(populateQuery).lean().exec();
        const filteredUsers = users
            .filter((user) => user.userId)
            .map((user) => ({
            userId: user._id.toString(),
            accountId: user.userId._id.toString(),
            email: user.userId.email,
            userName: user.fullName,
            role: user.userId.role,
            status: user.userId.status,
            phone: user.phone || '',
            address: user.address || '',
            createdAt: user.userId.createdAt,
        }));
        const responseData = {
            users: filteredUsers,
            totalItems,
        };
        if (pagination?.page && pagination?.pageSize) {
            const totalPages = Math.ceil(totalItems / pagination.pageSize);
            responseData.totalPages = totalPages;
            responseData.currentPage = pagination.page;
            responseData.pageSize = pagination.pageSize;
        }
        if (filteredUsers.length === 0) {
            return {
                status: 'success',
                message: filter
                    ? `No users found matching filters: ${JSON.stringify(filter)}`
                    : 'No users found in the system',
                data: responseData,
            };
        }
        return {
            status: 'success',
            message: 'Users retrieved successfully',
            data: responseData,
        };
    }
    async getUserByID(id) {
        const user = await this.userInfosModel
            .findOne({
            userId: new mongoose_2.Types.ObjectId(id),
        })
            .populate({
            path: 'avatar',
            model: 'Asset',
            select: 'url -_id',
        })
            .exec();
        if (!user) {
            throw new common_1.HttpException(`User with ID ${id} not found`, 404);
        }
        return {
            ...user.toObject(),
            avatar: user.avatar ? user.avatar.url?.toString() : null,
        };
    }
    async updateUserAvatar(id, file) {
        const userInfo = await this.userInfosModel
            .findOne({ userId: new mongoose_2.Types.ObjectId(id) })
            .populate('avatar')
            .exec();
        if (!userInfo) {
            throw new common_1.NotFoundException(`User info with ID ${id} not found`);
        }
        if (!file) {
            throw new common_1.BadRequestException('No file uploaded');
        }
        if (userInfo?.avatar?.publicId) {
            await this.assetsService.deleteImage(userInfo.avatar.publicId);
        }
        let uploadResult = null;
        let assetId;
        uploadResult = await this.assetsService.uploadImage(file, {
            public_id: `users/${userInfo._id}/google-avatar`,
            folder: 'vie-journey/avatars',
        });
        const assetData = {
            userId: new mongoose_2.Types.ObjectId(id),
            type: 'AVATAR',
            assetOwner: 'USER',
            subsection: null,
            url: uploadResult?.secure_url,
            publicId: uploadResult?.public_id,
            location: uploadResult.public_id.split('/')[0],
            format: uploadResult.format.toLocaleUpperCase(),
            file_size: `${(uploadResult.bytes / 1024).toFixed(2)} KB`,
            dimensions: `${uploadResult.width} x ${uploadResult.height}`,
        };
        if (userInfo?.avatar?._id) {
            await this.assetModel.updateOne({ _id: userInfo.avatar._id }, { $set: assetData });
        }
        else {
            const asset = await this.assetModel.create(assetData);
            assetId = asset._id;
            userInfo.avatar = assetId;
            await userInfo.save();
        }
        const updatedUserInfo = await this.userInfosModel
            .findById(userInfo._id)
            .populate('avatar')
            .exec();
        return updatedUserInfo;
    }
    async updateUserInfo(id, updateUserInfoDto) {
        const updatedUser = await this.userInfosModel
            .findOneAndUpdate({
            userId: new mongoose_2.Types.ObjectId(id),
        }, updateUserInfoDto, { new: true })
            .exec();
        if (!updatedUser) {
            throw new common_1.NotFoundException(`User info with ID ${id} not found`);
        }
        return updatedUser;
    }
    async deleteUserInfo(id) {
        const userInfo = await this.userInfosModel.findById(id).exec();
        if (!userInfo) {
            throw new common_1.NotFoundException(`User info with ID ${id} not found`);
        }
        await this.accountModel.findByIdAndDelete(userInfo.userId).exec();
        await this.userInfosModel.findByIdAndDelete(id).exec();
        return common_1.HttpStatus.OK;
    }
    async updateUserRole(userInfoId, role) {
        const userInfo = await this.userInfosModel.findById(userInfoId);
        if (!userInfo) {
            throw new common_1.NotFoundException(`User with ID ${userInfoId} not found`);
        }
        const account = await this.accountModel.findById(userInfo.userId);
        if (!account) {
            throw new common_1.NotFoundException(`Account not found for user ${userInfoId}`);
        }
        try {
            account.role = role;
            await account.save();
            return {
                status: 'success',
                message: 'User role updated successfully',
                data: {
                    userId: userInfo._id.toString(),
                    accountId: account._id.toString(),
                    email: account.email,
                    userName: userInfo.fullName,
                    role: account.role,
                    status: account.status,
                },
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to update user role: ${error.message}`);
        }
    }
    async getUserDetails(userId, email) {
        if (!userId) {
            throw new common_1.BadRequestException('User ID is required');
        }
        const userInfo = await this.userInfosModel
            .findOne({ userId: new mongoose_2.Types.ObjectId(userId) })
            .exec();
        const destination = await this.tripModel
            .find({
            tripmates: { $in: [email] },
        })
            .select('destination')
            .lean()
            .exec();
        const blogCount = await this.blogModel.countDocuments({
            createdBy: new mongoose_2.Types.ObjectId(userInfo?._id),
        });
        const likeCount = await this.blogModel.countDocuments({
            likes: { $in: [new mongoose_2.Types.ObjectId(userId)] },
        });
        const tripCount = await this.tripModel.countDocuments({
            tripmates: { $in: [email] },
        });
        return {
            destinations: destination.map((d) => {
                return {
                    name: d.destination.name,
                    location: d.destination.location,
                };
            }),
            blogCount,
            likeCount,
            tripCount,
        };
    }
};
exports.UserService = UserService;
exports.UserService = UserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('UserInfos')),
    __param(1, (0, mongoose_1.InjectModel)('Account')),
    __param(2, (0, mongoose_1.InjectModel)('Asset')),
    __param(3, (0, mongoose_1.InjectModel)('Trip')),
    __param(4, (0, mongoose_1.InjectModel)('Blog')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        assets_service_1.AssetsService])
], UserService);
//# sourceMappingURL=user.service.js.map