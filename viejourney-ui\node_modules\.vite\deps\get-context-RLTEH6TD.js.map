{"version": 3, "sources": ["../../@mui/x-telemetry/esm/context.js", "../../@mui/x-telemetry/esm/runtime/window-storage.js", "../../@mui/x-telemetry/esm/runtime/get-context.js"], "sourcesContent": ["export default {\n  \"config\": {\n    \"isInitialized\": true\n  },\n  \"traits\": {\n    \"isDocker\": false,\n    \"isCI\": false,\n    \"machineId\": null,\n    \"projectId\": \"b28c94b2195c8ed259f0b415aaee3f39b0b2920a4537611499fa044956917a21\",\n    \"sessionId\": \"ef94403e359bfc1a623012b1076f2ee5cc1f14ff2620da1457e8130eb4b5d89f\",\n    \"anonymousId\": \"77980b20a7bd57a2b7f97f504abc3a201768bbcab3957c2293d4b01e7e3198c4\"\n  }\n};", "const prefix = '__mui_x_telemetry_';\nfunction getStorageKey(key) {\n  return prefix + btoa(key);\n}\nexport function setWindowStorageItem(type, key, value) {\n  try {\n    if (typeof window !== 'undefined' && window[type]) {\n      window[type].setItem(getStorageKey(key), value);\n      return true;\n    }\n  } catch (_) {\n    // Storage is unavailable, skip it\n  }\n  return false;\n}\nexport function getWindowStorageItem(type, key) {\n  try {\n    if (typeof window !== 'undefined' && window[type]) {\n      return window[type].getItem(getStorageKey(key));\n    }\n  } catch (_) {\n    // Storage is unavailable, skip it\n  }\n  return null;\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport telemetryContext from \"../context.js\";\nimport { getWindowStorageItem, setWindowStorageItem } from \"./window-storage.js\";\nfunction generateId(length) {\n  let result = '';\n  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  const charactersLength = characters.length;\n  let counter = 0;\n  while (counter < length) {\n    result += characters.charAt(Math.floor(Math.random() * charactersLength));\n    counter += 1;\n  }\n  return result;\n}\nfunction pick(obj, keys) {\n  return keys.reduce((acc, key) => {\n    acc[key] = obj[key];\n    return acc;\n  }, {});\n}\nconst getBrowserFingerprint = typeof window === 'undefined' || process.env.NODE_ENV === 'test' ? () => undefined : async () => {\n  const fingerprintLCKey = 'fingerprint';\n  try {\n    const existingFingerprint = getWindowStorageItem('localStorage', fingerprintLCKey);\n    if (existingFingerprint) {\n      return JSON.parse(existingFingerprint);\n    }\n    const FingerprintJS = await import('@fingerprintjs/fingerprintjs');\n    const fp = await FingerprintJS.load({\n      monitoring: false\n    });\n    const fpResult = await fp.get();\n    const components = _extends({}, fpResult.components);\n    delete components.cookiesEnabled;\n    const fullHash = FingerprintJS.hashComponents(components);\n    const coreHash = FingerprintJS.hashComponents(_extends({}, pick(components, ['fonts', 'audio', 'languages', 'deviceMemory', 'timezone', 'sessionStorage', 'localStorage', 'indexedDB', 'openDatabase', 'platform', 'canvas', 'vendor', 'vendorFlavors', 'colorGamut', 'forcedColors', 'monochrome', 'contrast', 'reducedMotion', 'math', 'videoCard', 'architecture'])));\n    const result = {\n      fullHash,\n      coreHash\n    };\n    setWindowStorageItem('localStorage', fingerprintLCKey, JSON.stringify(result));\n    return result;\n  } catch (_) {\n    return null;\n  }\n};\nfunction getAnonymousId() {\n  const localStorageKey = 'anonymous_id';\n  const existingAnonymousId = getWindowStorageItem('localStorage', localStorageKey);\n  if (existingAnonymousId) {\n    return existingAnonymousId;\n  }\n  const generated = `anid_${generateId(32)}`;\n  if (setWindowStorageItem('localStorage', localStorageKey, generated)) {\n    return generated;\n  }\n  return '';\n}\nfunction getSessionId() {\n  const localStorageKey = 'session_id';\n  const existingSessionId = getWindowStorageItem('sessionStorage', localStorageKey);\n  if (existingSessionId) {\n    return existingSessionId;\n  }\n  const generated = `sesid_${generateId(32)}`;\n  if (setWindowStorageItem('sessionStorage', localStorageKey, generated)) {\n    return generated;\n  }\n  return `sestp_${generateId(32)}`;\n}\nasync function getTelemetryContext() {\n  telemetryContext.traits.sessionId = getSessionId();\n\n  // Initialize the context if it hasn't been initialized yet\n  // (e.g. postinstall not run)\n  if (!telemetryContext.config.isInitialized) {\n    telemetryContext.traits.anonymousId = getAnonymousId();\n    telemetryContext.config.isInitialized = true;\n  }\n  if (!telemetryContext.traits.fingerprint) {\n    telemetryContext.traits.fingerprint = await getBrowserFingerprint();\n  }\n  return telemetryContext;\n}\nexport default getTelemetryContext;"], "mappings": ";;;;;;AAAA,IAAO,kBAAQ;AAAA,EACb,UAAU;AAAA,IACR,iBAAiB;AAAA,EACnB;AAAA,EACA,UAAU;AAAA,IACR,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,IACb,aAAa;AAAA,IACb,eAAe;AAAA,EACjB;AACF;;;ACZA,IAAM,SAAS;AACf,SAAS,cAAc,KAAK;AAC1B,SAAO,SAAS,KAAK,GAAG;AAC1B;AACO,SAAS,qBAAqB,MAAM,KAAK,OAAO;AACrD,MAAI;AACF,QAAI,OAAO,WAAW,eAAe,OAAO,IAAI,GAAG;AACjD,aAAO,IAAI,EAAE,QAAQ,cAAc,GAAG,GAAG,KAAK;AAC9C,aAAO;AAAA,IACT;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;AACO,SAAS,qBAAqB,MAAM,KAAK;AAC9C,MAAI;AACF,QAAI,OAAO,WAAW,eAAe,OAAO,IAAI,GAAG;AACjD,aAAO,OAAO,IAAI,EAAE,QAAQ,cAAc,GAAG,CAAC;AAAA,IAChD;AAAA,EACF,SAAS,GAAG;AAAA,EAEZ;AACA,SAAO;AACT;;;ACrBA,SAAS,WAAW,QAAQ;AAC1B,MAAI,SAAS;AACb,QAAM,aAAa;AACnB,QAAM,mBAAmB,WAAW;AACpC,MAAI,UAAU;AACd,SAAO,UAAU,QAAQ;AACvB,cAAU,WAAW,OAAO,KAAK,MAAM,KAAK,OAAO,IAAI,gBAAgB,CAAC;AACxE,eAAW;AAAA,EACb;AACA,SAAO;AACT;AACA,SAAS,KAAK,KAAK,MAAM;AACvB,SAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC/B,QAAI,GAAG,IAAI,IAAI,GAAG;AAClB,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AACA,IAAM,wBAAwB,OAAO,WAAW,eAAe,QAAkC,MAAM,SAAY,YAAY;AAC7H,QAAM,mBAAmB;AACzB,MAAI;AACF,UAAM,sBAAsB,qBAAqB,gBAAgB,gBAAgB;AACjF,QAAI,qBAAqB;AACvB,aAAO,KAAK,MAAM,mBAAmB;AAAA,IACvC;AACA,UAAM,gBAAgB,MAAM,OAAO,sBAA8B;AACjE,UAAM,KAAK,MAAM,cAAc,KAAK;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AACD,UAAM,WAAW,MAAM,GAAG,IAAI;AAC9B,UAAM,aAAa,SAAS,CAAC,GAAG,SAAS,UAAU;AACnD,WAAO,WAAW;AAClB,UAAM,WAAW,cAAc,eAAe,UAAU;AACxD,UAAM,WAAW,cAAc,eAAe,SAAS,CAAC,GAAG,KAAK,YAAY,CAAC,SAAS,SAAS,aAAa,gBAAgB,YAAY,kBAAkB,gBAAgB,aAAa,gBAAgB,YAAY,UAAU,UAAU,iBAAiB,cAAc,gBAAgB,cAAc,YAAY,iBAAiB,QAAQ,aAAa,cAAc,CAAC,CAAC,CAAC;AACvW,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,yBAAqB,gBAAgB,kBAAkB,KAAK,UAAU,MAAM,CAAC;AAC7E,WAAO;AAAA,EACT,SAAS,GAAG;AACV,WAAO;AAAA,EACT;AACF;AACA,SAAS,iBAAiB;AACxB,QAAM,kBAAkB;AACxB,QAAM,sBAAsB,qBAAqB,gBAAgB,eAAe;AAChF,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,QAAQ,WAAW,EAAE,CAAC;AACxC,MAAI,qBAAqB,gBAAgB,iBAAiB,SAAS,GAAG;AACpE,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,eAAe;AACtB,QAAM,kBAAkB;AACxB,QAAM,oBAAoB,qBAAqB,kBAAkB,eAAe;AAChF,MAAI,mBAAmB;AACrB,WAAO;AAAA,EACT;AACA,QAAM,YAAY,SAAS,WAAW,EAAE,CAAC;AACzC,MAAI,qBAAqB,kBAAkB,iBAAiB,SAAS,GAAG;AACtE,WAAO;AAAA,EACT;AACA,SAAO,SAAS,WAAW,EAAE,CAAC;AAChC;AACA,eAAe,sBAAsB;AACnC,kBAAiB,OAAO,YAAY,aAAa;AAIjD,MAAI,CAAC,gBAAiB,OAAO,eAAe;AAC1C,oBAAiB,OAAO,cAAc,eAAe;AACrD,oBAAiB,OAAO,gBAAgB;AAAA,EAC1C;AACA,MAAI,CAAC,gBAAiB,OAAO,aAAa;AACxC,oBAAiB,OAAO,cAAc,MAAM,sBAAsB;AAAA,EACpE;AACA,SAAO;AACT;AACA,IAAO,sBAAQ;", "names": []}