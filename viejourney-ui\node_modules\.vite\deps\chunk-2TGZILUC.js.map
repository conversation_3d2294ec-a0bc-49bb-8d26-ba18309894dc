{"version": 3, "sources": ["../../@mui/x-internals/esm/reactMajor/index.js", "../../@mui/x-internals/esm/useComponentRenderer/useComponentRenderer.js"], "sourcesContent": ["import * as React from 'react';\nexport default parseInt(React.version, 10);", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\n/**\n * Resolves the rendering logic for a component.\n * Handles three scenarios:\n * 1. A render function that receives props and state\n * 2. A React element\n * 3. A default element\n *\n * @ignore - internal hook.\n */\nexport function useComponentRenderer(defaultElement, render, props, state = {}) {\n  if (typeof render === 'function') {\n    return render(props, state);\n  }\n  if (render) {\n    if (render.props.className) {\n      props.className = mergeClassNames(render.props.className, props.className);\n    }\n    if (render.props.style || props.style) {\n      props.style = _extends({}, props.style, render.props.style);\n    }\n    return /*#__PURE__*/React.cloneElement(render, props);\n  }\n  return /*#__PURE__*/React.createElement(defaultElement, props);\n}\nfunction mergeClassNames(className, otherClassName) {\n  if (!className || !otherClassName) {\n    return className || otherClassName;\n  }\n  return `${className} ${otherClassName}`;\n}"], "mappings": ";;;;;;;;;;;AAAA,YAAuB;AACvB,IAAO,qBAAQ,SAAe,eAAS,EAAE;;;ACAzC,IAAAA,SAAuB;AAUhB,SAAS,qBAAqB,gBAAgB,QAAQ,OAAO,QAAQ,CAAC,GAAG;AAC9E,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,QAAI,OAAO,MAAM,WAAW;AAC1B,YAAM,YAAY,gBAAgB,OAAO,MAAM,WAAW,MAAM,SAAS;AAAA,IAC3E;AACA,QAAI,OAAO,MAAM,SAAS,MAAM,OAAO;AACrC,YAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,OAAO,OAAO,MAAM,KAAK;AAAA,IAC5D;AACA,WAA0B,oBAAa,QAAQ,KAAK;AAAA,EACtD;AACA,SAA0B,qBAAc,gBAAgB,KAAK;AAC/D;AACA,SAAS,gBAAgB,WAAW,gBAAgB;AAClD,MAAI,CAAC,aAAa,CAAC,gBAAgB;AACjC,WAAO,aAAa;AAAA,EACtB;AACA,SAAO,GAAG,SAAS,IAAI,cAAc;AACvC;", "names": ["React"]}