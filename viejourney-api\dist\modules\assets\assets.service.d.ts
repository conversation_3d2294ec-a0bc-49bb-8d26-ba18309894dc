import { ConfigService } from '@nestjs/config';
import { UploadApiResponse } from 'cloudinary';
import { Request } from 'express';
import { Model } from 'mongoose';
import { Asset } from 'src/common/entities/asset.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
export declare class AssetsService {
    private readonly assetModel;
    private readonly userInfos;
    private configService;
    constructor(assetModel: Model<Asset>, userInfos: Model<UserInfos>, configService: ConfigService);
    getAllUserContentAssets(userId: string): Promise<(import("mongoose").Document<unknown, {}, Asset, {}> & Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    })[]>;
    addAssetSystem(file: Express.Multer.File, req: Request, type: string, subsection?: string | null): Promise<import("mongoose").Document<unknown, {}, Asset, {}> & Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }>;
    updateAssetById(publicId: string, file: Express.Multer.File): Promise<import("mongoose").Document<unknown, {}, Asset, {}> & Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }>;
    deleteAssetById(id: string): Promise<(import("mongoose").Document<unknown, {}, Asset, {}> & Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    }) | null>;
    getSubsection(): Promise<(string | undefined)[]>;
    getAssetsByType(type: string, subsection?: string): Promise<(import("mongoose").Document<unknown, {}, Asset, {}> & Asset & Required<{
        _id: import("mongoose").Schema.Types.ObjectId;
    }> & {
        __v: number;
    })[]>;
    fetchAllBannersBySubsection(): Promise<Record<string, any[]>>;
    uploadImage(file: any, options?: {
        public_id?: string;
        folder?: string;
    }): Promise<UploadApiResponse>;
    deleteImage(publicId: string): Promise<any>;
    getPublicIdFromUrl(url: string): string | null;
    uploadImageFromUrl(imageUrl: string, options?: {
        public_id?: string;
        folder?: string;
    }): Promise<UploadApiResponse>;
}
