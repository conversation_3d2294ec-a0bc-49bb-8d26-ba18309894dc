import {
  createSvgIcon
} from "./chunk-4YKN7KBD.js";
import {
  require_jsx_runtime
} from "./chunk-P7HSJSBW.js";
import {
  __toESM
} from "./chunk-LK32TJAX.js";

// node_modules/@mui/icons-material/esm/LocalBar.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var LocalBar_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M21 5V3H3v2l8 9v5H6v2h12v-2h-5v-5zM7.43 7 5.66 5h12.69l-1.78 2z"
}), "LocalBar");

export {
  LocalBar_default
};
//# sourceMappingURL=chunk-67IKRN6W.js.map
