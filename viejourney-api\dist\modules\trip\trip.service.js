"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripService = void 0;
const mailer_1 = require("@nestjs-modules/mailer");
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const account_service_1 = require("../account/account.service");
let TripService = class TripService {
    tripModel;
    planModel;
    userModel;
    accountModel;
    assetModel;
    accountService;
    jwtService;
    mailService;
    constructor(tripModel, planModel, userModel, accountModel, assetModel, accountService, jwtService, mailService) {
        this.tripModel = tripModel;
        this.planModel = planModel;
        this.userModel = userModel;
        this.accountModel = accountModel;
        this.assetModel = assetModel;
        this.accountService = accountService;
        this.jwtService = jwtService;
        this.mailService = mailService;
    }
    async removeTripmate(tripId, email, req) {
        const trip = await this.tripModel.findOne({ _id: tripId });
        if (!trip) {
            throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
        }
        if (trip.createdBy.toString() !== req.user?.['userId'].toString()) {
            throw new common_1.HttpException('Only the trip creator can remove tripmates', common_1.HttpStatus.FORBIDDEN);
        }
        else if (email === req.user?.['email']) {
            throw new common_1.HttpException('You cannot remove yourself from the trip', common_1.HttpStatus.BAD_REQUEST);
        }
        if (!trip.tripmates.includes(email)) {
            throw new common_1.HttpException('User not in trip', common_1.HttpStatus.NOT_FOUND);
        }
        trip.tripmates = trip.tripmates.filter((mate) => mate !== email);
        return await trip.save();
    }
    async create(createTripDto, req) {
        const [startDate, endDate] = createTripDto.dates[0] < createTripDto.dates[1]
            ? [createTripDto.dates[0], createTripDto.dates[1]]
            : [createTripDto.dates[1], createTripDto.dates[0]];
        console.log(req.user?.['userId']);
        const newTrip = new this.tripModel({
            title: `Trip to ${createTripDto.destination.name}`,
            destination: createTripDto.destination,
            startDate: startDate,
            endDate: endDate,
            createdBy: req.user?.['userId'],
            budgetRange: createTripDto?.budget,
            tripmateRange: createTripDto.travelers,
            description: createTripDto.description,
            visibility: createTripDto.visibility,
            tripmates: [req.user?.['email'], ...createTripDto.inviteEmails],
        });
        const saved = await newTrip.save();
        if (createTripDto.inviteEmails?.length) {
            await Promise.all(createTripDto.inviteEmails.map((email) => {
                const secret = process.env.JWT_SECRET || 'secret';
                const token = this.jwtService.sign({ sub: email, email: email, tripId: saved._id }, {
                    secret: secret,
                    expiresIn: '24h',
                });
                const joinLink = `${process.env.FE_URL}/trips/${saved._id}/join?token=${token}`;
                return this.mailService.sendMail({
                    to: email,
                    subject: 'You are invited to join a trip!',
                    template: './invitation',
                    context: {
                        destination: createTripDto.destination.name,
                        joinLink,
                    },
                });
            }));
        }
        return saved;
    }
    async validateInvite(tripId, token) {
        let decodedToken;
        try {
            if (!token) {
                throw new common_1.HttpException('No invitation token provided', common_1.HttpStatus.BAD_REQUEST);
            }
            const secret = process.env.JWT_SECRET || 'secret';
            decodedToken = this.jwtService.verify(token, {
                secret: secret,
            });
            console.log('Decoded token:', decodedToken);
            console.log(tripId);
            if (!decodedToken.tripId || !decodedToken.email) {
                throw new common_1.HttpException('Invalid token format', common_1.HttpStatus.BAD_REQUEST);
            }
            if (decodedToken.tripId !== tripId) {
                throw new common_1.HttpException('Invalid invitation token for this trip', common_1.HttpStatus.BAD_REQUEST);
            }
            if (decodedToken.expiresAt &&
                new Date() > new Date(decodedToken.expiresAt)) {
                throw new common_1.HttpException('Invitation token has expired', common_1.HttpStatus.BAD_REQUEST);
            }
            const trip = await this.tripModel.findById(tripId);
            if (!trip) {
                throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
            }
            const email = decodedToken.email;
            const isInvited = trip.tripmates.includes(email);
            if (!isInvited) {
                throw new common_1.HttpException('Your email is not on the invitation list', common_1.HttpStatus.BAD_REQUEST);
            }
            const existingUser = await this.userModel.findOne({ email });
            const hasAccount = !!existingUser;
            return {
                valid: true,
                trip: {
                    id: trip._id,
                    title: trip.title,
                    destination: trip.destination,
                    startDate: trip.startDate,
                    endDate: trip.endDate,
                },
                userExists: hasAccount,
                email: email,
            };
        }
        catch (error) {
            throw new common_1.HttpException('Error validating invitation token: ' + error.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async addToTrip(tripId, token) {
        const trip = await this.tripModel.findOne({ _id: tripId });
        if (!trip) {
            throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
        }
        try {
            const jwt = this.jwtService.verify(token);
            const email = jwt.sub;
            const account = await this.accountService.findByEmail(email);
            if (!account) {
                throw new common_1.HttpException('User not exists', common_1.HttpStatus.UNAUTHORIZED);
            }
            if (trip.tripmates.includes(account.email)) {
                throw new common_1.HttpException('User already joined', common_1.HttpStatus.NO_CONTENT);
            }
            trip.tripmates.push(account.email);
            await trip.save();
        }
        catch (err) {
            throw new common_1.HttpException('Invalid token', common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async findByUser(email) {
        try {
            const trips = await this.tripModel
                .find()
                .where({
                tripmates: { $in: [email] },
            })
                .populate({
                path: 'coverImage',
                model: 'Asset',
                select: 'url',
            });
            if (!trips || trips.length === 0) {
                throw new common_1.HttpException('No trips found for this user', common_1.HttpStatus.NOT_FOUND);
            }
            return trips;
        }
        catch (error) {
            console.error(error);
        }
    }
    findAll() {
        return `This action returns all trip`;
    }
    findOne(id) {
        const trip = this.tripModel
            .findOne({
            _id: id,
        })
            .populate({
            path: 'coverImage',
            model: 'Asset',
            select: 'url',
        })
            .exec();
        if (!trip)
            throw new common_1.HttpException(`No trip found with id ${id}`, common_1.HttpStatus.BAD_REQUEST);
        return trip;
    }
    update(id, updateTripDto) {
        return `This action updates a #${id} trip`;
    }
    async updatePlan(tripId, plan, userId) {
        return this.planModel
            .findOneAndUpdate({ tripId: new mongoose_2.Types.ObjectId(tripId) }, {
            plan,
            lastUpdated: new Date(),
            lastUpdatedBy: userId ? new mongoose_2.Types.ObjectId(userId) : undefined,
        }, { upsert: true, new: true })
            .exec();
    }
    remove(id) {
        return `This action removes a #${id} trip`;
    }
    async inviteToTrip(tripId, email) {
        try {
            const trip = await this.tripModel.findOne({ _id: tripId });
            if (!trip) {
                throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
            }
            const secret = process.env.JWT_SECRET || 'secret';
            const expiresAt = new Date();
            expiresAt.setDate(expiresAt.getDate() + 7);
            const token = this.jwtService.sign({
                sub: email,
                email,
                tripId: trip._id,
                expiresAt: expiresAt.toISOString(),
            }, { secret: secret });
            const joinLink = `${process.env.FE_URL}/trips/${trip._id}/join?token=${token}`;
            const result = await this.mailService.sendMail({
                to: email,
                subject: 'You are invited to join a trip!',
                template: './invitation',
                context: {
                    destination: trip.destination.name,
                    joinLink,
                },
            });
            if (result) {
                await this.tripModel.updateOne({ _id: tripId }, { $addToSet: { tripmates: email } });
            }
            return {
                success: true,
                message: `Invitation sent to ${email} (fallback plain text)`,
                expiresAt,
            };
        }
        catch (error) {
            console.error('Failed to send invitation email:', error);
            throw new common_1.HttpException(`Failed to send invitation: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findPlanByTripId(tripId) {
        return this.planModel
            .findOne({ tripId: new mongoose_2.Types.ObjectId(tripId) })
            .exec();
    }
    async updatePlanDates(tripId, startDate, endDate) {
        console.log(`Updating plan dates for trip ${tripId}: ${startDate} to ${endDate}`);
        const updatedPlan = await this.tripModel
            .findOneAndUpdate({ _id: new mongoose_2.Types.ObjectId(tripId) }, {
            startDate: startDate,
            endDate: endDate,
        }, { new: true })
            .exec();
        return updatedPlan;
    }
    async updateTripCoverImage(tripId, assetId, userId) {
        try {
            const asset = await this.assetModel.findOne({
                _id: new mongoose_2.Types.ObjectId(assetId),
                userId: new mongoose_2.Types.ObjectId(userId),
            });
            if (!asset) {
                throw new common_1.HttpException('Asset not found', common_1.HttpStatus.NOT_FOUND);
            }
            const updatedTrip = await this.tripModel
                .findOneAndUpdate({ _id: new mongoose_2.Types.ObjectId(tripId) }, { coverImage: asset?._id }, { new: true })
                .populate({
                path: 'coverImage',
                model: 'Asset',
                select: 'url',
            })
                .exec();
            if (!updatedTrip) {
                throw new common_1.HttpException('Trip not found', common_1.HttpStatus.NOT_FOUND);
            }
            return updatedTrip;
        }
        catch (error) {
            console.error(error);
        }
    }
};
exports.TripService = TripService;
exports.TripService = TripService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)('Trip')),
    __param(1, (0, mongoose_1.InjectModel)('Plan')),
    __param(2, (0, mongoose_1.InjectModel)('User')),
    __param(3, (0, mongoose_1.InjectModel)('Account')),
    __param(4, (0, mongoose_1.InjectModel)('Asset')),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        account_service_1.AccountService,
        jwt_1.JwtService,
        mailer_1.MailerService])
], TripService);
//# sourceMappingURL=trip.service.js.map