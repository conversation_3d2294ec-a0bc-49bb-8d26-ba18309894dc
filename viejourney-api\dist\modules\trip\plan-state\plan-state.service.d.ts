import { TripService } from '../trip.service';
import { Plan } from 'src/common/entities/plan.entity';
export type PlanSection = keyof Plan;
type AddPayload<T extends PlanSection> = Plan[T] extends Array<infer U> ? Omit<U, 'id'> : never;
type UpdatePayload<T extends PlanSection> = Plan[T] extends Array<infer U> ? U : Partial<Plan[T]>;
type DeletePayload<T extends PlanSection> = Plan[T] extends Array<infer U> ? U extends {
    id: infer ID;
} ? ID : never : never;
export interface AddMessage<T extends PlanSection> {
    section: T;
    item: AddPayload<T>;
}
export interface UpdateMessage<T extends PlanSection> {
    section: T;
    item: UpdatePayload<T>;
}
export interface DeleteMessage<T extends PlanSection> {
    section: T;
    itemId: DeletePayload<T>;
}
export declare class PlanStateService {
    private readonly tripService;
    private readonly saveDelayMillis;
    private planStates;
    constructor(tripService: TripService);
    addItem<T extends PlanSection>(tripId: string, section: T, item: AddPayload<T>, user?: {
        id?: string;
        email?: string;
        fullName?: string;
    }): string;
    updateItem<T extends PlanSection>(tripId: string, section: T, item: UpdatePayload<T>, user?: string): void;
    deleteItem<T extends PlanSection>(tripId: string, section: T, itemId: DeletePayload<T>): void;
    private savingStatus;
    isSavingPlan(tripId: string): boolean;
    savePlan(tripId: string): Promise<void>;
    notifySaveStatus?: (tripId: string, status: 'saving' | 'saved' | 'error', errorMessage?: string) => void;
    private emitSaveStatus;
    scheduleSave(tripId: string): void;
    getOrCreatePlan(tripId: string): Plan;
    initializePlan(tripId: string, plan: Plan): void;
    forceSave(tripId: string): Promise<void>;
}
export {};
