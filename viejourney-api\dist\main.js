"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const cookieParser = require("cookie-parser");
const session = require("express-session");
const passport = require("passport");
const bodyParser = require("body-parser");
const allowedOrigins = ['http://localhost:5173', 'https://vie-journey.site'];
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule, {
        logger: ['error', 'warn', 'log'],
    });
    app.setGlobalPrefix('api');
    app.enableCors({
        origin: function (origin, callback) {
            if (!origin || allowedOrigins.includes(origin)) {
                callback(null, true);
            }
            else {
                callback(new Error('Not allowed by CORS'));
            }
        },
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        credentials: true,
    });
    app.use(cookieParser());
    app.use(session({
        secret: process.env.SESSION_SECRET || 'vie-journey-secret',
        resave: false,
        saveUninitialized: false,
        cookie: {
            maxAge: 60000 * 60,
            secure: false,
        },
    }));
    const expressApp = app.getHttpAdapter().getInstance();
    expressApp.set('strict routing', true);
    app.use(bodyParser.json({ limit: '10mb' }));
    app.use(passport.initialize());
    app.use(passport.session());
    await app.listen(process.env.PORT ?? 5000);
}
bootstrap();
//# sourceMappingURL=main.js.map