import { MailerService } from '@nestjs-modules/mailer';
import { HttpStatus } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { Model } from 'mongoose';
import { AccountService } from '../account/account.service';
import { Account } from 'src/common/entities/account.entity';
import { UserInfos } from 'src/common/entities/userInfos.entity';
import { Status } from 'src/common/enums/status.enum';
import { AssetsService } from '../assets/assets.service';
import { Asset } from 'src/common/entities/asset.entity';
export declare class AuthService {
    private readonly accountService;
    private readonly assetsService;
    private readonly jwtService;
    private readonly accountModel;
    private readonly userModel;
    private readonly assetModel;
    private readonly mailService;
    private readonly assetService;
    private readonly logger;
    constructor(accountService: AccountService, assetsService: AssetsService, jwtService: JwtService, accountModel: Model<Account>, userModel: Model<UserInfos>, assetModel: Model<Asset>, mailService: MailerService, assetService: AssetsService);
    resendVerificationEmail(email: string, res: Response): Promise<void>;
    verifyEmail(token: string): Promise<HttpStatus>;
    sendForgotPasswordEmail(email: string): Promise<void>;
    forgotPassword(token: string, password: string): Promise<HttpStatus>;
    sendRegistrationEmail(user: Account): Promise<void>;
    register(email: string, password: string): Promise<HttpStatus>;
    login(res: Response, email: string, password: string): Promise<{
        userId: string;
        accessToken: string;
    }>;
    logout(req: Request, res: Response): Promise<HttpStatus>;
    refresh(req: Request, res: Response): Promise<{
        accessToken: string;
    }>;
    private createAccessToken;
    private createRefreshToken;
    sendEMail(token: string, mail: string): Promise<void>;
    googleAuth(profile: any, res: Response): Promise<void>;
    validateAccessToken(accessToken: string): Promise<{
        userId: string;
        status: Status;
    } | null | undefined>;
}
